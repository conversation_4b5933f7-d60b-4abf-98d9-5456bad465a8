"""
AI视频内容创作系统 - 多平台分发API (稳定版)

提供多平台分发的RESTful API接口，包括：
- 平台管理
- 分发任务管理
- 数据分析
- 状态监控
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from ..services.content_compliance_service import ContentComplianceService
from ..services.platform_service import platform_service
from ..services.system_monitoring_service import SystemMonitoringService

logger = logging.getLogger(__name__)

router = APIRouter()

# 初始化服务
compliance_service = ContentComplianceService()
monitoring_service = SystemMonitoringService()


# ==================== 请求模型 ====================


class ContentComplianceRequest(BaseModel):
    """内容合规检查请求"""

    content_type: str = Field(..., description="内容类型: text/image/video")
    content: Optional[str] = Field(None, description="文本内容")
    file_path: Optional[str] = Field(None, description="文件路径")
    platform: Optional[str] = Field(None, description="目标平台")
    rules: Optional[List[str]] = Field(None, description="指定检查规则")


class BatchComplianceRequest(BaseModel):
    """批量合规检查请求"""

    content_list: List[Dict[str, Any]] = Field(..., description="内容列表")
    platform: Optional[str] = Field(None, description="目标平台")


class PlatformRequirementRequest(BaseModel):
    """平台要求查询请求"""

    platform_type: str = Field(..., description="平台类型")


# ==================== 系统监控API ====================


@router.get("/monitoring/system/overview")
async def get_system_overview():
    """获取系统总览"""
    try:
        overview = await monitoring_service.get_system_overview()
        return {
            "system_overview": overview,
            "query_time": datetime.now().isoformat(),
        }
    except Exception as e:
        logger.error(f"获取系统总览失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统总览失败")


@router.get("/monitoring/services/health")
async def check_all_services_health():
    """检查所有服务健康状态"""
    try:
        health_results = await monitoring_service.check_all_services_health()
        return {
            "health_results": {
                name: {
                    "service_name": result.service_name,
                    "status": result.status.value,
                    "response_time_ms": result.response_time_ms,
                    "details": result.details,
                    "timestamp": result.timestamp.isoformat(),
                    "error_message": result.error_message,
                }
                for name, result in health_results.items()
            },
            "check_time": datetime.now().isoformat(),
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="健康检查失败")


@router.get("/monitoring/services/{service_name}/health")
async def check_service_health(service_name: str):
    """检查单个服务健康状态"""
    try:
        health_result = await monitoring_service.check_service_health(service_name)
        return {
            "health_result": {
                "service_name": health_result.service_name,
                "status": health_result.status.value,
                "response_time_ms": health_result.response_time_ms,
                "details": health_result.details,
                "timestamp": health_result.timestamp.isoformat(),
                "error_message": health_result.error_message,
            },
            "check_time": datetime.now().isoformat(),
        }
    except Exception as e:
        logger.error(f"检查服务健康状态失败: {e}")
        raise HTTPException(status_code=500, detail="检查服务健康状态失败")


@router.get("/monitoring/system/metrics")
async def get_system_metrics():
    """获取系统性能指标"""
    try:
        metrics = await monitoring_service.collect_system_metrics()
        return {
            "system_metrics": {
                "cpu_percent": metrics.cpu_percent,
                "memory_percent": metrics.memory_percent,
                "disk_percent": metrics.disk_percent,
                "network_io": metrics.network_io,
                "active_connections": metrics.active_connections,
                "process_count": metrics.process_count,
                "timestamp": metrics.timestamp.isoformat(),
            },
            "collection_time": datetime.now().isoformat(),
        }
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统指标失败")


# ==================== 内容合规API ====================


@router.post("/compliance/check")
async def check_content_compliance(request: ContentComplianceRequest):
    """检查内容合规性"""
    try:
        if request.content_type == "text":
            result = await compliance_service.check_text_compliance(
                request.content or "", request.platform
            )
        elif request.content_type == "image":
            result = await compliance_service.check_image_compliance(
                request.file_path or "", request.platform
            )
        elif request.content_type == "video":
            result = await compliance_service.check_video_compliance(
                request.file_path or "", request.platform
            )
        else:
            raise HTTPException(status_code=400, detail="不支持的内容类型")

        return {
            "compliance_result": result,
            "query_time": datetime.now().isoformat(),
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"内容合规检查失败: {e}")
        raise HTTPException(status_code=500, detail="内容合规检查失败")


@router.post("/compliance/batch")
async def batch_compliance_check(request: BatchComplianceRequest):
    """批量内容合规检查"""
    try:
        result = await compliance_service.batch_compliance_check(
            request.content_list, request.platform
        )
        return {
            "batch_result": result,
            "query_time": datetime.now().isoformat(),
        }
    except Exception as e:
        logger.error(f"批量合规检查失败: {e}")
        raise HTTPException(status_code=500, detail="批量合规检查失败")


@router.get("/compliance/service/status")
async def get_compliance_service_status():
    """获取合规检查服务状态"""
    try:
        status = await compliance_service.get_service_status()
        return {
            "service_status": status,
            "query_time": datetime.now().isoformat(),
        }
    except Exception as e:
        logger.error(f"获取合规服务状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取合规服务状态失败")


# ==================== 平台管理API ====================


@router.get("/platforms")
async def get_platforms():
    """获取支持的平台列表"""
    try:
        platforms = platform_service.get_platforms()
        return {
            "success": True,
            "platforms": platforms,
            "total": len(platforms),
        }
    except Exception as e:
        logger.error(f"获取平台列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取平台列表失败")


@router.get("/platforms/{platform_id}")
async def get_platform_info(platform_id: str):
    """获取平台详细信息"""
    try:
        platform_info = platform_service.get_platform_info(platform_id)
        if not platform_info:
            raise HTTPException(status_code=404, detail="平台不存在")

        return {"success": True, "platform": platform_info}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取平台信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取平台信息失败")


@router.post("/platforms/requirements")
async def get_platform_requirements(request: PlatformRequirementRequest):
    """获取平台内容要求"""
    try:
        requirements = compliance_service.get_platform_rules(request.platform_type)
        return {
            "success": True,
            "platform_type": request.platform_type,
            "requirements": requirements,
        }
    except Exception as e:
        logger.error(f"获取平台要求失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 基础分发API ====================


@router.post("/distribution/create-task")
async def create_basic_distribution_task(
    content: Dict[str, Any],
    platforms: List[str],
    schedule_time: Optional[datetime] = None,
):
    """创建基础分发任务"""
    try:
        # 临时返回模拟结果，避免循环导入
        return {
            "success": True,
            "task_id": f"task_{datetime.now().timestamp()}",
            "status": "created",
            "platforms": platforms,
            "message": "分发任务创建成功(模拟)",
        }
    except Exception as e:
        logger.error(f"创建分发任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/distribution/task/{task_id}")
async def get_distribution_task_status(task_id: str):
    """获取分发任务状态"""
    try:
        # 临时返回模拟结果
        return {
            "success": True,
            "task_id": task_id,
            "status": "completed",
            "progress": 100,
            "message": "任务状态查询成功(模拟)",
        }
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "compliance": "ready",
            "monitoring": "ready",
            "platform": "ready",
        },
    }
