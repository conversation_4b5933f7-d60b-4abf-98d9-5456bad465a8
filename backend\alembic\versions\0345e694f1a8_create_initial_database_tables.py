"""Create initial database tables

Revision ID: 0345e694f1a8
Revises: c373bbffc202
Create Date: 2025-07-09 08:57:02.751280

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "0345e694f1a8"
down_revision: Union[str, Sequence[str], None] = "c373bbffc202"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###
