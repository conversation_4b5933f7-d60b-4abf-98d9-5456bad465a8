#!/usr/bin/env python3
"""
AI视频内容创作系统 - 任务队列API
提供任务创建、状态查询、队列管理等功能的REST API接口
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from ..utils.auth_deps import get_current_user
from ..services.task_queue_service import (
    Task,
    TaskPriority,
    TaskStatus,
    task_queue_service,
)
from ..models import User
from ..core.pagination import PaginationParams, Paginator
from ..core.cache import CacheManager

router = APIRouter(prefix="/api/v1/tasks", tags=["任务队列"])


class TaskCreateRequest(BaseModel):
    """任务创建请求"""

    name: str
    task_type: str
    input_data: Dict[str, Any] = {}
    priority: TaskPriority = TaskPriority.NORMAL
    description: Optional[str] = None


class TaskResponse(BaseModel):
    """任务响应"""

    task_id: str
    name: str
    description: Optional[str]
    task_type: str
    priority: TaskPriority
    status: TaskStatus
    progress: float
    steps_total: int
    steps_completed: int
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]]
    error_message: Optional[str]
    created_at: str
    started_at: Optional[str]
    completed_at: Optional[str]
    updated_at: str
    user_id: Optional[str]
    retry_count: int
    max_retries: int


def _task_to_response(task: Task) -> TaskResponse:
    """转换任务对象为响应格式"""
    return TaskResponse(
        task_id=task.task_id,
        name=task.name,
        description=task.description,
        task_type=task.task_type,
        priority=task.priority,
        status=task.status,
        progress=task.progress,
        steps_total=task.steps_total,
        steps_completed=task.steps_completed,
        input_data=task.input_data,
        output_data=task.output_data,
        error_message=task.error_message,
        created_at=task.created_at.isoformat(),
        started_at=task.started_at.isoformat() if task.started_at else None,
        completed_at=(task.completed_at.isoformat() if task.completed_at else None),
        updated_at=task.updated_at.isoformat(),
        user_id=task.user_id,
        retry_count=task.retry_count,
        max_retries=task.max_retries,
    )


@router.post("/")
async def create_task(
    request: TaskCreateRequest, current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """创建任务"""
    try:
        task_id = task_queue_service.create_task(
            name=request.name,
            task_type=request.task_type,
            input_data=request.input_data,
            priority=request.priority,
            user_id=current_user.user_id,
            description=request.description,
        )

        return {"task_id": task_id, "message": f"任务创建成功: {request.name}"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/")
async def list_tasks(
    params: PaginationParams = Depends(),
    status: Optional[TaskStatus] = None,
    current_user: User = Depends(get_current_user),
) -> PaginatedResponse[TaskResponse]:
    """列出当前用户的任务"""
    try:
        cache = CacheManager()
        cache_key = f"tasks:{current_user.user_id}:{status}:{params.page}:{params.size}"
        cached = cache.get(cache_key)
        if cached:
            return PaginatedResponse(**cached)

        tasks = task_queue_service.list_tasks(
            user_id=current_user.user_id, status=status
        )
        paginator = Paginator(params)
        paginated = paginator.paginate_list([_task_to_response(task) for task in tasks])

        cache.set(cache_key, paginated.dict(), expire=300)  # 缓存5分钟
        return paginated
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}")
async def get_task(
    task_id: str, current_user: User = Depends(get_current_user)
) -> TaskResponse:
    """获取任务详情"""
    task = task_queue_service.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    # 检查权限：只能查看自己的任务或管理员可以查看所有任务
    if task.user_id != current_user.user_id and current_user.role.value != "admin":
        raise HTTPException(status_code=403, detail="无权访问此任务")

    return _task_to_response(task)


@router.delete("/{task_id}")
async def cancel_task(
    task_id: str, current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """取消任务"""
    task = task_queue_service.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    # 检查权限：只能取消自己的任务或管理员可以取消所有任务
    if task.user_id != current_user.user_id and current_user.role.value != "admin":
        raise HTTPException(status_code=403, detail="无权取消此任务")

    if task_queue_service.cancel_task(task_id):
        return {"message": f"任务已取消: {task_id}"}
    else:
        raise HTTPException(status_code=400, detail="无法取消此任务")


@router.get("/stats/overview")
async def get_task_stats() -> Dict[str, Any]:
    """获取任务统计信息"""
    try:
        return task_queue_service.get_service_stats()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start")
async def start_task_service() -> Dict[str, str]:
    """启动任务队列服务"""
    try:
        task_queue_service.start()
        return {"message": "任务队列服务已启动"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop")
async def stop_task_service() -> Dict[str, str]:
    """停止任务队列服务"""
    try:
        task_queue_service.stop()
        return {"message": "任务队列服务已停止"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 健康检查端点
@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        stats = task_queue_service.get_service_stats()

        health_status = "healthy" if stats["service_running"] else "degraded"

        return {
            "status": health_status,
            "service_info": {
                "service_running": stats["service_running"],
                "total_tasks": stats["total_tasks"],
                "available_workers": (stats["worker_stats"]["available_workers"]),
                "service_type": "task_queue",
            },
            "timestamp": "2025-07-07T14:00:00Z",
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2025-07-07T14:00:00Z",
        }
