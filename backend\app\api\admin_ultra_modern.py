"""
管理界面API - 纯后端接口
提供前端所需的数据接口，不再返回HTML
前后端完全分离架构
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import RedirectResponse
from contextlib import contextmanager
import pymysql
import threading
from typing import Dict, Any

router = APIRouter(tags=["管理界面API"])

# MySQL数据库配置
MYSQL_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "video_user",
    "password": "VideoSystem2024!@#",
    "database": "video_system",
    "charset": "utf8mb4",
}

# 数据库连接池
_db_lock = threading.Lock()


@contextmanager
def get_db_connection():
    """获取MySQL数据库连接的上下文管理器"""
    conn = None
    try:
        with _db_lock:
            conn = pymysql.connect(**MYSQL_CONFIG)
            yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"数据库连接失败: {str(e)}")
    finally:
        if conn:
            conn.close()


# API路由定义


@router.get("/")
async def admin_redirect():
    """重定向到前端管理界面"""
    return RedirectResponse(url="http://localhost:3000/admin", status_code=302)


@router.get("/dashboard-data")
async def get_dashboard_data() -> Dict[str, Any]:
    """获取仪表盘数据 - 纯API接口，优化查询性能"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 优化：使用单个查询获取用户统计
            cursor.execute(
                """
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active
                FROM users
            """
            )
            user_stats = cursor.fetchone()
            total_users = user_stats[0]
            active_users = user_stats[1]

            # 优化：使用单个查询获取项目统计
            cursor.execute(
                """
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active
                FROM projects
            """
            )
            project_stats = cursor.fetchone()
            total_projects = project_stats[0]
            active_projects = project_stats[1]

            return {
                "users": {
                    "total": total_users,
                    "active": active_users,
                    "inactive": total_users - active_users,
                },
                "projects": {
                    "total": total_projects,
                    "active": active_projects,
                    "inactive": total_projects - active_projects,
                },
                "system": {
                    "uptime": "99.9%",
                    "response_time": "45ms",
                    "cpu_usage": "12%",
                    "memory_usage": "68%",
                },
                "recent_activities": [
                    {"action": "用户登录", "user": "admin", "time": "2分钟前"},
                    {"action": "创建项目", "user": "zhang123", "time": "5分钟前"},
                    {"action": "更新设置", "user": "admin", "time": "10分钟前"},
                ],
                "charts": {
                    "user_growth": [65, 59, 80, 81, 56, 55, 40],
                    "project_completion": [28, 48, 40, 19, 86, 27, 90],
                    "system_usage": [12, 19, 3, 5, 2, 3, 8],
                },
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仪表盘数据失败: {str(e)}")


@router.get("/users-data")
async def get_users_data() -> Dict[str, Any]:
    """获取用户管理数据"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute(
                "SELECT id, username, email, status, role, created_at FROM users ORDER BY id"
            )
            rows = cursor.fetchall()

            users = []
            for row in rows:
                users.append(
                    {
                        "id": row[0],
                        "username": row[1],
                        "email": row[2],
                        "status": row[3],
                        "role": row[4],
                        "created_at": row[5],
                    }
                )

            return {
                "users": users,
                "total": len(users),
                "statistics": {
                    "active": len([u for u in users if u["status"] == "active"]),
                    "suspended": len([u for u in users if u["status"] == "suspended"]),
                    "admins": len([u for u in users if u["role"] == "admin"]),
                    "regular_users": len([u for u in users if u["role"] == "user"]),
                },
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户数据失败: {str(e)}")


@router.get("/projects-data")
async def get_projects_data() -> Dict[str, Any]:
    """获取项目管理数据"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute(
                "SELECT id, name, description, status, type, created_at FROM projects ORDER BY id"
            )
            rows = cursor.fetchall()

            projects = []
            for row in rows:
                projects.append(
                    {
                        "id": row[0],
                        "name": row[1],
                        "description": row[2],
                        "status": row[3],
                        "type": row[4],
                        "created_at": row[5],
                    }
                )

            return {
                "projects": projects,
                "total": len(projects),
                "statistics": {
                    "active": len([p for p in projects if p["status"] == "active"]),
                    "paused": len([p for p in projects if p["status"] == "paused"]),
                    "completed": len(
                        [p for p in projects if p["status"] == "completed"]
                    ),
                    "video_projects": len(
                        [p for p in projects if p["type"] == "video"]
                    ),
                    "content_projects": len(
                        [p for p in projects if p["type"] == "content"]
                    ),
                },
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目数据失败: {str(e)}")


@router.get("/system-logs")
async def get_system_logs() -> Dict[str, Any]:
    """获取系统日志"""
    # 模拟系统日志数据
    logs = [
        {
            "id": 1,
            "timestamp": "2024-01-19 12:00:00",
            "level": "INFO",
            "message": "用户登录成功",
            "user": "admin",
            "ip": "127.0.0.1",
        },
        {
            "id": 2,
            "timestamp": "2024-01-19 11:58:00",
            "level": "INFO",
            "message": "项目创建成功",
            "user": "zhang123",
            "ip": "*************",
        },
        {
            "id": 3,
            "timestamp": "2024-01-19 11:55:00",
            "level": "WARNING",
            "message": "API调用频率过高",
            "user": "li456",
            "ip": "*************",
        },
    ]

    return {
        "logs": logs,
        "total": len(logs),
        "summary": {
            "info": len([l for l in logs if l["level"] == "INFO"]),
            "warning": len([l for l in logs if l["level"] == "WARNING"]),
            "error": len([l for l in logs if l["level"] == "ERROR"]),
        },
    }


@router.get("/notifications")
async def get_notifications() -> Dict[str, Any]:
    """获取系统通知"""
    notifications = [
        {
            "id": 1,
            "type": "info",
            "title": "系统更新",
            "message": "系统将在今晚进行维护更新",
            "timestamp": "2024-01-19 10:00:00",
            "read": False,
        },
        {
            "id": 2,
            "type": "warning",
            "title": "存储空间",
            "message": "存储空间使用率已达到80%",
            "timestamp": "2024-01-19 09:30:00",
            "read": True,
        },
    ]

    return {
        "notifications": notifications,
        "total": len(notifications),
        "unread": len([n for n in notifications if not n["read"]]),
    }


@router.get("/services-status")
async def get_services_status() -> Dict[str, Any]:
    """获取服务状态"""
    services = [
        {
            "name": "数据库",
            "status": "running",
            "uptime": "99.9%",
            "response_time": "2ms",
        },
        {
            "name": "AI服务",
            "status": "running",
            "uptime": "98.5%",
            "response_time": "150ms",
        },
        {
            "name": "文件存储",
            "status": "running",
            "uptime": "99.8%",
            "response_time": "5ms",
        },
        {
            "name": "缓存服务",
            "status": "warning",
            "uptime": "95.2%",
            "response_time": "1ms",
        },
        {
            "name": "消息队列",
            "status": "running",
            "uptime": "99.1%",
            "response_time": "3ms",
        },
    ]

    return {
        "services": services,
        "total": len(services),
        "healthy": len([s for s in services if s["status"] == "running"]),
        "warning": len([s for s in services if s["status"] == "warning"]),
        "error": len([s for s in services if s["status"] == "error"]),
    }
