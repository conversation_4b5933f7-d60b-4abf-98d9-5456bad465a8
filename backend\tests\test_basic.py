"""基础测试
验证测试框架是否正常工作
"""

import pytest
from unittest.mock import Mock


def test_basic_functionality():
    """测试基本功能"""
    assert 1 + 1 == 2
    assert "hello" == "hello"
    assert [1, 2, 3] == [1, 2, 3]


def test_mock_functionality():
    """测试Mock功能"""
    mock_obj = Mock()
    mock_obj.method.return_value = "test_result"
    
    result = mock_obj.method()
    assert result == "test_result"
    mock_obj.method.assert_called_once()


@pytest.mark.asyncio
async def test_async_functionality():
    """测试异步功能"""
    async def async_function():
        return "async_result"
    
    result = await async_function()
    assert result == "async_result"


class TestBasicClass:
    """基础测试类"""
    
    def test_class_method(self):
        """测试类方法"""
        assert True
    
    def test_string_operations(self):
        """测试字符串操作"""
        text = "Hello World"
        assert text.lower() == "hello world"
        assert text.upper() == "HELLO WORLD"
        assert len(text) == 11
    
    def test_list_operations(self):
        """测试列表操作"""
        numbers = [1, 2, 3, 4, 5]
        assert len(numbers) == 5
        assert sum(numbers) == 15
        assert max(numbers) == 5
        assert min(numbers) == 1
    
    def test_dict_operations(self):
        """测试字典操作"""
        data = {"name": "John", "age": 30, "city": "New York"}
        assert data["name"] == "John"
        assert "age" in data
        assert len(data) == 3


@pytest.mark.parametrize("input_value,expected", [
    (1, 2),
    (2, 4),
    (3, 6),
    (4, 8),
    (5, 10)
])
def test_parametrized_function(input_value, expected):
    """测试参数化测试"""
    def double(x):
        return x * 2
    
    result = double(input_value)
    assert result == expected


def test_exception_handling():
    """测试异常处理"""
    with pytest.raises(ZeroDivisionError):
        result = 1 / 0
    
    with pytest.raises(ValueError):
        int("not_a_number")
    
    with pytest.raises(KeyError):
        data = {"key": "value"}
        _ = data["nonexistent_key"]


def test_fixtures_basic(temp_file):
    """测试基础夹具"""
    # temp_file 夹具应该在 conftest.py 中定义
    assert temp_file is not None
    # 可以写入文件
    with open(temp_file, "w") as f:
        f.write("test content")
    
    # 可以读取文件
    with open(temp_file, "r") as f:
        content = f.read()
        assert content == "test content"


class TestEnvironmentSetup:
    """测试环境设置"""
    
    def test_environment_variables(self):
        """测试环境变量设置"""
        import os
        
        # 检查测试环境变量是否正确设置
        assert os.getenv("ENVIRONMENT") == "test"
        assert os.getenv("DEBUG") == "true"
        assert os.getenv("SECRET_KEY") is not None
        assert len(os.getenv("SECRET_KEY", "")) >= 32
    
    def test_settings_import(self):
        """测试设置导入"""
        try:
            from app.core.config import settings
            assert settings.ENVIRONMENT == "test"
            assert settings.DEBUG is True
        except ImportError:
            # 如果导入失败，这是预期的，因为我们在conftest.py中有fallback
            pytest.skip("App modules not available, using mock settings")


@pytest.mark.integration
class TestIntegrationBasic:
    """基础集成测试"""
    
    def test_mock_integration(self):
        """测试模拟集成"""
        # 创建多个相互依赖的模拟对象
        service_a = Mock()
        service_b = Mock()
        
        service_a.get_data.return_value = {"id": 1, "name": "test"}
        service_b.process_data.return_value = "processed"
        
        # 模拟工作流
        data = service_a.get_data()
        result = service_b.process_data(data)
        
        assert data["id"] == 1
        assert result == "processed"
        
        service_a.get_data.assert_called_once()
        service_b.process_data.assert_called_once_with(data)


if __name__ == "__main__":
    pytest.main([__file__])