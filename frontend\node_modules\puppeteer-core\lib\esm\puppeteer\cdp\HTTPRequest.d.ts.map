{"version": 3, "file": "HTTPRequest.d.ts", "sourceRoot": "", "sources": ["../../../../src/cdp/HTTPRequest.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AACrD,OAAO,KAAK,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAC;AAC3C,OAAO,EACL,KAAK,wBAAwB,EAE7B,WAAW,EACX,KAAK,YAAY,EACjB,KAAK,kBAAkB,EAGxB,MAAM,uBAAuB,CAAC;AAI/B,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,mBAAmB,CAAC;AAEvD;;GAEG;AACH,qBAAa,cAAe,SAAQ,WAAW;;IACpC,EAAE,EAAE,MAAM,CAAC;IACZ,cAAc,EAAE,cAAc,EAAE,CAAC;IACjC,SAAS,EAAE,eAAe,GAAG,IAAI,CAAC;IAe1C,IAAa,MAAM,IAAI,UAAU,CAEhC;IAED,IAAa,MAAM,CAAC,SAAS,EAAE,UAAU,EAExC;gBAGC,MAAM,EAAE,UAAU,EAClB,KAAK,EAAE,KAAK,GAAG,IAAI,EACnB,cAAc,EAAE,MAAM,GAAG,SAAS,EAClC,iBAAiB,EAAE,OAAO,EAC1B,IAAI,EAAE;QACJ;;WAEG;QACH,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;QACtC;;WAEG;QACH,QAAQ,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC;QACrC;;WAEG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB;;WAEG;QACH,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;QAClC;;WAEG;QACH,SAAS,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;QACvC;;WAEG;QACH,IAAI,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC;KACtC,EACD,aAAa,EAAE,cAAc,EAAE;IAwBxB,GAAG,IAAI,MAAM;IAIb,YAAY,IAAI,YAAY;IAI5B,MAAM,IAAI,MAAM;IAIhB,QAAQ,IAAI,MAAM,GAAG,SAAS;IAI9B,WAAW,IAAI,OAAO;IAIhB,aAAa,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;IAYlD,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAIjC,QAAQ,IAAI,eAAe,GAAG,IAAI;IAIlC,KAAK,IAAI,KAAK,GAAG,IAAI;IAIrB,mBAAmB,IAAI,OAAO;IAI9B,SAAS,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS;IAInD,aAAa,IAAI,cAAc,EAAE;IAIjC,OAAO,IAAI;QAAC,SAAS,EAAE,MAAM,CAAA;KAAC,GAAG,IAAI;IAS9C;;OAEG;IACG,SAAS,CAAC,SAAS,GAAE,wBAA6B,GAAG,OAAO,CAAC,IAAI,CAAC;IA2BlE,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAoD9D,MAAM,CACV,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,GAC/C,OAAO,CAAC,IAAI,CAAC;CAcjB"}