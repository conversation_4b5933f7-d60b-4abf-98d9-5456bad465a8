"""
性能监控API端点
提供监控数据的REST API接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
import asyncio
from datetime import datetime, timedelta

# 导入监控模块（需要先创建这些模块）
try:
    from app.monitoring.mysql_monitor import (
        mysql_monitor,
        get_current_metrics,
        get_performance_report,
    )
    from app.monitoring.api_monitor import (
        api_monitor,
        get_api_performance_report,
        get_endpoint_stats,
    )

    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False

router = APIRouter(prefix="/performance", tags=["性能监控"])

# 后台监控任务状态
monitoring_task = None
monitoring_active = False


@router.get("/mysql/current")
async def get_mysql_current_metrics():
    """获取当前MySQL性能指标"""
    if not MONITORING_AVAILABLE:
        raise HTTPException(status_code=503, detail="监控模块未安装")

    try:
        metrics = get_current_metrics()
        return {
            "status": "success",
            "data": metrics.__dict__ if metrics else {},
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取MySQL指标失败: {str(e)}")


@router.get("/mysql/report")
async def get_mysql_performance_report():
    """获取MySQL性能报告"""
    if not MONITORING_AVAILABLE:
        raise HTTPException(status_code=503, detail="监控模块未安装")

    try:
        report = get_performance_report()
        return {
            "status": "success",
            "data": report,
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取MySQL报告失败: {str(e)}")


@router.get("/mysql/history")
async def get_mysql_metrics_history(hours: int = 24):
    """获取MySQL历史指标"""
    if not MONITORING_AVAILABLE:
        raise HTTPException(status_code=503, detail="监控模块未安装")

    try:
        if hours > 168:  # 限制最多7天
            hours = 168

        cutoff_time = datetime.now() - timedelta(hours=hours)

        # 过滤历史数据
        filtered_history = [
            metrics
            for metrics in mysql_monitor.metrics_history
            if datetime.fromisoformat(metrics.timestamp) > cutoff_time
        ]

        return {
            "status": "success",
            "data": {
                "metrics": [metrics.__dict__ for metrics in filtered_history],
                "count": len(filtered_history),
                "time_range": f"{hours}小时",
            },
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取MySQL历史数据失败: {str(e)}")


@router.get("/api/report")
async def get_api_performance_report_endpoint():
    """获取API性能报告"""
    if not MONITORING_AVAILABLE:
        raise HTTPException(status_code=503, detail="监控模块未安装")

    try:
        report = get_api_performance_report()
        return {
            "status": "success",
            "data": report,
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取API报告失败: {str(e)}")


@router.get("/api/endpoint/{endpoint_name}")
async def get_specific_endpoint_stats(endpoint_name: str, hours: int = 24):
    """获取特定端点的统计信息"""
    if not MONITORING_AVAILABLE:
        raise HTTPException(status_code=503, detail="监控模块未安装")

    try:
        stats = get_endpoint_stats(endpoint_name)
        return {
            "status": "success",
            "data": stats.__dict__,
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取端点统计失败: {str(e)}")


@router.get("/dashboard")
async def get_monitoring_dashboard():
    """获取监控仪表盘数据"""
    if not MONITORING_AVAILABLE:
        # 返回基础系统信息
        import psutil

        return {
            "status": "success",
            "data": {
                "system_health": {"score": 85, "status": "良好"},
                "basic_metrics": {
                    "cpu_usage": psutil.cpu_percent(),
                    "memory_usage": psutil.virtual_memory().percent,
                    "disk_usage": psutil.disk_usage("/").percent,
                },
                "monitoring_status": {"active": False, "message": "高级监控模块未安装"},
            },
            "timestamp": datetime.now().isoformat(),
        }

    try:
        # 获取MySQL当前指标
        mysql_current = get_current_metrics()

        # 获取API性能报告
        api_report = get_api_performance_report()

        # 获取告警信息
        mysql_alerts = []
        if mysql_monitor.metrics_history:
            mysql_alerts = mysql_monitor.check_alerts(mysql_monitor.metrics_history[-1])
        api_alerts = api_monitor.check_performance_alerts()

        # 计算系统健康评分
        health_score = calculate_system_health_score(mysql_current, api_report)

        return {
            "status": "success",
            "data": {
                "system_health": {
                    "score": health_score,
                    "status": get_health_status(health_score),
                },
                "mysql_metrics": mysql_current.__dict__ if mysql_current else {},
                "api_summary": {
                    "total_requests": api_report.get("total_statistics", {}).get(
                        "total_requests", 0
                    ),
                    "success_rate": api_report.get("total_statistics", {}).get(
                        "success_rate", 0
                    ),
                    "avg_response_time": api_report.get("total_statistics", {}).get(
                        "avg_response_time", 0
                    ),
                },
                "alerts": {
                    "total": len(mysql_alerts) + len(api_alerts),
                    "mysql": len(mysql_alerts),
                    "api": len(api_alerts),
                },
                "monitoring_status": {
                    "active": monitoring_active,
                    "uptime": "运行中" if monitoring_active else "已停止",
                },
            },
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仪表盘数据失败: {str(e)}")


@router.post("/start")
async def start_monitoring(background_tasks: BackgroundTasks):
    """启动后台监控"""
    if not MONITORING_AVAILABLE:
        raise HTTPException(status_code=503, detail="监控模块未安装")

    global monitoring_task, monitoring_active

    if monitoring_active:
        return {
            "status": "info",
            "message": "监控已经在运行中",
            "timestamp": datetime.now().isoformat(),
        }

    try:
        monitoring_active = True
        background_tasks.add_task(background_monitoring_task)

        return {
            "status": "success",
            "message": "监控已启动",
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        monitoring_active = False
        raise HTTPException(status_code=500, detail=f"启动监控失败: {str(e)}")


@router.get("/status")
async def get_monitoring_status():
    """获取监控状态"""
    if not MONITORING_AVAILABLE:
        return {
            "status": "success",
            "data": {
                "monitoring_available": False,
                "message": "高级监控模块未安装，仅提供基础监控",
            },
            "timestamp": datetime.now().isoformat(),
        }

    return {
        "status": "success",
        "data": {
            "monitoring_available": True,
            "monitoring_active": monitoring_active,
            "mysql_metrics_count": len(mysql_monitor.metrics_history),
            "api_metrics_count": len(api_monitor.metrics_history),
            "last_mysql_update": (
                mysql_monitor.metrics_history[-1].timestamp
                if mysql_monitor.metrics_history
                else None
            ),
            "endpoints_monitored": len(api_monitor.endpoint_stats),
        },
        "timestamp": datetime.now().isoformat(),
    }


async def background_monitoring_task():
    """后台监控任务"""
    global monitoring_active

    while monitoring_active:
        try:
            # 收集MySQL指标
            mysql_metrics = get_current_metrics()

            # 保存指标到文件
            mysql_monitor.save_metrics(mysql_metrics)
            api_monitor.save_metrics()

            # 等待30秒
            await asyncio.sleep(30)

        except Exception as e:
            print(f"后台监控任务错误: {e}")
            await asyncio.sleep(60)  # 出错时等待更长时间


def calculate_system_health_score(mysql_metrics, api_report) -> int:
    """计算系统健康评分 (0-100)"""
    score = 100

    if mysql_metrics:
        # MySQL健康评分
        if mysql_metrics.cpu_usage > 80:
            score -= 20
        elif mysql_metrics.cpu_usage > 60:
            score -= 10

        if mysql_metrics.memory_usage > 85:
            score -= 20
        elif mysql_metrics.memory_usage > 70:
            score -= 10

        if mysql_metrics.innodb_buffer_pool_hit_ratio < 95:
            score -= 15

    if api_report and "total_statistics" in api_report:
        # API健康评分
        success_rate = api_report["total_statistics"].get("success_rate", 100)
        if success_rate < 95:
            score -= 20
        elif success_rate < 98:
            score -= 10

        avg_response_time = api_report["total_statistics"].get("avg_response_time", 0)
        if avg_response_time > 1000:
            score -= 15
        elif avg_response_time > 500:
            score -= 10

    return max(0, score)


def get_health_status(score: int) -> str:
    """根据评分获取健康状态"""
    if score >= 90:
        return "优秀"
    elif score >= 80:
        return "良好"
    elif score >= 70:
        return "一般"
    elif score >= 60:
        return "警告"
    else:
        return "严重"
