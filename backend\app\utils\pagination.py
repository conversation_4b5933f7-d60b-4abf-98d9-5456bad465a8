"""API分页机制
提供统一的分页功能和响应格式
"""

import math
from typing import Any, Dict, List, Optional, TypeVar, Generic, Callable
from pydantic import BaseModel, Field, validator
from fastapi import Query, HTTPException, status
from sqlalchemy.orm import Query as SQLQuery
from sqlalchemy import func, text
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T')


class PaginationParams(BaseModel):
    """分页参数模型"""
    
    page: int = Field(default=1, ge=1, description="页码，从1开始")
    limit: int = Field(default=20, ge=1, le=100, description="每页数量，最大100")
    sort_by: Optional[str] = Field(default=None, description="排序字段")
    sort_order: Optional[str] = Field(default="desc", description="排序方向：asc或desc")
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v and v.lower() not in ['asc', 'desc']:
            raise ValueError('排序方向必须是asc或desc')
        return v.lower() if v else 'desc'
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.limit
    
    @property
    def skip(self) -> int:
        """计算跳过的记录数（offset的别名）"""
        return self.offset


class PaginationMeta(BaseModel):
    """分页元数据"""
    
    page: int = Field(description="当前页码")
    limit: int = Field(description="每页数量")
    total: int = Field(description="总记录数")
    pages: int = Field(description="总页数")
    has_prev: bool = Field(description="是否有上一页")
    has_next: bool = Field(description="是否有下一页")
    prev_page: Optional[int] = Field(description="上一页页码")
    next_page: Optional[int] = Field(description="下一页页码")
    
    @classmethod
    def create(cls, page: int, limit: int, total: int) -> 'PaginationMeta':
        """创建分页元数据"""
        pages = math.ceil(total / limit) if total > 0 else 0
        has_prev = page > 1
        has_next = page < pages
        prev_page = page - 1 if has_prev else None
        next_page = page + 1 if has_next else None
        
        return cls(
            page=page,
            limit=limit,
            total=total,
            pages=pages,
            has_prev=has_prev,
            has_next=has_next,
            prev_page=prev_page,
            next_page=next_page
        )


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""
    
    data: List[T] = Field(description="数据列表")
    meta: PaginationMeta = Field(description="分页元数据")
    message: str = Field(default="success", description="响应消息")
    
    class Config:
        arbitrary_types_allowed = True


class Paginator:
    """分页器"""
    
    def __init__(self, 
                 page: int = 1, 
                 limit: int = 20, 
                 max_limit: int = 100,
                 sort_by: Optional[str] = None,
                 sort_order: str = "desc"):
        """
        初始化分页器
        
        Args:
            page: 页码
            limit: 每页数量
            max_limit: 最大每页数量
            sort_by: 排序字段
            sort_order: 排序方向
        """
        self.page = max(1, page)
        self.limit = min(max(1, limit), max_limit)
        self.max_limit = max_limit
        self.sort_by = sort_by
        self.sort_order = sort_order.lower() if sort_order else "desc"
        
        if self.sort_order not in ['asc', 'desc']:
            self.sort_order = "desc"
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.limit
    
    @property
    def skip(self) -> int:
        """计算跳过的记录数"""
        return self.offset
    
    def paginate_query(self, query: SQLQuery, total_count: Optional[int] = None) -> Dict[str, Any]:
        """
        对SQLAlchemy查询进行分页
        
        Args:
            query: SQLAlchemy查询对象
            total_count: 总记录数（如果已知）
            
        Returns:
            Dict[str, Any]: 包含数据和分页信息的字典
        """
        try:
            # 获取总记录数
            if total_count is None:
                # 创建计数查询
                count_query = query.statement.with_only_columns([func.count()]).order_by(None)
                total_count = query.session.execute(count_query).scalar()
            
            # 应用排序
            if self.sort_by:
                try:
                    # 验证排序字段是否存在
                    sort_column = getattr(query.column_descriptions[0]['type'], self.sort_by, None)
                    if sort_column is not None:
                        if self.sort_order == 'asc':
                            query = query.order_by(sort_column.asc())
                        else:
                            query = query.order_by(sort_column.desc())
                    else:
                        logger.warning(f"排序字段不存在: {self.sort_by}")
                except Exception as e:
                    logger.warning(f"应用排序失败: {e}")
            
            # 应用分页
            paginated_query = query.offset(self.offset).limit(self.limit)
            
            # 执行查询
            items = paginated_query.all()
            
            # 创建分页元数据
            meta = PaginationMeta.create(self.page, self.limit, total_count)
            
            return {
                'data': items,
                'meta': meta,
                'message': 'success'
            }
            
        except Exception as e:
            logger.error(f"分页查询失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="分页查询失败"
            )
    
    def paginate_list(self, items: List[Any], total_count: Optional[int] = None) -> Dict[str, Any]:
        """
        对列表进行分页
        
        Args:
            items: 数据列表
            total_count: 总记录数（如果已知）
            
        Returns:
            Dict[str, Any]: 包含数据和分页信息的字典
        """
        if total_count is None:
            total_count = len(items)
        
        # 计算分页范围
        start = self.offset
        end = start + self.limit
        
        # 获取当前页数据
        paginated_items = items[start:end]
        
        # 创建分页元数据
        meta = PaginationMeta.create(self.page, self.limit, total_count)
        
        return {
            'data': paginated_items,
            'meta': meta,
            'message': 'success'
        }
    
    def paginate_async_query(self, query_func: Callable, *args, **kwargs) -> Dict[str, Any]:
        """
        对异步查询进行分页
        
        Args:
            query_func: 查询函数
            *args: 查询函数参数
            **kwargs: 查询函数关键字参数
            
        Returns:
            Dict[str, Any]: 包含数据和分页信息的字典
        """
        try:
            # 执行查询函数
            result = query_func(offset=self.offset, limit=self.limit, *args, **kwargs)
            
            if isinstance(result, tuple) and len(result) == 2:
                items, total_count = result
            else:
                items = result
                total_count = len(items) if isinstance(items, list) else 0
            
            # 创建分页元数据
            meta = PaginationMeta.create(self.page, self.limit, total_count)
            
            return {
                'data': items,
                'meta': meta,
                'message': 'success'
            }
            
        except Exception as e:
            logger.error(f"异步分页查询失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="分页查询失败"
            )


class CursorPaginator:
    """游标分页器（适用于大数据集）"""
    
    def __init__(self, 
                 limit: int = 20, 
                 max_limit: int = 100,
                 cursor_field: str = "id",
                 sort_order: str = "desc"):
        """
        初始化游标分页器
        
        Args:
            limit: 每页数量
            max_limit: 最大每页数量
            cursor_field: 游标字段名
            sort_order: 排序方向
        """
        self.limit = min(max(1, limit), max_limit)
        self.max_limit = max_limit
        self.cursor_field = cursor_field
        self.sort_order = sort_order.lower() if sort_order else "desc"
        
        if self.sort_order not in ['asc', 'desc']:
            self.sort_order = "desc"
    
    def paginate_query(self, 
                      query: SQLQuery, 
                      cursor: Optional[str] = None) -> Dict[str, Any]:
        """
        使用游标对查询进行分页
        
        Args:
            query: SQLAlchemy查询对象
            cursor: 游标值
            
        Returns:
            Dict[str, Any]: 包含数据和分页信息的字典
        """
        try:
            # 应用游标过滤
            if cursor:
                cursor_column = getattr(query.column_descriptions[0]['type'], self.cursor_field)
                if self.sort_order == 'desc':
                    query = query.filter(cursor_column < cursor)
                else:
                    query = query.filter(cursor_column > cursor)
            
            # 应用排序
            cursor_column = getattr(query.column_descriptions[0]['type'], self.cursor_field)
            if self.sort_order == 'desc':
                query = query.order_by(cursor_column.desc())
            else:
                query = query.order_by(cursor_column.asc())
            
            # 获取数据（多获取一条用于判断是否有下一页）
            items = query.limit(self.limit + 1).all()
            
            # 检查是否有下一页
            has_next = len(items) > self.limit
            if has_next:
                items = items[:-1]  # 移除多获取的那一条
            
            # 获取下一页游标
            next_cursor = None
            if has_next and items:
                next_cursor = str(getattr(items[-1], self.cursor_field))
            
            return {
                'data': items,
                'meta': {
                    'limit': self.limit,
                    'has_next': has_next,
                    'next_cursor': next_cursor,
                    'cursor_field': self.cursor_field,
                    'sort_order': self.sort_order
                },
                'message': 'success'
            }
            
        except Exception as e:
            logger.error(f"游标分页查询失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="分页查询失败"
            )


# 便捷函数
def create_pagination_params(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    limit: int = Query(20, ge=1, le=100, description="每页数量，最大100"),
    sort_by: Optional[str] = Query(None, description="排序字段"),
    sort_order: Optional[str] = Query("desc", description="排序方向：asc或desc")
) -> PaginationParams:
    """
    创建分页参数（用于FastAPI依赖注入）
    
    Args:
        page: 页码
        limit: 每页数量
        sort_by: 排序字段
        sort_order: 排序方向
        
    Returns:
        PaginationParams: 分页参数对象
    """
    return PaginationParams(
        page=page,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order
    )


def create_cursor_pagination_params(
    limit: int = Query(20, ge=1, le=100, description="每页数量，最大100"),
    cursor: Optional[str] = Query(None, description="游标值"),
    cursor_field: str = Query("id", description="游标字段"),
    sort_order: str = Query("desc", description="排序方向：asc或desc")
) -> Dict[str, Any]:
    """
    创建游标分页参数（用于FastAPI依赖注入）
    
    Args:
        limit: 每页数量
        cursor: 游标值
        cursor_field: 游标字段
        sort_order: 排序方向
        
    Returns:
        Dict[str, Any]: 游标分页参数字典
    """
    return {
        'limit': limit,
        'cursor': cursor,
        'cursor_field': cursor_field,
        'sort_order': sort_order
    }


def paginate(
    items: List[Any],
    page: int = 1,
    limit: int = 20,
    total_count: Optional[int] = None
) -> PaginatedResponse:
    """
    快速分页函数
    
    Args:
        items: 数据列表
        page: 页码
        limit: 每页数量
        total_count: 总记录数
        
    Returns:
        PaginatedResponse: 分页响应
    """
    paginator = Paginator(page=page, limit=limit)
    result = paginator.paginate_list(items, total_count)
    
    return PaginatedResponse(
        data=result['data'],
        meta=result['meta'],
        message=result['message']
    )


def validate_pagination_params(page: int, limit: int, max_limit: int = 100) -> tuple[int, int]:
    """
    验证分页参数
    
    Args:
        page: 页码
        limit: 每页数量
        max_limit: 最大每页数量
        
    Returns:
        tuple[int, int]: 验证后的页码和每页数量
        
    Raises:
        HTTPException: 参数无效时抛出异常
    """
    if page < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="页码必须大于0"
        )
    
    if limit < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="每页数量必须大于0"
        )
    
    if limit > max_limit:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"每页数量不能超过{max_limit}"
        )
    
    return page, limit