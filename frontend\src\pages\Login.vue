<template>
  <div class="login-page">
    <!-- 🔒 证据链: 登录页面布局 -->
    <div class="login-container">
      <div class="login-header">
        <div class="login-brand">
          <div class="login-brand-icon">🎬</div>
        </div>
        <h1 class="login-title">二创短视频分发系统</h1>
        <p class="login-subtitle">请选择登录方式并输入您的凭据</p>
      </div>

      <!-- 用户类型选择 -->
      <div class="user-type-selector">
        <button
          type="button"
          class="user-type-option"
          :class="{ active: userType === 'user' }"
          @click="userType = 'user'"
        >
          👤 普通用户
        </button>
        <button
          type="button"
          class="user-type-option"
          :class="{ active: userType === 'admin' }"
          @click="userType = 'admin'"
        >
          🔧 管理员
        </button>
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage" class="error-alert">
        <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="15" y1="9" x2="9" y2="15"></line>
          <line x1="9" y1="9" x2="15" y2="15"></line>
        </svg>
        <span>{{ errorMessage }}</span>
        <button @click="clearError" class="clear-error-btn">×</button>
      </div>

      <!-- 登录表单 -->
      <form @submit.prevent="handleLogin" class="login-form">
        <!-- 用户名输入 -->
        <div class="form-group">
          <label for="username" class="form-label required">
            {{ userType === 'admin' ? '管理员账号' : '用户名/邮箱' }}
          </label>
          <div class="input-wrapper">
            <input
              id="username"
              v-model="formData.username"
              type="text"
              class="form-input"
              :placeholder="userType === 'admin' ? '请输入管理员账号' : '请输入用户名或邮箱'"
              autocomplete="username"
              :disabled="isLoading"
              required
            />
          </div>
        </div>

        <!-- 密码输入 -->
        <div class="form-group">
          <label for="password" class="form-label required">密码</label>
          <div class="input-wrapper">
            <input
              id="password"
              v-model="formData.password"
              :type="showPassword ? 'text' : 'password'"
              class="form-input"
              placeholder="请输入密码"
              autocomplete="current-password"
              :disabled="isLoading"
              required
            />
            <button
              type="button"
              @click="togglePasswordVisibility"
              class="password-toggle"
              :disabled="isLoading"
            >
              <svg v-if="showPassword" class="password-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
              <svg v-else class="password-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </button>
          </div>
        </div>

        <!-- 记住我 -->
        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="formData.rememberMe"
              type="checkbox"
              class="checkbox-input"
              :disabled="isLoading"
            />
            <span class="checkbox-text">记住我</span>
          </label>
        </div>

        <!-- 登录按钮 -->
        <button
          type="submit"
          class="login-button"
          :disabled="isLoading || !canSubmit"
        >
          <svg v-if="isLoading" class="loading-icon" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
              <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
              <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
            </circle>
          </svg>
          {{ isLoading ? '登录中...' : '登录' }}
        </button>
      </form>

      <!-- 快速登录 -->
      <div class="quick-login">
        <div class="quick-login-title">快速登录（测试用）</div>
        <div class="quick-login-buttons">
          <button
            type="button"
            class="quick-login-btn"
            @click="quickLogin('user')"
            :disabled="isLoading"
          >
            测试用户
          </button>
          <button
            type="button"
            class="quick-login-btn"
            @click="quickLogin('admin')"
            :disabled="isLoading"
          >
            测试管理员
          </button>
        </div>
      </div>

      <!-- 登录限制提示 -->
      <div v-if="!authStore.canAttemptLogin" class="lockout-notice">
        <svg class="warning-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <p>登录尝试次数过多，请稍后再试</p>
        <p class="lockout-time">剩余时间: {{ lockoutTimeRemaining }}</p>
      </div>

      <!-- 安全提示 -->
      <div class="security-notice">
        <svg class="security-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
        </svg>
        <p class="security-text">
          您的登录信息受到加密保护
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth'

// 🔒 证据链: 组件状态管理
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 用户类型选择
const userType = ref<'user' | 'admin'>('user')

// 组件状态
const isLoading = ref(false)
const showPassword = ref(false)
const errorMessage = ref('')
const lockoutTimer = ref<number | null>(null)

// 🔒 证据链: 计算属性
const canSubmit = computed(() => {
  return formData.username.trim() && formData.password.trim() && authStore.canAttemptLogin
})

const lockoutTimeRemaining = ref('')

// 🔒 证据链: 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 🔒 证据链: 清除错误信息
const clearError = () => {
  errorMessage.value = ''
}

// 🔒 证据链: 更新锁定倒计时
const updateLockoutTimer = () => {
  if (!authStore.canAttemptLogin) {
    const now = Date.now()
    const timeSinceLastAttempt = now - authStore.lastLoginAttempt
    const lockoutTime = 15 * 60 * 1000 // 15分钟
    const remaining = lockoutTime - timeSinceLastAttempt
    
    if (remaining > 0) {
      const minutes = Math.floor(remaining / 60000)
      const seconds = Math.floor((remaining % 60000) / 1000)
      lockoutTimeRemaining.value = `${minutes}:${seconds.toString().padStart(2, '0')}`
    } else {
      lockoutTimeRemaining.value = ''
      authStore.resetLoginAttempts()
    }
  }
}

// 🔒 证据链: 快速登录功能
const quickLogin = (type: 'user' | 'admin') => {
  userType.value = type
  if (type === 'admin') {
    formData.username = 'admin'
    formData.password = 'admin123'
  } else {
    formData.username = 'testuser'
    formData.password = 'password123'
  }
}

// 🔒 证据链: 处理登录
const handleLogin = async () => {
  if (!canSubmit.value) return
  
  clearError()
  isLoading.value = true
  
  try {
    await authStore.login({
      username: formData.username.trim(),
      password: formData.password,
      rememberMe: formData.rememberMe,
      userType: userType.value
    })
    
    // 登录成功，重定向
    const redirectPath = (route.query.redirect as string) || '/'
    
    console.log('🔒 登录成功，重定向到:', redirectPath)
    
    await router.push(redirectPath)
    
  } catch (error) {
    console.error('登录失败:', error)
    
    if (error instanceof Error) {
      errorMessage.value = error.message
    } else {
      errorMessage.value = '登录失败，请重试'
    }
    
    // 清空密码
    formData.password = ''
  } finally {
    isLoading.value = false
  }
}

// 🔒 证据链: 组件生命周期
onMounted(() => {
  // 如果已经登录，直接重定向
  if (authStore.isAuthenticated) {
    const redirectPath = (route.query.redirect as string) || '/'
    router.push(redirectPath)
    return
  }
  
  // 检查是否有过期提示
  if (route.query.expired === 'true') {
    errorMessage.value = '登录已过期，请重新登录'
  }
  
  // 启动锁定倒计时
  if (!authStore.canAttemptLogin) {
    lockoutTimer.value = window.setInterval(updateLockoutTimer, 1000)
    updateLockoutTimer()
  }
})

onUnmounted(() => {
  if (lockoutTimer.value) {
    clearInterval(lockoutTimer.value)
  }
})
</script>

<style scoped>
/* 引入统一登录样式 */
@import url('/shared-login-styles.css');

/* 组件特定样式覆盖 */
.loading-icon {
  width: 1.25rem;
  height: 1.25rem;
  animation: spin 1s linear infinite;
}

.password-icon {
  width: 1rem;
  height: 1rem;
}
</style>