<template>
  <div class="user-login-page">
    <div class="login-container">
      <h1>��� 二创短视频分发系统</h1>
      <p>用户登录</p>
      
      <div v-if="errorMessage" class="error">
        {{ errorMessage }}
      </div>
      
      <form @submit.prevent="handleLogin">
        <div class="form-group">
          <label>用户名:</label>
          <input v-model="formData.username" type="text" required />
        </div>
        <div class="form-group">
          <label>密码:</label>
          <input v-model="formData.password" type="password" required />
        </div>
        <button type="submit" :disabled="isLoading">
          {{ isLoading ? '登录中...' : '登录' }}
        </button>
      </form>
      
      <div class="quick-access">
        <p>快速登录:</p>
        <button @click="quickLogin('admin')" type="button">管理员登录</button>
        <button @click="quickLogin('user')" type="button">普通用户登录</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const formData = reactive({
  username: '',
  password: ''
})

const isLoading = ref(false)
const errorMessage = ref('')

const handleLogin = async () => {
  if (!formData.username || !formData.password) {
    errorMessage.value = '请输入用户名和密码'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    // 直接调用API而不通过store，避免被拦截
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: formData.username,
        password: formData.password
      })
    })

    const data = await response.json()

    if (response.ok && data.success) {
      // 保存用户信息
      localStorage.setItem('user_info', JSON.stringify(data.user))
      localStorage.setItem('access_token', 'simple-token')

      // 跳转到主页
      router.push('/')
    } else {
      errorMessage.value = data.detail || '登录失败，请重试'
    }
  } catch (error) {
    errorMessage.value = error.message || '登录失败，请重试'
  } finally {
    isLoading.value = false
  }
}

const quickLogin = (type: 'user' | 'admin') => {
  if (type === 'admin') {
    formData.username = 'admin_user'
    formData.password = 'Admin123!'
  } else {
    formData.username = 'demo_user'
    formData.password = 'Demo123!'
  }
}
</script>

<style scoped>
.user-login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-container {
  background: white;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  max-width: 400px;
  width: 100%;
  text-align: center;
}

h1 {
  color: #333;
  margin-bottom: 10px;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  box-sizing: border-box;
}

button {
  background: #007bff;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  margin: 5px;
}

button:hover:not(:disabled) {
  background: #0056b3;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

button[type="submit"] {
  width: 100%;
  margin: 20px 0;
}

.error {
  background: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.quick-access {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.quick-access button {
  background: #28a745;
  font-size: 14px;
  padding: 8px 16px;
}
</style>
