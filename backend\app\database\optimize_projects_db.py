"""
数据库性能优化脚本 - P1级优化
解决索引设计、查询优化、数据一致性问题
"""

import sqlite3
from datetime import datetime
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库路径
DB_PATH = Path(__file__).parent.parent.parent / "ai_video_system.db"


def backup_database():
    """备份数据库"""
    backup_path = DB_PATH.with_suffix(
        f".optimize_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    )

    import shutil

    shutil.copy2(DB_PATH, backup_path)
    logger.info(f"✅ 数据库已备份到: {backup_path}")
    return backup_path


def analyze_current_performance():
    """分析当前数据库性能"""
    logger.info("🔍 分析当前数据库性能...")

    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查表大小
        cursor.execute("SELECT COUNT(*) FROM projects")
        project_count = cursor.fetchone()[0]

        # 检查索引使用情况
        cursor.execute("PRAGMA index_list(projects)")
        indexes = cursor.fetchall()

        # 分析查询计划
        common_queries = [
            "SELECT * FROM projects WHERE status = 'in_progress'",
            "SELECT * FROM projects WHERE project_type = 'video_creation'",
            "SELECT * FROM projects WHERE status = 'in_progress' AND project_type = 'video_creation'",
            "SELECT * FROM projects WHERE owner_id = 1",
            "SELECT * FROM projects ORDER BY created_at DESC LIMIT 10",
        ]

        query_plans = {}
        for query in common_queries:
            cursor.execute(f"EXPLAIN QUERY PLAN {query}")
            plan = cursor.fetchall()
            query_plans[query] = plan

        logger.info(f"📊 当前状态:")
        logger.info(f"   - 项目数量: {project_count}")
        logger.info(f"   - 索引数量: {len(indexes)}")

        for query, plan in query_plans.items():
            logger.info(f"   - 查询: {query[:50]}...")
            for step in plan:
                if "SCAN" in str(step):
                    logger.warning(f"     ⚠️ 全表扫描: {step}")
                else:
                    logger.info(f"     ✅ 使用索引: {step}")

        conn.close()
        return {
            "project_count": project_count,
            "indexes": indexes,
            "query_plans": query_plans,
        }

    except Exception as e:
        logger.error(f"❌ 性能分析失败: {e}")
        return None


def optimize_indexes():
    """优化索引设计"""
    logger.info("🚀 优化索引设计...")

    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 删除可能重复的索引
        cursor.execute("DROP INDEX IF EXISTS idx_project_status")
        cursor.execute("DROP INDEX IF EXISTS idx_project_type")
        cursor.execute("DROP INDEX IF EXISTS idx_project_owner")
        cursor.execute("DROP INDEX IF EXISTS idx_project_created")
        cursor.execute("DROP INDEX IF EXISTS idx_project_priority")

        # 创建优化的复合索引
        optimized_indexes = [
            # 最常用的查询组合
            "CREATE INDEX idx_projects_status_type ON projects(status, project_type)",
            "CREATE INDEX idx_projects_owner_status ON projects(owner_id, status)",
            "CREATE INDEX idx_projects_type_priority ON projects(project_type, priority)",
            # 时间相关查询
            "CREATE INDEX idx_projects_created_desc ON projects(created_at DESC)",
            "CREATE INDEX idx_projects_updated_desc ON projects(updated_at DESC)",
            # 搜索优化
            "CREATE INDEX idx_projects_name_lower ON projects(LOWER(name))",
            # 状态和时间组合
            "CREATE INDEX idx_projects_status_created ON projects(status, created_at DESC)",
            # 活跃项目查询
            "CREATE INDEX idx_projects_active ON projects(is_active, status) WHERE is_active = 1",
            # 进度查询
            "CREATE INDEX idx_projects_progress ON projects(progress_percentage) WHERE progress_percentage > 0",
        ]

        for index_sql in optimized_indexes:
            try:
                cursor.execute(index_sql)
                logger.info(
                    f"✅ 创建索引: {index_sql.split('ON')[0].split('INDEX')[1].strip()}"
                )
            except Exception as e:
                logger.warning(f"⚠️ 索引创建失败: {e}")

        # 为关联表创建索引
        cursor.execute(
            "CREATE INDEX IF NOT EXISTS idx_project_members_composite ON project_members(project_id, user_id, is_active)"
        )
        cursor.execute(
            "CREATE INDEX IF NOT EXISTS idx_project_history_composite ON project_history(project_id, created_at DESC)"
        )

        conn.commit()
        conn.close()

        logger.info("✅ 索引优化完成")
        return True

    except Exception as e:
        logger.error(f"❌ 索引优化失败: {e}")
        return False


def add_foreign_key_constraints():
    """添加外键约束（SQLite限制，需要重建表）"""
    logger.info("🔗 添加外键约束...")

    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")

        # 检查外键约束是否已存在
        cursor.execute("PRAGMA foreign_key_check(projects)")
        fk_violations = cursor.fetchall()

        if fk_violations:
            logger.warning(f"⚠️ 发现 {len(fk_violations)} 个外键约束违反")
            for violation in fk_violations:
                logger.warning(f"   - {violation}")

        # 为新记录添加外键约束检查触发器
        cursor.execute(
            """
            CREATE TRIGGER IF NOT EXISTS check_project_owner_exists
            BEFORE INSERT ON projects
            BEGIN
                SELECT CASE
                    WHEN NEW.owner_id IS NOT NULL AND NEW.owner_id <= 0 THEN
                        RAISE(ABORT, 'Invalid owner_id: must be positive')
                END;
            END
        """
        )

        cursor.execute(
            """
            CREATE TRIGGER IF NOT EXISTS check_project_creator_exists
            BEFORE INSERT ON projects
            BEGIN
                SELECT CASE
                    WHEN NEW.created_by IS NOT NULL AND NEW.created_by <= 0 THEN
                        RAISE(ABORT, 'Invalid created_by: must be positive')
                END;
            END
        """
        )

        conn.commit()
        conn.close()

        logger.info("✅ 外键约束检查已添加")
        return True

    except Exception as e:
        logger.error(f"❌ 外键约束添加失败: {e}")
        return False


def optimize_data_consistency():
    """优化数据一致性"""
    logger.info("🔧 优化数据一致性...")

    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 修复不一致的数据
        fixes = [
            # 确保进度百分比在有效范围内
            "UPDATE projects SET progress_percentage = 0 WHERE progress_percentage < 0",
            "UPDATE projects SET progress_percentage = 100 WHERE progress_percentage > 100",
            # 确保已完成项目的进度为100%
            "UPDATE projects SET progress_percentage = 100 WHERE status = 'completed' AND progress_percentage < 100",
            # 确保草稿项目的进度为0%
            "UPDATE projects SET progress_percentage = 0 WHERE status = 'draft' AND progress_percentage > 0",
            # 设置完成时间
            "UPDATE projects SET completed_at = updated_at WHERE status = 'completed' AND completed_at IS NULL",
            # 清理无效的完成时间
            "UPDATE projects SET completed_at = NULL WHERE status != 'completed' AND completed_at IS NOT NULL",
            # 确保活跃状态一致性
            "UPDATE projects SET is_active = 1 WHERE status IN ('draft', 'planning', 'in_progress', 'review')",
            "UPDATE projects SET is_active = 0 WHERE status IN ('completed', 'archived', 'cancelled')",
            # 确保任务计数一致性
            "UPDATE projects SET completed_task_count = 0 WHERE completed_task_count < 0",
            "UPDATE projects SET completed_task_count = task_count WHERE completed_task_count > task_count",
        ]

        for fix_sql in fixes:
            cursor.execute(fix_sql)
            affected_rows = cursor.rowcount
            if affected_rows > 0:
                logger.info(f"✅ 修复了 {affected_rows} 条记录: {fix_sql[:50]}...")

        # 添加数据一致性检查触发器
        cursor.execute(
            """
            CREATE TRIGGER IF NOT EXISTS ensure_progress_consistency
            BEFORE UPDATE OF progress_percentage ON projects
            BEGIN
                SELECT CASE
                    WHEN NEW.progress_percentage < 0 OR NEW.progress_percentage > 100 THEN
                        RAISE(ABORT, 'Progress percentage must be between 0 and 100')
                    WHEN NEW.status = 'completed' AND NEW.progress_percentage < 100 THEN
                        RAISE(ABORT, 'Completed projects must have 100% progress')
                    WHEN NEW.status = 'draft' AND NEW.progress_percentage > 0 THEN
                        RAISE(ABORT, 'Draft projects should have 0% progress')
                END;
            END
        """
        )

        cursor.execute(
            """
            CREATE TRIGGER IF NOT EXISTS ensure_task_consistency
            BEFORE UPDATE OF completed_task_count ON projects
            BEGIN
                SELECT CASE
                    WHEN NEW.completed_task_count < 0 THEN
                        RAISE(ABORT, 'Completed task count cannot be negative')
                    WHEN NEW.completed_task_count > NEW.task_count THEN
                        RAISE(ABORT, 'Completed tasks cannot exceed total tasks')
                END;
            END
        """
        )

        conn.commit()
        conn.close()

        logger.info("✅ 数据一致性优化完成")
        return True

    except Exception as e:
        logger.error(f"❌ 数据一致性优化失败: {e}")
        return False


def create_performance_views():
    """创建性能优化视图"""
    logger.info("📊 创建性能优化视图...")

    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 项目统计视图
        cursor.execute(
            """
            CREATE VIEW IF NOT EXISTS project_stats_view AS
            SELECT 
                COUNT(*) as total_projects,
                COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as active_projects,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects,
                COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_projects,
                AVG(progress_percentage) as avg_progress,
                COUNT(CASE WHEN project_type = 'video_creation' THEN 1 END) as video_projects,
                COUNT(CASE WHEN project_type = 'content_distribution' THEN 1 END) as content_projects
            FROM projects
            WHERE is_active = 1
        """
        )

        # 项目详情视图（包含计算字段）
        cursor.execute(
            """
            CREATE VIEW IF NOT EXISTS project_details_view AS
            SELECT 
                p.*,
                CASE 
                    WHEN p.progress_percentage >= 80 THEN 'high'
                    WHEN p.progress_percentage >= 60 THEN 'medium'
                    WHEN p.progress_percentage >= 40 THEN 'low'
                    ELSE 'very_low'
                END as progress_level,
                CASE 
                    WHEN p.due_date IS NOT NULL AND p.due_date < datetime('now') AND p.status != 'completed' THEN 1
                    ELSE 0
                END as is_overdue,
                (SELECT COUNT(*) FROM project_members pm WHERE pm.project_id = p.id AND pm.is_active = 1) as member_count_actual
            FROM projects p
            WHERE p.is_active = 1
        """
        )

        # 最近活动视图
        cursor.execute(
            """
            CREATE VIEW IF NOT EXISTS recent_projects_view AS
            SELECT *
            FROM projects
            WHERE is_active = 1
            ORDER BY updated_at DESC, created_at DESC
            LIMIT 50
        """
        )

        conn.commit()
        conn.close()

        logger.info("✅ 性能视图创建完成")
        return True

    except Exception as e:
        logger.error(f"❌ 性能视图创建失败: {e}")
        return False


def analyze_optimized_performance():
    """分析优化后的性能"""
    logger.info("📈 分析优化后的性能...")

    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 测试优化后的查询性能
        import time

        test_queries = [
            ("状态过滤", "SELECT * FROM projects WHERE status = 'in_progress'"),
            (
                "类型过滤",
                "SELECT * FROM projects WHERE project_type = 'video_creation'",
            ),
            (
                "复合查询",
                "SELECT * FROM projects WHERE status = 'in_progress' AND project_type = 'video_creation'",
            ),
            ("排序查询", "SELECT * FROM projects ORDER BY created_at DESC LIMIT 10"),
            ("统计查询", "SELECT COUNT(*) FROM project_stats_view"),
            ("搜索查询", "SELECT * FROM projects WHERE LOWER(name) LIKE '%test%'"),
        ]

        performance_results = {}

        for query_name, query in test_queries:
            start_time = time.time()
            cursor.execute(query)
            results = cursor.fetchall()
            end_time = time.time()

            execution_time = (end_time - start_time) * 1000  # 转换为毫秒
            performance_results[query_name] = {
                "execution_time_ms": execution_time,
                "result_count": len(results),
            }

            logger.info(
                f"   - {query_name}: {execution_time:.2f}ms ({len(results)} 条记录)"
            )

        # 检查索引使用情况
        cursor.execute("PRAGMA index_list(projects)")
        indexes = cursor.fetchall()
        logger.info(f"📊 优化后状态:")
        logger.info(f"   - 索引数量: {len(indexes)}")

        conn.close()
        return performance_results

    except Exception as e:
        logger.error(f"❌ 性能分析失败: {e}")
        return None


def main():
    """主优化函数"""
    logger.info("🚀 开始数据库性能优化...")

    # 备份数据库
    backup_path = backup_database()

    try:
        # 分析当前性能
        current_perf = analyze_current_performance()

        # 执行优化
        optimizations = [
            ("索引优化", optimize_indexes),
            ("外键约束", add_foreign_key_constraints),
            ("数据一致性", optimize_data_consistency),
            ("性能视图", create_performance_views),
        ]

        success_count = 0
        for name, func in optimizations:
            logger.info(f"\n🔧 执行 {name}...")
            if func():
                success_count += 1
            else:
                logger.error(f"❌ {name} 失败")

        # 分析优化后性能
        logger.info(
            f"\n📈 优化完成，成功执行 {success_count}/{len(optimizations)} 项优化"
        )
        optimized_perf = analyze_optimized_performance()

        if optimized_perf:
            logger.info("\n🎉 数据库性能优化完成！")
            logger.info("主要改进:")
            logger.info("  ✅ 添加了复合索引，提升查询性能")
            logger.info("  ✅ 增强了数据一致性约束")
            logger.info("  ✅ 创建了性能优化视图")
            logger.info("  ✅ 添加了数据完整性检查")

        return True

    except Exception as e:
        logger.error(f"❌ 优化过程失败: {e}")

        # 恢复备份
        import shutil

        shutil.copy2(backup_path, DB_PATH)
        logger.info("🔄 已从备份恢复数据库")

        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
