"""输入验证中间件
提供全面的输入数据验证和清理
"""

import re
import html
import json
import logging
from typing import Any, Dict, List, Optional, Union, Set
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError
from datetime import datetime
import bleach
from urllib.parse import unquote

logger = logging.getLogger(__name__)


class ValidationRule:
    """验证规则基类"""
    
    def __init__(self, field_name: str, required: bool = True):
        self.field_name = field_name
        self.required = required
    
    def validate(self, value: Any) -> tuple[bool, Optional[str]]:
        """
        验证值
        
        Args:
            value: 要验证的值
            
        Returns:
            tuple[bool, Optional[str]]: (是否有效, 错误消息)
        """
        raise NotImplementedError


class StringValidationRule(ValidationRule):
    """字符串验证规则"""
    
    def __init__(self, field_name: str, min_length: int = 0, max_length: int = 1000, 
                 pattern: Optional[str] = None, required: bool = True):
        super().__init__(field_name, required)
        self.min_length = min_length
        self.max_length = max_length
        self.pattern = re.compile(pattern) if pattern else None
    
    def validate(self, value: Any) -> tuple[bool, Optional[str]]:
        if value is None:
            if self.required:
                return False, f"{self.field_name} is required"
            return True, None
        
        if not isinstance(value, str):
            return False, f"{self.field_name} must be a string"
        
        if len(value) < self.min_length:
            return False, f"{self.field_name} must be at least {self.min_length} characters"
        
        if len(value) > self.max_length:
            return False, f"{self.field_name} must be at most {self.max_length} characters"
        
        if self.pattern and not self.pattern.match(value):
            return False, f"{self.field_name} format is invalid"
        
        return True, None


class EmailValidationRule(ValidationRule):
    """邮箱验证规则"""
    
    def __init__(self, field_name: str, required: bool = True):
        super().__init__(field_name, required)
        self.email_pattern = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )
    
    def validate(self, value: Any) -> tuple[bool, Optional[str]]:
        if value is None:
            if self.required:
                return False, f"{self.field_name} is required"
            return True, None
        
        if not isinstance(value, str):
            return False, f"{self.field_name} must be a string"
        
        if not self.email_pattern.match(value):
            return False, f"{self.field_name} must be a valid email address"
        
        return True, None


class NumericValidationRule(ValidationRule):
    """数字验证规则"""
    
    def __init__(self, field_name: str, min_value: Optional[Union[int, float]] = None,
                 max_value: Optional[Union[int, float]] = None, required: bool = True):
        super().__init__(field_name, required)
        self.min_value = min_value
        self.max_value = max_value
    
    def validate(self, value: Any) -> tuple[bool, Optional[str]]:
        if value is None:
            if self.required:
                return False, f"{self.field_name} is required"
            return True, None
        
        if not isinstance(value, (int, float)):
            try:
                value = float(value)
            except (ValueError, TypeError):
                return False, f"{self.field_name} must be a number"
        
        if self.min_value is not None and value < self.min_value:
            return False, f"{self.field_name} must be at least {self.min_value}"
        
        if self.max_value is not None and value > self.max_value:
            return False, f"{self.field_name} must be at most {self.max_value}"
        
        return True, None


class InputSanitizer:
    """输入清理器"""
    
    # 危险的HTML标签
    DANGEROUS_TAGS = {
        'script', 'iframe', 'object', 'embed', 'form', 'input', 'button',
        'textarea', 'select', 'option', 'meta', 'link', 'style', 'base'
    }
    
    # 允许的HTML标签（用于富文本）
    ALLOWED_TAGS = {
        'p', 'br', 'strong', 'em', 'u', 'i', 'b', 'span', 'div',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li',
        'a', 'img', 'blockquote', 'code', 'pre'
    }
    
    # 允许的HTML属性
    ALLOWED_ATTRIBUTES = {
        'a': ['href', 'title'],
        'img': ['src', 'alt', 'title', 'width', 'height'],
        'span': ['class'],
        'div': ['class'],
        'p': ['class'],
        'code': ['class']
    }
    
    # SQL注入关键词
    SQL_INJECTION_PATTERNS = [
        r'\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b',
        r'--',
        r'/\*.*?\*/',
        r';\s*(drop|delete|update|insert)',
        r'\b(or|and)\s+\d+\s*=\s*\d+',
        r'\b(or|and)\s+["\']\w+["\']\s*=\s*["\']\w+["\']'
    ]
    
    # XSS攻击模式
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
        r'<iframe[^>]*>.*?</iframe>',
        r'<object[^>]*>.*?</object>',
        r'<embed[^>]*>.*?</embed>'
    ]
    
    @classmethod
    def sanitize_string(cls, value: str, allow_html: bool = False) -> str:
        """
        清理字符串输入
        
        Args:
            value: 输入字符串
            allow_html: 是否允许HTML标签
            
        Returns:
            str: 清理后的字符串
        """
        if not isinstance(value, str):
            return str(value)
        
        # URL解码
        try:
            value = unquote(value)
        except Exception:
            pass
        
        # 移除控制字符
        value = ''.join(char for char in value if ord(char) >= 32 or char in '\n\r\t')
        
        if allow_html:
            # 使用bleach清理HTML
            value = bleach.clean(
                value,
                tags=cls.ALLOWED_TAGS,
                attributes=cls.ALLOWED_ATTRIBUTES,
                strip=True
            )
        else:
            # HTML转义
            value = html.escape(value)
        
        # 检查SQL注入
        for pattern in cls.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"Potential SQL injection detected: {pattern}")
                value = re.sub(pattern, '', value, flags=re.IGNORECASE)
        
        # 检查XSS攻击
        for pattern in cls.XSS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"Potential XSS attack detected: {pattern}")
                value = re.sub(pattern, '', value, flags=re.IGNORECASE)
        
        return value.strip()
    
    @classmethod
    def sanitize_dict(cls, data: Dict[str, Any], allow_html_fields: Set[str] = None) -> Dict[str, Any]:
        """
        清理字典数据
        
        Args:
            data: 输入字典
            allow_html_fields: 允许HTML的字段集合
            
        Returns:
            Dict[str, Any]: 清理后的字典
        """
        if allow_html_fields is None:
            allow_html_fields = set()
        
        sanitized = {}
        for key, value in data.items():
            if isinstance(value, str):
                allow_html = key in allow_html_fields
                sanitized[key] = cls.sanitize_string(value, allow_html)
            elif isinstance(value, dict):
                sanitized[key] = cls.sanitize_dict(value, allow_html_fields)
            elif isinstance(value, list):
                sanitized[key] = [cls.sanitize_string(item, key in allow_html_fields) 
                                if isinstance(item, str) else item for item in value]
            else:
                sanitized[key] = value
        
        return sanitized


class InputValidator:
    """输入验证器"""
    
    def __init__(self):
        # 路径特定的验证规则
        self.path_rules: Dict[str, List[ValidationRule]] = {
            "/api/v1/auth/register": [
                StringValidationRule("username", min_length=3, max_length=50, 
                                   pattern=r'^[a-zA-Z0-9_]+$'),
                EmailValidationRule("email"),
                StringValidationRule("password", min_length=8, max_length=128)
            ],
            "/api/v1/auth/login": [
                StringValidationRule("username", min_length=1, max_length=100),
                StringValidationRule("password", min_length=1, max_length=128)
            ],
            
            "/api/v1/videos/upload": [
                StringValidationRule("title", min_length=1, max_length=200),
                StringValidationRule("description", min_length=0, max_length=2000, required=False),
                StringValidationRule("tags", required=False)
            ],
            
            "/api/v1/projects": [
                StringValidationRule("name", min_length=1, max_length=100),
                StringValidationRule("description", min_length=0, max_length=1000, required=False),
                StringValidationRule("type", pattern=r'^(video|image|audio)$')
            ]
        }
        
        # 通用验证规则
        self.common_rules = {
            "page": NumericValidationRule("page", min_value=1, required=False),
            "limit": NumericValidationRule("limit", min_value=1, max_value=100, required=False),
            "id": NumericValidationRule("id", min_value=1)
        }
        
        # 允许HTML的字段
        self.html_allowed_fields = {
            "description", "content", "bio", "about"
        }
    
    def validate_request_data(self, path: str, data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """
        验证请求数据
        
        Args:
            path: 请求路径
            data: 请求数据
            
        Returns:
            tuple[bool, List[str]]: (是否有效, 错误消息列表)
        """
        errors = []
        
        # 获取路径特定规则
        rules = self.path_rules.get(path, [])
        
        # 验证每个规则
        for rule in rules:
            value = data.get(rule.field_name)
            is_valid, error_msg = rule.validate(value)
            if not is_valid:
                errors.append(error_msg)
        
        # 验证通用字段
        for field_name, rule in self.common_rules.items():
            if field_name in data:
                value = data[field_name]
                is_valid, error_msg = rule.validate(value)
                if not is_valid:
                    errors.append(error_msg)
        
        return len(errors) == 0, errors
    
    def sanitize_request_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理请求数据
        
        Args:
            data: 请求数据
            
        Returns:
            Dict[str, Any]: 清理后的数据
        """
        return InputSanitizer.sanitize_dict(data, self.html_allowed_fields)


# 全局验证器实例
input_validator = InputValidator()


async def input_validation_middleware(request: Request, call_next):
    """
    输入验证中间件
    
    Args:
        request: FastAPI请求对象
        call_next: 下一个中间件或路由处理器
        
    Returns:
        Response: 响应对象
    """
    try:
        # 只对特定方法进行验证
        if request.method in {"POST", "PUT", "PATCH"}:
            # 获取请求数据
            request_data = {}
            
            # 处理JSON数据
            content_type = request.headers.get("content-type", "")
            if "application/json" in content_type:
                try:
                    body = await request.body()
                    if body:
                        request_data = json.loads(body)
                except (json.JSONDecodeError, UnicodeDecodeError) as e:
                    logger.warning(f"Invalid JSON in request: {e}")
                    return JSONResponse(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        content={
                            "error": "Invalid JSON",
                            "message": "Request body contains invalid JSON"
                        }
                    )
            
            # 处理查询参数
            query_params = dict(request.query_params)
            request_data.update(query_params)
            
            # 清理输入数据
            sanitized_data = input_validator.sanitize_request_data(request_data)
            
            # 验证输入数据
            is_valid, errors = input_validator.validate_request_data(
                request.url.path, sanitized_data
            )
            
            if not is_valid:
                logger.warning(f"Input validation failed for {request.url.path}: {errors}")
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content={
                        "error": "Input validation failed",
                        "message": "Invalid input data",
                        "details": errors
                    }
                )
            
            # 将清理后的数据添加到请求状态
            request.state.validated_data = sanitized_data
            request.state.input_validated = True
        
        # 继续处理请求
        response = await call_next(request)
        return response
        
    except Exception as e:
        logger.error(f"Input validation middleware error: {e}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "error": "Input validation error",
                "message": "Internal server error during input validation"
            }
        )


# 便捷函数
def get_validated_data(request: Request) -> Dict[str, Any]:
    """
    获取验证后的请求数据
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        Dict[str, Any]: 验证后的数据
    """
    return getattr(request.state, 'validated_data', {})


def add_validation_rule(path: str, rule: ValidationRule) -> None:
    """
    添加验证规则
    
    Args:
        path: 路径
        rule: 验证规则
    """
    if path not in input_validator.path_rules:
        input_validator.path_rules[path] = []
    input_validator.path_rules[path].append(rule)