"""
启动生产服务器
"""

import subprocess
import sys
import os

def install_dependencies():
    """安装必要的依赖"""
    try:
        import fastapi
        import uvicorn
        print("✅ 依赖已安装")
        return True
    except ImportError:
        print("📦 安装依赖...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "fastapi", "uvicorn[standard]"])
            return True
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败")
            return False

def start_server():
    """启动服务器"""
    if not install_dependencies():
        return False
    
    print("🚀 启动API服务器...")
    
    try:
        # 直接导入并运行
        from api_server import app
        import uvicorn
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            log_level="info"
        )
        
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return False

if __name__ == "__main__":
    start_server()
