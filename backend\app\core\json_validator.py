"""
JSON安全验证模块
🔒 证据链: 防止JSON反序列化攻击，确保数据安全
"""

import json
import logging
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, ValidationError, validator


logger = logging.getLogger(__name__)


class JSONSecurityError(Exception):
    """JSON安全错误异常"""

    def __init__(self, message: str, error_type: str = "SECURITY_ERROR"):
        self.message = message
        self.error_type = error_type
        super().__init__(message)


class JSONValidator:
    """JSON安全验证器"""

    # 🔒 证据链: 定义安全的JSON配置
    MAX_JSON_SIZE = 1024 * 1024  # 1MB
    MAX_NESTING_DEPTH = 10
    MAX_ARRAY_LENGTH = 1000
    MAX_STRING_LENGTH = 10000

    # 危险的JSON模式
    DANGEROUS_PATTERNS = [
        "__proto__",
        "constructor",
        "prototype",
        "eval",
        "function",
        "script",
        "javascript:",
        "data:",
        "vbscript:",
    ]

    @classmethod
    def validate_json_string(cls, json_str: str) -> bool:
        """验证JSON字符串安全性"""
        if not json_str:
            return True

        # 🔒 证据链: 检查JSON大小
        if len(json_str.encode("utf-8")) > cls.MAX_JSON_SIZE:
            raise JSONSecurityError(
                f"JSON size exceeds maximum allowed size of {cls.MAX_JSON_SIZE} bytes",
                "SIZE_EXCEEDED",
            )

        # 检查危险模式
        json_lower = json_str.lower()
        for pattern in cls.DANGEROUS_PATTERNS:
            if pattern in json_lower:
                raise JSONSecurityError(
                    f"JSON contains dangerous pattern: {pattern}", "DANGEROUS_PATTERN"
                )

        return True

    @classmethod
    def safe_json_loads(cls, json_str: str, max_depth: int = None) -> Any:
        """安全的JSON解析"""
        if not json_str:
            return None

        # 验证JSON字符串
        cls.validate_json_string(json_str)

        try:
            # 🔒 证据链: 使用安全的JSON解析
            data = json.loads(json_str)

            # 验证解析后的数据
            max_depth = max_depth or cls.MAX_NESTING_DEPTH
            cls._validate_json_structure(data, max_depth)

            return data

        except json.JSONDecodeError as e:
            raise JSONSecurityError(f"Invalid JSON format: {e}", "INVALID_FORMAT")
        except RecursionError:
            raise JSONSecurityError("JSON nesting too deep", "NESTING_TOO_DEEP")

    @classmethod
    def safe_json_dumps(cls, data: Any, ensure_ascii: bool = True) -> str:
        """安全的JSON序列化"""
        try:
            # 验证数据结构
            cls._validate_json_structure(data, cls.MAX_NESTING_DEPTH)

            # 🔒 证据链: 使用安全的JSON序列化选项
            return json.dumps(
                data,
                ensure_ascii=ensure_ascii,
                separators=(",", ":"),  # 紧凑格式
                sort_keys=True,  # 排序键名
                default=cls._json_serializer,
            )

        except (TypeError, ValueError) as e:
            raise JSONSecurityError(
                f"JSON serialization failed: {e}", "SERIALIZATION_ERROR"
            )

    @classmethod
    def _validate_json_structure(
        cls, data: Any, max_depth: int, current_depth: int = 0
    ):
        """验证JSON数据结构"""
        if current_depth > max_depth:
            raise JSONSecurityError(
                f"JSON nesting depth exceeds maximum of {max_depth}", "NESTING_TOO_DEEP"
            )

        if isinstance(data, dict):
            if len(data) > cls.MAX_ARRAY_LENGTH:
                raise JSONSecurityError(
                    f"JSON object has too many keys: {len(data)} > {cls.MAX_ARRAY_LENGTH}",
                    "TOO_MANY_KEYS",
                )

            for key, value in data.items():
                # 验证键名
                if not isinstance(key, str):
                    raise JSONSecurityError(
                        "JSON object keys must be strings", "INVALID_KEY_TYPE"
                    )

                if len(key) > cls.MAX_STRING_LENGTH:
                    raise JSONSecurityError(
                        f"JSON key too long: {len(key)} > {cls.MAX_STRING_LENGTH}",
                        "KEY_TOO_LONG",
                    )

                # 检查危险键名
                key_lower = key.lower()
                for pattern in cls.DANGEROUS_PATTERNS:
                    if pattern in key_lower:
                        raise JSONSecurityError(
                            f"JSON key contains dangerous pattern: {pattern}",
                            "DANGEROUS_KEY",
                        )

                # 递归验证值
                cls._validate_json_structure(value, max_depth, current_depth + 1)

        elif isinstance(data, list):
            if len(data) > cls.MAX_ARRAY_LENGTH:
                raise JSONSecurityError(
                    f"JSON array too long: {len(data)} > {cls.MAX_ARRAY_LENGTH}",
                    "ARRAY_TOO_LONG",
                )

            for item in data:
                cls._validate_json_structure(item, max_depth, current_depth + 1)

        elif isinstance(data, str):
            if len(data) > cls.MAX_STRING_LENGTH:
                raise JSONSecurityError(
                    f"JSON string too long: {len(data)} > {cls.MAX_STRING_LENGTH}",
                    "STRING_TOO_LONG",
                )

            # 检查危险字符串内容
            data_lower = data.lower()
            for pattern in cls.DANGEROUS_PATTERNS:
                if pattern in data_lower:
                    raise JSONSecurityError(
                        f"JSON string contains dangerous pattern: {pattern}",
                        "DANGEROUS_STRING",
                    )

    @staticmethod
    def _json_serializer(obj):
        """自定义JSON序列化器"""
        # 🔒 证据链: 安全的对象序列化
        if hasattr(obj, "isoformat"):  # datetime对象
            return obj.isoformat()
        elif hasattr(obj, "__dict__"):  # 普通对象
            return obj.__dict__
        else:
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


# Pydantic模型用于JSON验证
class SafeJSONModel(BaseModel):
    """安全的JSON数据模型"""

    @validator("*", pre=True)
    def validate_json_fields(cls, v):
        """验证JSON字段"""
        if isinstance(v, str):
            # 检查是否为JSON字符串
            if v.strip().startswith(("{", "[")):
                try:
                    return JSONValidator.safe_json_loads(v)
                except JSONSecurityError:
                    raise ValueError("Invalid or unsafe JSON data")
        return v


class ContentMetadataModel(SafeJSONModel):
    """内容元数据模型"""

    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    tags: Optional[List[str]] = Field(None, max_items=20)
    category: Optional[str] = Field(None, max_length=50)
    language: Optional[str] = Field(None, max_length=10)
    duration: Optional[float] = Field(None, ge=0, le=3600)  # 最长1小时
    resolution: Optional[str] = Field(None, pattern=r"^\d+x\d+$")
    file_size: Optional[int] = Field(None, ge=0)

    @validator("tags")
    def validate_tags(cls, v):
        if v:
            for tag in v:
                if len(tag) > 50:
                    raise ValueError("Tag too long")
                if not tag.replace("-", "").replace("_", "").isalnum():
                    raise ValueError("Tag contains invalid characters")
        return v


class VideoProcessingMetadataModel(SafeJSONModel):
    """视频处理元数据模型"""

    source_url: Optional[str] = Field(None, max_length=2000)
    quality: Optional[str] = Field(None, pattern=r"^(360p|720p|1080p|1440p|best)$")
    format: Optional[str] = Field(None, pattern=r"^(mp4|avi|mov|mkv|webm)$")
    processing_status: Optional[str] = Field(
        None, pattern=r"^(pending|processing|completed|failed)$"
    )
    error_message: Optional[str] = Field(None, max_length=500)
    processing_time: Optional[float] = Field(None, ge=0)
    file_path: Optional[str] = Field(None, max_length=500)


# 工具函数
def safe_parse_json(json_str: str, model_class: BaseModel = None) -> Union[Dict, Any]:
    """安全解析JSON字符串"""
    try:
        # 使用安全验证器解析
        data = JSONValidator.safe_json_loads(json_str)

        # 如果提供了模型类，进行验证
        if model_class:
            return model_class(**data)

        return data

    except JSONSecurityError as e:
        logger.warning(f"JSON security validation failed: {e.message}")
        raise
    except ValidationError as e:
        logger.warning(f"JSON model validation failed: {e}")
        raise JSONSecurityError(f"JSON validation failed: {e}", "VALIDATION_ERROR")


def safe_serialize_json(data: Any) -> str:
    """安全序列化为JSON字符串"""
    try:
        return JSONValidator.safe_json_dumps(data)
    except JSONSecurityError as e:
        logger.warning(f"JSON serialization failed: {e.message}")
        raise


def validate_metadata_json(metadata_str: str, metadata_type: str = "content") -> Dict:
    """验证元数据JSON"""
    if not metadata_str:
        return {}

    # 🔒 证据链: 根据类型选择相应的验证模型
    model_map = {
        "content": ContentMetadataModel,
        "video": VideoProcessingMetadataModel,
    }

    model_class = model_map.get(metadata_type, ContentMetadataModel)

    try:
        validated_data = safe_parse_json(metadata_str, model_class)
        if isinstance(validated_data, BaseModel):
            return validated_data.dict()
        return validated_data
    except Exception as e:
        logger.error(f"Metadata validation failed for type {metadata_type}: {e}")
        raise JSONSecurityError(
            f"Invalid {metadata_type} metadata: {e}", "METADATA_VALIDATION_ERROR"
        )


# 装饰器
def json_validated(field_name: str = "metadata", metadata_type: str = "content"):
    """JSON验证装饰器"""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 查找并验证JSON字段
            for key, value in kwargs.items():
                if key == field_name and isinstance(value, str):
                    try:
                        kwargs[key] = validate_metadata_json(value, metadata_type)
                    except JSONSecurityError as e:
                        from fastapi import HTTPException, status

                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"Invalid JSON in {field_name}: {e.message}",
                        )

            return await func(*args, **kwargs)

        return wrapper

    return decorator


# 中间件支持
class JSONValidationMiddleware:
    """JSON验证中间件"""

    def __init__(self, max_size: int = None):
        self.max_size = max_size or JSONValidator.MAX_JSON_SIZE

    async def __call__(self, request, call_next):
        """处理请求的JSON验证"""
        # 检查Content-Type
        content_type = request.headers.get("content-type", "")

        if "application/json" in content_type:
            # 获取请求体
            body = await request.body()

            if len(body) > self.max_size:
                from fastapi import HTTPException, status

                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail=f"Request body too large: {len(body)} > {self.max_size}",
                )

            # 验证JSON
            try:
                if body:
                    JSONValidator.validate_json_string(body.decode("utf-8"))
            except JSONSecurityError as e:
                from fastapi import HTTPException, status

                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid JSON: {e.message}",
                )

        return await call_next(request)
