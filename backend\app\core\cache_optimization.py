"""
缓存策略优化模块
"""

import asyncio
import json
import logging
import time
from functools import wraps
from typing import Any, Dict, List, Optional

from app.services.redis_cache_service import RedisCache

logger = logging.getLogger(__name__)


class CacheStrategy:
    """缓存策略基类"""

    def __init__(self, ttl: int = 3600, namespace: str = "default"):
        self.ttl = ttl
        self.namespace = namespace
        self.redis_cache = RedisCache(namespace)
        self.local_cache = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "errors": 0,
        }

    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            # 先检查本地缓存
            local_result = self._get_local(key)
            if local_result is not None:
                self.cache_stats["hits"] += 1
                return local_result

            # 检查Redis缓存
            redis_result = await self.redis_cache.get(key)
            if redis_result is not None:
                # 回填本地缓存
                self._set_local(key, redis_result, self.ttl // 4)  # 本地缓存时间更短
                self.cache_stats["hits"] += 1
                return redis_result

            self.cache_stats["misses"] += 1
            return None

        except Exception as e:
            logger.error(f"缓存获取失败 {key}: {e}")
            self.cache_stats["errors"] += 1
            return None

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        try:
            cache_ttl = ttl or self.ttl

            # 设置Redis缓存
            redis_success = await self.redis_cache.set(key, value, cache_ttl)

            # 设置本地缓存
            self._set_local(key, value, cache_ttl // 4)

            if redis_success:
                self.cache_stats["sets"] += 1
                return True

            return False

        except Exception as e:
            logger.error(f"缓存设置失败 {key}: {e}")
            self.cache_stats["errors"] += 1
            return False

    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            # 删除本地缓存
            self._delete_local(key)

            # 删除Redis缓存
            redis_success = await self.redis_cache.delete(key)

            if redis_success:
                self.cache_stats["deletes"] += 1
                return True

            return False

        except Exception as e:
            logger.error(f"缓存删除失败 {key}: {e}")
            self.cache_stats["errors"] += 1
            return False

    def _get_local(self, key: str) -> Optional[Any]:
        """获取本地缓存"""
        if key in self.local_cache:
            item = self.local_cache[key]
            if item["expires_at"] > time.time():
                return item["value"]
            else:
                # 过期删除
                del self.local_cache[key]
        return None

    def _set_local(self, key: str, value: Any, ttl: int):
        """设置本地缓存"""
        self.local_cache[key] = {
            "value": value,
            "expires_at": time.time() + ttl,
            "created_at": time.time(),
        }

        # 限制本地缓存大小
        if len(self.local_cache) > 1000:
            # 删除最旧的条目
            oldest_key = min(
                self.local_cache.keys(), key=lambda k: self.local_cache[k]["created_at"]
            )
            del self.local_cache[oldest_key]

    def _delete_local(self, key: str):
        """删除本地缓存"""
        self.local_cache.pop(key, None)

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = (
            (self.cache_stats["hits"] / total_requests * 100)
            if total_requests > 0
            else 0
        )

        return {
            **self.cache_stats,
            "hit_rate": round(hit_rate, 2),
            "local_cache_size": len(self.local_cache),
            "namespace": self.namespace,
        }


class UserCacheStrategy(CacheStrategy):
    """用户缓存策略"""

    def __init__(self):
        super().__init__(ttl=1800, namespace="users")  # 30分钟

    async def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取用户缓存"""
        return await self.get(f"user:{user_id}")

    async def set_user(self, user_id: int, user_data: Dict[str, Any]) -> bool:
        """设置用户缓存"""
        return await self.set(f"user:{user_id}", user_data)

    async def invalidate_user(self, user_id: int) -> bool:
        """使用户缓存失效"""
        keys_to_delete = [
            f"user:{user_id}",
            f"user_projects:{user_id}",
            f"user_stats:{user_id}",
        ]

        results = []
        for key in keys_to_delete:
            results.append(await self.delete(key))

        return all(results)


class ContentCacheStrategy(CacheStrategy):
    """内容缓存策略"""

    def __init__(self):
        super().__init__(ttl=3600, namespace="content")  # 1小时

    async def get_content_list(
        self, project_id: int, page: int = 1, limit: int = 20
    ) -> Optional[List[Dict[str, Any]]]:
        """获取内容列表缓存"""
        cache_key = f"content_list:{project_id}:{page}:{limit}"
        return await self.get(cache_key)

    async def set_content_list(
        self,
        project_id: int,
        content_list: List[Dict[str, Any]],
        page: int = 1,
        limit: int = 20,
    ) -> bool:
        """设置内容列表缓存"""
        cache_key = f"content_list:{project_id}:{page}:{limit}"
        return await self.set(cache_key, content_list, ttl=1800)  # 30分钟

    async def invalidate_project_content(self, project_id: int) -> bool:
        """使项目内容缓存失效"""
        # 这里需要实现模式匹配删除，简化版本只删除第一页
        keys_to_delete = [
            f"content_list:{project_id}:1:20",
            f"project_stats:{project_id}",
        ]

        results = []
        for key in keys_to_delete:
            results.append(await self.delete(key))

        return all(results)


class APIResponseCacheStrategy(CacheStrategy):
    """API响应缓存策略"""

    def __init__(self):
        super().__init__(ttl=300, namespace="api_responses")  # 5分钟

    def generate_cache_key(self, endpoint: str, params: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 对参数进行排序以确保一致性
        sorted_params = json.dumps(params, sort_keys=True)
        return f"api:{endpoint}:{hash(sorted_params)}"


# 缓存装饰器
def cache_result(
    strategy: CacheStrategy,
    ttl: Optional[int] = None,
    key_generator: Optional[callable] = None,
):
    """缓存结果装饰器"""

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成缓存键
            if key_generator:
                cache_key = key_generator(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"

            # 尝试从缓存获取
            cached_result = await strategy.get(cache_key)
            if cached_result is not None:
                return cached_result

            # 执行函数
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)

            # 缓存结果
            await strategy.set(cache_key, result, ttl)

            return result

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 同步版本需要在事件循环中运行
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(async_wrapper(*args, **kwargs))

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


# 缓存预热
class CacheWarmer:
    """缓存预热器"""

    def __init__(self):
        self.user_cache = UserCacheStrategy()
        self.content_cache = ContentCacheStrategy()

    async def warm_user_cache(self, user_ids: List[int]):
        """预热用户缓存"""
        logger.info(f"开始预热用户缓存，用户数量: {len(user_ids)}")

        for user_id in user_ids:
            try:
                # 这里应该从数据库获取用户数据
                # user_data = await get_user_from_db(user_id)
                # await self.user_cache.set_user(user_id, user_data)
                pass
            except Exception as e:
                logger.error(f"预热用户缓存失败 {user_id}: {e}")

    async def warm_content_cache(self, project_ids: List[int]):
        """预热内容缓存"""
        logger.info(f"开始预热内容缓存，项目数量: {len(project_ids)}")

        for project_id in project_ids:
            try:
                # 这里应该从数据库获取内容列表
                # content_list = await get_content_list_from_db(project_id)
                # await self.content_cache.set_content_list(project_id, content_list)
                pass
            except Exception as e:
                logger.error(f"预热内容缓存失败 {project_id}: {e}")


# 全局缓存实例
user_cache = UserCacheStrategy()
content_cache = ContentCacheStrategy()
api_cache = APIResponseCacheStrategy()
cache_warmer = CacheWarmer()


async def get_cache_health() -> Dict[str, Any]:
    """获取缓存健康状态"""
    caches = {
        "user_cache": user_cache,
        "content_cache": content_cache,
        "api_cache": api_cache,
    }

    health_status = {}

    for name, cache in caches.items():
        try:
            stats = cache.get_stats()
            health_status[name] = {
                "status": "healthy" if stats["hit_rate"] > 50 else "warning",
                "stats": stats,
            }
        except Exception as e:
            health_status[name] = {"status": "error", "error": str(e)}

    return health_status


def initialize_cache_optimization():
    """初始化缓存优化"""
    logger.info("初始化缓存优化...")

    # 这里可以添加缓存预热逻辑
    # asyncio.create_task(cache_warmer.warm_user_cache([1, 2, 3]))

    logger.info("缓存优化初始化完成")


if __name__ == "__main__":
    initialize_cache_optimization()
