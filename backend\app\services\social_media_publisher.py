"""
多平台发布服务框架
支持主流社交媒体平台的自动化内容发布
"""

import base64
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional

import aiohttp

from app.core.exceptions import (
    ExternalServiceException,
    RateLimitException,
    ValidationException,
)
from app.core.logging import get_service_logger, log_function_call


class PlatformType(Enum):
    """支持的发布平台类型"""

    WEIBO = "weibo"
    DOUYIN = "douyin"
    XIAOHONGSHU = "xiaohongshu"
    BILIBILI = "bilibili"
    YOUTUBE = "youtube"
    TWITTER = "twitter"
    INSTAGRAM = "instagram"
    TIKTOK = "tiktok"


class ContentType(Enum):
    """内容类型"""

    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    MIXED = "mixed"


class PublishStatus(Enum):
    """发布状态"""

    PENDING = "pending"
    PUBLISHING = "publishing"
    SUCCESS = "success"
    FAILED = "failed"
    RATE_LIMITED = "rate_limited"


class PublishRequest:
    """发布请求数据结构"""

    def __init__(
        self,
        platform: PlatformType,
        content_type: ContentType,
        title: str,
        content: str,
        media_files: List[str] = None,
        tags: List[str] = None,
        scheduled_time: Optional[datetime] = None,
        metadata: Dict[str, Any] = None,
    ):
        self.platform = platform
        self.content_type = content_type
        self.title = title
        self.content = content
        self.media_files = media_files or []
        self.tags = tags or []
        self.scheduled_time = scheduled_time
        self.metadata = metadata or {}
        self.created_at = datetime.now()


class BasePlatformPublisher:
    """基础平台发布器"""

    def __init__(self, platform: PlatformType):
        self.platform = platform
        self.logger = get_service_logger(f"publisher_{platform.value}")
        self.rate_limits = {"requests_per_hour": 100, "requests_per_day": 1000}
        self.request_history = []

    def _check_rate_limit(self) -> bool:
        """检查速率限制"""
        now = datetime.now()
        hour_ago = now - timedelta(hours=1)
        day_ago = now - timedelta(days=1)

        # 清理过期记录
        self.request_history = [
            req_time for req_time in self.request_history if req_time > day_ago
        ]

        # 检查限制
        hour_requests = len(
            [req_time for req_time in self.request_history if req_time > hour_ago]
        )
        day_requests = len(self.request_history)

        if hour_requests >= self.rate_limits["requests_per_hour"]:
            self.logger.warning(
                "小时请求限制达到",
                extra_data={
                    "platform": self.platform.value,
                    "hour_requests": hour_requests,
                    "limit": self.rate_limits["requests_per_hour"],
                },
            )
            return False

        if day_requests >= self.rate_limits["requests_per_day"]:
            self.logger.warning(
                "日请求限制达到",
                extra_data={
                    "platform": self.platform.value,
                    "day_requests": day_requests,
                    "limit": self.rate_limits["requests_per_day"],
                },
            )
            return False

        return True

    def _record_request(self):
        """记录请求时间"""
        self.request_history.append(datetime.now())

    async def validate_content(self, request: PublishRequest) -> bool:
        """验证内容格式"""
        if not request.title or not request.title.strip():
            raise ValidationException("标题不能为空", field="title")

        if not request.content or not request.content.strip():
            raise ValidationException("内容不能为空", field="content")

        # 检查媒体文件
        for media_file in request.media_files:
            if not Path(media_file).exists():
                raise ValidationException(
                    f"媒体文件不存在: {media_file}", field="media_files"
                )

        return True

    @log_function_call
    async def publish(self, request: PublishRequest) -> Dict[str, Any]:
        """发布内容 - 子类需要实现"""
        raise NotImplementedError("子类必须实现publish方法")


class WeiboPublisher(BasePlatformPublisher):
    """微博发布器"""

    def __init__(self, access_token: str):
        super().__init__(PlatformType.WEIBO)
        self.access_token = access_token
        self.api_base = "https://api.weibo.com/2"
        self.rate_limits = {"requests_per_hour": 150, "requests_per_day": 1000}

    async def publish(self, request: PublishRequest) -> Dict[str, Any]:
        """发布到微博"""
        if not self._check_rate_limit():
            raise RateLimitException("微博API请求频率限制")

        await self.validate_content(request)

        try:
            # 构建发布内容
            status_text = f"{request.title}\n\n{request.content}"
            if request.tags:
                hashtags = " ".join([f"#{tag}#" for tag in request.tags])
                status_text += f"\n\n{hashtags}"

            # 发布参数
            params = {
                "access_token": self.access_token,
                "status": status_text[:140],  # 微博字数限制
            }

            # 处理图片上传
            has_images = (
                request.media_files and request.content_type == ContentType.IMAGE
            )
            if has_images:
                # 先上传图片
                pic_id = await self._upload_image(request.media_files[0])
                if pic_id:
                    params["pic"] = pic_id

            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base}/statuses/update.json", data=params
                ) as response:
                    result = await response.json()

                    if response.status == 200 and "id" in result:
                        self._record_request()
                        self.logger.info(
                            "微博发布成功",
                            extra_data={
                                "post_id": result["id"],
                                "platform": "weibo",
                            },
                        )

                        post_url = (
                            f"https://weibo.com/{result['user']['id']}/"
                            f"{result['id']}"
                        )

                        return {
                            "status": PublishStatus.SUCCESS.value,
                            "platform": self.platform.value,
                            "post_id": result["id"],
                            "post_url": post_url,
                            "published_at": datetime.now().isoformat(),
                        }
                    else:
                        error_msg = result.get("error", "未知错误")
                        self.logger.error(
                            "微博发布失败",
                            extra_data={
                                "error": error_msg,
                                "response": result,
                            },
                        )

                        raise ExternalServiceException(
                            f"微博发布失败: {error_msg}", service_name="weibo"
                        )

        except Exception as e:
            self.logger.error(
                "微博发布异常",
                extra_data={"error": str(e), "error_type": type(e).__name__},
            )

            if isinstance(
                e,
                (
                    ValidationException,
                    RateLimitException,
                    ExternalServiceException,
                ),
            ):
                raise
            else:
                raise ExternalServiceException(
                    f"微博发布异常: {str(e)}", service_name="weibo"
                )

    async def _upload_image(self, image_path: str) -> Optional[str]:
        """上传图片到微博"""
        try:
            with open(image_path, "rb") as f:
                image_data = f.read()

            params = {
                "access_token": self.access_token,
                "pic": base64.b64encode(image_data).decode(),
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base}/statuses/upload_pic.json", data=params
                ) as response:
                    result = await response.json()

                    if response.status == 200 and "pic_id" in result:
                        return result["pic_id"]

                    return None

        except Exception as e:
            self.logger.error(
                "图片上传失败",
                extra_data={"image_path": image_path, "error": str(e)},
            )
            return None


class DouyinPublisher(BasePlatformPublisher):
    """抖音发布器"""

    def __init__(self, access_token: str):
        super().__init__(PlatformType.DOUYIN)
        self.access_token = access_token
        self.api_base = "https://open.douyin.com"
        self.rate_limits = {"requests_per_hour": 100, "requests_per_day": 500}

    async def publish(self, request: PublishRequest) -> Dict[str, Any]:
        """发布到抖音"""
        if not self._check_rate_limit():
            raise RateLimitException("抖音API请求频率限制")

        await self.validate_content(request)

        if request.content_type != ContentType.VIDEO:
            raise ValidationException("抖音仅支持视频内容发布")

        if not request.media_files:
            raise ValidationException("抖音发布需要视频文件")

        try:
            # 先上传视频
            video_id = await self._upload_video(request.media_files[0])
            if not video_id:
                raise ExternalServiceException("视频上传失败", service_name="douyin")

            # 发布视频
            params = {
                "access_token": self.access_token,
                "video_id": video_id,
                "text": f"{request.title}\n{request.content}",
                "poi_id": "",
                "micro_app_id": "",
                "micro_app_title": "",
                "micro_app_url": "",
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base}/video/create/", json=params
                ) as response:
                    result = await response.json()

                    if result.get("error_code") == 0:
                        self._record_request()
                        item_id = result["data"]["item_id"]

                        self.logger.info(
                            "抖音发布成功",
                            extra_data={
                                "item_id": item_id,
                                "platform": "douyin",
                            },
                        )

                        return {
                            "status": PublishStatus.SUCCESS.value,
                            "platform": self.platform.value,
                            "post_id": item_id,
                            "post_url": (f"https://www.douyin.com/video/" f"{item_id}"),
                            "published_at": datetime.now().isoformat(),
                        }
                    else:
                        error_msg = result.get("description", "未知错误")
                        self.logger.error(
                            "抖音发布失败",
                            extra_data={
                                "error": error_msg,
                                "response": result,
                            },
                        )

                        raise ExternalServiceException(
                            f"抖音发布失败: {error_msg}", service_name="douyin"
                        )

        except Exception as e:
            self.logger.error(
                "抖音发布异常",
                extra_data={"error": str(e), "error_type": type(e).__name__},
            )

            if isinstance(
                e,
                (
                    ValidationException,
                    RateLimitException,
                    ExternalServiceException,
                ),
            ):
                raise
            else:
                raise ExternalServiceException(
                    f"抖音发布异常: {str(e)}", service_name="douyin"
                )

    async def _upload_video(self, video_path: str) -> Optional[str]:
        """上传视频到抖音"""
        try:
            # 实际实现需要分片上传等复杂逻辑
            # 这里提供基础框架
            self.logger.info("开始上传视频", extra_data={"video_path": video_path})

            # 模拟返回video_id
            return f"video_{int(datetime.now().timestamp())}"

        except Exception as e:
            self.logger.error(
                "视频上传失败",
                extra_data={"video_path": video_path, "error": str(e)},
            )
            return None


class MultiPlatformPublisher:
    """多平台发布管理器"""

    def __init__(self):
        self.publishers: Dict[PlatformType, BasePlatformPublisher] = {}
        self.logger = get_service_logger("multi_platform_publisher")
        self.publish_queue = []

    def register_publisher(
        self, platform: PlatformType, publisher: BasePlatformPublisher
    ):
        """注册平台发布器"""
        self.publishers[platform] = publisher
        self.logger.info(
            "注册平台发布器",
            extra_data={
                "platform": platform.value,
                "publisher_type": type(publisher).__name__,
            },
        )

    @log_function_call
    async def publish_to_platform(
        self, platform: PlatformType, request: PublishRequest
    ) -> Dict[str, Any]:
        """发布到指定平台"""
        if platform not in self.publishers:
            raise ValidationException(
                f"平台 {platform.value} 未注册发布器", field="platform"
            )

        publisher = self.publishers[platform]

        try:
            result = await publisher.publish(request)

            self.logger.info(
                "平台发布成功",
                extra_data={
                    "platform": platform.value,
                    "post_id": result.get("post_id"),
                    "title": request.title,
                },
            )

            return result

        except Exception as e:
            self.logger.error(
                "平台发布失败",
                extra_data={
                    "platform": platform.value,
                    "error": str(e),
                    "title": request.title,
                },
            )
            raise

    @log_function_call
    async def publish_to_multiple_platforms(
        self, platforms: List[PlatformType], request: PublishRequest
    ) -> Dict[str, Dict[str, Any]]:
        """发布到多个平台"""
        results = {}

        for platform in platforms:
            try:
                result = await self.publish_to_platform(platform, request)
                results[platform.value] = result

            except Exception as e:
                results[platform.value] = {
                    "status": PublishStatus.FAILED.value,
                    "platform": platform.value,
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "failed_at": datetime.now().isoformat(),
                }

        # 统计发布结果
        success_count = sum(
            1
            for result in results.values()
            if result.get("status") == PublishStatus.SUCCESS.value
        )

        self.logger.info(
            "多平台发布完成",
            extra_data={
                "total_platforms": len(platforms),
                "success_count": success_count,
                "failure_count": len(platforms) - success_count,
                "results": results,
            },
        )

        return results

    async def schedule_publish(
        self,
        platforms: List[PlatformType],
        request: PublishRequest,
        scheduled_time: datetime,
    ) -> str:
        """计划发布任务"""
        task_id = f"publish_{int(datetime.now().timestamp())}"

        # 添加到发布队列
        self.publish_queue.append(
            {
                "task_id": task_id,
                "platforms": platforms,
                "request": request,
                "scheduled_time": scheduled_time,
                "status": "scheduled",
                "created_at": datetime.now(),
            }
        )

        self.logger.info(
            "计划发布任务创建",
            extra_data={
                "task_id": task_id,
                "platforms": [p.value for p in platforms],
                "scheduled_time": scheduled_time.isoformat(),
            },
        )

        return task_id

    async def get_supported_platforms(self) -> List[str]:
        """获取支持的平台列表"""
        return [platform.value for platform in self.publishers.keys()]

    async def get_platform_status(self, platform: PlatformType) -> Dict[str, Any]:
        """获取平台状态"""
        if platform not in self.publishers:
            return {"status": "not_registered", "platform": platform.value}

        publisher = self.publishers[platform]

        return {
            "status": "registered",
            "platform": platform.value,
            "rate_limits": publisher.rate_limits,
            "recent_requests": len(publisher.request_history),
            "can_publish": publisher._check_rate_limit(),
        }


# 工厂函数
def create_weibo_publisher(access_token: str) -> WeiboPublisher:
    """创建微博发布器"""
    return WeiboPublisher(access_token)


def create_douyin_publisher(access_token: str) -> DouyinPublisher:
    """创建抖音发布器"""
    return DouyinPublisher(access_token)


def create_multi_platform_publisher() -> MultiPlatformPublisher:
    """创建多平台发布管理器"""
    return MultiPlatformPublisher()
