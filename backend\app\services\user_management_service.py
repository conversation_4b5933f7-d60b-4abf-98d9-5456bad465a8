#!/usr/bin/env python3
"""
AI视频内容创作系统 - 用户管理服务
提供用户注册、登录、权限管理等功能
"""

import logging
import secrets
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional

import bcrypt
import jwt
from pydantic import BaseModel, EmailStr, Field


class UserRole(str, Enum):
    """用户角色"""

    ADMIN = "admin"  # 管理员
    CREATOR = "creator"  # 内容创作者
    EDITOR = "editor"  # 编辑者
    VIEWER = "viewer"  # 观看者


class UserStatus(str, Enum):
    """用户状态"""

    ACTIVE = "active"  # 活跃
    INACTIVE = "inactive"  # 非活跃
    SUSPENDED = "suspended"  # 暂停
    BANNED = "banned"  # 封禁


class User(BaseModel):
    """用户模型"""

    user_id: str = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: EmailStr = Field(..., description="邮箱")
    password_hash: str = Field(..., description="密码哈希")
    role: UserRole = Field(default=UserRole.CREATOR, description="用户角色")
    status: UserStatus = Field(default=UserStatus.ACTIVE, description="用户状态")

    # 个人信息
    display_name: Optional[str] = Field(None, description="显示名称")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    bio: Optional[str] = Field(None, description="个人简介")

    # 统计信息
    video_count: int = Field(default=0, description="视频数量")
    total_views: int = Field(default=0, description="总观看数")
    total_likes: int = Field(default=0, description="总点赞数")

    # 时间信息
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")


class UserCreateRequest(BaseModel):
    """用户创建请求"""

    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    password: str = Field(..., min_length=6, max_length=128)
    display_name: Optional[str] = Field(None, max_length=100)
    role: UserRole = Field(default=UserRole.CREATOR)


class UserLoginRequest(BaseModel):
    """用户登录请求"""

    username_or_email: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")


class UserLoginResponse(BaseModel):
    """用户登录响应"""

    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="Bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    user_info: Dict[str, Any] = Field(..., description="用户信息")


class UserPermission(BaseModel):
    """用户权限"""

    user_id: str
    permissions: List[str] = Field(default_factory=list)
    resource_access: Dict[str, List[str]] = Field(default_factory=dict)


class UserManagementService:
    """用户管理服务"""

    def __init__(self, secret_key: str = None):
        self.logger = logging.getLogger(__name__)
        self.secret_key = secret_key or secrets.token_urlsafe(32)

        # 用户存储 (生产环境应使用数据库)
        self.users: Dict[str, User] = {}
        self.users_by_email: Dict[str, str] = {}  # email -> user_id
        self.users_by_username: Dict[str, str] = {}  # username -> user_id

        # 权限存储
        self.permissions: Dict[str, UserPermission] = {}

        # JWT设置
        self.jwt_algorithm = "HS256"
        self.jwt_expiration = 3600 * 24  # 24小时

        # 初始化默认权限
        self._init_default_permissions()

        # 创建默认管理员账户
        self._create_default_admin()

    def _init_default_permissions(self):
        """初始化默认权限"""
        self.role_permissions = {
            UserRole.ADMIN: [
                "user:create",
                "user:read",
                "user:update",
                "user:delete",
                "video:create",
                "video:read",
                "video:update",
                "video:delete",
                "content:create",
                "content:read",
                "content:update",
                "content:delete",
                "system:admin",
                "system:monitor",
            ],
            UserRole.CREATOR: [
                "user:read",
                "user:update",
                "video:create",
                "video:read",
                "video:update",
                "content:create",
                "content:read",
                "content:update",
            ],
            UserRole.EDITOR: [
                "user:read",
                "video:read",
                "video:update",
                "content:read",
                "content:update",
            ],
            UserRole.VIEWER: ["user:read", "video:read", "content:read"],
        }

    def _create_default_admin(self):
        """创建默认管理员账户"""
        admin_username = "admin"
        admin_email = "<EMAIL>"
        admin_password = "admin123"

        if admin_username not in self.users_by_username:
            try:
                self.create_user(
                    UserCreateRequest(
                        username=admin_username,
                        email=admin_email,
                        password=admin_password,
                        display_name="系统管理员",
                        role=UserRole.ADMIN,
                    )
                )
                self.logger.info("默认管理员账户已创建")
            except Exception as e:
                self.logger.error(f"创建默认管理员失败: {e}")

    def _generate_user_id(self) -> str:
        """生成用户ID"""
        import uuid

        return str(uuid.uuid4())

    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode("utf-8"), salt).decode("utf-8")

    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode("utf-8"), password_hash.encode("utf-8"))

    def create_user(self, user_request: UserCreateRequest) -> User:
        """创建用户"""
        # 检查用户名和邮箱是否已存在
        if user_request.username in self.users_by_username:
            raise ValueError(f"用户名已存在: {user_request.username}")

        if user_request.email in self.users_by_email:
            raise ValueError(f"邮箱已存在: {user_request.email}")

        # 生成用户ID和密码哈希
        user_id = self._generate_user_id()
        password_hash = self._hash_password(user_request.password)

        # 创建用户对象
        user = User(
            user_id=user_id,
            username=user_request.username,
            email=user_request.email,
            password_hash=password_hash,
            role=user_request.role,
            display_name=user_request.display_name or user_request.username,
        )

        # 存储用户
        self.users[user_id] = user
        self.users_by_email[user_request.email] = user_id
        self.users_by_username[user_request.username] = user_id

        # 创建用户权限
        permissions = UserPermission(
            user_id=user_id,
            permissions=self.role_permissions.get(user_request.role, []),
        )
        self.permissions[user_id] = permissions

        self.logger.info(f"用户创建成功: {user_request.username}")
        return user

    def authenticate_user(self, login_request: UserLoginRequest) -> Optional[User]:
        """用户认证"""
        # 根据用户名或邮箱查找用户
        user_id = None
        if "@" in login_request.username_or_email:
            user_id = self.users_by_email.get(login_request.username_or_email)
        else:
            user_id = self.users_by_username.get(login_request.username_or_email)

        if not user_id:
            return None

        user = self.users.get(user_id)
        if not user:
            return None

        # 验证密码
        if not self._verify_password(login_request.password, user.password_hash):
            return None

        # 检查用户状态
        if user.status != UserStatus.ACTIVE:
            raise ValueError(f"用户状态异常: {user.status}")

        # 更新最后登录时间
        user.last_login_at = datetime.now()
        user.updated_at = datetime.now()

        return user

    def generate_token(self, user: User) -> str:
        """生成JWT令牌"""
        payload = {
            "user_id": user.user_id,
            "username": user.username,
            "role": user.role,
            "exp": datetime.utcnow() + timedelta(seconds=self.jwt_expiration),
            "iat": datetime.utcnow(),
        }

        return jwt.encode(payload, self.secret_key, algorithm=self.jwt_algorithm)

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(
                token, self.secret_key, algorithms=[self.jwt_algorithm]
            )
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None

    def login(self, login_request: UserLoginRequest) -> UserLoginResponse:
        """用户登录"""
        user = self.authenticate_user(login_request)
        if not user:
            raise ValueError("用户名/邮箱或密码错误")

        # 生成访问令牌
        access_token = self.generate_token(user)

        # 返回登录响应
        return UserLoginResponse(
            access_token=access_token,
            expires_in=self.jwt_expiration,
            user_info={
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "display_name": user.display_name,
                "role": user.role,
                "avatar_url": user.avatar_url,
            },
        )

    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """根据ID获取用户"""
        return self.users.get(user_id)

    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        user_id = self.users_by_username.get(username)
        return self.users.get(user_id) if user_id else None

    def get_user_permissions(self, user_id: str) -> List[str]:
        """获取用户权限"""
        permission = self.permissions.get(user_id)
        return permission.permissions if permission else []

    def has_permission(self, user_id: str, permission: str) -> bool:
        """检查用户是否有特定权限"""
        user_permissions = self.get_user_permissions(user_id)
        return permission in user_permissions

    def update_user(self, user_id: str, updates: Dict[str, Any]) -> Optional[User]:
        """更新用户信息"""
        user = self.users.get(user_id)
        if not user:
            return None

        # 更新允许的字段
        allowed_fields = ["display_name", "avatar_url", "bio", "status"]

        for field, value in updates.items():
            if field in allowed_fields and hasattr(user, field):
                setattr(user, field, value)

        user.updated_at = datetime.now()
        return user

    def list_users(
        self, role: UserRole = None, status: UserStatus = None
    ) -> List[User]:
        """列出用户"""
        users = list(self.users.values())

        if role:
            users = [u for u in users if u.role == role]

        if status:
            users = [u for u in users if u.status == status]

        return users

    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计"""
        users = list(self.users.values())

        return {
            "total_users": len(users),
            "active_users": len([u for u in users if u.status == UserStatus.ACTIVE]),
            "role_distribution": {
                role.value: len([u for u in users if u.role == role])
                for role in UserRole
            },
            "status_distribution": {
                status.value: len([u for u in users if u.status == status])
                for status in UserStatus
            },
            "total_videos": sum(u.video_count for u in users),
            "total_views": sum(u.total_views for u in users),
        }


# 全局服务实例
user_management_service = UserManagementService()
