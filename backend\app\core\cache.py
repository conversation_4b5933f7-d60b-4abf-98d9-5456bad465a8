# -*- coding: utf-8 -*-
"""
Redis缓存层实现
提供高性能的缓存服务和会话管理
"""

import json
import pickle
import hashlib
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
import redis
from redis.exceptions import RedisError, ConnectionError
import logging
from functools import wraps

from app.core.config import settings

logger = logging.getLogger(__name__)

class CacheManager:
    """Redis缓存管理器"""
    
    def __init__(self):
        self.redis_client = None
        self.fallback_cache = {}  # 内存备用缓存
        self.connect()
    
    def connect(self):
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD,
                db=settings.REDIS_DB,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            # 测试连接
            self.redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.redis_client = None
    
    def is_connected(self) -> bool:
        """检查Redis连接状态"""
        if not self.redis_client:
            return False
        try:
            self.redis_client.ping()
            return True
        except:
            return False
    
    def _get_key(self, key: str, prefix: str = None) -> str:
        """生成缓存键"""
        if prefix:
            return f"{settings.REDIS_PREFIX}:{prefix}:{key}"
        return f"{settings.REDIS_PREFIX}:{key}"
    
    def set(self, key: str, value: Any, expire: int = None, prefix: str = None) -> bool:
        """设置缓存
        
        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间(秒)
            prefix: 键前缀
        
        Returns:
            bool: 是否成功
        """
        cache_key = self._get_key(key, prefix)
        
        try:
            # 序列化值
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            elif isinstance(value, (int, float, str, bool)):
                serialized_value = str(value)
            else:
                serialized_value = pickle.dumps(value)
            
            if self.is_connected():
                if expire:
                    return self.redis_client.setex(cache_key, expire, serialized_value)
                else:
                    return self.redis_client.set(cache_key, serialized_value)
            else:
                # 使用内存备用缓存
                expire_time = None
                if expire:
                    expire_time = datetime.now() + timedelta(seconds=expire)
                
                self.fallback_cache[cache_key] = {
                    'value': serialized_value,
                    'expire_time': expire_time,
                    'type': type(value).__name__
                }
                return True
                
        except Exception as e:
            logger.error(f"设置缓存失败 {cache_key}: {e}")
            return False
    
    def get(self, key: str, prefix: str = None, default: Any = None) -> Any:
        """获取缓存
        
        Args:
            key: 缓存键
            prefix: 键前缀
            default: 默认值
        
        Returns:
            Any: 缓存值
        """
        cache_key = self._get_key(key, prefix)
        
        try:
            if self.is_connected():
                value = self.redis_client.get(cache_key)
                if value is None:
                    return default
                
                # 尝试反序列化
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    try:
                        return pickle.loads(value.encode() if isinstance(value, str) else value)
                    except:
                        return value
            else:
                # 使用内存备用缓存
                if cache_key in self.fallback_cache:
                    cache_item = self.fallback_cache[cache_key]
                    
                    # 检查是否过期
                    if cache_item['expire_time'] and datetime.now() > cache_item['expire_time']:
                        del self.fallback_cache[cache_key]
                        return default
                    
                    value = cache_item['value']
                    # 反序列化
                    try:
                        if cache_item['type'] in ['dict', 'list']:
                            return json.loads(value)
                        elif cache_item['type'] in ['int', 'float', 'str', 'bool']:
                            return eval(value) if cache_item['type'] != 'str' else value
                        else:
                            return pickle.loads(value)
                    except:
                        return value
                
                return default
                
        except Exception as e:
            logger.error(f"获取缓存失败 {cache_key}: {e}")
            return default
    
    def delete(self, key: str, prefix: str = None) -> bool:
        """删除缓存"""
        cache_key = self._get_key(key, prefix)
        
        try:
            if self.is_connected():
                return bool(self.redis_client.delete(cache_key))
            else:
                if cache_key in self.fallback_cache:
                    del self.fallback_cache[cache_key]
                    return True
                return False
        except Exception as e:
            logger.error(f"删除缓存失败 {cache_key}: {e}")
            return False
    
    def exists(self, key: str, prefix: str = None) -> bool:
        """检查缓存是否存在"""
        cache_key = self._get_key(key, prefix)
        
        try:
            if self.is_connected():
                return bool(self.redis_client.exists(cache_key))
            else:
                if cache_key in self.fallback_cache:
                    cache_item = self.fallback_cache[cache_key]
                    # 检查是否过期
                    if cache_item['expire_time'] and datetime.now() > cache_item['expire_time']:
                        del self.fallback_cache[cache_key]
                        return False
                    return True
                return False
        except Exception as e:
            logger.error(f"检查缓存存在失败 {cache_key}: {e}")
            return False
    
    def expire(self, key: str, seconds: int, prefix: str = None) -> bool:
        """设置缓存过期时间"""
        cache_key = self._get_key(key, prefix)
        
        try:
            if self.is_connected():
                return bool(self.redis_client.expire(cache_key, seconds))
            else:
                if cache_key in self.fallback_cache:
                    self.fallback_cache[cache_key]['expire_time'] = datetime.now() + timedelta(seconds=seconds)
                    return True
                return False
        except Exception as e:
            logger.error(f"设置缓存过期时间失败 {cache_key}: {e}")
            return False
    
    def clear_pattern(self, pattern: str, prefix: str = None) -> int:
        """清除匹配模式的缓存"""
        cache_pattern = self._get_key(pattern, prefix)
        
        try:
            if self.is_connected():
                keys = self.redis_client.keys(cache_pattern)
                if keys:
                    return self.redis_client.delete(*keys)
                return 0
            else:
                # 内存缓存模式匹配
                import fnmatch
                deleted_count = 0
                keys_to_delete = []
                
                for key in self.fallback_cache.keys():
                    if fnmatch.fnmatch(key, cache_pattern):
                        keys_to_delete.append(key)
                
                for key in keys_to_delete:
                    del self.fallback_cache[key]
                    deleted_count += 1
                
                return deleted_count
        except Exception as e:
            logger.error(f"清除缓存模式失败 {cache_pattern}: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if self.is_connected():
                info = self.redis_client.info()
                return {
                    'connected': True,
                    'used_memory': info.get('used_memory_human', 'N/A'),
                    'connected_clients': info.get('connected_clients', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0)
                }
            else:
                return {
                    'connected': False,
                    'fallback_cache_size': len(self.fallback_cache),
                    'fallback_memory_usage': f"{len(str(self.fallback_cache))} bytes"
                }
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {'connected': False, 'error': str(e)}

# 全局缓存管理器实例
cache_manager = CacheManager()

def cache_result(expire: int = 3600, prefix: str = "func", key_func=None):
    """缓存函数结果装饰器
    
    Args:
        expire: 过期时间(秒)
        prefix: 缓存键前缀
        key_func: 自定义键生成函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 使用函数名和参数生成键
                key_parts = [func.__name__]
                if args:
                    key_parts.extend([str(arg) for arg in args])
                if kwargs:
                    key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
                
                key_string = ":".join(key_parts)
                cache_key = hashlib.md5(key_string.encode()).hexdigest()
            
            # 尝试从缓存获取
            cached_result = cache_manager.get(cache_key, prefix)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, expire, prefix)
            
            return result
        return wrapper
    return decorator

def invalidate_cache(pattern: str, prefix: str = None):
    """清除缓存
    
    Args:
        pattern: 缓存键模式
        prefix: 键前缀
    """
    return cache_manager.clear_pattern(pattern, prefix)

# 便捷函数
def get_cache(key: str, default: Any = None, prefix: str = None) -> Any:
    """获取缓存"""
    return cache_manager.get(key, prefix, default)

def set_cache(key: str, value: Any, expire: int = None, prefix: str = None) -> bool:
    """设置缓存"""
    return cache_manager.set(key, value, expire, prefix)

def delete_cache(key: str, prefix: str = None) -> bool:
    """删除缓存"""
    return cache_manager.delete(key, prefix)

def cache_exists(key: str, prefix: str = None) -> bool:
    """检查缓存是否存在"""
    return cache_manager.exists(key, prefix)