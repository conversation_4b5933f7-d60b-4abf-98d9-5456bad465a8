"""
Ollama服务集成 - 基于开源项目Ollama
GitHub: https://github.com/ollama/ollama
用途: 简化的本地LLM管理工具，模型下载、管理和推理服务
"""

from datetime import datetime
from typing import Any, Dict, List

import httpx


class OllamaService:
    """Ollama开源项目集成服务"""

    def __init__(self, base_url: str = "http://localhost:11434"):
        """
        初始化Ollama服务

        Args:
            base_url: Ollama服务地址
        """
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=300.0)

    async def health_check(self) -> Dict[str, Any]:
        """检查Ollama服务健康状态"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                models_data = response.json()
                models = [model["name"] for model in models_data.get("models", [])]
                return {
                    "status": "healthy",
                    "available_models": models,
                    "service": "Ollama",
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": f"HTTP {response.status_code}",
                    "service": "Ollama",
                }
        except Exception as e:
            return {
                "status": "unreachable",
                "error": str(e),
                "service": "Ollama",
            }

    async def list_models(self) -> Dict[str, Any]:
        """获取可用模型列表"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                models = []
                for model in data.get("models", []):
                    models.append(
                        {
                            "name": model["name"],
                            "size": model.get("size", 0),
                            "modified_at": model.get("modified_at"),
                            "digest": model.get("digest", ""),
                        }
                    )
                return {
                    "success": True,
                    "models": models,
                    "count": len(models),
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                }
        except Exception as e:
            return {"success": False, "error": f"获取模型列表失败: {str(e)}"}

    async def pull_model(self, model_name: str) -> Dict[str, Any]:
        """
        下载模型

        Args:
            model_name: 模型名称 (如: "llama2:7b", "deepseek-coder:6.7b")

        Returns:
            Dict[str, Any]: 下载结果
        """
        try:
            response = await self.client.post(
                f"{self.base_url}/api/pull",
                json={"name": model_name},
                timeout=1800.0,  # 30分钟超时
            )

            if response.status_code == 200:
                return {
                    "success": True,
                    "model": model_name,
                    "message": f"模型 {model_name} 下载完成",
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                return {
                    "success": False,
                    "model": model_name,
                    "error": f"HTTP {response.status_code}: {response.text}",
                }
        except Exception as e:
            return {
                "success": False,
                "model": model_name,
                "error": f"模型下载失败: {str(e)}",
            }

    async def generate_text(
        self,
        model: str,
        prompt: str,
        stream: bool = False,
        options: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        生成文本

        Args:
            model: 模型名称
            prompt: 输入提示
            stream: 是否流式输出
            options: 生成选项

        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            payload = {"model": model, "prompt": prompt, "stream": stream}

            if options:
                payload["options"] = options

            response = await self.client.post(
                f"{self.base_url}/api/generate", json=payload
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "response": result["response"],
                    "model": model,
                    "done": result.get("done", True),
                    "context": result.get("context", []),
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                return {
                    "success": False,
                    "model": model,
                    "error": f"HTTP {response.status_code}: {response.text}",
                }

        except Exception as e:
            return {
                "success": False,
                "model": model,
                "error": f"文本生成失败: {str(e)}",
            }

    async def chat_completion(
        self, model: str, messages: List[Dict[str, str]], stream: bool = False
    ) -> Dict[str, Any]:
        """
        聊天对话完成

        Args:
            model: 模型名称
            messages: 消息列表
            stream: 是否流式输出

        Returns:
            Dict[str, Any]: 对话结果
        """
        try:
            payload = {"model": model, "messages": messages, "stream": stream}

            response = await self.client.post(f"{self.base_url}/api/chat", json=payload)

            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "content": result["message"]["content"],
                    "role": result["message"]["role"],
                    "model": model,
                    "done": result.get("done", True),
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                return {
                    "success": False,
                    "model": model,
                    "error": f"HTTP {response.status_code}: {response.text}",
                }

        except Exception as e:
            return {
                "success": False,
                "model": model,
                "error": f"聊天完成失败: {str(e)}",
            }

    async def content_optimization(
        self,
        content: str,
        optimization_type: str = "改写",
        model: str = "llama2:7b",
    ) -> Dict[str, Any]:
        """
        内容优化服务

        Args:
            content: 原始内容
            optimization_type: 优化类型 (改写/润色/总结)
            model: 使用的模型

        Returns:
            Dict[str, Any]: 优化结果
        """
        if optimization_type == "改写":
            prompt = f"请改写以下内容，保持原意但提升表达质量：\n\n{content}"
        elif optimization_type == "润色":
            prompt = f"请润色以下内容，使其更加流畅自然：\n\n{content}"
        elif optimization_type == "总结":
            prompt = f"请总结以下内容的核心要点：\n\n{content}"
        else:
            prompt = f"请优化以下内容：\n\n{content}"

        result = await self.generate_text(model=model, prompt=prompt)

        if result["success"]:
            return {
                "success": True,
                "original_content": content,
                "optimized_content": result["response"],
                "optimization_type": optimization_type,
                "model": model,
                "timestamp": datetime.now().isoformat(),
            }
        else:
            return {
                "success": False,
                "error": result["error"],
                "original_content": content,
                "optimization_type": optimization_type,
            }

    async def batch_model_setup(self, models: List[str]) -> Dict[str, Any]:
        """
        批量下载模型

        Args:
            models: 模型名称列表

        Returns:
            Dict[str, Any]: 批量下载结果
        """
        results = []
        success_count = 0

        for model in models:
            print(f"正在下载模型: {model}")
            result = await self.pull_model(model)
            results.append(result)

            if result["success"]:
                success_count += 1
                print(f"✅ {model} 下载完成")
            else:
                print(f"❌ {model} 下载失败: {result['error']}")

        return {
            "success": success_count == len(models),
            "total_models": len(models),
            "success_count": success_count,
            "failed_count": len(models) - success_count,
            "results": results,
            "timestamp": datetime.now().isoformat(),
        }

    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()


# 全局Ollama服务实例
ollama_service = OllamaService()


# 推荐模型列表
RECOMMENDED_MODELS = [
    "llama2:7b",  # 通用对话模型
    "deepseek-coder:6.7b",  # 代码生成模型
    "qwen:7b",  # 中文优化模型
    "mistral:7b",  # 高性能模型
    "codellama:7b",  # 代码专用模型
]


# 快捷函数
async def setup_recommended_models() -> Dict[str, Any]:
    """设置推荐模型"""
    return await ollama_service.batch_model_setup(RECOMMENDED_MODELS)


async def optimize_content(content: str, opt_type: str = "改写") -> Dict[str, Any]:
    """快捷内容优化函数"""
    return await ollama_service.content_optimization(content, opt_type)
