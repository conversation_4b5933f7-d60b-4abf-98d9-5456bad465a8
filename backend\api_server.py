"""
生产API服务器
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import sqlite3
import hashlib
from datetime import datetime

# 配置
DATABASE_PATH = "production.db"

# Pydantic模型
class UserLogin(BaseModel):
    username: str
    password: str

# 数据库连接
def get_db_connection():
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def hash_password(password: str) -> str:
    return hashlib.sha256(password.encode()).hexdigest()

# FastAPI应用
app = FastAPI(title="二创短视频分发系统", version="1.0.0")

# CORS配置 - 更宽松的配置避免被拦截
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://0.0.0.0:3000"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"],
    allow_headers=["*"],
    expose_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "二创短视频分发系统 API",
        "version": "1.0.0",
        "status": "running"
    }

@app.options("/{full_path:path}")
async def options_handler():
    return {"message": "OK"}

@app.get("/health")
async def health_check():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        conn.close()
        
        return {
            "status": "healthy",
            "database": "connected",
            "users": user_count
        }
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}

@app.get("/users")
async def get_all_users():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT id, username, email, role, subscription_type FROM users")
        users = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return {"users": users}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/auth/login")
async def login(user_data: UserLogin):
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute(
        "SELECT * FROM users WHERE username = ? AND password_hash = ?",
        (user_data.username, hash_password(user_data.password))
    )
    user = cursor.fetchone()
    
    if not user:
        conn.close()
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    cursor.execute(
        "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
        (user["id"],)
    )
    conn.commit()
    conn.close()
    
    return {
        "success": True,
        "user": {
            "id": user["id"],
            "username": user["username"],
            "email": user["email"],
            "role": user["role"]
        }
    }

@app.post("/workflow/process")
async def process_video(
    step: int,
    input_type: str,
    input_data: str,
    ai_model: str = "default",
    user_id: int = 1
):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        output_data = {
            "step": step,
            "processed_at": datetime.now().isoformat(),
            "ai_model": ai_model,
            "result": f"Step {step} completed",
            "success": True
        }
        
        cursor.execute(
            """INSERT INTO video_processing 
               (user_id, step, input_type, input_data, output_data, ai_model, status)
               VALUES (?, ?, ?, ?, ?, ?, ?)""",
            (user_id, step, input_type, input_data, str(output_data), ai_model, "completed")
        )
        
        processing_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return {
            "success": True,
            "processing_id": processing_id,
            "output": output_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats")
async def get_stats():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM projects")
        total_projects = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM video_processing")
        total_processing = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            "total_users": total_users,
            "total_projects": total_projects,
            "total_processing": total_processing
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动API服务器...")
    uvicorn.run("api_server:app", host="0.0.0.0", port=8000, reload=False)
