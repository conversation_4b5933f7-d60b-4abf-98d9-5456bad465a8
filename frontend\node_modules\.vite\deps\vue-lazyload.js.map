{"version": 3, "sources": ["../../vue-lazyload/vue-lazyload.esm.js"], "sourcesContent": ["/*!\n * Vue-Lazyload.js v3.0.0\n * (c) 2023 Awe <<EMAIL>>\n * Released under the MIT License.\n */\n\nimport { nextTick, reactive, defineComponent, ref, computed, onMounted, onUnmounted, createVNode, watch } from 'vue';\n\nfunction createCommonjsModule(fn, module) {\n\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n}\n\nvar assignSymbols$1 = createCommonjsModule(function (module) {\n\n  const toString = Object.prototype.toString;\n  const isEnumerable = Object.prototype.propertyIsEnumerable;\n  const getSymbols = Object.getOwnPropertySymbols;\n\n  module.exports = (target, ...args) => {\n    if (!isObject(target)) {\n      throw new TypeError('expected the first argument to be an object');\n    }\n\n    if (args.length === 0 || typeof Symbol !== 'function' || typeof getSymbols !== 'function') {\n      return target;\n    }\n\n    for (let arg of args) {\n      let names = getSymbols(arg);\n\n      for (let key of names) {\n        if (isEnumerable.call(arg, key)) {\n          target[key] = arg[key];\n        }\n      }\n    }\n    return target;\n  };\n\n  function isObject(val) {\n    return typeof val === 'function' || toString.call(val) === '[object Object]' || Array.isArray(val);\n  }\n});\n\nvar assignSymbols$2 = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\t'default': assignSymbols$1,\n\t__moduleExports: assignSymbols$1\n});\n\nvar assignSymbols = ( assignSymbols$2 && assignSymbols$1 ) || assignSymbols$2;\n\nvar assignDeep = createCommonjsModule(function (module) {\n\n  const toString = Object.prototype.toString;\n\n  const isValidKey = key => {\n    return key !== '__proto__' && key !== 'constructor' && key !== 'prototype';\n  };\n\n  const assign = module.exports = (target, ...args) => {\n    let i = 0;\n    if (isPrimitive(target)) target = args[i++];\n    if (!target) target = {};\n    for (; i < args.length; i++) {\n      if (isObject(args[i])) {\n        for (const key of Object.keys(args[i])) {\n          if (isValidKey(key)) {\n            if (isObject(target[key]) && isObject(args[i][key])) {\n              assign(target[key], args[i][key]);\n            } else {\n              target[key] = args[i][key];\n            }\n          }\n        }\n        assignSymbols(target, args[i]);\n      }\n    }\n    return target;\n  };\n\n  function isObject(val) {\n    return typeof val === 'function' || toString.call(val) === '[object Object]';\n  }\n\n  function isPrimitive(val) {\n    return typeof val === 'object' ? val === null : typeof val !== 'function';\n  }\n});\n\nconst inBrowser = typeof window !== 'undefined' && window !== null;\nconst hasIntersectionObserver = checkIntersectionObserver();\nfunction checkIntersectionObserver() {\n    if (inBrowser && 'IntersectionObserver' in window && 'IntersectionObserverEntry' in window && 'intersectionRatio' in window.IntersectionObserverEntry.prototype) {\n        // Minimal polyfill for Edge 15's lack of `isIntersecting`\n        // See: https://github.com/w3c/IntersectionObserver/issues/211\n        if (!('isIntersecting' in window.IntersectionObserverEntry.prototype)) {\n            Object.defineProperty(window.IntersectionObserverEntry.prototype, 'isIntersecting', {\n                get: function () {\n                    return this.intersectionRatio > 0;\n                }\n            });\n        }\n        return true;\n    }\n    return false;\n}\nconst modeType = {\n    event: 'event',\n    observer: 'observer'\n};\nfunction remove(arr, item) {\n    if (!arr.length) return;\n    const index = arr.indexOf(item);\n    if (index > -1) return arr.splice(index, 1);\n}\nfunction getBestSelectionFromSrcset(el, scale) {\n    if (el.tagName !== 'IMG' || !el.getAttribute('data-srcset')) return '';\n    let options = el.getAttribute('data-srcset').trim().split(',');\n    const result = [];\n    const container = el.parentNode;\n    const containerWidth = container.offsetWidth * scale;\n    let spaceIndex;\n    let tmpSrc;\n    let tmpWidth;\n    options.forEach(item => {\n        item = item.trim();\n        spaceIndex = item.lastIndexOf(' ');\n        if (spaceIndex === -1) {\n            tmpSrc = item;\n            tmpWidth = 99999;\n        } else {\n            tmpSrc = item.substr(0, spaceIndex);\n            tmpWidth = parseInt(item.substr(spaceIndex + 1, item.length - spaceIndex - 2), 10);\n        }\n        result.push([tmpWidth, tmpSrc]);\n    });\n    result.sort((a, b) => {\n        if (a[0] < b[0]) {\n            return 1;\n        }\n        if (a[0] > b[0]) {\n            return -1;\n        }\n        if (a[0] === b[0]) {\n            if (b[1].indexOf('.webp', b[1].length - 5) !== -1) {\n                return 1;\n            }\n            if (a[1].indexOf('.webp', a[1].length - 5) !== -1) {\n                return -1;\n            }\n        }\n        return 0;\n    });\n    let bestSelectedSrc = '';\n    let tmpOption;\n    for (let i = 0; i < result.length; i++) {\n        tmpOption = result[i];\n        bestSelectedSrc = tmpOption[1];\n        const next = result[i + 1];\n        if (next && next[0] < containerWidth) {\n            bestSelectedSrc = tmpOption[1];\n            break;\n        } else if (!next) {\n            bestSelectedSrc = tmpOption[1];\n            break;\n        }\n    }\n    return bestSelectedSrc;\n}\nconst getDPR = (scale = 1) => inBrowser ? window.devicePixelRatio || scale : scale;\n// https://developers.google.com/speed/webp/faq#how_can_i_detect_browser_support_using_javascript\nfunction supportWebp() {\n    if (!inBrowser) return false;\n    let support = true;\n    function checkWebpFeature(feature, callback) {\n        const kTestImages = {\n            lossy: 'UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA',\n            lossless: 'UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==',\n            alpha: 'UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==',\n            animation: 'UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA'\n        };\n        const img = new Image();\n        img.onload = function () {\n            const result = img.width > 0 && img.height > 0;\n            callback(result);\n        };\n        img.onerror = function () {\n            // eslint-disable-next-line node/no-callback-literal\n            callback(false);\n        };\n        img.src = 'data:image/webp;base64,' + kTestImages[feature];\n    }\n    checkWebpFeature('lossy', isSupported => {\n        support = isSupported;\n    });\n    checkWebpFeature('lossless', isSupported => {\n        support = isSupported;\n    });\n    checkWebpFeature('alpha', isSupported => {\n        support = isSupported;\n    });\n    checkWebpFeature('animation', isSupported => {\n        support = isSupported;\n    });\n    return support;\n}\nfunction throttle(action, delay) {\n    let timeout = null;\n    let lastRun = 0;\n    return function () {\n        if (timeout) {\n            return;\n        }\n        const elapsed = Date.now() - lastRun;\n        // @ts-ignore\n        const context = this;\n        const args = arguments;\n        const runCallback = function () {\n            lastRun = Date.now();\n            timeout = false;\n            action.apply(context, args);\n        };\n        if (elapsed >= delay) {\n            runCallback();\n        } else {\n            timeout = setTimeout(runCallback, delay);\n        }\n    };\n}\nfunction testSupportsPassive() {\n    if (!inBrowser) return false;\n    let support = false;\n    try {\n        const opts = Object.defineProperty({}, 'passive', {\n            get: function () {\n                support = true;\n            }\n        });\n        window.addEventListener('test', noop, opts);\n    } catch (e) {}\n    return support;\n}\nconst supportsPassive = testSupportsPassive();\nconst _ = {\n    on(el, type, func, capture = false) {\n        if (supportsPassive) {\n            el.addEventListener(type, func, {\n                capture: capture,\n                passive: true\n            });\n        } else {\n            el.addEventListener(type, func, capture);\n        }\n    },\n    off(el, type, func, capture = false) {\n        el.removeEventListener(type, func, capture);\n    }\n};\nconst loadImageAsync = (item, resolve, reject) => {\n    let image = new Image();\n    if (!item || !item.src) {\n        const err = new Error('image src is required');\n        return reject(err);\n    }\n    if (item.cors) {\n        image.crossOrigin = item.cors;\n    }\n    image.src = item.src;\n    image.onload = function () {\n        resolve({\n            naturalHeight: image.naturalHeight,\n            naturalWidth: image.naturalWidth,\n            src: image.src\n        });\n        image = null;\n    };\n    image.onerror = function (e) {\n        reject(e);\n    };\n};\n// keyof CSSStyleDeclaration\nconst style = (el, prop) => {\n    return typeof getComputedStyle !== 'undefined' ? getComputedStyle(el, null).getPropertyValue(prop) : el.style[prop];\n};\nconst overflow = el => {\n    return style(el, 'overflow') + style(el, 'overflowY') + style(el, 'overflowX');\n};\nconst scrollParent = el => {\n    if (!inBrowser) return;\n    if (!(el instanceof Element)) {\n        return window;\n    }\n    let parent = el;\n    while (parent) {\n        if (parent === document.body || parent === document.documentElement) {\n            break;\n        }\n        if (!parent.parentNode) {\n            break;\n        }\n        if (/(scroll|auto)/.test(overflow(parent))) {\n            return parent;\n        }\n        parent = parent.parentNode;\n    }\n    return window;\n};\nfunction isObject(obj) {\n    return obj !== null && typeof obj === 'object';\n}\nfunction noop() {}\nclass ImageCache {\n    constructor(max) {\n        this.max = max || 100;\n        this._caches = [];\n    }\n    has(key) {\n        return this._caches.indexOf(key) > -1;\n    }\n    add(key) {\n        if (this.has(key)) return;\n        this._caches.push(key);\n        if (this._caches.length > this.max) {\n            this.free();\n        }\n    }\n    free() {\n        this._caches.shift();\n    }\n}\n\n// el: {\n//     state,\n//     src,\n//     error,\n//     loading\n// }\nclass ReactiveListener {\n    constructor(el, src, error, loading, bindType, $parent, options, cors, elRenderer, imageCache) {\n        this.el = el;\n        this.src = src;\n        this.error = error;\n        this.loading = loading;\n        this.bindType = bindType;\n        this.attempt = 0;\n        this.cors = cors;\n        this.naturalHeight = 0;\n        this.naturalWidth = 0;\n        this.options = options;\n        this.rect = {};\n        this.$parent = $parent;\n        this.elRenderer = elRenderer;\n        this._imageCache = imageCache;\n        this.performanceData = {\n            init: Date.now(),\n            loadStart: 0,\n            loadEnd: 0\n        };\n        this.filter();\n        this.initState();\n        this.render('loading', false);\n    }\n    /*\r\n     * init listener state\r\n     * @return\r\n     */\n    initState() {\n        if ('dataset' in this.el) {\n            this.el.dataset.src = this.src;\n        } else {\n            this.el.setAttribute('data-src', this.src);\n        }\n        this.state = {\n            loading: false,\n            error: false,\n            loaded: false,\n            rendered: false\n        };\n    }\n    /*\r\n     * record performance\r\n     * @return\r\n     */\n    record(event) {\n        this.performanceData[event] = Date.now();\n    }\n    /*\r\n     * update image listener data\r\n     * @param  {String} image uri\r\n     * @param  {String} loading image uri\r\n     * @param  {String} error image uri\r\n     * @return\r\n     */\n    update(option) {\n        const oldSrc = this.src;\n        this.src = option.src;\n        this.loading = option.loading;\n        this.error = option.error;\n        this.filter();\n        if (oldSrc !== this.src) {\n            this.attempt = 0;\n            this.initState();\n        }\n    }\n    /*\r\n     * get el node rect\r\n     * @return\r\n     */\n    getRect() {\n        this.rect = this.el.getBoundingClientRect();\n    }\n    /*\r\n     * check el is in view\r\n     * @return {Boolean} el is in view\r\n     */\n    checkInView() {\n        this.getRect();\n        return this.rect.top < window.innerHeight * this.options.preLoad && this.rect.bottom > this.options.preLoadTop && this.rect.left < window.innerWidth * this.options.preLoad && this.rect.right > 0;\n    }\n    /*\r\n     * listener filter\r\n     */\n    filter() {\n        for (const key in this.options.filter) {\n            this.options.filter[key](this, this.options);\n        }\n    }\n    /*\r\n     * render loading first\r\n     * @params cb:Function\r\n     * @return\r\n     */\n    renderLoading(cb) {\n        this.state.loading = true;\n        loadImageAsync({\n            src: this.loading,\n            cors: this.cors\n        }, () => {\n            this.render('loading', false);\n            this.state.loading = false;\n            cb();\n        }, () => {\n            // handler `loading image` load failed\n            cb();\n            this.state.loading = false;\n            if (!this.options.silent) console.warn(`VueLazyload log: load failed with loading image(${this.loading})`);\n        });\n    }\n    /*\r\n     * try load image and  render it\r\n     * @return\r\n     */\n    load(onFinish = noop) {\n        if (this.attempt > this.options.attempt - 1 && this.state.error) {\n            if (!this.options.silent) console.log(`VueLazyload log: ${this.src} tried too more than ${this.options.attempt} times`);\n            onFinish();\n            return;\n        }\n        if (this.state.rendered && this.state.loaded) return;\n        if (this._imageCache.has(this.src)) {\n            this.state.loaded = true;\n            this.render('loaded', true);\n            this.state.rendered = true;\n            return onFinish();\n        }\n        this.renderLoading(() => {\n            this.attempt++;\n            this.options.adapter.beforeLoad && this.options.adapter.beforeLoad(this, this.options);\n            this.record('loadStart');\n            loadImageAsync({\n                src: this.src,\n                cors: this.cors\n            }, data => {\n                this.naturalHeight = data.naturalHeight;\n                this.naturalWidth = data.naturalWidth;\n                this.state.loaded = true;\n                this.state.error = false;\n                this.record('loadEnd');\n                this.render('loaded', false);\n                this.state.rendered = true;\n                this._imageCache.add(this.src);\n                onFinish();\n            }, err => {\n                !this.options.silent && console.error(err);\n                this.state.error = true;\n                this.state.loaded = false;\n                this.render('error', false);\n            });\n        });\n    }\n    /*\r\n     * render image\r\n     * @param  {String} state to render // ['loading', 'src', 'error']\r\n     * @param  {String} is form cache\r\n     * @return\r\n     */\n    render(state, cache) {\n        this.elRenderer(this, state, cache);\n    }\n    /*\r\n     * output performance data\r\n     * @return {Object} performance data\r\n     */\n    performance() {\n        let state = 'loading';\n        let time = 0;\n        if (this.state.loaded) {\n            state = 'loaded';\n            time = (this.performanceData.loadEnd - this.performanceData.loadStart) / 1000;\n        }\n        if (this.state.error) state = 'error';\n        return {\n            src: this.src,\n            state,\n            time\n        };\n    }\n    /*\r\n     * $destroy\r\n     * @return\r\n     */\n    $destroy() {\n        this.el = null;\n        this.src = '';\n        this.error = null;\n        this.loading = '';\n        this.bindType = null;\n        this.attempt = 0;\n    }\n}\n\nconst DEFAULT_URL = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';\nconst DEFAULT_EVENTS = ['scroll', 'wheel', 'mousewheel', 'resize', 'animationend', 'transitionend', 'touchmove'];\nconst DEFAULT_OBSERVER_OPTIONS = {\n    rootMargin: '0px',\n    threshold: 0\n};\nclass Lazy {\n    constructor({ preLoad, error, throttleWait, preLoadTop, dispatchEvent, loading, attempt, silent = true, scale, listenEvents, filter, adapter, observer, observerOptions }) {\n        this.version = '\"3.0.0\"';\n        this.lazyContainerMananger = null;\n        this.mode = modeType.event;\n        this.ListenerQueue = [];\n        this.TargetIndex = 0;\n        this.TargetQueue = [];\n        this.options = {\n            silent: silent,\n            dispatchEvent: !!dispatchEvent,\n            throttleWait: throttleWait || 200,\n            preLoad: preLoad || 1.3,\n            preLoadTop: preLoadTop || 0,\n            error: error || DEFAULT_URL,\n            loading: loading || DEFAULT_URL,\n            attempt: attempt || 3,\n            scale: scale || getDPR(scale),\n            listenEvents: listenEvents || DEFAULT_EVENTS,\n            supportWebp: supportWebp(),\n            filter: filter || {},\n            adapter: adapter || {},\n            observer: !!observer,\n            observerOptions: observerOptions || DEFAULT_OBSERVER_OPTIONS\n        };\n        this._initEvent();\n        this._imageCache = new ImageCache(200);\n        this.lazyLoadHandler = throttle(this._lazyLoadHandler.bind(this), this.options.throttleWait);\n        this.setMode(this.options.observer ? modeType.observer : modeType.event);\n    }\n    /**\r\n     * output listener's load performance\r\n     * @return {Array}\r\n     */\n    performance() {\n        const list = [];\n        this.ListenerQueue.map(item => list.push(item.performance()));\n        return list;\n    }\n    /*\r\n     * add lazy component to queue\r\n     * @param  {Vue} vm lazy component instance\r\n     * @return\r\n     */\n    addLazyBox(vm) {\n        this.ListenerQueue.push(vm);\n        if (inBrowser) {\n            this._addListenerTarget(window);\n            this._observer && this._observer.observe(vm.el);\n            if (vm.$el && vm.$el.parentNode) {\n                this._addListenerTarget(vm.$el.parentNode);\n            }\n        }\n    }\n    /*\r\n     * add image listener to queue\r\n     * @param  {DOM} el\r\n     * @param  {object} binding vue directive binding\r\n     * @param  {vnode} vnode vue directive vnode\r\n     * @return\r\n     */\n    add(el, binding, vnode) {\n        if (this.ListenerQueue.some(item => item.el === el)) {\n            this.update(el, binding);\n            return nextTick(this.lazyLoadHandler);\n        }\n        let { src, loading, error, cors } = this._valueFormatter(binding.value);\n        nextTick(() => {\n            src = getBestSelectionFromSrcset(el, this.options.scale) || src;\n            this._observer && this._observer.observe(el);\n            const container = Object.keys(binding.modifiers)[0];\n            let $parent;\n            if (container) {\n                $parent = binding.instance.$refs[container];\n                // if there is container passed in, try ref first, then fallback to getElementById to support the original usage\n                $parent = $parent ? $parent.el || $parent : document.getElementById(container);\n            }\n            if (!$parent) {\n                $parent = scrollParent(el);\n            }\n            const newListener = new ReactiveListener(el, src, error, loading, binding.arg, $parent, this.options, cors, this._elRenderer.bind(this), this._imageCache);\n            this.ListenerQueue.push(newListener);\n            if (inBrowser) {\n                this._addListenerTarget(window);\n                this._addListenerTarget($parent);\n            }\n            nextTick(this.lazyLoadHandler);\n        });\n    }\n    /**\r\n    * update image src\r\n    * @param  {DOM} el\r\n    * @param  {object} vue directive binding\r\n    * @return\r\n    */\n    update(el, binding, vnode) {\n        let { src, loading, error } = this._valueFormatter(binding.value);\n        src = getBestSelectionFromSrcset(el, this.options.scale) || src;\n        const exist = this.ListenerQueue.find(item => item.el === el);\n        if (!exist) {\n            // https://github.com/hilongjw/vue-lazyload/issues/374\n            if (el.getAttribute('lazy') !== 'loaded' || el.dataset.src !== src) {\n                this.add(el, binding, vnode);\n            }\n        } else {\n            exist.update({\n                src,\n                loading,\n                error\n            });\n        }\n        if (this._observer) {\n            this._observer.unobserve(el);\n            this._observer.observe(el);\n        }\n        nextTick(this.lazyLoadHandler);\n    }\n    /**\r\n    * remove listener form list\r\n    * @param  {DOM} el\r\n    * @return\r\n    */\n    remove(el) {\n        if (!el) return;\n        this._observer && this._observer.unobserve(el);\n        const existItem = this.ListenerQueue.find(item => item.el === el);\n        if (existItem) {\n            this._removeListenerTarget(existItem.$parent);\n            this._removeListenerTarget(window);\n            remove(this.ListenerQueue, existItem);\n            existItem.$destroy && existItem.$destroy();\n        }\n    }\n    /*\r\n     * remove lazy components form list\r\n     * @param  {Vue} vm Vue instance\r\n     * @return\r\n     */\n    removeComponent(vm) {\n        if (!vm) return;\n        remove(this.ListenerQueue, vm);\n        this._observer && this._observer.unobserve(vm.el);\n        if (vm.$parent && vm.$el.parentNode) {\n            this._removeListenerTarget(vm.$el.parentNode);\n        }\n        this._removeListenerTarget(window);\n    }\n    setMode(mode) {\n        if (!hasIntersectionObserver && mode === modeType.observer) {\n            mode = modeType.event;\n        }\n        this.mode = mode; // event or observer\n        if (mode === modeType.event) {\n            if (this._observer) {\n                this.ListenerQueue.forEach(listener => {\n                    this._observer.unobserve(listener.el);\n                });\n                this._observer = null;\n            }\n            this.TargetQueue.forEach(target => {\n                this._initListen(target.el, true);\n            });\n        } else {\n            this.TargetQueue.forEach(target => {\n                this._initListen(target.el, false);\n            });\n            this._initIntersectionObserver();\n        }\n    }\n    /*\r\n    *** Private functions ***\r\n    */\n    /*\r\n     * add listener target\r\n     * @param  {DOM} el listener target\r\n     * @return\r\n     */\n    _addListenerTarget(el) {\n        if (!el) return;\n        let target = this.TargetQueue.find(target => target.el === el);\n        if (!target) {\n            target = {\n                el: el,\n                id: ++this.TargetIndex,\n                childrenCount: 1,\n                listened: true\n            };\n            this.mode === modeType.event && this._initListen(target.el, true);\n            this.TargetQueue.push(target);\n        } else {\n            target.childrenCount++;\n        }\n        return this.TargetIndex;\n    }\n    /*\r\n     * remove listener target or reduce target childrenCount\r\n     * @param  {DOM} el or window\r\n     * @return\r\n     */\n    _removeListenerTarget(el) {\n        this.TargetQueue.forEach((target, index) => {\n            if (target.el === el) {\n                target.childrenCount--;\n                if (!target.childrenCount) {\n                    this._initListen(target.el, false);\n                    this.TargetQueue.splice(index, 1);\n                    target = null;\n                }\n            }\n        });\n    }\n    /*\r\n     * add or remove eventlistener\r\n     * @param  {DOM} el DOM or Window\r\n     * @param  {boolean} start flag\r\n     * @return\r\n     */\n    _initListen(el, start) {\n        this.options.listenEvents.forEach(evt => _[start ? 'on' : 'off'](el, evt, this.lazyLoadHandler));\n    }\n    _initEvent() {\n        this.Event = {\n            listeners: {\n                loading: [],\n                loaded: [],\n                error: []\n            }\n        };\n        this.$on = (event, func) => {\n            if (!this.Event.listeners[event]) this.Event.listeners[event] = [];\n            this.Event.listeners[event].push(func);\n        };\n        this.$once = (event, func) => {\n            const vm = this;\n            function on() {\n                vm.$off(event, on);\n                func.apply(vm, arguments);\n            }\n            this.$on(event, on);\n        };\n        this.$off = (event, func) => {\n            if (!func) {\n                if (!this.Event.listeners[event]) return;\n                this.Event.listeners[event].length = 0;\n                return;\n            }\n            remove(this.Event.listeners[event], func);\n        };\n        this.$emit = (event, context, inCache) => {\n            if (!this.Event.listeners[event]) return;\n            this.Event.listeners[event].forEach(func => func(context, inCache));\n        };\n    }\n    /**\r\n     * find nodes which in viewport and trigger load\r\n     * @return\r\n     */\n    _lazyLoadHandler() {\n        const freeList = [];\n        this.ListenerQueue.forEach((listener, index) => {\n            if (!listener.el || !listener.el.parentNode || listener.state.loaded) {\n                freeList.push(listener);\n            }\n            const catIn = listener.checkInView();\n            if (!catIn) return;\n            if (!listener.state.loaded) listener.load();\n        });\n        freeList.forEach(item => {\n            remove(this.ListenerQueue, item);\n            item.$destroy && item.$destroy();\n        });\n    }\n    /**\r\n    * init IntersectionObserver\r\n    * set mode to observer\r\n    * @return\r\n    */\n    _initIntersectionObserver() {\n        if (!hasIntersectionObserver) return;\n        this._observer = new IntersectionObserver(this._observerHandler.bind(this), this.options.observerOptions);\n        if (this.ListenerQueue.length) {\n            this.ListenerQueue.forEach(listener => {\n                this._observer.observe(listener.el);\n            });\n        }\n    }\n    /**\r\n    * init IntersectionObserver\r\n    * @param {Array<IntersectionObserverEntry>} entries\r\n    * @return\r\n    */\n    _observerHandler(entries) {\n        entries.forEach(entry => {\n            if (entry.isIntersecting) {\n                this.ListenerQueue.forEach(listener => {\n                    if (listener.el === entry.target) {\n                        if (listener.state.loaded) return this._observer.unobserve(listener.el);\n                        listener.load();\n                    }\n                });\n            }\n        });\n    }\n    /**\r\n    * set element attribute with image'url and state\r\n    * @param  {ReactiveListener} lazyload listener object\r\n    * @param  {TeventType} state will be rendered\r\n    * @param  {bool} inCache  is rendered from cache\r\n    * @return\r\n    */\n    _elRenderer(listener, state, cache) {\n        if (!listener.el) return;\n        const { el, bindType } = listener;\n        let src;\n        switch (state) {\n            case 'loading':\n                src = listener.loading;\n                break;\n            case 'error':\n                src = listener.error;\n                break;\n            default:\n                src = listener.src;\n                break;\n        }\n        if (bindType) {\n            // @ts-ignore\n            el.style[bindType] = 'url(\"' + src + '\")';\n        } else if (el.getAttribute('src') !== src) {\n            el.setAttribute('src', src);\n        }\n        el.setAttribute('lazy', state);\n        this.$emit(state, listener, cache);\n        this.options.adapter[state] && this.options.adapter[state](listener, this.options);\n        if (this.options.dispatchEvent) {\n            const event = new CustomEvent(state, {\n                detail: listener\n            });\n            el.dispatchEvent(event);\n        }\n    }\n    _valueFormatter(value) {\n        if (isObject(value)) {\n            if (!value.src && !this.options.silent) console.error('Vue Lazyload warning: miss src with ' + value);\n            return {\n                src: value.src,\n                loading: value.loading || this.options.loading,\n                error: value.error || this.options.error,\n                cors: this.options.cors\n            };\n        }\n        return {\n            src: value,\n            loading: this.options.loading,\n            error: this.options.error,\n            cors: this.options.cors\n        };\n    }\n}\n\nconst useCheckInView = (el, preLoad) => {\n    let rect = reactive({});\n    const getRect = () => {\n        rect = el.value.getBoundingClientRect();\n    };\n    const checkInView = () => {\n        getRect();\n        return inBrowser && rect.top < window.innerHeight * preLoad && rect.bottom > 0 && rect.left < window.innerWidth * preLoad && rect.right > 0;\n    };\n    return {\n        rect,\n        checkInView\n    };\n};\n\nvar LazyComponent = (lazy => {\n    return defineComponent({\n        props: {\n            tag: {\n                type: String,\n                default: 'div'\n            }\n        },\n        emits: ['show'],\n        setup(props, { emit, slots }) {\n            const el = ref();\n            const state = reactive({\n                loaded: false,\n                error: false,\n                attempt: 0\n            });\n            const show = ref(false);\n            const { rect, checkInView } = useCheckInView(el, lazy.options.preLoad);\n            const load = () => {\n                show.value = true;\n                state.loaded = true;\n                emit('show', show.value);\n            };\n            const vm = computed(() => {\n                return {\n                    el: el.value,\n                    rect,\n                    checkInView,\n                    load,\n                    state\n                };\n            });\n            onMounted(() => {\n                lazy.addLazyBox(vm.value);\n                lazy.lazyLoadHandler();\n            });\n            onUnmounted(() => {\n                lazy.removeComponent(vm.value);\n            });\n            return () => {\n                var _a;\n                return createVNode(props.tag, {\n                    ref: el\n                }, [show.value && ((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots))]);\n            };\n        }\n    });\n});\n\nclass LazyContainerMananger {\n    constructor(lazy) {\n        this.lazy = lazy;\n        lazy.lazyContainerMananger = this;\n        this._queue = [];\n    }\n    bind(el, binding, vnode) {\n        const container = new LazyContainer(el, binding, vnode, this.lazy);\n        this._queue.push(container);\n    }\n    update(el, binding, vnode) {\n        const container = this._queue.find(item => item.el === el);\n        if (!container) return;\n        container.update(el, binding);\n    }\n    unbind(el, binding, vnode) {\n        const container = this._queue.find(item => item.el === el);\n        if (!container) return;\n        container.clear();\n        remove(this._queue, container);\n    }\n}\nconst defaultOptions = {\n    selector: 'img',\n    error: '',\n    loading: ''\n};\nclass LazyContainer {\n    constructor(el, binding, vnode, lazy) {\n        this.el = el;\n        this.vnode = vnode;\n        this.binding = binding;\n        this.options = {};\n        this.lazy = lazy;\n        this._queue = [];\n        this.update(el, binding);\n    }\n    update(el, binding) {\n        this.el = el;\n        this.options = assignDeep({}, defaultOptions, binding.value);\n        const imgs = this.getImgs();\n        imgs.forEach(el => {\n            this.lazy.add(el, assignDeep({}, this.binding, {\n                value: {\n                    src: el.getAttribute('data-src') || el.dataset.src,\n                    error: el.getAttribute('data-error') || el.dataset.error || this.options.error,\n                    loading: el.getAttribute('data-loading') || el.dataset.loading || this.options.loading\n                }\n            }), this.vnode);\n        });\n    }\n    getImgs() {\n        return Array.from(this.el.querySelectorAll(this.options.selector));\n    }\n    clear() {\n        const imgs = this.getImgs();\n        imgs.forEach(el => this.lazy.remove(el));\n        this.vnode = null;\n        this.binding = null;\n        this.lazy = null;\n    }\n}\n\nvar LazyImage = (lazy => {\n    return defineComponent({\n        setup(props, { slots }) {\n            const el = ref();\n            const options = reactive({\n                src: '',\n                error: '',\n                loading: '',\n                attempt: lazy.options.attempt\n            });\n            const state = reactive({\n                loaded: false,\n                error: false,\n                attempt: 0\n            });\n            const { rect, checkInView } = useCheckInView(el, lazy.options.preLoad);\n            const renderSrc = ref('');\n            const load = (onFinish = noop) => {\n                if (state.attempt > options.attempt - 1 && state.error) {\n                    if (!lazy.options.silent) console.log(`VueLazyload log: ${options.src} tried too more than ${options.attempt} times`);\n                    return onFinish();\n                }\n                const src = options.src;\n                loadImageAsync({ src }, ({ src }) => {\n                    renderSrc.value = src;\n                    state.loaded = true;\n                }, () => {\n                    state.attempt++;\n                    renderSrc.value = options.error;\n                    state.error = true;\n                });\n            };\n            const vm = computed(() => {\n                return {\n                    el: el.value,\n                    rect,\n                    checkInView,\n                    load,\n                    state\n                };\n            });\n            onMounted(() => {\n                lazy.addLazyBox(vm.value);\n                lazy.lazyLoadHandler();\n            });\n            onUnmounted(() => {\n                lazy.removeComponent(vm.value);\n            });\n            const init = () => {\n                const { src, loading, error } = lazy._valueFormatter(props.src);\n                state.loaded = false;\n                options.src = src;\n                options.error = error;\n                options.loading = loading;\n                renderSrc.value = options.loading;\n            };\n            watch(() => props.src, () => {\n                init();\n                lazy.addLazyBox(vm.value);\n                lazy.lazyLoadHandler();\n            }, {\n                immediate: true\n            });\n            return () => {\n                var _a;\n                return createVNode(props.tag || 'img', {\n                    src: renderSrc.value,\n                    ref: el\n                }, [(_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]);\n            };\n        }\n    });\n});\n\nvar index = {\n    /*\r\n    * install function\r\n    * @param  {Vue} Vue\r\n    * @param  {object} options lazyload options\r\n    */\n    install(Vue, options = {}) {\n        const lazy = new Lazy(options);\n        const lazyContainer = new LazyContainerMananger(lazy);\n        const vueVersion = Number(Vue.version.split('.')[0]);\n        if (vueVersion < 3) return new Error('Vue version at least 3.0');\n        Vue.config.globalProperties.$Lazyload = lazy;\n        Vue.provide('Lazyload', lazy);\n        if (options.lazyComponent) {\n            Vue.component('lazy-component', LazyComponent(lazy));\n        }\n        if (options.lazyImage) {\n            Vue.component('lazy-image', LazyImage(lazy));\n        }\n        Vue.directive('lazy', {\n            beforeMount: lazy.add.bind(lazy),\n            beforeUpdate: lazy.update.bind(lazy),\n            updated: lazy.lazyLoadHandler.bind(lazy),\n            unmounted: lazy.remove.bind(lazy)\n        });\n        Vue.directive('lazy-container', {\n            beforeMount: lazyContainer.bind.bind(lazyContainer),\n            updated: lazyContainer.update.bind(lazyContainer),\n            unmounted: lazyContainer.unbind.bind(lazyContainer)\n        });\n    }\n};\n\nexport { index as default };\n"], "mappings": ";;;;;;;;;;;;;;AAQA,SAAS,qBAAqB,IAAI,QAAQ;AACzC,SAAO,SAAS,EAAE,SAAS,CAAC,EAAE,GAAG,GAAG,QAAQ,OAAO,OAAO,GAAG,OAAO;AACrE;AAEA,IAAI,kBAAkB,qBAAqB,SAAU,QAAQ;AAE3D,QAAM,WAAW,OAAO,UAAU;AAClC,QAAM,eAAe,OAAO,UAAU;AACtC,QAAM,aAAa,OAAO;AAE1B,SAAO,UAAU,CAAC,WAAW,SAAS;AACpC,QAAI,CAACA,UAAS,MAAM,GAAG;AACrB,YAAM,IAAI,UAAU,6CAA6C;AAAA,IACnE;AAEA,QAAI,KAAK,WAAW,KAAK,OAAO,WAAW,cAAc,OAAO,eAAe,YAAY;AACzF,aAAO;AAAA,IACT;AAEA,aAAS,OAAO,MAAM;AACpB,UAAI,QAAQ,WAAW,GAAG;AAE1B,eAAS,OAAO,OAAO;AACrB,YAAI,aAAa,KAAK,KAAK,GAAG,GAAG;AAC/B,iBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,WAASA,UAAS,KAAK;AACrB,WAAO,OAAO,QAAQ,cAAc,SAAS,KAAK,GAAG,MAAM,qBAAqB,MAAM,QAAQ,GAAG;AAAA,EACnG;AACF,CAAC;AAED,IAAI,kBAA+B,OAAO,OAAO;AAAA,EAChD,WAAW;AAAA,EACX,WAAW;AAAA,EACX,iBAAiB;AAClB,CAAC;AAED,IAAI,gBAAkB,mBAAmB,mBAAqB;AAE9D,IAAI,aAAa,qBAAqB,SAAU,QAAQ;AAEtD,QAAM,WAAW,OAAO,UAAU;AAElC,QAAM,aAAa,SAAO;AACxB,WAAO,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ;AAAA,EACjE;AAEA,QAAM,SAAS,OAAO,UAAU,CAAC,WAAW,SAAS;AACnD,QAAI,IAAI;AACR,QAAI,YAAY,MAAM,EAAG,UAAS,KAAK,GAAG;AAC1C,QAAI,CAAC,OAAQ,UAAS,CAAC;AACvB,WAAO,IAAI,KAAK,QAAQ,KAAK;AAC3B,UAAIA,UAAS,KAAK,CAAC,CAAC,GAAG;AACrB,mBAAW,OAAO,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG;AACtC,cAAI,WAAW,GAAG,GAAG;AACnB,gBAAIA,UAAS,OAAO,GAAG,CAAC,KAAKA,UAAS,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG;AACnD,qBAAO,OAAO,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC;AAAA,YAClC,OAAO;AACL,qBAAO,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AACA,sBAAc,QAAQ,KAAK,CAAC,CAAC;AAAA,MAC/B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,WAASA,UAAS,KAAK;AACrB,WAAO,OAAO,QAAQ,cAAc,SAAS,KAAK,GAAG,MAAM;AAAA,EAC7D;AAEA,WAAS,YAAY,KAAK;AACxB,WAAO,OAAO,QAAQ,WAAW,QAAQ,OAAO,OAAO,QAAQ;AAAA,EACjE;AACF,CAAC;AAED,IAAM,YAAY,OAAO,WAAW,eAAe,WAAW;AAC9D,IAAM,0BAA0B,0BAA0B;AAC1D,SAAS,4BAA4B;AACjC,MAAI,aAAa,0BAA0B,UAAU,+BAA+B,UAAU,uBAAuB,OAAO,0BAA0B,WAAW;AAG7J,QAAI,EAAE,oBAAoB,OAAO,0BAA0B,YAAY;AACnE,aAAO,eAAe,OAAO,0BAA0B,WAAW,kBAAkB;AAAA,QAChF,KAAK,WAAY;AACb,iBAAO,KAAK,oBAAoB;AAAA,QACpC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACd;AACA,SAAS,OAAO,KAAK,MAAM;AACvB,MAAI,CAAC,IAAI,OAAQ;AACjB,QAAMC,SAAQ,IAAI,QAAQ,IAAI;AAC9B,MAAIA,SAAQ,GAAI,QAAO,IAAI,OAAOA,QAAO,CAAC;AAC9C;AACA,SAAS,2BAA2B,IAAI,OAAO;AAC3C,MAAI,GAAG,YAAY,SAAS,CAAC,GAAG,aAAa,aAAa,EAAG,QAAO;AACpE,MAAI,UAAU,GAAG,aAAa,aAAa,EAAE,KAAK,EAAE,MAAM,GAAG;AAC7D,QAAM,SAAS,CAAC;AAChB,QAAM,YAAY,GAAG;AACrB,QAAM,iBAAiB,UAAU,cAAc;AAC/C,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,UAAQ,QAAQ,UAAQ;AACpB,WAAO,KAAK,KAAK;AACjB,iBAAa,KAAK,YAAY,GAAG;AACjC,QAAI,eAAe,IAAI;AACnB,eAAS;AACT,iBAAW;AAAA,IACf,OAAO;AACH,eAAS,KAAK,OAAO,GAAG,UAAU;AAClC,iBAAW,SAAS,KAAK,OAAO,aAAa,GAAG,KAAK,SAAS,aAAa,CAAC,GAAG,EAAE;AAAA,IACrF;AACA,WAAO,KAAK,CAAC,UAAU,MAAM,CAAC;AAAA,EAClC,CAAC;AACD,SAAO,KAAK,CAAC,GAAG,MAAM;AAClB,QAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG;AACb,aAAO;AAAA,IACX;AACA,QAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG;AACb,aAAO;AAAA,IACX;AACA,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACf,UAAI,EAAE,CAAC,EAAE,QAAQ,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI;AAC/C,eAAO;AAAA,MACX;AACA,UAAI,EAAE,CAAC,EAAE,QAAQ,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI;AAC/C,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX,CAAC;AACD,MAAI,kBAAkB;AACtB,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,gBAAY,OAAO,CAAC;AACpB,sBAAkB,UAAU,CAAC;AAC7B,UAAM,OAAO,OAAO,IAAI,CAAC;AACzB,QAAI,QAAQ,KAAK,CAAC,IAAI,gBAAgB;AAClC,wBAAkB,UAAU,CAAC;AAC7B;AAAA,IACJ,WAAW,CAAC,MAAM;AACd,wBAAkB,UAAU,CAAC;AAC7B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,SAAS,CAAC,QAAQ,MAAM,YAAY,OAAO,oBAAoB,QAAQ;AAE7E,SAAS,cAAc;AACnB,MAAI,CAAC,UAAW,QAAO;AACvB,MAAI,UAAU;AACd,WAAS,iBAAiB,SAAS,UAAU;AACzC,UAAM,cAAc;AAAA,MAChB,OAAO;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,IACf;AACA,UAAM,MAAM,IAAI,MAAM;AACtB,QAAI,SAAS,WAAY;AACrB,YAAM,SAAS,IAAI,QAAQ,KAAK,IAAI,SAAS;AAC7C,eAAS,MAAM;AAAA,IACnB;AACA,QAAI,UAAU,WAAY;AAEtB,eAAS,KAAK;AAAA,IAClB;AACA,QAAI,MAAM,4BAA4B,YAAY,OAAO;AAAA,EAC7D;AACA,mBAAiB,SAAS,iBAAe;AACrC,cAAU;AAAA,EACd,CAAC;AACD,mBAAiB,YAAY,iBAAe;AACxC,cAAU;AAAA,EACd,CAAC;AACD,mBAAiB,SAAS,iBAAe;AACrC,cAAU;AAAA,EACd,CAAC;AACD,mBAAiB,aAAa,iBAAe;AACzC,cAAU;AAAA,EACd,CAAC;AACD,SAAO;AACX;AACA,SAAS,SAAS,QAAQ,OAAO;AAC7B,MAAI,UAAU;AACd,MAAI,UAAU;AACd,SAAO,WAAY;AACf,QAAI,SAAS;AACT;AAAA,IACJ;AACA,UAAM,UAAU,KAAK,IAAI,IAAI;AAE7B,UAAM,UAAU;AAChB,UAAM,OAAO;AACb,UAAM,cAAc,WAAY;AAC5B,gBAAU,KAAK,IAAI;AACnB,gBAAU;AACV,aAAO,MAAM,SAAS,IAAI;AAAA,IAC9B;AACA,QAAI,WAAW,OAAO;AAClB,kBAAY;AAAA,IAChB,OAAO;AACH,gBAAU,WAAW,aAAa,KAAK;AAAA,IAC3C;AAAA,EACJ;AACJ;AACA,SAAS,sBAAsB;AAC3B,MAAI,CAAC,UAAW,QAAO;AACvB,MAAI,UAAU;AACd,MAAI;AACA,UAAM,OAAO,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,MAC9C,KAAK,WAAY;AACb,kBAAU;AAAA,MACd;AAAA,IACJ,CAAC;AACD,WAAO,iBAAiB,QAAQ,MAAM,IAAI;AAAA,EAC9C,SAAS,GAAG;AAAA,EAAC;AACb,SAAO;AACX;AACA,IAAM,kBAAkB,oBAAoB;AAC5C,IAAM,IAAI;AAAA,EACN,GAAG,IAAI,MAAM,MAAM,UAAU,OAAO;AAChC,QAAI,iBAAiB;AACjB,SAAG,iBAAiB,MAAM,MAAM;AAAA,QAC5B;AAAA,QACA,SAAS;AAAA,MACb,CAAC;AAAA,IACL,OAAO;AACH,SAAG,iBAAiB,MAAM,MAAM,OAAO;AAAA,IAC3C;AAAA,EACJ;AAAA,EACA,IAAI,IAAI,MAAM,MAAM,UAAU,OAAO;AACjC,OAAG,oBAAoB,MAAM,MAAM,OAAO;AAAA,EAC9C;AACJ;AACA,IAAM,iBAAiB,CAAC,MAAM,SAAS,WAAW;AAC9C,MAAI,QAAQ,IAAI,MAAM;AACtB,MAAI,CAAC,QAAQ,CAAC,KAAK,KAAK;AACpB,UAAM,MAAM,IAAI,MAAM,uBAAuB;AAC7C,WAAO,OAAO,GAAG;AAAA,EACrB;AACA,MAAI,KAAK,MAAM;AACX,UAAM,cAAc,KAAK;AAAA,EAC7B;AACA,QAAM,MAAM,KAAK;AACjB,QAAM,SAAS,WAAY;AACvB,YAAQ;AAAA,MACJ,eAAe,MAAM;AAAA,MACrB,cAAc,MAAM;AAAA,MACpB,KAAK,MAAM;AAAA,IACf,CAAC;AACD,YAAQ;AAAA,EACZ;AACA,QAAM,UAAU,SAAU,GAAG;AACzB,WAAO,CAAC;AAAA,EACZ;AACJ;AAEA,IAAM,QAAQ,CAAC,IAAI,SAAS;AACxB,SAAO,OAAO,qBAAqB,cAAc,iBAAiB,IAAI,IAAI,EAAE,iBAAiB,IAAI,IAAI,GAAG,MAAM,IAAI;AACtH;AACA,IAAM,WAAW,QAAM;AACnB,SAAO,MAAM,IAAI,UAAU,IAAI,MAAM,IAAI,WAAW,IAAI,MAAM,IAAI,WAAW;AACjF;AACA,IAAM,eAAe,QAAM;AACvB,MAAI,CAAC,UAAW;AAChB,MAAI,EAAE,cAAc,UAAU;AAC1B,WAAO;AAAA,EACX;AACA,MAAI,SAAS;AACb,SAAO,QAAQ;AACX,QAAI,WAAW,SAAS,QAAQ,WAAW,SAAS,iBAAiB;AACjE;AAAA,IACJ;AACA,QAAI,CAAC,OAAO,YAAY;AACpB;AAAA,IACJ;AACA,QAAI,gBAAgB,KAAK,SAAS,MAAM,CAAC,GAAG;AACxC,aAAO;AAAA,IACX;AACA,aAAS,OAAO;AAAA,EACpB;AACA,SAAO;AACX;AACA,SAAS,SAAS,KAAK;AACnB,SAAO,QAAQ,QAAQ,OAAO,QAAQ;AAC1C;AACA,SAAS,OAAO;AAAC;AACjB,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,KAAK;AACb,SAAK,MAAM,OAAO;AAClB,SAAK,UAAU,CAAC;AAAA,EACpB;AAAA,EACA,IAAI,KAAK;AACL,WAAO,KAAK,QAAQ,QAAQ,GAAG,IAAI;AAAA,EACvC;AAAA,EACA,IAAI,KAAK;AACL,QAAI,KAAK,IAAI,GAAG,EAAG;AACnB,SAAK,QAAQ,KAAK,GAAG;AACrB,QAAI,KAAK,QAAQ,SAAS,KAAK,KAAK;AAChC,WAAK,KAAK;AAAA,IACd;AAAA,EACJ;AAAA,EACA,OAAO;AACH,SAAK,QAAQ,MAAM;AAAA,EACvB;AACJ;AAQA,IAAM,mBAAN,MAAuB;AAAA,EACnB,YAAY,IAAI,KAAK,OAAO,SAAS,UAAU,SAAS,SAAS,MAAM,YAAY,YAAY;AAC3F,SAAK,KAAK;AACV,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,OAAO,CAAC;AACb,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AAAA,MACnB,MAAM,KAAK,IAAI;AAAA,MACf,WAAW;AAAA,MACX,SAAS;AAAA,IACb;AACA,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,OAAO,WAAW,KAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACR,QAAI,aAAa,KAAK,IAAI;AACtB,WAAK,GAAG,QAAQ,MAAM,KAAK;AAAA,IAC/B,OAAO;AACH,WAAK,GAAG,aAAa,YAAY,KAAK,GAAG;AAAA,IAC7C;AACA,SAAK,QAAQ;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU;AAAA,IACd;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACV,SAAK,gBAAgB,KAAK,IAAI,KAAK,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,QAAQ;AACX,UAAM,SAAS,KAAK;AACpB,SAAK,MAAM,OAAO;AAClB,SAAK,UAAU,OAAO;AACtB,SAAK,QAAQ,OAAO;AACpB,SAAK,OAAO;AACZ,QAAI,WAAW,KAAK,KAAK;AACrB,WAAK,UAAU;AACf,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACN,SAAK,OAAO,KAAK,GAAG,sBAAsB;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACV,SAAK,QAAQ;AACb,WAAO,KAAK,KAAK,MAAM,OAAO,cAAc,KAAK,QAAQ,WAAW,KAAK,KAAK,SAAS,KAAK,QAAQ,cAAc,KAAK,KAAK,OAAO,OAAO,aAAa,KAAK,QAAQ,WAAW,KAAK,KAAK,QAAQ;AAAA,EACrM;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,eAAW,OAAO,KAAK,QAAQ,QAAQ;AACnC,WAAK,QAAQ,OAAO,GAAG,EAAE,MAAM,KAAK,OAAO;AAAA,IAC/C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,IAAI;AACd,SAAK,MAAM,UAAU;AACrB,mBAAe;AAAA,MACX,KAAK,KAAK;AAAA,MACV,MAAM,KAAK;AAAA,IACf,GAAG,MAAM;AACL,WAAK,OAAO,WAAW,KAAK;AAC5B,WAAK,MAAM,UAAU;AACrB,SAAG;AAAA,IACP,GAAG,MAAM;AAEL,SAAG;AACH,WAAK,MAAM,UAAU;AACrB,UAAI,CAAC,KAAK,QAAQ,OAAQ,SAAQ,KAAK,mDAAmD,KAAK,OAAO,GAAG;AAAA,IAC7G,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,WAAW,MAAM;AAClB,QAAI,KAAK,UAAU,KAAK,QAAQ,UAAU,KAAK,KAAK,MAAM,OAAO;AAC7D,UAAI,CAAC,KAAK,QAAQ,OAAQ,SAAQ,IAAI,oBAAoB,KAAK,GAAG,wBAAwB,KAAK,QAAQ,OAAO,QAAQ;AACtH,eAAS;AACT;AAAA,IACJ;AACA,QAAI,KAAK,MAAM,YAAY,KAAK,MAAM,OAAQ;AAC9C,QAAI,KAAK,YAAY,IAAI,KAAK,GAAG,GAAG;AAChC,WAAK,MAAM,SAAS;AACpB,WAAK,OAAO,UAAU,IAAI;AAC1B,WAAK,MAAM,WAAW;AACtB,aAAO,SAAS;AAAA,IACpB;AACA,SAAK,cAAc,MAAM;AACrB,WAAK;AACL,WAAK,QAAQ,QAAQ,cAAc,KAAK,QAAQ,QAAQ,WAAW,MAAM,KAAK,OAAO;AACrF,WAAK,OAAO,WAAW;AACvB,qBAAe;AAAA,QACX,KAAK,KAAK;AAAA,QACV,MAAM,KAAK;AAAA,MACf,GAAG,UAAQ;AACP,aAAK,gBAAgB,KAAK;AAC1B,aAAK,eAAe,KAAK;AACzB,aAAK,MAAM,SAAS;AACpB,aAAK,MAAM,QAAQ;AACnB,aAAK,OAAO,SAAS;AACrB,aAAK,OAAO,UAAU,KAAK;AAC3B,aAAK,MAAM,WAAW;AACtB,aAAK,YAAY,IAAI,KAAK,GAAG;AAC7B,iBAAS;AAAA,MACb,GAAG,SAAO;AACN,SAAC,KAAK,QAAQ,UAAU,QAAQ,MAAM,GAAG;AACzC,aAAK,MAAM,QAAQ;AACnB,aAAK,MAAM,SAAS;AACpB,aAAK,OAAO,SAAS,KAAK;AAAA,MAC9B,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,OAAO;AACjB,SAAK,WAAW,MAAM,OAAO,KAAK;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACV,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,QAAI,KAAK,MAAM,QAAQ;AACnB,cAAQ;AACR,cAAQ,KAAK,gBAAgB,UAAU,KAAK,gBAAgB,aAAa;AAAA,IAC7E;AACA,QAAI,KAAK,MAAM,MAAO,SAAQ;AAC9B,WAAO;AAAA,MACH,KAAK,KAAK;AAAA,MACV;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACP,SAAK,KAAK;AACV,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,UAAU;AAAA,EACnB;AACJ;AAEA,IAAM,cAAc;AACpB,IAAM,iBAAiB,CAAC,UAAU,SAAS,cAAc,UAAU,gBAAgB,iBAAiB,WAAW;AAC/G,IAAM,2BAA2B;AAAA,EAC7B,YAAY;AAAA,EACZ,WAAW;AACf;AACA,IAAM,OAAN,MAAW;AAAA,EACP,YAAY,EAAE,SAAS,OAAO,cAAc,YAAY,eAAe,SAAS,SAAS,SAAS,MAAM,OAAO,cAAc,QAAQ,SAAS,UAAU,gBAAgB,GAAG;AACvK,SAAK,UAAU;AACf,SAAK,wBAAwB;AAC7B,SAAK,OAAO,SAAS;AACrB,SAAK,gBAAgB,CAAC;AACtB,SAAK,cAAc;AACnB,SAAK,cAAc,CAAC;AACpB,SAAK,UAAU;AAAA,MACX;AAAA,MACA,eAAe,CAAC,CAAC;AAAA,MACjB,cAAc,gBAAgB;AAAA,MAC9B,SAAS,WAAW;AAAA,MACpB,YAAY,cAAc;AAAA,MAC1B,OAAO,SAAS;AAAA,MAChB,SAAS,WAAW;AAAA,MACpB,SAAS,WAAW;AAAA,MACpB,OAAO,SAAS,OAAO,KAAK;AAAA,MAC5B,cAAc,gBAAgB;AAAA,MAC9B,aAAa,YAAY;AAAA,MACzB,QAAQ,UAAU,CAAC;AAAA,MACnB,SAAS,WAAW,CAAC;AAAA,MACrB,UAAU,CAAC,CAAC;AAAA,MACZ,iBAAiB,mBAAmB;AAAA,IACxC;AACA,SAAK,WAAW;AAChB,SAAK,cAAc,IAAI,WAAW,GAAG;AACrC,SAAK,kBAAkB,SAAS,KAAK,iBAAiB,KAAK,IAAI,GAAG,KAAK,QAAQ,YAAY;AAC3F,SAAK,QAAQ,KAAK,QAAQ,WAAW,SAAS,WAAW,SAAS,KAAK;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACV,UAAM,OAAO,CAAC;AACd,SAAK,cAAc,IAAI,UAAQ,KAAK,KAAK,KAAK,YAAY,CAAC,CAAC;AAC5D,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,IAAI;AACX,SAAK,cAAc,KAAK,EAAE;AAC1B,QAAI,WAAW;AACX,WAAK,mBAAmB,MAAM;AAC9B,WAAK,aAAa,KAAK,UAAU,QAAQ,GAAG,EAAE;AAC9C,UAAI,GAAG,OAAO,GAAG,IAAI,YAAY;AAC7B,aAAK,mBAAmB,GAAG,IAAI,UAAU;AAAA,MAC7C;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,IAAI,SAAS,OAAO;AACpB,QAAI,KAAK,cAAc,KAAK,UAAQ,KAAK,OAAO,EAAE,GAAG;AACjD,WAAK,OAAO,IAAI,OAAO;AACvB,aAAO,SAAS,KAAK,eAAe;AAAA,IACxC;AACA,QAAI,EAAE,KAAK,SAAS,OAAO,KAAK,IAAI,KAAK,gBAAgB,QAAQ,KAAK;AACtE,aAAS,MAAM;AACX,YAAM,2BAA2B,IAAI,KAAK,QAAQ,KAAK,KAAK;AAC5D,WAAK,aAAa,KAAK,UAAU,QAAQ,EAAE;AAC3C,YAAM,YAAY,OAAO,KAAK,QAAQ,SAAS,EAAE,CAAC;AAClD,UAAI;AACJ,UAAI,WAAW;AACX,kBAAU,QAAQ,SAAS,MAAM,SAAS;AAE1C,kBAAU,UAAU,QAAQ,MAAM,UAAU,SAAS,eAAe,SAAS;AAAA,MACjF;AACA,UAAI,CAAC,SAAS;AACV,kBAAU,aAAa,EAAE;AAAA,MAC7B;AACA,YAAM,cAAc,IAAI,iBAAiB,IAAI,KAAK,OAAO,SAAS,QAAQ,KAAK,SAAS,KAAK,SAAS,MAAM,KAAK,YAAY,KAAK,IAAI,GAAG,KAAK,WAAW;AACzJ,WAAK,cAAc,KAAK,WAAW;AACnC,UAAI,WAAW;AACX,aAAK,mBAAmB,MAAM;AAC9B,aAAK,mBAAmB,OAAO;AAAA,MACnC;AACA,eAAS,KAAK,eAAe;AAAA,IACjC,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,IAAI,SAAS,OAAO;AACvB,QAAI,EAAE,KAAK,SAAS,MAAM,IAAI,KAAK,gBAAgB,QAAQ,KAAK;AAChE,UAAM,2BAA2B,IAAI,KAAK,QAAQ,KAAK,KAAK;AAC5D,UAAM,QAAQ,KAAK,cAAc,KAAK,UAAQ,KAAK,OAAO,EAAE;AAC5D,QAAI,CAAC,OAAO;AAER,UAAI,GAAG,aAAa,MAAM,MAAM,YAAY,GAAG,QAAQ,QAAQ,KAAK;AAChE,aAAK,IAAI,IAAI,SAAS,KAAK;AAAA,MAC/B;AAAA,IACJ,OAAO;AACH,YAAM,OAAO;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,KAAK,WAAW;AAChB,WAAK,UAAU,UAAU,EAAE;AAC3B,WAAK,UAAU,QAAQ,EAAE;AAAA,IAC7B;AACA,aAAS,KAAK,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,IAAI;AACP,QAAI,CAAC,GAAI;AACT,SAAK,aAAa,KAAK,UAAU,UAAU,EAAE;AAC7C,UAAM,YAAY,KAAK,cAAc,KAAK,UAAQ,KAAK,OAAO,EAAE;AAChE,QAAI,WAAW;AACX,WAAK,sBAAsB,UAAU,OAAO;AAC5C,WAAK,sBAAsB,MAAM;AACjC,aAAO,KAAK,eAAe,SAAS;AACpC,gBAAU,YAAY,UAAU,SAAS;AAAA,IAC7C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,IAAI;AAChB,QAAI,CAAC,GAAI;AACT,WAAO,KAAK,eAAe,EAAE;AAC7B,SAAK,aAAa,KAAK,UAAU,UAAU,GAAG,EAAE;AAChD,QAAI,GAAG,WAAW,GAAG,IAAI,YAAY;AACjC,WAAK,sBAAsB,GAAG,IAAI,UAAU;AAAA,IAChD;AACA,SAAK,sBAAsB,MAAM;AAAA,EACrC;AAAA,EACA,QAAQ,MAAM;AACV,QAAI,CAAC,2BAA2B,SAAS,SAAS,UAAU;AACxD,aAAO,SAAS;AAAA,IACpB;AACA,SAAK,OAAO;AACZ,QAAI,SAAS,SAAS,OAAO;AACzB,UAAI,KAAK,WAAW;AAChB,aAAK,cAAc,QAAQ,cAAY;AACnC,eAAK,UAAU,UAAU,SAAS,EAAE;AAAA,QACxC,CAAC;AACD,aAAK,YAAY;AAAA,MACrB;AACA,WAAK,YAAY,QAAQ,YAAU;AAC/B,aAAK,YAAY,OAAO,IAAI,IAAI;AAAA,MACpC,CAAC;AAAA,IACL,OAAO;AACH,WAAK,YAAY,QAAQ,YAAU;AAC/B,aAAK,YAAY,OAAO,IAAI,KAAK;AAAA,MACrC,CAAC;AACD,WAAK,0BAA0B;AAAA,IACnC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB,IAAI;AACnB,QAAI,CAAC,GAAI;AACT,QAAI,SAAS,KAAK,YAAY,KAAK,CAAAC,YAAUA,QAAO,OAAO,EAAE;AAC7D,QAAI,CAAC,QAAQ;AACT,eAAS;AAAA,QACL;AAAA,QACA,IAAI,EAAE,KAAK;AAAA,QACX,eAAe;AAAA,QACf,UAAU;AAAA,MACd;AACA,WAAK,SAAS,SAAS,SAAS,KAAK,YAAY,OAAO,IAAI,IAAI;AAChE,WAAK,YAAY,KAAK,MAAM;AAAA,IAChC,OAAO;AACH,aAAO;AAAA,IACX;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,IAAI;AACtB,SAAK,YAAY,QAAQ,CAAC,QAAQD,WAAU;AACxC,UAAI,OAAO,OAAO,IAAI;AAClB,eAAO;AACP,YAAI,CAAC,OAAO,eAAe;AACvB,eAAK,YAAY,OAAO,IAAI,KAAK;AACjC,eAAK,YAAY,OAAOA,QAAO,CAAC;AAChC,mBAAS;AAAA,QACb;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,IAAI,OAAO;AACnB,SAAK,QAAQ,aAAa,QAAQ,SAAO,EAAE,QAAQ,OAAO,KAAK,EAAE,IAAI,KAAK,KAAK,eAAe,CAAC;AAAA,EACnG;AAAA,EACA,aAAa;AACT,SAAK,QAAQ;AAAA,MACT,WAAW;AAAA,QACP,SAAS,CAAC;AAAA,QACV,QAAQ,CAAC;AAAA,QACT,OAAO,CAAC;AAAA,MACZ;AAAA,IACJ;AACA,SAAK,MAAM,CAAC,OAAO,SAAS;AACxB,UAAI,CAAC,KAAK,MAAM,UAAU,KAAK,EAAG,MAAK,MAAM,UAAU,KAAK,IAAI,CAAC;AACjE,WAAK,MAAM,UAAU,KAAK,EAAE,KAAK,IAAI;AAAA,IACzC;AACA,SAAK,QAAQ,CAAC,OAAO,SAAS;AAC1B,YAAM,KAAK;AACX,eAAS,KAAK;AACV,WAAG,KAAK,OAAO,EAAE;AACjB,aAAK,MAAM,IAAI,SAAS;AAAA,MAC5B;AACA,WAAK,IAAI,OAAO,EAAE;AAAA,IACtB;AACA,SAAK,OAAO,CAAC,OAAO,SAAS;AACzB,UAAI,CAAC,MAAM;AACP,YAAI,CAAC,KAAK,MAAM,UAAU,KAAK,EAAG;AAClC,aAAK,MAAM,UAAU,KAAK,EAAE,SAAS;AACrC;AAAA,MACJ;AACA,aAAO,KAAK,MAAM,UAAU,KAAK,GAAG,IAAI;AAAA,IAC5C;AACA,SAAK,QAAQ,CAAC,OAAO,SAAS,YAAY;AACtC,UAAI,CAAC,KAAK,MAAM,UAAU,KAAK,EAAG;AAClC,WAAK,MAAM,UAAU,KAAK,EAAE,QAAQ,UAAQ,KAAK,SAAS,OAAO,CAAC;AAAA,IACtE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACf,UAAM,WAAW,CAAC;AAClB,SAAK,cAAc,QAAQ,CAAC,UAAUA,WAAU;AAC5C,UAAI,CAAC,SAAS,MAAM,CAAC,SAAS,GAAG,cAAc,SAAS,MAAM,QAAQ;AAClE,iBAAS,KAAK,QAAQ;AAAA,MAC1B;AACA,YAAM,QAAQ,SAAS,YAAY;AACnC,UAAI,CAAC,MAAO;AACZ,UAAI,CAAC,SAAS,MAAM,OAAQ,UAAS,KAAK;AAAA,IAC9C,CAAC;AACD,aAAS,QAAQ,UAAQ;AACrB,aAAO,KAAK,eAAe,IAAI;AAC/B,WAAK,YAAY,KAAK,SAAS;AAAA,IACnC,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,4BAA4B;AACxB,QAAI,CAAC,wBAAyB;AAC9B,SAAK,YAAY,IAAI,qBAAqB,KAAK,iBAAiB,KAAK,IAAI,GAAG,KAAK,QAAQ,eAAe;AACxG,QAAI,KAAK,cAAc,QAAQ;AAC3B,WAAK,cAAc,QAAQ,cAAY;AACnC,aAAK,UAAU,QAAQ,SAAS,EAAE;AAAA,MACtC,CAAC;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,SAAS;AACtB,YAAQ,QAAQ,WAAS;AACrB,UAAI,MAAM,gBAAgB;AACtB,aAAK,cAAc,QAAQ,cAAY;AACnC,cAAI,SAAS,OAAO,MAAM,QAAQ;AAC9B,gBAAI,SAAS,MAAM,OAAQ,QAAO,KAAK,UAAU,UAAU,SAAS,EAAE;AACtE,qBAAS,KAAK;AAAA,UAClB;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU,OAAO,OAAO;AAChC,QAAI,CAAC,SAAS,GAAI;AAClB,UAAM,EAAE,IAAI,SAAS,IAAI;AACzB,QAAI;AACJ,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,cAAM,SAAS;AACf;AAAA,MACJ,KAAK;AACD,cAAM,SAAS;AACf;AAAA,MACJ;AACI,cAAM,SAAS;AACf;AAAA,IACR;AACA,QAAI,UAAU;AAEV,SAAG,MAAM,QAAQ,IAAI,UAAU,MAAM;AAAA,IACzC,WAAW,GAAG,aAAa,KAAK,MAAM,KAAK;AACvC,SAAG,aAAa,OAAO,GAAG;AAAA,IAC9B;AACA,OAAG,aAAa,QAAQ,KAAK;AAC7B,SAAK,MAAM,OAAO,UAAU,KAAK;AACjC,SAAK,QAAQ,QAAQ,KAAK,KAAK,KAAK,QAAQ,QAAQ,KAAK,EAAE,UAAU,KAAK,OAAO;AACjF,QAAI,KAAK,QAAQ,eAAe;AAC5B,YAAM,QAAQ,IAAI,YAAY,OAAO;AAAA,QACjC,QAAQ;AAAA,MACZ,CAAC;AACD,SAAG,cAAc,KAAK;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,gBAAgB,OAAO;AACnB,QAAI,SAAS,KAAK,GAAG;AACjB,UAAI,CAAC,MAAM,OAAO,CAAC,KAAK,QAAQ,OAAQ,SAAQ,MAAM,yCAAyC,KAAK;AACpG,aAAO;AAAA,QACH,KAAK,MAAM;AAAA,QACX,SAAS,MAAM,WAAW,KAAK,QAAQ;AAAA,QACvC,OAAO,MAAM,SAAS,KAAK,QAAQ;AAAA,QACnC,MAAM,KAAK,QAAQ;AAAA,MACvB;AAAA,IACJ;AACA,WAAO;AAAA,MACH,KAAK;AAAA,MACL,SAAS,KAAK,QAAQ;AAAA,MACtB,OAAO,KAAK,QAAQ;AAAA,MACpB,MAAM,KAAK,QAAQ;AAAA,IACvB;AAAA,EACJ;AACJ;AAEA,IAAM,iBAAiB,CAAC,IAAI,YAAY;AACpC,MAAI,OAAO,SAAS,CAAC,CAAC;AACtB,QAAM,UAAU,MAAM;AAClB,WAAO,GAAG,MAAM,sBAAsB;AAAA,EAC1C;AACA,QAAM,cAAc,MAAM;AACtB,YAAQ;AACR,WAAO,aAAa,KAAK,MAAM,OAAO,cAAc,WAAW,KAAK,SAAS,KAAK,KAAK,OAAO,OAAO,aAAa,WAAW,KAAK,QAAQ;AAAA,EAC9I;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAI,gBAAiB,UAAQ;AACzB,SAAO,gBAAgB;AAAA,IACnB,OAAO;AAAA,MACH,KAAK;AAAA,QACD,MAAM;AAAA,QACN,SAAS;AAAA,MACb;AAAA,IACJ;AAAA,IACA,OAAO,CAAC,MAAM;AAAA,IACd,MAAM,OAAO,EAAE,MAAM,MAAM,GAAG;AAC1B,YAAM,KAAK,IAAI;AACf,YAAM,QAAQ,SAAS;AAAA,QACnB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACb,CAAC;AACD,YAAM,OAAO,IAAI,KAAK;AACtB,YAAM,EAAE,MAAM,YAAY,IAAI,eAAe,IAAI,KAAK,QAAQ,OAAO;AACrE,YAAM,OAAO,MAAM;AACf,aAAK,QAAQ;AACb,cAAM,SAAS;AACf,aAAK,QAAQ,KAAK,KAAK;AAAA,MAC3B;AACA,YAAM,KAAK,SAAS,MAAM;AACtB,eAAO;AAAA,UACH,IAAI,GAAG;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,gBAAU,MAAM;AACZ,aAAK,WAAW,GAAG,KAAK;AACxB,aAAK,gBAAgB;AAAA,MACzB,CAAC;AACD,kBAAY,MAAM;AACd,aAAK,gBAAgB,GAAG,KAAK;AAAA,MACjC,CAAC;AACD,aAAO,MAAM;AACT,YAAI;AACJ,eAAO,YAAY,MAAM,KAAK;AAAA,UAC1B,KAAK;AAAA,QACT,GAAG,CAAC,KAAK,WAAW,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,EAAE,CAAC;AAAA,MACjG;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAEA,IAAM,wBAAN,MAA4B;AAAA,EACxB,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,SAAK,wBAAwB;AAC7B,SAAK,SAAS,CAAC;AAAA,EACnB;AAAA,EACA,KAAK,IAAI,SAAS,OAAO;AACrB,UAAM,YAAY,IAAI,cAAc,IAAI,SAAS,OAAO,KAAK,IAAI;AACjE,SAAK,OAAO,KAAK,SAAS;AAAA,EAC9B;AAAA,EACA,OAAO,IAAI,SAAS,OAAO;AACvB,UAAM,YAAY,KAAK,OAAO,KAAK,UAAQ,KAAK,OAAO,EAAE;AACzD,QAAI,CAAC,UAAW;AAChB,cAAU,OAAO,IAAI,OAAO;AAAA,EAChC;AAAA,EACA,OAAO,IAAI,SAAS,OAAO;AACvB,UAAM,YAAY,KAAK,OAAO,KAAK,UAAQ,KAAK,OAAO,EAAE;AACzD,QAAI,CAAC,UAAW;AAChB,cAAU,MAAM;AAChB,WAAO,KAAK,QAAQ,SAAS;AAAA,EACjC;AACJ;AACA,IAAM,iBAAiB;AAAA,EACnB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AACb;AACA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,IAAI,SAAS,OAAO,MAAM;AAClC,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,OAAO;AACZ,SAAK,SAAS,CAAC;AACf,SAAK,OAAO,IAAI,OAAO;AAAA,EAC3B;AAAA,EACA,OAAO,IAAI,SAAS;AAChB,SAAK,KAAK;AACV,SAAK,UAAU,WAAW,CAAC,GAAG,gBAAgB,QAAQ,KAAK;AAC3D,UAAM,OAAO,KAAK,QAAQ;AAC1B,SAAK,QAAQ,CAAAE,QAAM;AACf,WAAK,KAAK,IAAIA,KAAI,WAAW,CAAC,GAAG,KAAK,SAAS;AAAA,QAC3C,OAAO;AAAA,UACH,KAAKA,IAAG,aAAa,UAAU,KAAKA,IAAG,QAAQ;AAAA,UAC/C,OAAOA,IAAG,aAAa,YAAY,KAAKA,IAAG,QAAQ,SAAS,KAAK,QAAQ;AAAA,UACzE,SAASA,IAAG,aAAa,cAAc,KAAKA,IAAG,QAAQ,WAAW,KAAK,QAAQ;AAAA,QACnF;AAAA,MACJ,CAAC,GAAG,KAAK,KAAK;AAAA,IAClB,CAAC;AAAA,EACL;AAAA,EACA,UAAU;AACN,WAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,KAAK,QAAQ,QAAQ,CAAC;AAAA,EACrE;AAAA,EACA,QAAQ;AACJ,UAAM,OAAO,KAAK,QAAQ;AAC1B,SAAK,QAAQ,QAAM,KAAK,KAAK,OAAO,EAAE,CAAC;AACvC,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EAChB;AACJ;AAEA,IAAI,YAAa,UAAQ;AACrB,SAAO,gBAAgB;AAAA,IACnB,MAAM,OAAO,EAAE,MAAM,GAAG;AACpB,YAAM,KAAK,IAAI;AACf,YAAM,UAAU,SAAS;AAAA,QACrB,KAAK;AAAA,QACL,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,KAAK,QAAQ;AAAA,MAC1B,CAAC;AACD,YAAM,QAAQ,SAAS;AAAA,QACnB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACb,CAAC;AACD,YAAM,EAAE,MAAM,YAAY,IAAI,eAAe,IAAI,KAAK,QAAQ,OAAO;AACrE,YAAM,YAAY,IAAI,EAAE;AACxB,YAAM,OAAO,CAAC,WAAW,SAAS;AAC9B,YAAI,MAAM,UAAU,QAAQ,UAAU,KAAK,MAAM,OAAO;AACpD,cAAI,CAAC,KAAK,QAAQ,OAAQ,SAAQ,IAAI,oBAAoB,QAAQ,GAAG,wBAAwB,QAAQ,OAAO,QAAQ;AACpH,iBAAO,SAAS;AAAA,QACpB;AACA,cAAM,MAAM,QAAQ;AACpB,uBAAe,EAAE,IAAI,GAAG,CAAC,EAAE,KAAAC,KAAI,MAAM;AACjC,oBAAU,QAAQA;AAClB,gBAAM,SAAS;AAAA,QACnB,GAAG,MAAM;AACL,gBAAM;AACN,oBAAU,QAAQ,QAAQ;AAC1B,gBAAM,QAAQ;AAAA,QAClB,CAAC;AAAA,MACL;AACA,YAAM,KAAK,SAAS,MAAM;AACtB,eAAO;AAAA,UACH,IAAI,GAAG;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,gBAAU,MAAM;AACZ,aAAK,WAAW,GAAG,KAAK;AACxB,aAAK,gBAAgB;AAAA,MACzB,CAAC;AACD,kBAAY,MAAM;AACd,aAAK,gBAAgB,GAAG,KAAK;AAAA,MACjC,CAAC;AACD,YAAM,OAAO,MAAM;AACf,cAAM,EAAE,KAAK,SAAS,MAAM,IAAI,KAAK,gBAAgB,MAAM,GAAG;AAC9D,cAAM,SAAS;AACf,gBAAQ,MAAM;AACd,gBAAQ,QAAQ;AAChB,gBAAQ,UAAU;AAClB,kBAAU,QAAQ,QAAQ;AAAA,MAC9B;AACA,YAAM,MAAM,MAAM,KAAK,MAAM;AACzB,aAAK;AACL,aAAK,WAAW,GAAG,KAAK;AACxB,aAAK,gBAAgB;AAAA,MACzB,GAAG;AAAA,QACC,WAAW;AAAA,MACf,CAAC;AACD,aAAO,MAAM;AACT,YAAI;AACJ,eAAO,YAAY,MAAM,OAAO,OAAO;AAAA,UACnC,KAAK,UAAU;AAAA,UACf,KAAK;AAAA,QACT,GAAG,EAAE,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,MACjF;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAEA,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,QAAQ,KAAK,UAAU,CAAC,GAAG;AACvB,UAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,UAAM,gBAAgB,IAAI,sBAAsB,IAAI;AACpD,UAAM,aAAa,OAAO,IAAI,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC;AACnD,QAAI,aAAa,EAAG,QAAO,IAAI,MAAM,0BAA0B;AAC/D,QAAI,OAAO,iBAAiB,YAAY;AACxC,QAAI,QAAQ,YAAY,IAAI;AAC5B,QAAI,QAAQ,eAAe;AACvB,UAAI,UAAU,kBAAkB,cAAc,IAAI,CAAC;AAAA,IACvD;AACA,QAAI,QAAQ,WAAW;AACnB,UAAI,UAAU,cAAc,UAAU,IAAI,CAAC;AAAA,IAC/C;AACA,QAAI,UAAU,QAAQ;AAAA,MAClB,aAAa,KAAK,IAAI,KAAK,IAAI;AAAA,MAC/B,cAAc,KAAK,OAAO,KAAK,IAAI;AAAA,MACnC,SAAS,KAAK,gBAAgB,KAAK,IAAI;AAAA,MACvC,WAAW,KAAK,OAAO,KAAK,IAAI;AAAA,IACpC,CAAC;AACD,QAAI,UAAU,kBAAkB;AAAA,MAC5B,aAAa,cAAc,KAAK,KAAK,aAAa;AAAA,MAClD,SAAS,cAAc,OAAO,KAAK,aAAa;AAAA,MAChD,WAAW,cAAc,OAAO,KAAK,aAAa;AAAA,IACtD,CAAC;AAAA,EACL;AACJ;", "names": ["isObject", "index", "target", "el", "src"]}