"""
数据库事务管理模块
🔒 证据链: 确保数据一致性，防止并发操作导致的数据不一致
"""

import logging
from contextlib import contextmanager
from functools import wraps
from typing import Any, Callable

from sqlalchemy import event
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.core.database import SessionLocal


logger = logging.getLogger(__name__)


class TransactionError(Exception):
    """事务错误异常"""

    def __init__(self, message: str, original_error: Exception = None):
        self.message = message
        self.original_error = original_error
        super().__init__(message)


class TransactionManager:
    """事务管理器"""

    def __init__(self):
        self.active_transactions = {}
        self._setup_event_listeners()

    def _setup_event_listeners(self):
        """设置数据库事件监听器"""
        # 🔒 证据链: 监听数据库连接事件，确保事务正确处理

        @event.listens_for(SessionLocal, "before_commit")
        def before_commit(session):
            """提交前的验证"""
            logger.debug(f"Transaction about to commit: {id(session)}")

        @event.listens_for(SessionLocal, "after_commit")
        def after_commit(session):
            """提交后的清理"""
            logger.debug(f"Transaction committed successfully: {id(session)}")

        @event.listens_for(SessionLocal, "after_rollback")
        def after_rollback(session):
            """回滚后的清理"""
            logger.warning(f"Transaction rolled back: {id(session)}")

    @contextmanager
    def transaction(self, session: Session = None, auto_commit: bool = True):
        """事务上下文管理器"""
        # 🔒 证据链: 使用上下文管理器确保事务正确提交或回滚

        if session is None:
            session = SessionLocal()
            should_close = True
        else:
            should_close = False

        transaction_id = id(session)
        self.active_transactions[transaction_id] = {
            "session": session,
            "auto_commit": auto_commit,
            "operations": [],
        }

        try:
            logger.debug(f"Starting transaction: {transaction_id}")
            yield session

            if auto_commit:
                session.commit()
                logger.debug(f"Transaction committed: {transaction_id}")

        except Exception as e:
            logger.error(f"Transaction error: {transaction_id}, error: {e}")
            session.rollback()

            # 🔒 证据链: 包装原始异常，提供更好的错误信息
            if isinstance(e, SQLAlchemyError):
                raise TransactionError(
                    f"Database transaction failed: {str(e)}", original_error=e
                )
            else:
                raise TransactionError(
                    f"Transaction failed: {str(e)}", original_error=e
                )

        finally:
            # 清理事务记录
            if transaction_id in self.active_transactions:
                del self.active_transactions[transaction_id]

            if should_close:
                session.close()

    @contextmanager
    def nested_transaction(self, session: Session):
        """嵌套事务（保存点）"""
        # 🔒 证据链: 使用保存点支持嵌套事务

        savepoint = session.begin_nested()
        try:
            logger.debug(f"Starting nested transaction: {id(savepoint)}")
            yield session
            savepoint.commit()
            logger.debug(f"Nested transaction committed: {id(savepoint)}")
        except Exception as e:
            logger.error(f"Nested transaction error: {id(savepoint)}, error: {e}")
            savepoint.rollback()
            raise

    def execute_in_transaction(
        self, func: Callable, *args, session: Session = None, **kwargs
    ) -> Any:
        """在事务中执行函数"""
        with self.transaction(session) as tx_session:
            # 如果函数需要session参数，则传入
            if "session" in func.__code__.co_varnames:
                kwargs["session"] = tx_session
            return func(*args, **kwargs)

    def get_active_transactions(self) -> dict:
        """获取活跃事务信息"""
        return {
            tx_id: {
                "operations_count": len(tx_info["operations"]),
                "auto_commit": tx_info["auto_commit"],
            }
            for tx_id, tx_info in self.active_transactions.items()
        }


# 全局事务管理器实例
transaction_manager = TransactionManager()


# 装饰器函数
def transactional(auto_commit: bool = True, session_param: str = "db"):
    """事务装饰器"""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 🔒 证据链: 支持异步函数的事务处理
            session = kwargs.get(session_param)

            with transaction_manager.transaction(session, auto_commit) as tx_session:
                # 替换或添加session参数
                kwargs[session_param] = tx_session
                return await func(*args, **kwargs)

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            session = kwargs.get(session_param)

            with transaction_manager.transaction(session, auto_commit) as tx_session:
                kwargs[session_param] = tx_session
                return func(*args, **kwargs)

        # 根据函数类型返回相应的包装器
        import asyncio

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def nested_transactional(session_param: str = "db"):
    """嵌套事务装饰器"""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            session = kwargs.get(session_param)
            if not session:
                raise ValueError(
                    f"Session parameter '{session_param}' is required for nested transaction"
                )

            with transaction_manager.nested_transaction(session):
                return await func(*args, **kwargs)

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            session = kwargs.get(session_param)
            if not session:
                raise ValueError(
                    f"Session parameter '{session_param}' is required for nested transaction"
                )

            with transaction_manager.nested_transaction(session):
                return func(*args, **kwargs)

        import asyncio

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# 批量操作支持
class BatchOperationManager:
    """批量操作管理器"""

    def __init__(self, session: Session, batch_size: int = 1000):
        self.session = session
        self.batch_size = batch_size
        self.operations = []
        self.current_batch = 0

    def add_operation(self, operation: Callable, *args, **kwargs):
        """添加操作到批次"""
        self.operations.append((operation, args, kwargs))

        if len(self.operations) >= self.batch_size:
            self.flush_batch()

    def flush_batch(self):
        """执行当前批次的操作"""
        if not self.operations:
            return

        # 🔒 证据链: 批量操作使用嵌套事务确保原子性
        with transaction_manager.nested_transaction(self.session):
            for operation, args, kwargs in self.operations:
                operation(*args, **kwargs)

            self.current_batch += 1
            logger.debug(
                f"Batch {self.current_batch} executed with {len(self.operations)} operations"
            )
            self.operations.clear()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            self.flush_batch()


# 常用事务操作函数
def safe_create(session: Session, model_instance):
    """安全创建对象"""
    try:
        session.add(model_instance)
        session.flush()  # 获取ID但不提交
        return model_instance
    except SQLAlchemyError as e:
        session.rollback()
        raise TransactionError(f"Failed to create {type(model_instance).__name__}: {e}")


def safe_update(session: Session, model_instance, **updates):
    """安全更新对象"""
    try:
        for key, value in updates.items():
            if hasattr(model_instance, key):
                setattr(model_instance, key, value)

        session.flush()
        return model_instance
    except SQLAlchemyError as e:
        session.rollback()
        raise TransactionError(f"Failed to update {type(model_instance).__name__}: {e}")


def safe_delete(session: Session, model_instance):
    """安全删除对象"""
    try:
        session.delete(model_instance)
        session.flush()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        raise TransactionError(f"Failed to delete {type(model_instance).__name__}: {e}")


def bulk_insert(session: Session, model_class, data_list: list):
    """批量插入数据"""
    try:
        session.bulk_insert_mappings(model_class, data_list)
        session.flush()
        return len(data_list)
    except SQLAlchemyError as e:
        session.rollback()
        raise TransactionError(f"Failed to bulk insert {model_class.__name__}: {e}")


def bulk_update(session: Session, model_class, data_list: list):
    """批量更新数据"""
    try:
        session.bulk_update_mappings(model_class, data_list)
        session.flush()
        return len(data_list)
    except SQLAlchemyError as e:
        session.rollback()
        raise TransactionError(f"Failed to bulk update {model_class.__name__}: {e}")


# 事务状态检查
def check_transaction_status(session: Session) -> dict:
    """检查事务状态"""
    return {
        "is_active": session.is_active,
        "in_transaction": session.in_transaction(),
        "in_nested_transaction": session.in_nested_transaction(),
        "dirty_objects": len(session.dirty),
        "new_objects": len(session.new),
        "deleted_objects": len(session.deleted),
    }


# 事务重试机制
def retry_transaction(max_retries: int = 3, delay: float = 0.1):
    """事务重试装饰器"""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            import asyncio

            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except TransactionError as e:
                    if attempt == max_retries - 1:
                        raise

                    logger.warning(
                        f"Transaction attempt {attempt + 1} failed: {e}, retrying..."
                    )
                    await asyncio.sleep(delay * (2**attempt))  # 指数退避

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            import time

            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except TransactionError as e:
                    if attempt == max_retries - 1:
                        raise

                    logger.warning(
                        f"Transaction attempt {attempt + 1} failed: {e}, retrying..."
                    )
                    time.sleep(delay * (2**attempt))

        import asyncio

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator
