{"result": [{"scriptId": "866", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/tests/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49811, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49811, "count": 3}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 577, "endOffset": 946, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 981, "endOffset": 996, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 999, "endOffset": 1016, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1019, "endOffset": 1037, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 1175, "endOffset": 1190, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 1193, "endOffset": 1210, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1213, "endOffset": 1231, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3284, "endOffset": 3354, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3458, "endOffset": 4555, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4665, "endOffset": 6533, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7131, "endOffset": 7312, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7339, "endOffset": 8381, "count": 1}], "isBlockCoverage": false}, {"functionName": "getVoices", "ranges": [{"startOffset": 7400, "endOffset": 7745, "count": 0}], "isBlockCoverage": false}, {"functionName": "speak", "ranges": [{"startOffset": 7748, "endOffset": 8170, "count": 0}], "isBlockCoverage": false}, {"functionName": "cancel", "ranges": [{"startOffset": 8173, "endOffset": 8240, "count": 0}], "isBlockCoverage": false}, {"functionName": "pause", "ranges": [{"startOffset": 8243, "endOffset": 8280, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 8283, "endOffset": 8322, "count": 0}], "isBlockCoverage": false}, {"functionName": "addEventListener", "ranges": [{"startOffset": 8325, "endOffset": 8349, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEventListener", "ranges": [{"startOffset": 8352, "endOffset": 8379, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 8633, "endOffset": 9480, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 9703, "endOffset": 11003, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTracks", "ranges": [{"startOffset": 11429, "endOffset": 11618, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAudioTracks", "ranges": [{"startOffset": 11638, "endOffset": 11833, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVideoTracks", "ranges": [{"startOffset": 11853, "endOffset": 11861, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTest", "ranges": [{"startOffset": 13502, "endOffset": 13920, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14018, "endOffset": 14043, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupTest", "ranges": [{"startOffset": 14047, "endOffset": 14164, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14264, "endOffset": 14291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14400, "endOffset": 14432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14542, "endOffset": 14576, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14678, "endOffset": 14704, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14810, "endOffset": 14840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14940, "endOffset": 14964, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15001, "endOffset": 15073, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15108, "endOffset": 15177, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "957", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/tests/api.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27017, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27017, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 345, "endOffset": 805, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 791, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 836, "endOffset": 1025, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1290, "endOffset": 1361, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1891, "endOffset": 7995, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1912, "endOffset": 1975, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1990, "endOffset": 2027, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2050, "endOffset": 3799, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2077, "endOffset": 2610, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2632, "endOffset": 3016, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3038, "endOffset": 3637, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3659, "endOffset": 3793, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3824, "endOffset": 5420, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3851, "endOffset": 4410, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4431, "endOffset": 5007, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5027, "endOffset": 5414, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5445, "endOffset": 6541, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5472, "endOffset": 6049, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6071, "endOffset": 6535, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6567, "endOffset": 7361, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6600, "endOffset": 6949, "count": 1}, {"startOffset": 6865, "endOffset": 6874, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6973, "endOffset": 7147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7052, "endOffset": 7067, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7169, "endOffset": 7289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7311, "endOffset": 7355, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7385, "endOffset": 7991, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7414, "endOffset": 7684, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7706, "endOffset": 7985, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "958", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/services/api.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23125, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23125, "count": 1}, {"startOffset": 512, "endOffset": 538, "count": 0}, {"startOffset": 671, "endOffset": 724, "count": 0}], "isBlockCoverage": true}, {"functionName": "getSecureToken", "ranges": [{"startOffset": 926, "endOffset": 1005, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateToken", "ranges": [{"startOffset": 1029, "endOffset": 1465, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1505, "endOffset": 1680, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1684, "endOffset": 1734, "count": 0}], "isBlockCoverage": false}, {"functionName": "secureCleanup", "ranges": [{"startOffset": 1760, "endOffset": 2195, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleApiError", "ranges": [{"startOffset": 2220, "endOffset": 2480, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2521, "endOffset": 2561, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2565, "endOffset": 2784, "count": 0}], "isBlockCoverage": false}, {"functionName": "login", "ranges": [{"startOffset": 2825, "endOffset": 3021, "count": 4}, {"startOffset": 2991, "endOffset": 3020, "count": 2}], "isBlockCoverage": true}, {"functionName": "register", "ranges": [{"startOffset": 3045, "endOffset": 3167, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCurrentUser", "ranges": [{"startOffset": 3201, "endOffset": 3298, "count": 2}, {"startOffset": 3268, "endOffset": 3297, "count": 0}], "isBlockCoverage": true}, {"functionName": "verifyToken", "ranges": [{"startOffset": 3325, "endOffset": 3433, "count": 0}], "isBlockCoverage": false}, {"functionName": "logout", "ranges": [{"startOffset": 3455, "endOffset": 3557, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3656, "endOffset": 3679, "count": 7}], "isBlockCoverage": true}, {"functionName": "getProjects", "ranges": [{"startOffset": 3731, "endOffset": 3847, "count": 1}], "isBlockCoverage": true}, {"functionName": "getProject", "ranges": [{"startOffset": 3875, "endOffset": 3995, "count": 0}], "isBlockCoverage": false}, {"functionName": "createProject", "ranges": [{"startOffset": 4024, "endOffset": 4147, "count": 1}], "isBlockCoverage": true}, {"functionName": "updateProject", "ranges": [{"startOffset": 4176, "endOffset": 4322, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteProject", "ranges": [{"startOffset": 4351, "endOffset": 4474, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4576, "endOffset": 4602, "count": 3}], "isBlockCoverage": true}, {"functionName": "getContents", "ranges": [{"startOffset": 4654, "endOffset": 4769, "count": 1}], "isBlockCoverage": true}, {"functionName": "get<PERSON>ontent", "ranges": [{"startOffset": 4797, "endOffset": 4916, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContent", "ranges": [{"startOffset": 4945, "endOffset": 5067, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkCompliance", "ranges": [{"startOffset": 5100, "endOffset": 5237, "count": 1}], "isBlockCoverage": true}, {"functionName": "getOptimizationSuggestions", "ranges": [{"startOffset": 5281, "endOffset": 5413, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5515, "endOffset": 5541, "count": 2}], "isBlockCoverage": true}, {"functionName": "getDistributionTasks", "ranges": [{"startOffset": 5609, "endOffset": 5735, "count": 0}], "isBlockCoverage": false}, {"functionName": "createDistributionTask", "ranges": [{"startOffset": 5775, "endOffset": 5902, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDistributionTask", "ranges": [{"startOffset": 5941, "endOffset": 6065, "count": 0}], "isBlockCoverage": false}, {"functionName": "executeDistributionTask", "ranges": [{"startOffset": 6106, "endOffset": 6239, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPlatformConfigs", "ranges": [{"startOffset": 6275, "endOffset": 6387, "count": 0}], "isBlockCoverage": false}, {"functionName": "updatePlatformConfig", "ranges": [{"startOffset": 6425, "endOffset": 6573, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6680, "endOffset": 6711, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkHealth", "ranges": [{"startOffset": 6762, "endOffset": 6858, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6959, "endOffset": 6984, "count": 0}], "isBlockCoverage": false}]}]}