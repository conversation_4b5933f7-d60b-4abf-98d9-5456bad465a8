"""
AI视频处理API端点
提供增强的AI视频处理功能
"""

from fastapi import APIRouter, HTTPException, Form
from typing import Dict, Any, List
from pydantic import BaseModel

# 导入AI服务（需要先创建）
try:
    from app.ai.enhanced_ai_service import enhanced_ai_service

    AI_SERVICE_AVAILABLE = True
except ImportError:
    AI_SERVICE_AVAILABLE = False

router = APIRouter(prefix="/ai-video", tags=["AI视频处理"])


# Pydantic模型
class ScriptGenerationRequest(BaseModel):
    topic: str
    style: str = "engaging"
    duration: int = 60
    target_audience: str = "general"
    model_name: str = "gpt-4"


class VideoAnalysisRequest(BaseModel):
    video_description: str
    video_metadata: Dict[str, Any]
    model_name: str = "claude-3"


class SubtitleGenerationRequest(BaseModel):
    audio_transcript: str
    video_duration: int
    model_name: str = "gpt-4-turbo"


class PlatformOptimizationRequest(BaseModel):
    content: str
    platform: str
    model_name: str = "claude-3-sonnet"


class VideoEditingPlanRequest(BaseModel):
    video_metadata: Dict[str, Any]
    editing_style: str = "dynamic"
    target_duration: int = 60
    model_name: str = "gpt-4"


@router.post("/generate-script")
async def generate_video_script(request: ScriptGenerationRequest):
    """生成视频脚本"""
    raise HTTPException(status_code=501, detail="This endpoint is deprecated. Process in frontend using browser compute.")


@router.post("/analyze-content")
async def analyze_video_content(request: VideoAnalysisRequest):
    """分析视频内容"""
    raise HTTPException(status_code=501, detail="This endpoint is deprecated. Process in frontend using browser compute.")


@router.post("/generate-subtitles")
async def generate_subtitles(request: SubtitleGenerationRequest):
    """生成智能字幕"""
    if not AI_SERVICE_AVAILABLE:
        raise HTTPException(status_code=503, detail="AI服务未安装")

    try:
        result = await enhanced_ai_service.generate_subtitles(
            audio_transcript=request.audio_transcript,
            video_duration=request.video_duration,
            model_name=request.model_name,
        )

        return {
            "status": "success",
            "data": {"subtitles": result, "total_segments": len(result)},
            "model_used": request.model_name,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"字幕生成失败: {str(e)}")


@router.post("/optimize-for-platform")
async def optimize_content_for_platform(request: PlatformOptimizationRequest):
    """为特定平台优化内容"""
    if not AI_SERVICE_AVAILABLE:
        raise HTTPException(status_code=503, detail="AI服务未安装")

    try:
        result = await enhanced_ai_service.optimize_content_for_platform(
            content=request.content,
            platform=request.platform,
            model_name=request.model_name,
        )

        return {"status": "success", "data": result, "model_used": request.model_name}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"平台优化失败: {str(e)}")


@router.post("/generate-editing-plan")
async def generate_video_editing_plan(request: VideoEditingPlanRequest):
    """生成智能视频剪辑方案"""
    if not AI_SERVICE_AVAILABLE:
        raise HTTPException(status_code=503, detail="AI服务未安装")

    try:
        result = await enhanced_ai_service.generate_video_editing_plan(
            video_metadata=request.video_metadata,
            editing_style=request.editing_style,
            target_duration=request.target_duration,
            model_name=request.model_name,
        )

        return {"status": "success", "data": result, "model_used": request.model_name}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"剪辑方案生成失败: {str(e)}")


@router.get("/models")
async def get_available_models():
    """获取可用的AI模型"""
    if not AI_SERVICE_AVAILABLE:
        return {"status": "error", "message": "AI服务未安装", "available_models": []}

    try:
        models = enhanced_ai_service.models
        return {
            "status": "success",
            "data": {
                "available_models": [
                    {
                        "name": config.name,
                        "model_id": config.model_id,
                        "provider": config.provider,
                        "enabled": config.enabled,
                        "max_tokens": config.max_tokens,
                    }
                    for config in models.values()
                ],
                "total_models": len(models),
            },
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@router.get("/usage-stats")
async def get_ai_usage_stats():
    """获取AI使用统计"""
    if not AI_SERVICE_AVAILABLE:
        return {"status": "error", "message": "AI服务未安装", "usage_stats": {}}

    try:
        stats = enhanced_ai_service.get_usage_stats()
        return {"status": "success", "data": stats}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取使用统计失败: {str(e)}")


@router.post("/batch-process")
async def batch_process_videos(
    video_descriptions: List[str] = Form(...),
    processing_type: str = Form(...),  # analysis, script_generation, optimization
    model_name: str = Form("gpt-4"),
):
    """批量处理视频"""
    if not AI_SERVICE_AVAILABLE:
        raise HTTPException(status_code=503, detail="AI服务未安装")

    if len(video_descriptions) > 10:
        raise HTTPException(status_code=400, detail="批量处理最多支持10个视频")

    try:
        results = []

        for i, description in enumerate(video_descriptions):
            try:
                if processing_type == "analysis":
                    result = await enhanced_ai_service.analyze_video_content(
                        video_description=description,
                        video_metadata={"index": i},
                        model_name=model_name,
                    )
                    results.append(
                        {
                            "index": i,
                            "status": "success",
                            "data": {
                                "content_type": result.content_type,
                                "quality_score": result.quality_score,
                                "engagement_prediction": result.engagement_prediction,
                                "target_audience": result.target_audience,
                            },
                        }
                    )
                elif processing_type == "script_generation":
                    result = await enhanced_ai_service.generate_video_script(
                        topic=description, model_name=model_name
                    )
                    results.append(
                        {
                            "index": i,
                            "status": "success",
                            "data": {
                                "script_text": result.script_text[:200]
                                + "...",  # 截取前200字符
                                "estimated_duration": result.estimated_duration,
                                "difficulty_level": result.difficulty_level,
                            },
                        }
                    )
                else:
                    results.append(
                        {
                            "index": i,
                            "status": "error",
                            "error": f"不支持的处理类型: {processing_type}",
                        }
                    )

            except Exception as e:
                results.append({"index": i, "status": "error", "error": str(e)})

        success_count = sum(1 for r in results if r["status"] == "success")

        return {
            "status": "success",
            "data": {
                "results": results,
                "total_processed": len(video_descriptions),
                "success_count": success_count,
                "error_count": len(video_descriptions) - success_count,
            },
            "model_used": model_name,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量处理失败: {str(e)}")


@router.get("/health")
async def ai_service_health():
    """AI服务健康检查"""
    if not AI_SERVICE_AVAILABLE:
        return {
            "status": "unavailable",
            "message": "AI服务未安装",
            "health": "unhealthy",
        }

    try:
        # 检查客户端连接状态
        clients_status = {}
        for provider, client in enhanced_ai_service.clients.items():
            clients_status[provider] = "connected" if client else "disconnected"

        return {
            "status": "available",
            "health": "healthy",
            "data": {
                "clients": clients_status,
                "models_count": len(enhanced_ai_service.models),
                "usage_stats": enhanced_ai_service.usage_stats,
            },
        }
    except Exception as e:
        return {"status": "error", "health": "unhealthy", "error": str(e)}
