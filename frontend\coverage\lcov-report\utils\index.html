
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">61.75% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>1778/2879</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.44% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>143/229</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.02% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>111/146</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">61.75% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>1778/2879</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="accessibility.ts"><a href="accessibility.ts.html">accessibility.ts</a></td>
	<td data-value="75.41" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75.41" class="pct medium">75.41%</td>
	<td data-value="537" class="abs medium">405/537</td>
	<td data-value="70.83" class="pct medium">70.83%</td>
	<td data-value="48" class="abs medium">34/48</td>
	<td data-value="75.67" class="pct medium">75.67%</td>
	<td data-value="37" class="abs medium">28/37</td>
	<td data-value="75.41" class="pct medium">75.41%</td>
	<td data-value="537" class="abs medium">405/537</td>
	</tr>

<tr>
	<td class="file medium" data-value="ai-optimizer.ts"><a href="ai-optimizer.ts.html">ai-optimizer.ts</a></td>
	<td data-value="77.73" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 77%"></div><div class="cover-empty" style="width: 23%"></div></div>
	</td>
	<td data-value="77.73" class="pct medium">77.73%</td>
	<td data-value="494" class="abs medium">384/494</td>
	<td data-value="61.81" class="pct medium">61.81%</td>
	<td data-value="55" class="abs medium">34/55</td>
	<td data-value="89.65" class="pct high">89.65%</td>
	<td data-value="29" class="abs high">26/29</td>
	<td data-value="77.73" class="pct medium">77.73%</td>
	<td data-value="494" class="abs medium">384/494</td>
	</tr>

<tr>
	<td class="file medium" data-value="edge-optimizer.ts"><a href="edge-optimizer.ts.html">edge-optimizer.ts</a></td>
	<td data-value="69.77" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 69%"></div><div class="cover-empty" style="width: 31%"></div></div>
	</td>
	<td data-value="69.77" class="pct medium">69.77%</td>
	<td data-value="569" class="abs medium">397/569</td>
	<td data-value="50.94" class="pct medium">50.94%</td>
	<td data-value="53" class="abs medium">27/53</td>
	<td data-value="76.66" class="pct medium">76.66%</td>
	<td data-value="30" class="abs medium">23/30</td>
	<td data-value="69.77" class="pct medium">69.77%</td>
	<td data-value="569" class="abs medium">397/569</td>
	</tr>

<tr>
	<td class="file medium" data-value="route-preloader.ts"><a href="route-preloader.ts.html">route-preloader.ts</a></td>
	<td data-value="59.64" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 59%"></div><div class="cover-empty" style="width: 41%"></div></div>
	</td>
	<td data-value="59.64" class="pct medium">59.64%</td>
	<td data-value="285" class="abs medium">170/285</td>
	<td data-value="62.5" class="pct medium">62.5%</td>
	<td data-value="16" class="abs medium">10/16</td>
	<td data-value="47.05" class="pct low">47.05%</td>
	<td data-value="17" class="abs low">8/17</td>
	<td data-value="59.64" class="pct medium">59.64%</td>
	<td data-value="285" class="abs medium">170/285</td>
	</tr>

<tr>
	<td class="file medium" data-value="rum-monitoring.ts"><a href="rum-monitoring.ts.html">rum-monitoring.ts</a></td>
	<td data-value="75.79" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75.79" class="pct medium">75.79%</td>
	<td data-value="376" class="abs medium">285/376</td>
	<td data-value="84.21" class="pct high">84.21%</td>
	<td data-value="38" class="abs high">32/38</td>
	<td data-value="87.5" class="pct high">87.5%</td>
	<td data-value="24" class="abs high">21/24</td>
	<td data-value="75.79" class="pct medium">75.79%</td>
	<td data-value="376" class="abs medium">285/376</td>
	</tr>

<tr>
	<td class="file low" data-value="security.ts"><a href="security.ts.html">security.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="404" class="abs low">0/404</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="404" class="abs low">0/404</td>
	</tr>

<tr>
	<td class="file medium" data-value="test-helpers.ts"><a href="test-helpers.ts.html">test-helpers.ts</a></td>
	<td data-value="64.01" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 64%"></div><div class="cover-empty" style="width: 36%"></div></div>
	</td>
	<td data-value="64.01" class="pct medium">64.01%</td>
	<td data-value="214" class="abs medium">137/214</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="18" class="abs low">6/18</td>
	<td data-value="62.5" class="pct medium">62.5%</td>
	<td data-value="8" class="abs medium">5/8</td>
	<td data-value="64.01" class="pct medium">64.01%</td>
	<td data-value="214" class="abs medium">137/214</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-24T04:22:31.209Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    