/**
 * 图标组件系统 - 2025年最佳实践
 * 基于SVG的轻量级图标组件
 */

import { defineComponent, h } from 'vue'

// 图标基础组件
const createIcon = (path: string, viewBox = '0 0 24 24') => {
  return defineComponent({
    name: 'Icon',
    props: {
      size: {
        type: [String, Number],
        default: 24
      },
      color: {
        type: String,
        default: 'currentColor'
      }
    },
    setup(props) {
      return () => h('svg', {
        width: props.size,
        height: props.size,
        viewBox,
        fill: 'none',
        stroke: props.color,
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        class: 'icon'
      }, [
        h('path', { d: path })
      ])
    }
  })
}

// 多路径图标组件
const createMultiPathIcon = (paths: string[], viewBox = '0 0 24 24') => {
  return defineComponent({
    name: 'MultiPathIcon',
    props: {
      size: {
        type: [String, Number],
        default: 24
      },
      color: {
        type: String,
        default: 'currentColor'
      }
    },
    setup(props) {
      return () => h('svg', {
        width: props.size,
        height: props.size,
        viewBox,
        fill: 'none',
        stroke: props.color,
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        class: 'icon'
      }, paths.map(path => h('path', { d: path })))
    }
  })
}

// 导出图标组件
export const HomeIcon = createIcon('M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z')

export const VideoIcon = createMultiPathIcon([
  'M23 7l-7 5 7 5V7z',
  'M15 5H3a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2z'
])

export const CpuIcon = createMultiPathIcon([
  'M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z',
  'M9 9h6v6H9z'
])

export const UserIcon = createIcon('M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2')

export const AnalyticsIcon = createMultiPathIcon([
  'M18 20V10',
  'M12 20V4',
  'M6 20v-6'
])

export const LayersIcon = createMultiPathIcon([
  'M12 2l8 4-8 4-8-4 8-4z',
  'M4 10l8 4 8-4',
  'M4 14l8 4 8-4'
])

export const SettingsIcon = createMultiPathIcon([
  'M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6z',
  'M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z'
])

export const LogoutIcon = createMultiPathIcon([
  'M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4',
  'M16 17l5-5-5-5',
  'M21 12H9'
])

export const SearchIcon = createMultiPathIcon([
  'M11 11m-8 0a8 8 0 1 0 16 0a8 8 0 1 0 -16 0',
  'M21 21l-4.35-4.35'
])

export const BellIcon = createMultiPathIcon([
  'M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9',
  'M13.73 21a2 2 0 0 1-3.46 0'
])

export const MenuIcon = createMultiPathIcon([
  'M3 12h18',
  'M3 6h18',
  'M3 18h18'
])

export const XIcon = createMultiPathIcon([
  'M18 6L6 18',
  'M6 6l12 12'
])

// 导出所有图标
export const Icons = {
  HomeIcon,
  VideoIcon,
  CpuIcon,
  UserIcon,
  AnalyticsIcon,
  LayersIcon,
  SettingsIcon,
  LogoutIcon,
  SearchIcon,
  BellIcon,
  MenuIcon,
  XIcon
}

// 默认导出
export default Icons
