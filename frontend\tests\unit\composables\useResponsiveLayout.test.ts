/**
 * 响应式布局组合式函数单元测试 - 2025年最佳实践
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { nextTick } from 'vue'
import { useResponsiveLayout, BREAKPOINTS } from '@/composables/useResponsiveLayout'

// 模拟window.innerWidth和window.innerHeight
const mockWindowSize = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  })
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  })
}

// 模拟resize事件
const triggerResize = () => {
  window.dispatchEvent(new Event('resize'))
}

// 模拟orientationchange事件
const triggerOrientationChange = () => {
  window.dispatchEvent(new Event('orientationchange'))
}

describe('useResponsiveLayout', () => {
  let cleanup: (() => void) | undefined

  beforeEach(() => {
    // 重置window尺寸
    mockWindowSize(1920, 1080)
    
    // 清理document.body样式
    document.body.className = ''
    document.body.style.overflow = ''
  })

  afterEach(() => {
    // 清理事件监听器
    if (cleanup) {
      cleanup()
      cleanup = undefined
    }
  })

  describe('设备类型检测', () => {
    it('应该正确识别桌面设备', async () => {
      mockWindowSize(1920, 1080)
      const { deviceType, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(deviceType.value).toBe('desktop')
    })

    it('应该正确识别平板设备', async () => {
      mockWindowSize(1024, 768)
      const { deviceType, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(deviceType.value).toBe('tablet')
    })

    it('应该正确识别移动设备', async () => {
      mockWindowSize(375, 667)
      const { deviceType, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(deviceType.value).toBe('mobile')
    })
  })

  describe('断点检测', () => {
    it('应该正确识别xs断点', async () => {
      mockWindowSize(320, 568)
      const { currentBreakpoint, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(currentBreakpoint.value).toBe('xs')
    })

    it('应该正确识别sm断点', async () => {
      mockWindowSize(640, 480)
      const { currentBreakpoint, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(currentBreakpoint.value).toBe('sm')
    })

    it('应该正确识别md断点', async () => {
      mockWindowSize(800, 600)
      const { currentBreakpoint, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(currentBreakpoint.value).toBe('md')
    })

    it('应该正确识别lg断点', async () => {
      mockWindowSize(1200, 800)
      const { currentBreakpoint, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(currentBreakpoint.value).toBe('lg')
    })

    it('应该正确识别xl断点', async () => {
      mockWindowSize(1400, 900)
      const { currentBreakpoint, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(currentBreakpoint.value).toBe('xl')
    })

    it('应该正确识别xxl断点', async () => {
      mockWindowSize(1600, 1000)
      const { currentBreakpoint, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(currentBreakpoint.value).toBe('xxl')
    })
  })

  describe('布局模式', () => {
    it('移动设备应该使用mobile布局模式', async () => {
      mockWindowSize(375, 667)
      const { layoutMode, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(layoutMode.value).toBe('mobile')
    })

    it('平板设备应该使用tablet布局模式', async () => {
      mockWindowSize(1024, 768)
      const { layoutMode, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(layoutMode.value).toBe('tablet')
    })

    it('桌面设备应该使用desktop布局模式', async () => {
      mockWindowSize(1920, 1080)
      const { layoutMode, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(layoutMode.value).toBe('desktop')
    })
  })

  describe('UI元素显示控制', () => {
    it('移动设备应该显示汉堡菜单', async () => {
      mockWindowSize(375, 667)
      const { shouldShowHamburgerMenu, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(shouldShowHamburgerMenu.value).toBe(true)
    })

    it('桌面设备不应该显示汉堡菜单', async () => {
      mockWindowSize(1920, 1080)
      const { shouldShowHamburgerMenu, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(shouldShowHamburgerMenu.value).toBe(false)
    })

    it('移动设备应该显示底部导航', async () => {
      mockWindowSize(375, 667)
      const { shouldShowBottomNavigation, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(shouldShowBottomNavigation.value).toBe(true)
    })

    it('桌面设备不应该显示底部导航', async () => {
      mockWindowSize(1920, 1080)
      const { shouldShowBottomNavigation, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(shouldShowBottomNavigation.value).toBe(false)
    })

    it('桌面设备应该显示侧边栏', async () => {
      mockWindowSize(1920, 1080)
      const { shouldShowSidebar, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(shouldShowSidebar.value).toBe(true)
    })

    it('移动设备默认不应该显示侧边栏', async () => {
      mockWindowSize(375, 667)
      const { shouldShowSidebar, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(shouldShowSidebar.value).toBe(false)
    })
  })

  describe('移动端菜单控制', () => {
    it('应该能够切换移动端菜单', async () => {
      mockWindowSize(375, 667)
      const { isMobileMenuOpen, toggleMobileMenu, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(isMobileMenuOpen.value).toBe(false)
      
      toggleMobileMenu()
      expect(isMobileMenuOpen.value).toBe(true)
      
      toggleMobileMenu()
      expect(isMobileMenuOpen.value).toBe(false)
    })

    it('打开移动端菜单时应该阻止背景滚动', async () => {
      mockWindowSize(375, 667)
      const { toggleMobileMenu, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      toggleMobileMenu()
      expect(document.body.style.overflow).toBe('hidden')
    })

    it('关闭移动端菜单时应该恢复背景滚动', async () => {
      mockWindowSize(375, 667)
      const { toggleMobileMenu, closeMobileMenu, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      toggleMobileMenu()
      closeMobileMenu()
      expect(document.body.style.overflow).toBe('')
    })

    it('切换到桌面端时应该自动关闭移动端菜单', async () => {
      mockWindowSize(375, 667)
      const { isMobileMenuOpen, toggleMobileMenu, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      toggleMobileMenu()
      expect(isMobileMenuOpen.value).toBe(true)
      
      // 切换到桌面端
      mockWindowSize(1920, 1080)
      updateWindowSize()
      await nextTick()
      
      expect(isMobileMenuOpen.value).toBe(false)
    })
  })

  describe('尺寸计算', () => {
    it('应该为不同设备类型返回正确的导航栏高度', async () => {
      const { navbarHeight, updateWindowSize } = useResponsiveLayout()
      
      // 移动设备
      mockWindowSize(375, 667)
      updateWindowSize()
      await nextTick()
      expect(navbarHeight.value).toBe('56px')
      
      // 平板设备
      mockWindowSize(1024, 768)
      updateWindowSize()
      await nextTick()
      expect(navbarHeight.value).toBe('64px')
      
      // 桌面设备
      mockWindowSize(1920, 1080)
      updateWindowSize()
      await nextTick()
      expect(navbarHeight.value).toBe('72px')
    })

    it('应该为不同设备类型返回正确的侧边栏宽度', async () => {
      const { sidebarWidth, updateWindowSize } = useResponsiveLayout()
      
      // 移动设备（菜单关闭）
      mockWindowSize(375, 667)
      updateWindowSize()
      await nextTick()
      expect(sidebarWidth.value).toBe('0px')
      
      // 桌面设备
      mockWindowSize(1920, 1080)
      updateWindowSize()
      await nextTick()
      expect(sidebarWidth.value).toBe('280px')
    })

    it('移动设备打开菜单时应该返回正确的侧边栏宽度', async () => {
      mockWindowSize(375, 667)
      const { sidebarWidth, toggleMobileMenu, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      toggleMobileMenu()
      expect(sidebarWidth.value).toBe('280px')
    })
  })

  describe('断点匹配工具', () => {
    it('matchesBreakpoint应该正确匹配向上断点', async () => {
      mockWindowSize(1200, 800)
      const { matchesBreakpoint, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(matchesBreakpoint('lg', 'up')).toBe(true)
      expect(matchesBreakpoint('xl', 'up')).toBe(false)
    })

    it('matchesBreakpoint应该正确匹配向下断点', async () => {
      mockWindowSize(800, 600)
      const { matchesBreakpoint, updateWindowSize } = useResponsiveLayout()
      
      updateWindowSize()
      await nextTick()
      
      expect(matchesBreakpoint('lg', 'down')).toBe(true)
      expect(matchesBreakpoint('sm', 'down')).toBe(false)
    })

    it('getMediaQuery应该返回正确的媒体查询字符串', () => {
      const { getMediaQuery } = useResponsiveLayout()
      
      expect(getMediaQuery('md', 'up')).toBe('(min-width: 768px)')
      expect(getMediaQuery('lg', 'down')).toBe('(max-width: 991px)')
    })
  })

  describe('事件处理', () => {
    it('应该监听window resize事件', async () => {
      const { windowWidth, windowHeight } = useResponsiveLayout()
      
      mockWindowSize(800, 600)
      triggerResize()
      
      // 等待事件处理
      await new Promise(resolve => setTimeout(resolve, 0))
      
      expect(windowWidth.value).toBe(800)
      expect(windowHeight.value).toBe(600)
    })

    it('应该监听orientationchange事件', async () => {
      const { windowWidth, windowHeight } = useResponsiveLayout()
      
      mockWindowSize(667, 375) // 横屏
      triggerOrientationChange()
      
      // orientationchange有100ms延迟
      await new Promise(resolve => setTimeout(resolve, 150))
      
      expect(windowWidth.value).toBe(667)
      expect(windowHeight.value).toBe(375)
    })
  })

  describe('BREAKPOINTS常量', () => {
    it('应该包含所有必要的断点', () => {
      expect(BREAKPOINTS).toHaveProperty('xs')
      expect(BREAKPOINTS).toHaveProperty('sm')
      expect(BREAKPOINTS).toHaveProperty('md')
      expect(BREAKPOINTS).toHaveProperty('lg')
      expect(BREAKPOINTS).toHaveProperty('xl')
      expect(BREAKPOINTS).toHaveProperty('xxl')
    })

    it('断点值应该按升序排列', () => {
      const values = Object.values(BREAKPOINTS)
      for (let i = 1; i < values.length; i++) {
        expect(values[i]).toBeGreaterThan(values[i - 1])
      }
    })
  })
})
