from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.models import User
from app.utils.auth_deps import get_password_hash


class TestAdminAuth:
    def setup_method(self, method):
        """在每个测试前设置，创建一个测试用户"""
        self.username = "testadmin"
        self.password = "testpassword"

    def test_successful_login(self, client: TestClient, db: Session):
        """测试成功登录"""
        # 创建用户
        db.add(
            User(
                username=self.username,
                password_hash=get_password_hash(self.password),
                is_active=True,
                is_superuser=True,
            )
        )
        db.commit()

        response = client.post(
            "/admin/login", data={"username": self.username, "password": self.password}
        )
        assert response.status_code == 200
        assert "access_token" in response.cookies
        assert response.json()["success"] is True

    def test_login_wrong_password(self, client: TestClient, db: Session):
        """测试密码错误"""
        db.add(
            User(
                username=self.username,
                password_hash=get_password_hash("wrongpassword"),
                is_active=True,
                is_superuser=True,
            )
        )
        db.commit()

        response = client.post(
            "/admin/login", data={"username": self.username, "password": self.password}
        )
        assert response.status_code == 401
        assert "access_token" not in response.cookies
        assert "您还有" in response.json()["message"]

    def test_login_account_lockout(self, client: TestClient, db: Session):
        """测试账户因尝试过多而被锁定"""
        db.add(
            User(
                username=self.username,
                password_hash=get_password_hash("anotherwrongpassword"),
                is_active=True,
                is_superuser=True,
            )
        )
        db.commit()

        # 模拟5次失败尝试
        for _ in range(5):
            client.post(
                "/admin/login", data={"username": self.username, "password": "wrong"}
            )

        # 第6次尝试应该被锁定
        response = client.post(
            "/admin/login", data={"username": self.username, "password": "wrong"}
        )
        assert response.status_code == 429
        assert "账户已锁定" in response.json()["message"]
