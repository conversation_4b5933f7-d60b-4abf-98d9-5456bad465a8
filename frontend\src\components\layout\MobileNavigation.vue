<!--
  移动端专用导航组件 - 2025年最佳实践
  包含汉堡菜单、底部导航和侧滑菜单
-->

<template>
  <div class="mobile-navigation">
    <!-- 移动端顶部导航栏 -->
    <header class="mobile-header">
      <div class="mobile-header__content">
        <!-- 汉堡菜单按钮 -->
        <button 
          class="mobile-menu-toggle"
          @click="toggleMobileMenu"
          :aria-expanded="isMobileMenuOpen"
          aria-label="切换导航菜单"
        >
          <div class="hamburger-icon" :class="{ 'is-active': isMobileMenuOpen }">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
        
        <!-- 品牌标识 -->
        <div class="mobile-brand">
          <router-link to="/" class="brand-link" @click="closeMobileMenu">
            <img src="/logo.svg" alt="Logo" class="brand-logo" />
            <span class="brand-text">二创短视频</span>
          </router-link>
        </div>
        
        <!-- 右侧操作按钮 -->
        <div class="mobile-actions">
          <button class="action-btn" aria-label="搜索">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
          <button class="action-btn" aria-label="通知">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
            </svg>
          </button>
        </div>
      </div>
    </header>
    
    <!-- 移动端侧滑菜单 -->
    <transition name="mobile-menu">
      <div v-if="isMobileMenuOpen" class="mobile-menu-overlay" @click="closeMobileMenu">
        <nav class="mobile-menu" @click.stop>
          <!-- 用户信息 -->
          <div class="mobile-user-info">
            <div class="user-avatar">
              <img :src="userAvatar" :alt="userName" />
            </div>
            <div class="user-details">
              <h3 class="user-name">{{ userName }}</h3>
              <p class="user-role">{{ userRole }}</p>
            </div>
          </div>
          
          <!-- 主导航菜单 -->
          <div class="mobile-nav-section">
            <h4 class="nav-section-title">主要功能</h4>
            <ul class="mobile-nav-list">
              <li v-for="item in mainNavItems" :key="item.path" class="mobile-nav-item">
                <router-link 
                  :to="item.path" 
                  class="mobile-nav-link"
                  :class="{ 'is-active': $route.path === item.path }"
                  @click="closeMobileMenu"
                >
                  <component :is="item.icon" class="nav-icon" />
                  <span class="nav-text">{{ item.text }}</span>
                  <svg v-if="item.badge" class="nav-badge" viewBox="0 0 8 8">
                    <circle cx="4" cy="4" r="4" fill="currentColor" />
                  </svg>
                </router-link>
              </li>
            </ul>
          </div>
          
          <!-- 工具菜单 -->
          <div class="mobile-nav-section">
            <h4 class="nav-section-title">工具</h4>
            <ul class="mobile-nav-list">
              <li v-for="item in toolNavItems" :key="item.path" class="mobile-nav-item">
                <router-link 
                  :to="item.path" 
                  class="mobile-nav-link"
                  :class="{ 'is-active': $route.path === item.path }"
                  @click="closeMobileMenu"
                >
                  <component :is="item.icon" class="nav-icon" />
                  <span class="nav-text">{{ item.text }}</span>
                </router-link>
              </li>
            </ul>
          </div>
          
          <!-- 底部操作 -->
          <div class="mobile-menu-footer">
            <button class="footer-btn" @click="handleSettings">
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>
              </svg>
              <span>设置</span>
            </button>
            <button class="footer-btn" @click="handleLogout">
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                <polyline points="16,17 21,12 16,7"></polyline>
                <line x1="21" y1="12" x2="9" y2="12"></line>
              </svg>
              <span>退出</span>
            </button>
          </div>
        </nav>
      </div>
    </transition>
    
    <!-- 移动端底部导航 -->
    <nav class="mobile-bottom-nav">
      <div class="bottom-nav-content">
        <router-link 
          v-for="item in bottomNavItems" 
          :key="item.path"
          :to="item.path"
          class="bottom-nav-item"
          :class="{ 'is-active': $route.path === item.path }"
        >
          <component :is="item.icon" class="bottom-nav-icon" />
          <span class="bottom-nav-text">{{ item.text }}</span>
        </router-link>
      </div>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useResponsiveLayout } from '../../composables/useResponsiveLayout'
import { useAuthStore } from '../../stores/auth'
import {
  HomeIcon,
  VideoIcon,
  CpuIcon,
  UserIcon,
  AnalyticsIcon,
  LayersIcon
} from '../../components/icons'

// 组合式函数
const { isMobileMenuOpen, toggleMobileMenu, closeMobileMenu } = useResponsiveLayout()
const authStore = useAuthStore()

// 用户信息
const userName = computed(() => authStore.user?.username || '测试用户')
const userRole = computed(() => authStore.user?.role || '管理员')
const userAvatar = computed(() => authStore.user?.avatar || '/default-avatar.png')

// 主导航菜单项
const mainNavItems = [
  {
    path: '/',
    text: '首页',
    icon: HomeIcon,
    badge: false
  },
  {
    path: '/video-creation',
    text: '视频创作',
    icon: VideoIcon,
    badge: true
  },
  {
    path: '/compute-test',
    text: '计算引擎',
    icon: CpuIcon,
    badge: false
  },
  {
    path: '/profile',
    text: '个人中心',
    icon: UserIcon,
    badge: false
  }
]

// 工具菜单项
const toolNavItems = [
  {
    path: '/content-analysis',
    text: '内容分析',
    icon: AnalyticsIcon
  },
  {
    path: '/batch-processing',
    text: '批量处理',
    icon: LayersIcon
  }
]

// 底部导航菜单项
const bottomNavItems = [
  {
    path: '/',
    text: '首页',
    icon: HomeIcon
  },
  {
    path: '/video-creation',
    text: '创作',
    icon: VideoIcon
  },
  {
    path: '/compute-test',
    text: '工具',
    icon: CpuIcon
  },
  {
    path: '/profile',
    text: '我的',
    icon: UserIcon
  }
]

// 事件处理
const handleSettings = () => {
  closeMobileMenu()
  // 导航到设置页面
}

const handleLogout = () => {
  closeMobileMenu()
  authStore.logout()
}
</script>

<style scoped>
/* 移动端导航样式 */
.mobile-navigation {
  position: relative;
  z-index: 1000;
}

/* 移动端顶部导航栏 */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 56px;
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
  z-index: 1001;
}

.mobile-header__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 16px;
}

/* 汉堡菜单按钮 */
.mobile-menu-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.mobile-menu-toggle:hover {
  background-color: var(--color-background-soft);
}

.hamburger-icon {
  width: 24px;
  height: 18px;
  position: relative;
  transform: rotate(0deg);
  transition: 0.3s ease-in-out;
}

.hamburger-icon span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: var(--color-text);
  border-radius: 1px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}

.hamburger-icon span:nth-child(1) {
  top: 0px;
}

.hamburger-icon span:nth-child(2) {
  top: 8px;
}

.hamburger-icon span:nth-child(3) {
  top: 16px;
}

.hamburger-icon.is-active span:nth-child(1) {
  top: 8px;
  transform: rotate(135deg);
}

.hamburger-icon.is-active span:nth-child(2) {
  opacity: 0;
  left: -60px;
}

.hamburger-icon.is-active span:nth-child(3) {
  top: 8px;
  transform: rotate(-135deg);
}

/* 品牌标识 */
.mobile-brand {
  flex: 1;
  display: flex;
  justify-content: center;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--color-text);
}

.brand-logo {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}

.brand-text {
  font-size: 18px;
  font-weight: 600;
}

/* 右侧操作按钮 */
.mobile-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.action-btn:hover {
  background-color: var(--color-background-soft);
}

.action-btn .icon {
  width: 20px;
  height: 20px;
}

/* 移动端侧滑菜单 */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1002;
  backdrop-filter: blur(4px);
}

.mobile-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background: var(--color-background);
  box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15);
  overflow-y: auto;
  padding: 56px 0 0 0;
}

/* 用户信息 */
.mobile-user-info {
  display: flex;
  align-items: center;
  padding: 24px 20px;
  border-bottom: 1px solid var(--color-border);
}

.user-avatar img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details {
  margin-left: 12px;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.user-role {
  font-size: 14px;
  color: var(--color-text-soft);
  margin: 0;
}

/* 导航菜单 */
.mobile-nav-section {
  padding: 16px 0;
  border-bottom: 1px solid var(--color-border);
}

.nav-section-title {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--color-text-soft);
  margin: 0 0 12px 0;
  padding: 0 20px;
  letter-spacing: 0.5px;
}

.mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobile-nav-item {
  margin: 0;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  text-decoration: none;
  color: var(--color-text);
  transition: background-color 0.2s ease;
  position: relative;
}

.mobile-nav-link:hover {
  background-color: var(--color-background-soft);
}

.mobile-nav-link.is-active {
  background-color: var(--color-primary-soft);
  color: var(--color-primary);
}

.nav-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
}

.nav-text {
  flex: 1;
  font-size: 16px;
}

.nav-badge {
  width: 8px;
  height: 8px;
  color: var(--color-primary);
}

/* 菜单底部 */
.mobile-menu-footer {
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-btn {
  display: flex;
  align-items: center;
  padding: 12px 0;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text);
  font-size: 16px;
  transition: color 0.2s ease;
}

.footer-btn:hover {
  color: var(--color-primary);
}

.footer-btn .icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
}

/* 底部导航 */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 64px;
  background: var(--color-background);
  border-top: 1px solid var(--color-border);
  z-index: 1001;
}

.bottom-nav-content {
  display: flex;
  height: 100%;
}

.bottom-nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: var(--color-text-soft);
  transition: color 0.2s ease;
  padding: 8px 4px;
}

.bottom-nav-item:hover,
.bottom-nav-item.is-active {
  color: var(--color-primary);
}

.bottom-nav-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.bottom-nav-text {
  font-size: 12px;
  font-weight: 500;
}

/* 过渡动画 */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: opacity 0.3s ease;
}

.mobile-menu-enter-active .mobile-menu,
.mobile-menu-leave-active .mobile-menu {
  transition: transform 0.3s ease;
}

.mobile-menu-enter-from,
.mobile-menu-leave-to {
  opacity: 0;
}

.mobile-menu-enter-from .mobile-menu,
.mobile-menu-leave-to .mobile-menu {
  transform: translateX(-100%);
}
</style>
