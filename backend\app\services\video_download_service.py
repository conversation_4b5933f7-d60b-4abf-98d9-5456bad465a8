"""
视频下载服务 - 基于开源项目yt-dlp和gallery-dl
GitHub: https://github.com/yt-dlp/yt-dlp
GitHub: https://github.com/mikf/gallery-dl
用途: 支持数百个平台的视频和媒体内容下载
"""

import asyncio
import json
import os
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Any, Dict

# 导入标准化错误处理和日志
from app.core.exceptions import ExternalServiceException, ValidationException
from app.core.logging import get_service_logger, log_function_call


class VideoDownloadService:
    """基于yt-dlp和gallery-dl的视频下载服务"""

    def __init__(self, download_dir: str = "downloads"):
        """
        初始化视频下载服务

        Args:
            download_dir: 下载目录
        """
        self.download_dir = download_dir
        os.makedirs(download_dir, exist_ok=True)

        # 初始化日志
        self.logger = get_service_logger("video_download")

        # 支持的平台列表
        self.supported_platforms = [
            "youtube",
            "bilibili",
            "douyin",
            "kua<PERSON>ou",
            "xiaohongshu",
            "tiktok",
            "instagram",
            "twitter",
            "weibo",
            "zhihu",
            "youtube.com",
            "youtu.be",
        ]

        # gallery-dl支持的平台
        self.gallery_dl_platforms = [
            "pixiv",
            "danbooru",
            "gelbooru",
            "safebooru",
            "twitter",
            "instagram",
            "tumblr",
            "flickr",
            "reddit",
            "deviantart",
            "artstation",
        ]

    @log_function_call
    async def check_dependencies(self) -> Dict[str, Any]:
        """检查依赖工具是否安装"""
        tools_status = {}

        # 检查yt-dlp
        try:
            result = subprocess.run(
                ["yt-dlp", "--version"],
                capture_output=True,
                text=True,
                timeout=10,
            )
            if result.returncode == 0:
                tools_status["yt-dlp"] = {
                    "available": True,
                    "version": result.stdout.strip(),
                }
                self.logger.info(
                    "yt-dlp可用", extra_data={"version": result.stdout.strip()}
                )
            else:
                tools_status["yt-dlp"] = {
                    "available": False,
                    "error": "yt-dlp not working",
                }
                self.logger.warning("yt-dlp不可用")
        except Exception as e:
            tools_status["yt-dlp"] = {
                "available": False,
                "error": f"yt-dlp not found: {str(e)}",
            }
            self.logger.error("yt-dlp检查失败", extra_data={"error": str(e)})

        # 检查gallery-dl
        try:
            result = subprocess.run(
                ["gallery-dl", "--version"],
                capture_output=True,
                text=True,
                timeout=10,
            )
            if result.returncode == 0:
                tools_status["gallery-dl"] = {
                    "available": True,
                    "version": result.stdout.strip(),
                }
                self.logger.info(
                    "gallery-dl可用",
                    extra_data={"version": result.stdout.strip()},
                )
            else:
                tools_status["gallery-dl"] = {
                    "available": False,
                    "error": "gallery-dl not working",
                }
                self.logger.warning("gallery-dl不可用")
        except Exception as e:
            tools_status["gallery-dl"] = {
                "available": False,
                "error": f"gallery-dl not found: {str(e)}",
            }
            self.logger.error("gallery-dl检查失败", extra_data={"error": str(e)})

        return {"tools": tools_status, "timestamp": datetime.now().isoformat()}

    def _get_platform_from_url(self, url: str) -> str:
        """从URL识别平台"""
        url_lower = url.lower()

        if "youtube.com" in url_lower or "youtu.be" in url_lower:
            return "youtube"
        elif "bilibili.com" in url_lower:
            return "bilibili"
        elif "douyin.com" in url_lower:
            return "douyin"
        elif "kuaishou.com" in url_lower:
            return "kuaishou"
        elif "xiaohongshu.com" in url_lower:
            return "xiaohongshu"
        elif "tiktok.com" in url_lower:
            return "tiktok"
        elif "instagram.com" in url_lower:
            return "instagram"
        elif "twitter.com" in url_lower or "x.com" in url_lower:
            return "twitter"
        elif "weibo.com" in url_lower:
            return "weibo"
        elif "zhihu.com" in url_lower:
            return "zhihu"
        elif "pixiv.net" in url_lower:
            return "pixiv"
        elif "danbooru.donmai.us" in url_lower:
            return "danbooru"
        elif "gelbooru.com" in url_lower:
            return "gelbooru"
        elif "safebooru.org" in url_lower:
            return "safebooru"
        elif "tumblr.com" in url_lower:
            return "tumblr"
        elif "flickr.com" in url_lower:
            return "flickr"
        elif "reddit.com" in url_lower:
            return "reddit"
        elif "deviantart.com" in url_lower:
            return "deviantart"
        elif "artstation.com" in url_lower:
            return "artstation"
        else:
            return "unknown"

    def _should_use_gallery_dl(self, platform: str) -> bool:
        """判断是否应该使用gallery-dl"""
        return platform in self.gallery_dl_platforms

    @log_function_call
    async def download_with_gallery_dl(
        self, url: str, options: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """使用gallery-dl下载内容"""
        if not options:
            options = {}

        try:
            # 构建gallery-dl命令
            cmd = ["gallery-dl"]

            # 添加输出目录
            output_dir = os.path.join(self.download_dir, "gallery-dl")
            os.makedirs(output_dir, exist_ok=True)
            cmd.extend(["--destination", output_dir])

            # 添加元数据输出
            cmd.extend(["--write-metadata"])

            # 添加JSON输出格式
            cmd.extend(["--dump-json"])

            # 添加质量选项
            if options.get("quality"):
                quality_option = f"extractor.quality={options['quality']}"
                cmd.extend(["--option", quality_option])

            # 添加并发数限制
            if options.get("concurrent"):
                sleep_option = f"extractor.sleep={options['concurrent']}"
                cmd.extend(["--option", sleep_option])

            # 添加用户代理
            user_agent = "Mozilla/5.0 (compatible; gallery-dl)"
            cmd.extend(["--user-agent", user_agent])

            # 添加URL
            cmd.append(url)

            self.logger.info(
                "开始gallery-dl下载",
                extra_data={
                    "url": url,
                    "command": " ".join(cmd),
                    "output_dir": output_dir,
                },
            )

            # 执行下载
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=output_dir,
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                # 解析输出
                output_text = stdout.decode("utf-8", errors="ignore")
                stderr_text = stderr.decode("utf-8", errors="ignore")

                # 尝试解析JSON输出
                downloaded_files = []
                for line in output_text.split("\n"):
                    if line.strip() and line.strip().startswith("{"):
                        try:
                            file_info = json.loads(line.strip())
                            downloaded_files.append(file_info)
                        except json.JSONDecodeError:
                            continue

                result = {
                    "success": True,
                    "platform": self._get_platform_from_url(url),
                    "url": url,
                    "tool": "gallery-dl",
                    "output_dir": output_dir,
                    "files": downloaded_files,
                    "file_count": len(downloaded_files),
                    "stdout": output_text,
                    "stderr": stderr_text,
                    "timestamp": datetime.now().isoformat(),
                }

                self.logger.info(
                    "gallery-dl下载成功",
                    extra_data={
                        "url": url,
                        "file_count": len(downloaded_files),
                        "output_dir": output_dir,
                    },
                )

                return result

            else:
                error_msg = stderr.decode("utf-8", errors="ignore")
                self.logger.error(
                    "gallery-dl下载失败",
                    extra_data={
                        "url": url,
                        "return_code": process.returncode,
                        "error": error_msg,
                    },
                )

                raise ExternalServiceException(
                    f"gallery-dl下载失败: {error_msg}",
                    service_name="gallery-dl",
                )

        except Exception as e:
            self.logger.error(
                "gallery-dl下载异常",
                extra_data={
                    "url": url,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
            )

            if isinstance(e, ExternalServiceException):
                raise
            else:
                raise ExternalServiceException(
                    f"gallery-dl下载异常: {str(e)}", service_name="gallery-dl"
                )

    @log_function_call
    async def download_with_yt_dlp(
        self, url: str, options: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """使用yt-dlp下载视频"""
        if not options:
            options = {}

        try:
            # 构建yt-dlp命令
            cmd = ["yt-dlp"]

            # 添加输出目录和文件名模板
            output_dir = os.path.join(self.download_dir, "yt-dlp")
            os.makedirs(output_dir, exist_ok=True)

            output_template = os.path.join(output_dir, "%(title)s.%(ext)s")
            cmd.extend(["-o", output_template])

            # 添加JSON输出
            cmd.extend(["--dump-single-json"])

            # 添加质量选项
            if options.get("quality"):
                cmd.extend(["-f", options["quality"]])
            else:
                cmd.extend(["-f", "best[height<=720]"])

            # 添加字幕下载
            if options.get("subtitles"):
                cmd.extend(["--write-subs", "--write-auto-subs"])

            # 添加缩略图下载
            if options.get("thumbnail"):
                cmd.extend(["--write-thumbnail"])

            # 添加元数据
            cmd.extend(["--write-info-json"])

            # 添加URL
            cmd.append(url)

            self.logger.info(
                "开始yt-dlp下载",
                extra_data={
                    "url": url,
                    "command": " ".join(cmd),
                    "output_dir": output_dir,
                },
            )

            # 执行下载
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=output_dir,
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                # 解析JSON输出
                output_text = stdout.decode("utf-8", errors="ignore")
                stderr_text = stderr.decode("utf-8", errors="ignore")

                try:
                    video_info = json.loads(output_text)
                except json.JSONDecodeError:
                    video_info = {"title": "Unknown", "duration": 0}

                # 查找下载的文件
                downloaded_files = []
                for file_path in Path(output_dir).glob("*"):
                    if file_path.is_file():
                        downloaded_files.append(
                            {
                                "filename": file_path.name,
                                "path": str(file_path),
                                "size": file_path.stat().st_size,
                            }
                        )

                result = {
                    "success": True,
                    "platform": self._get_platform_from_url(url),
                    "url": url,
                    "tool": "yt-dlp",
                    "output_dir": output_dir,
                    "video_info": video_info,
                    "files": downloaded_files,
                    "file_count": len(downloaded_files),
                    "stdout": output_text,
                    "stderr": stderr_text,
                    "timestamp": datetime.now().isoformat(),
                }

                self.logger.info(
                    "yt-dlp下载成功",
                    extra_data={
                        "url": url,
                        "title": video_info.get("title", "Unknown"),
                        "file_count": len(downloaded_files),
                        "output_dir": output_dir,
                    },
                )

                return result

            else:
                error_msg = stderr.decode("utf-8", errors="ignore")
                self.logger.error(
                    "yt-dlp下载失败",
                    extra_data={
                        "url": url,
                        "return_code": process.returncode,
                        "error": error_msg,
                    },
                )

                raise ExternalServiceException(
                    f"yt-dlp下载失败: {error_msg}", service_name="yt-dlp"
                )

        except Exception as e:
            self.logger.error(
                "yt-dlp下载异常",
                extra_data={
                    "url": url,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
            )

            if isinstance(e, ExternalServiceException):
                raise
            else:
                raise ExternalServiceException(
                    f"yt-dlp下载异常: {str(e)}", service_name="yt-dlp"
                )

    async def download_video(
        self, url: str, options: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """智能下载视频/媒体内容"""
        try:
            # 验证URL
            if not url or not url.strip():
                raise ValidationException("URL不能为空")

            # 识别平台
            platform = self._get_platform_from_url(url)

            if platform == "unknown":
                self.logger.warning("未识别的平台", extra_data={"url": url})

            self.logger.info(
                "开始下载",
                extra_data={
                    "url": url,
                    "platform": platform,
                    "options": options,
                },
            )

            # 根据平台选择工具
            if self._should_use_gallery_dl(platform):
                return await self.download_with_gallery_dl(url, options)
            else:
                return await self.download_with_yt_dlp(url, options)

        except Exception as e:
            self.logger.error(
                "下载失败",
                extra_data={
                    "url": url,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
            )

            if isinstance(e, (ValidationException, ExternalServiceException)):
                raise
            else:
                raise ExternalServiceException(
                    f"下载失败: {str(e)}", service_name="video_download"
                )


# 全局服务实例
video_download_service = VideoDownloadService()
