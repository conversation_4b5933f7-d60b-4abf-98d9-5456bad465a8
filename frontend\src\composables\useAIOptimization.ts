/**
 * AI优化组合式函数 - 2025年最佳实践
 * 为Vue组件提供AI驱动的性能优化功能
 */

import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getAIOptimizer, initAIOptimizer, type PredictionResult, type OptimizationSuggestion } from '@/utils/ai-optimizer'

export function useAIOptimization() {
  const route = useRoute()
  const router = useRouter()
  
  // 响应式状态
  const isEnabled = ref(true)
  const isLearning = ref(false)
  const predictions = ref<PredictionResult[]>([])
  const suggestions = ref<OptimizationSuggestion[]>([])
  const stats = ref({
    behaviorDataCount: 0,
    modelSize: 0,
    optimizationCount: 0,
    config: {}
  })

  // AI优化器实例
  let optimizer = getAIOptimizer()

  // 计算属性
  const hasHighConfidencePredictions = computed(() => {
    return predictions.value.some(p => p.confidence > 0.7)
  })

  const topPrediction = computed(() => {
    return predictions.value.length > 0 ? predictions.value[0] : null
  })

  const optimizationScore = computed(() => {
    const totalSuggestions = suggestions.value.length
    const highImpactSuggestions = suggestions.value.filter(s => s.impact === 'high').length
    
    if (totalSuggestions === 0) return 100
    
    const score = ((totalSuggestions - highImpactSuggestions) / totalSuggestions) * 100
    return Math.round(score)
  })

  // 初始化AI优化器
  const initOptimizer = (config = {}) => {
    if (!optimizer) {
      optimizer = initAIOptimizer({
        enableBehaviorTracking: true,
        enablePredictivePreloading: true,
        enableAdaptiveOptimization: true,
        learningRate: 0.1,
        predictionThreshold: 0.3,
        maxPredictions: 3,
        dataRetentionDays: 30,
        ...config
      })
    }
    
    updateStats()
    updatePredictions()
    updateSuggestions()
  }

  // 更新预测结果
  const updatePredictions = () => {
    if (!optimizer) return
    
    const currentPath = route.path
    predictions.value = optimizer.getPredictions(currentPath)
  }

  // 更新优化建议
  const updateSuggestions = () => {
    if (!optimizer) return
    
    suggestions.value = optimizer.getOptimizationSuggestions()
  }

  // 更新统计信息
  const updateStats = () => {
    if (!optimizer) return
    
    stats.value = optimizer.getStats()
  }

  // 手动触发页面预加载
  const preloadPage = (path: string, priority: 'high' | 'medium' | 'low' = 'medium') => {
    if (!optimizer) return
    
    // 创建预加载链接
    const link = document.createElement('link')
    link.rel = priority === 'high' ? 'preload' : 'prefetch'
    link.href = path
    
    if (priority === 'high') {
      link.as = 'document'
    }
    
    document.head.appendChild(link)
    
    console.log(`🚀 手动预加载: ${path} (优先级: ${priority})`)
  }

  // 预加载推荐的页面
  const preloadRecommendedPages = () => {
    predictions.value.forEach(prediction => {
      if (prediction.confidence > 0.5) {
        preloadPage(prediction.nextPage, prediction.priority)
      }
    })
  }

  // 应用优化建议
  const applySuggestion = (suggestion: OptimizationSuggestion) => {
    switch (suggestion.type) {
      case 'preload':
        preloadPage(suggestion.resource, 'high')
        break
      case 'prefetch':
        preloadPage(suggestion.resource, 'medium')
        break
      case 'lazy-load':
        // 实现懒加载逻辑
        console.log(`💤 懒加载: ${suggestion.resource}`)
        break
      case 'critical-css':
        // 实现关键CSS优化
        console.log(`🎨 关键CSS优化: ${suggestion.resource}`)
        break
    }
  }

  // 应用所有高优先级建议
  const applyHighPrioritySuggestions = () => {
    const highPrioritySuggestions = suggestions.value.filter(s => s.impact === 'high')
    highPrioritySuggestions.forEach(applySuggestion)
  }

  // 开始学习模式
  const startLearning = () => {
    isLearning.value = true
    
    // 增加数据收集频率
    if (optimizer) {
      // 这里可以调整学习参数
      console.log('🧠 开始AI学习模式')
    }
  }

  // 停止学习模式
  const stopLearning = () => {
    isLearning.value = false
    console.log('🧠 停止AI学习模式')
  }

  // 清除学习数据
  const clearLearningData = () => {
    if (optimizer) {
      optimizer.clearData()
      updateStats()
      updatePredictions()
      updateSuggestions()
      console.log('🗑️ 已清除AI学习数据')
    }
  }

  // 获取页面性能预测
  const getPagePerformancePrediction = (path: string) => {
    const prediction = predictions.value.find(p => p.nextPage === path)
    if (!prediction) return null
    
    return {
      confidence: prediction.confidence,
      estimatedLoadTime: prediction.estimatedTime,
      recommendation: prediction.confidence > 0.7 ? 'preload' : 
                     prediction.confidence > 0.4 ? 'prefetch' : 'lazy-load'
    }
  }

  // 智能路由导航
  const smartNavigate = async (path: string) => {
    const prediction = getPagePerformancePrediction(path)
    
    if (prediction && prediction.confidence > 0.8) {
      // 高置信度预测，直接导航
      await router.push(path)
    } else {
      // 低置信度，先预加载再导航
      preloadPage(path, 'high')
      
      // 等待一小段时间让预加载生效
      setTimeout(async () => {
        await router.push(path)
      }, 100)
    }
  }

  // 获取用户行为洞察
  const getUserBehaviorInsights = () => {
    if (!optimizer) return null
    
    const currentStats = optimizer.getStats()
    
    return {
      totalSessions: currentStats.behaviorDataCount,
      modelAccuracy: currentStats.modelSize > 0 ? 
        Math.min(currentStats.behaviorDataCount / currentStats.modelSize * 10, 100) : 0,
      optimizationEffectiveness: optimizationScore.value,
      topPredictedPages: predictions.value.slice(0, 3),
      recommendedActions: suggestions.value.filter(s => s.impact === 'high').length
    }
  }

  // 导出性能报告
  const exportPerformanceReport = () => {
    const insights = getUserBehaviorInsights()
    const report = {
      timestamp: new Date().toISOString(),
      currentPage: route.path,
      predictions: predictions.value,
      suggestions: suggestions.value,
      stats: stats.value,
      insights,
      optimizationScore: optimizationScore.value
    }
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { 
      type: 'application/json' 
    })
    
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `ai-optimization-report-${Date.now()}.json`
    link.click()
    
    URL.revokeObjectURL(url)
  }

  // 生命周期钩子
  onMounted(() => {
    initOptimizer()
    
    // 定期更新预测和建议
    const updateInterval = setInterval(() => {
      if (isEnabled.value) {
        updatePredictions()
        updateSuggestions()
        updateStats()
      }
    }, 30000) // 30秒更新一次
    
    // 清理定时器
    onUnmounted(() => {
      clearInterval(updateInterval)
    })
  })

  // 监听路由变化
  watch(() => route.path, (newPath, oldPath) => {
    if (optimizer && newPath !== oldPath) {
      // 通知AI优化器页面变化
      optimizer.trackPageView(newPath)
      
      // 更新预测和建议
      setTimeout(() => {
        updatePredictions()
        updateSuggestions()
      }, 100)
    }
  })

  // 监听学习模式变化
  watch(isLearning, (learning) => {
    if (learning) {
      // 学习模式下更频繁地更新
      const learningInterval = setInterval(() => {
        updatePredictions()
        updateSuggestions()
        updateStats()
      }, 10000) // 10秒更新一次
      
      // 停止学习时清理定时器
      const stopLearningWatch = watch(isLearning, (stillLearning) => {
        if (!stillLearning) {
          clearInterval(learningInterval)
          stopLearningWatch()
        }
      })
    }
  })

  return {
    // 状态
    isEnabled,
    isLearning,
    predictions,
    suggestions,
    stats,
    
    // 计算属性
    hasHighConfidencePredictions,
    topPrediction,
    optimizationScore,
    
    // 方法
    initOptimizer,
    updatePredictions,
    updateSuggestions,
    updateStats,
    preloadPage,
    preloadRecommendedPages,
    applySuggestion,
    applyHighPrioritySuggestions,
    startLearning,
    stopLearning,
    clearLearningData,
    getPagePerformancePrediction,
    smartNavigate,
    getUserBehaviorInsights,
    exportPerformanceReport
  }
}
