import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import compression from 'vite-plugin-compression'
import { fileURLToPath, URL } from 'node:url'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 🔒 证据链: 环境变量加载 - 安全配置
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    plugins: [vue(), compression({ algorithm: 'gzip' })],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    server: {
      port: 3000,
      open: true,
      // API代理配置 - 解决CORS问题
      proxy: {
        '/api': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      },
      // 🔒 证据链: CORS安全配置 - 限制来源
      cors: {
        origin: mode === 'development' ? true : [
          'http://localhost:3000',
          'http://127.0.0.1:3000'
        ],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization']
      },
      headers: {
        // 🔒 证据链: 安全头配置 - 防止XSS和点击劫持
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        // 开发环境禁用CSP，避免资源加载问题
        ...(env.VITE_ENABLE_CSP === 'true' && mode === 'production' && {
          'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' ws: wss: http: https:;"
        })
      },
    },
    // 🔒 证据链: 依赖优化 - 性能和安全
    optimizeDeps: {
      include: ['@ffmpeg/ffmpeg', '@ffmpeg/util', 'long', 'seedrandom'],
      exclude: ['@tensorflow/tfjs'] // 大型依赖延迟加载
    },
    build: {
      target: 'esnext',
      // 🔒 证据链: 构建安全配置
      sourcemap: mode === 'development',
      minify: mode === 'production' ? 'terser' : false,
      rollupOptions: {
        output: {
          // 🚀 2025年最佳实践：智能代码分割
          manualChunks: (id) => {
            // 第三方库分割
            if (id.includes('node_modules')) {
              // Vue核心库
              if (id.includes('vue') || id.includes('@vue') || id.includes('pinia')) {
                return 'vue-core'
              }
              // 大型AI库
              if (id.includes('@ffmpeg/ffmpeg') || id.includes('@ffmpeg/util')) {
                return 'ffmpeg'
              }
              if (id.includes('@tensorflow/tfjs')) {
                return 'tensorflow'
              }
              // 工具库
              if (id.includes('lodash') || id.includes('axios') || id.includes('dayjs')) {
                return 'utils-lib'
              }
              // 其他第三方库
              return 'vendor'
            }

            // 页面组件分割
            if (id.includes('/pages/')) {
              const pageName = id.split('/pages/')[1].split('.')[0].toLowerCase()
              return `page-${pageName}`
            }

            // 组件分割
            if (id.includes('/components/')) {
              if (id.includes('/layout/')) {
                return 'layout-components'
              }
              if (id.includes('/icons/')) {
                return 'icon-components'
              }
              return 'shared-components'
            }

            // 工具函数分割
            if (id.includes('/utils/') || id.includes('/composables/')) {
              return 'utils'
            }

            // Store分割
            if (id.includes('/stores/')) {
              return 'stores'
            }
          },
          // 优化chunk文件名
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId
            if (facadeModuleId && facadeModuleId.includes('/pages/')) {
              const pageName = facadeModuleId.split('/pages/')[1].split('.')[0]
              return `pages/${pageName}-[hash].js`
            }
            return 'chunks/[name]-[hash].js'
          },
          entryFileNames: 'entries/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]'
        }
      },
      // 🔒 证据链: 内存优化 - 防止构建时内存溢出
      chunkSizeWarningLimit: 1000
    },
    define: {
      global: 'globalThis',
      'process.env': {},
      module: {},
      require: 'undefined',
    },
    // 🔒 证据链: 环境变量安全 - 只暴露VITE_前缀的变量
    envPrefix: 'VITE_'
  }
})