"""
标准化异常处理模块
提供一致的错误处理和响应格式
"""

import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional

from fastapi import HTTPException


class ErrorCode(Enum):
    """错误代码枚举"""

    # 通用错误
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"

    # 认证错误
    AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"

    # 业务逻辑错误
    CONTENT_COMPLIANCE_FAILED = "CONTENT_COMPLIANCE_FAILED"
    AI_SERVICE_UNAVAILABLE = "AI_SERVICE_UNAVAILABLE"
    DOWNLOAD_FAILED = "DOWNLOAD_FAILED"
    UPLOAD_FAILED = "UPLOAD_FAILED"

    # 系统错误
    DATABASE_ERROR = "DATABASE_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"


class BaseServiceException(Exception):
    """基础服务异常类"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = 500,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.status_code = status_code
        self.timestamp = datetime.now().isoformat()
        super().__init__(self.message)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error": {
                "code": self.error_code.value,
                "message": self.message,
                "details": self.details,
                "timestamp": self.timestamp,
            }
        }


class ValidationException(BaseServiceException):
    """数据验证异常"""

    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        details = {"field": field} if field else {}
        details.update(kwargs)
        super().__init__(
            message=message,
            error_code=ErrorCode.VALIDATION_ERROR,
            details=details,
            status_code=400,
        )


class AuthenticationException(BaseServiceException):
    """认证异常"""

    def __init__(self, message: str = "认证失败", **kwargs):
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTHENTICATION_FAILED,
            status_code=401,
            **kwargs,
        )


class PermissionException(BaseServiceException):
    """权限异常"""

    def __init__(self, message: str = "权限不足", **kwargs):
        super().__init__(
            message=message,
            error_code=ErrorCode.PERMISSION_DENIED,
            status_code=403,
            **kwargs,
        )


class ResourceNotFoundException(BaseServiceException):
    """资源未找到异常"""

    def __init__(self, resource_type: str, resource_id: str = None, **kwargs):
        message = f"{resource_type}未找到"
        if resource_id:
            message += f": {resource_id}"

        super().__init__(
            message=message,
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            details={
                "resource_type": resource_type,
                "resource_id": resource_id,
            },
            status_code=404,
            **kwargs,
        )


class AIServiceException(BaseServiceException):
    """AI服务异常"""

    def __init__(self, message: str, service_name: str = None, **kwargs):
        details = {"service": service_name} if service_name else {}
        super().__init__(
            message=message,
            error_code=ErrorCode.AI_SERVICE_UNAVAILABLE,
            details=details,
            status_code=503,
            **kwargs,
        )


class ContentComplianceException(BaseServiceException):
    """内容合规异常"""

    def __init__(self, message: str, content_type: str = None, **kwargs):
        details = {"content_type": content_type} if content_type else {}
        super().__init__(
            message=message,
            error_code=ErrorCode.CONTENT_COMPLIANCE_FAILED,
            details=details,
            status_code=422,
            **kwargs,
        )


class DatabaseException(BaseServiceException):
    """数据库异常"""

    def __init__(self, message: str, operation: str = None, **kwargs):
        details = {"operation": operation} if operation else {}
        super().__init__(
            message=message,
            error_code=ErrorCode.DATABASE_ERROR,
            details=details,
            status_code=500,
            **kwargs,
        )


class ExternalServiceException(BaseServiceException):
    """外部服务异常"""

    def __init__(self, message: str, service_name: str = None, **kwargs):
        details = {"service": service_name} if service_name else {}
        super().__init__(
            message=message,
            error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
            details=details,
            status_code=502,
            **kwargs,
        )


class RateLimitException(BaseServiceException):
    """速率限制异常"""

    def __init__(self, message: str = "请求过于频繁", **kwargs):
        super().__init__(
            message=message,
            error_code=ErrorCode.RATE_LIMIT_EXCEEDED,
            status_code=429,
            **kwargs,
        )


def format_error_response(
    exception: Exception, include_traceback: bool = False
) -> Dict[str, Any]:
    """格式化错误响应"""

    if isinstance(exception, BaseServiceException):
        response = exception.to_dict()
    elif isinstance(exception, HTTPException):
        response = {
            "error": {
                "code": "HTTP_ERROR",
                "message": exception.detail,
                "details": {"status_code": exception.status_code},
                "timestamp": datetime.now().isoformat(),
            }
        }
    else:
        response = {
            "error": {
                "code": ErrorCode.UNKNOWN_ERROR.value,
                "message": str(exception),
                "details": {"type": type(exception).__name__},
                "timestamp": datetime.now().isoformat(),
            }
        }

    if include_traceback:
        response["error"]["traceback"] = traceback.format_exc()

    return response


class CustomHTTPException(HTTPException):
    """自定义HTTP异常类"""

    def __init__(
        self,
        status_code: int,
        detail: str,
        error_code: str = "HTTP_ERROR",
        timestamp: Optional[datetime] = None,
    ):
        super().__init__(status_code=status_code, detail=detail)
        self.error_code = error_code
        self.timestamp = timestamp or datetime.now()


def create_http_exception(exception: BaseServiceException) -> HTTPException:
    """将服务异常转换为HTTP异常"""

    return HTTPException(status_code=exception.status_code, detail=exception.to_dict())
