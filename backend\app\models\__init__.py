"""
用户数据模型
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Float, Integer, String, Text
from sqlalchemy.sql import func

from app.core.database import Base


class User(Base):
    """用户模型"""

    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=True)
    phone_number = Column(String(20), nullable=True, index=True)  # 添加手机号字段
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    role = Column(
        String(20), default="user", nullable=False, index=True
    )  # 🔒 证据链: 添加角色字段
    avatar_url = Column(String(255), nullable=True)
    bio = Column(Text, nullable=True)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}')>"


class Project(Base):
    """项目模型"""

    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    owner_id = Column(Integer, nullable=False, index=True)
    status = Column(String(20), default="active")

    # 项目配置
    config = Column(Text, nullable=True)  # JSON字符串

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}')>"


class Content(Base):
    """内容模型"""

    __tablename__ = "contents"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, index=True)
    content_type = Column(
        String(50), nullable=False, index=True
    )  # text, video, image, audio

    # 内容文本
    original_text = Column(Text, nullable=True)  # 原始文本
    rewritten_text = Column(Text, nullable=True)  # AI改写后的文本

    # 关联信息
    creator_id = Column(Integer, nullable=False, index=True)
    project_id = Column(Integer, nullable=True, index=True)

    # 状态管理
    status = Column(String(20), default="draft")  # draft, approved, rejected, published

    # 合规检测
    compliance_score = Column(Float, nullable=True)  # 合规得分 0-1
    compliance_issues = Column(Text, nullable=True)  # JSON格式的问题列表

    # 元数据
    content_metadata = Column(Text, nullable=True)  # JSON格式的元数据
    rewrite_metadata = Column(Text, nullable=True)  # AI改写的元数据

    # 媒体文件路径
    media_files = Column(Text, nullable=True)  # JSON格式的文件路径列表

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    published_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return (
            f"<Content(id={self.id}, title='{self.title}', "
            f"type='{self.content_type}')>"
        )


class ContentItem(Base):
    """内容项模型"""

    __tablename__ = "content_items"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, nullable=False, index=True)
    title = Column(String(200), nullable=False)
    content_type = Column(String(20), nullable=False)  # text, video, image
    original_content = Column(Text, nullable=True)
    processed_content = Column(Text, nullable=True)

    # 内容状态
    status = Column(
        String(20), default="pending"
    )  # pending, processing, completed, failed
    compliance_status = Column(
        String(20), default="pending"
    )  # pending, approved, rejected
    compliance_result = Column(Text, nullable=True)  # JSON字符串

    # 文件路径
    file_path = Column(String(500), nullable=True)
    thumbnail_path = Column(String(500), nullable=True)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<ContentItem(id={self.id}, title='{self.title}')>"


class DistributionTask(Base):
    """分发任务模型"""

    __tablename__ = "distribution_tasks"

    id = Column(Integer, primary_key=True, index=True)
    content_item_id = Column(Integer, nullable=False, index=True)
    platform = Column(String(50), nullable=False)  # douyin, kuaishou, bilibili
    status = Column(String(20), default="pending")

    # 分发配置
    platform_config = Column(Text, nullable=True)  # JSON字符串

    # 分发结果
    platform_id = Column(String(100), nullable=True)  # 平台返回的ID
    platform_url = Column(String(500), nullable=True)  # 平台链接
    error_message = Column(Text, nullable=True)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    distributed_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<DistributionTask(id={self.id}, platform='{self.platform}')>"


class Distribution(Base):
    """分发任务模型"""

    __tablename__ = "distributions"

    id = Column(Integer, primary_key=True, index=True)
    content_id = Column(Integer, nullable=False, index=True)
    platform = Column(
        String(50), nullable=False, index=True
    )  # douyin, xiaohongshu, weibo, bilibili
    creator_id = Column(Integer, nullable=False, index=True)

    # 分发内容
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)

    # 时间安排
    scheduled_time = Column(DateTime(timezone=True), nullable=True)  # 计划发布时间
    published_at = Column(DateTime(timezone=True), nullable=True)  # 实际发布时间

    # 状态管理
    status = Column(
        String(20), default="pending"
    )  # pending, processing, completed, failed, cancelled

    # 平台配置和结果
    platform_config = Column(Text, nullable=True)  # JSON格式的平台特定配置
    result_data = Column(Text, nullable=True)  # JSON格式的发布结果数据
    error_message = Column(Text, nullable=True)  # 错误信息

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return (
            f"<Distribution(id={self.id}, content_id={self.content_id}, "
            f"platform='{self.platform}')>"
        )


class AuditLog(Base):
    """审计日志模型"""

    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=True, index=True)
    action = Column(String(100), nullable=False, index=True)
    resource_type = Column(String(50), nullable=False, index=True)
    resource_id = Column(String(100), nullable=True, index=True)

    # 详细信息
    description = Column(Text, nullable=True)
    old_values = Column(Text, nullable=True)  # JSON格式的旧值
    new_values = Column(Text, nullable=True)  # JSON格式的新值

    # 请求信息
    ip_address = Column(String(45), nullable=True)  # 支持IPv6
    user_agent = Column(Text, nullable=True)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return (
            f"<AuditLog(id={self.id}, action='{self.action}', "
            f"resource_type='{self.resource_type}')>"
        )
