/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const RectangleHorizontal = createLucideIcon("rectangle-horizontal", [
  ["rect", { width: "20", height: "12", x: "2", y: "6", rx: "2", key: "9lu3g6" }]
]);

export { RectangleHorizontal as default };
//# sourceMappingURL=rectangle-horizontal.js.map
