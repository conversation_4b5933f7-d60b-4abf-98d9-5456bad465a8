{"config": {"configFile": "D:\\二创\\二创短视频分发\\frontend\\playwright.config.ts", "rootDir": "D:/二创/二创短视频分发/frontend/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "login-comprehensive.spec.ts", "file": "login-comprehensive.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "登录页面全面检测", "file": "login-comprehensive.spec.ts", "line": 3, "column": 6, "specs": [{"title": "页面基本元素检测", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 9197, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('h1, h2').filter({ hasText: '系统登录' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1, h2').filter({ hasText: '系统登录' })\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('h1, h2').filter({ hasText: '系统登录' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1, h2').filter({ hasText: '系统登录' })\u001b[22m\n\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:14:70", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 70, "line": 14}, "snippet": "\u001b[0m \u001b[90m 12 |\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 检查主要UI元素\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'系统登录'\u001b[39m }))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 15 |\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 70, "line": 14}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('h1, h2').filter({ hasText: '系统登录' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1, h2').filter({ hasText: '系统登录' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 12 |\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 检查主要UI元素\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'系统登录'\u001b[39m }))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 15 |\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:14:70\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:01:59.126Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-页面基本元素检测-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-页面基本元素检测-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-页面基本元素检测-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 70, "line": 14}}], "status": "unexpected"}], "id": "a26028a4201f41a662a8-0bcad5b3e45c71303fd8", "file": "login-comprehensive.spec.ts", "line": 9, "column": 3}, {"title": "表单验证功能检测", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "timedOut", "duration": 30530, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 24, "line": 36}, "message": "Error: locator.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button[type=\"submit\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 34 |\u001b[39m\n \u001b[90m 35 |\u001b[39m     \u001b[90m// 测试空表单提交\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 36 |\u001b[39m     \u001b[36mawait\u001b[39m submitButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 37 |\u001b[39m\n \u001b[90m 38 |\u001b[39m     \u001b[90m// 检查HTML5验证或自定义验证消息\u001b[39m\n \u001b[90m 39 |\u001b[39m     \u001b[36mconst\u001b[39m emailInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:36:24\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:01:59.130Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-表单验证功能检测-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-表单验证功能检测-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-表单验证功能检测-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a26028a4201f41a662a8-a8dee50a1cce3c10a789", "file": "login-comprehensive.spec.ts", "line": 32, "column": 3}, {"title": "登录功能测试 - 成功场景", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "timedOut", "duration": 30549, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 47, "line": 83}, "message": "Error: locator.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 81 |\u001b[39m\n \u001b[90m 82 |\u001b[39m     \u001b[90m// 填写登录表单\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 83 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 84 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"]'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 85 |\u001b[39m\n \u001b[90m 86 |\u001b[39m     \u001b[90m// 提交表单\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:83:47\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:01:59.219Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-登录功能测试---成功场景-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-登录功能测试---成功场景-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-登录功能测试---成功场景-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a26028a4201f41a662a8-87adc819fe454db796b3", "file": "login-comprehensive.spec.ts", "line": 62, "column": 3}, {"title": "登录功能测试 - 失败场景", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "timedOut", "duration": 30559, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 47, "line": 116}, "message": "Error: locator.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 114 |\u001b[39m\n \u001b[90m 115 |\u001b[39m     \u001b[90m// 填写错误的登录信息\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"]'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'wrongpassword'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m     \u001b[90m// 提交表单\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:116:47\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:01:59.188Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-登录功能测试---失败场景-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-登录功能测试---失败场景-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-登录功能测试---失败场景-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a26028a4201f41a662a8-54694620c692a448d87a", "file": "login-comprehensive.spec.ts", "line": 102, "column": 3}, {"title": "加载状态检测", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "timedOut", "duration": 30566, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 47, "line": 141}, "message": "Error: locator.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 139 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 140 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 141 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 142 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"]'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 143 |\u001b[39m\n \u001b[90m 144 |\u001b[39m     \u001b[90m// 点击提交按钮\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:141:47\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:01:59.238Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-加载状态检测-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-加载状态检测-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-加载状态检测-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a26028a4201f41a662a8-983759a33955ba676695", "file": "login-comprehensive.spec.ts", "line": 130, "column": 3}, {"title": "响应式设计检测", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 9098, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('form')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('form')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('form')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('form')\u001b[22m\n\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:159:40", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 40, "line": 159}, "snippet": "\u001b[0m \u001b[90m 157 |\u001b[39m     \u001b[90m// 测试桌面视图\u001b[39m\n \u001b[90m 158 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 159 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'form'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 160 |\u001b[39m\n \u001b[90m 161 |\u001b[39m     \u001b[90m// 测试平板视图\u001b[39m\n \u001b[90m 162 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m768\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m1024\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 40, "line": 159}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('form')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('form')\u001b[22m\n\n\n\u001b[0m \u001b[90m 157 |\u001b[39m     \u001b[90m// 测试桌面视图\u001b[39m\n \u001b[90m 158 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 159 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'form'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 160 |\u001b[39m\n \u001b[90m 161 |\u001b[39m     \u001b[90m// 测试平板视图\u001b[39m\n \u001b[90m 162 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m768\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m1024\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:159:40\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:01:59.250Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-响应式设计检测-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-响应式设计检测-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-响应式设计检测-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 40, "line": 159}}], "status": "unexpected"}], "id": "a26028a4201f41a662a8-5d08f6a35a9c5a2fee48", "file": "login-comprehensive.spec.ts", "line": 156, "column": 3}, {"title": "键盘导航检测", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 6632, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: locator('input[type=\"email\"]')\nExpected: focused\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: locator('input[type=\"email\"]')\nExpected: focused\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:182:55", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 55, "line": 182}, "snippet": "\u001b[0m \u001b[90m 180 |\u001b[39m     \u001b[90m// 使用Tab键导航\u001b[39m\n \u001b[90m 181 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Tab'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 182 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeFocused()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 183 |\u001b[39m\n \u001b[90m 184 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Tab'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeFocused()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 55, "line": 182}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: locator('input[type=\"email\"]')\nExpected: focused\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 180 |\u001b[39m     \u001b[90m// 使用Tab键导航\u001b[39m\n \u001b[90m 181 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Tab'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 182 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeFocused()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 183 |\u001b[39m\n \u001b[90m 184 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Tab'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeFocused()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:182:55\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:02:09.688Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-键盘导航检测-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-键盘导航检测-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-键盘导航检测-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 55, "line": 182}}], "status": "unexpected"}], "id": "a26028a4201f41a662a8-5d7d999f234cf0eb75f8", "file": "login-comprehensive.spec.ts", "line": 179, "column": 3}, {"title": "安全性检测", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 5, "status": "failed", "duration": 6604, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveAttribute\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('input[type=\"password\"]')\nExpected string: \u001b[32m\"password\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[type=\"password\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveAttribute\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('input[type=\"password\"]')\nExpected string: \u001b[32m\"password\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[type=\"password\"]')\u001b[22m\n\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:202:33", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 33, "line": 202}, "snippet": "\u001b[0m \u001b[90m 200 |\u001b[39m     \u001b[90m// 检查密码字段是否正确隐藏\u001b[39m\n \u001b[90m 201 |\u001b[39m     \u001b[36mconst\u001b[39m passwordInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 202 |\u001b[39m     \u001b[36mawait\u001b[39m expect(passwordInput)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'type'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'password'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 203 |\u001b[39m\n \u001b[90m 204 |\u001b[39m     \u001b[90m// 检查是否有密码显示/隐藏功能\u001b[39m\n \u001b[90m 205 |\u001b[39m     \u001b[36mconst\u001b[39m toggleButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[35m/显示|隐藏|👁/\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 33, "line": 202}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveAttribute\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('input[type=\"password\"]')\nExpected string: \u001b[32m\"password\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[type=\"password\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 200 |\u001b[39m     \u001b[90m// 检查密码字段是否正确隐藏\u001b[39m\n \u001b[90m 201 |\u001b[39m     \u001b[36mconst\u001b[39m passwordInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 202 |\u001b[39m     \u001b[36mawait\u001b[39m expect(passwordInput)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'type'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'password'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 203 |\u001b[39m\n \u001b[90m 204 |\u001b[39m     \u001b[90m// 检查是否有密码显示/隐藏功能\u001b[39m\n \u001b[90m 205 |\u001b[39m     \u001b[36mconst\u001b[39m toggleButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[35m/显示|隐藏|👁/\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:202:33\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:02:09.708Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-安全性检测-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-安全性检测-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-安全性检测-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 33, "line": 202}}], "status": "unexpected"}], "id": "a26028a4201f41a662a8-dce461b676f4a238c5a2", "file": "login-comprehensive.spec.ts", "line": 199, "column": 3}, {"title": "错误处理和用户反馈", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 5, "status": "timedOut", "duration": 30891, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 47, "line": 226}, "message": "Error: locator.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 224 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mroute(\u001b[32m'**/api/v1/auth/login'\u001b[39m\u001b[33m,\u001b[39m route \u001b[33m=>\u001b[39m route\u001b[33m.\u001b[39mabort())\u001b[33m;\u001b[39m\n \u001b[90m 225 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 226 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 227 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"]'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 228 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m 229 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:226:47\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:02:17.634Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-错误处理和用户反馈-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-错误处理和用户反馈-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-错误处理和用户反馈-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a26028a4201f41a662a8-3880d2f70aa329dbcd15", "file": "login-comprehensive.spec.ts", "line": 222, "column": 3}, {"title": "页面性能检测", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "failed", "duration": 5458, "error": {"message": "Error: \u001b[31mTimed out 3000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[type=\"email\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 3000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 3000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[type=\"email\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 3000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:259:55", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 55, "line": 259}, "snippet": "\u001b[0m \u001b[90m 257 |\u001b[39m\n \u001b[90m 258 |\u001b[39m     \u001b[90m// 检查关键元素是否快速显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 259 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m3000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 260 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m3000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 261 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button[type=\"submit\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m3000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 262 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 55, "line": 259}, "message": "Error: \u001b[31mTimed out 3000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[type=\"email\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 3000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 257 |\u001b[39m\n \u001b[90m 258 |\u001b[39m     \u001b[90m// 检查关键元素是否快速显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 259 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m3000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 260 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m3000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 261 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button[type=\"submit\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m3000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 262 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts:259:55\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:02:17.634Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-页面性能检测-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-页面性能检测-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\login-comprehensive-登录页面全面检测-页面性能检测-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\login-comprehensive.spec.ts", "column": 55, "line": 259}}], "status": "unexpected"}], "id": "a26028a4201f41a662a8-b81c3c5a4ccd75b98f92", "file": "login-comprehensive.spec.ts", "line": 244, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-24T02:01:57.565Z", "duration": 51395.886, "expected": 0, "skipped": 0, "unexpected": 10, "flaky": 0}}