{"config": {"configFile": "D:\\二创\\二创短视频分发\\frontend\\playwright.config.ts", "rootDir": "D:/二创/二创短视频分发/frontend/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 1, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "mobile-responsive.spec.js", "file": "mobile-responsive.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "iPhone 15 Pro 响应式测试", "file": "mobile-responsive.spec.js", "line": 63, "column": 8, "specs": [{"title": "首页 - 响应式布局测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 8101, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n    at testUnifiedLayoutResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:192:26)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:96:17", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 192}, "snippet": "\u001b[0m \u001b[90m 190 |\u001b[39m   \u001b[36mif\u001b[39m (device\u001b[33m.\u001b[39misMobile) {\n \u001b[90m 191 |\u001b[39m     \u001b[90m// 移动端布局检查\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 192 |\u001b[39m     \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 193 |\u001b[39m     \n \u001b[90m 194 |\u001b[39m     \u001b[90m// 移动端侧边栏可能隐藏或折叠\u001b[39m\n \u001b[90m 195 |\u001b[39m     \u001b[36mconst\u001b[39m sidebarVisible \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m sidebar\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 192}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n\n\u001b[0m \u001b[90m 190 |\u001b[39m   \u001b[36mif\u001b[39m (device\u001b[33m.\u001b[39misMobile) {\n \u001b[90m 191 |\u001b[39m     \u001b[90m// 移动端布局检查\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 192 |\u001b[39m     \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 193 |\u001b[39m     \n \u001b[90m 194 |\u001b[39m     \u001b[90m// 移动端侧边栏可能隐藏或折叠\u001b[39m\n \u001b[90m 195 |\u001b[39m     \u001b[36mconst\u001b[39m sidebarVisible \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m sidebar\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at testUnifiedLayoutResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:192:26)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:96:17\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:17:00.744Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPhone-15-Pro-响应式测试-首页---响应式布局测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPhone-15-Pro-响应式测试-首页---响应式布局测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPhone-15-Pro-响应式测试-首页---响应式布局测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 192}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-6164654e686a38b50d28", "file": "mobile-responsive.spec.js", "line": 83, "column": 7}, {"title": "登录页面 - 响应式布局测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "failed", "duration": 8110, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n\n    at testLoginPageResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:160:32)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:93:17", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}, "snippet": "\u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[90m// 检查登录容器\u001b[39m\n \u001b[90m 159 |\u001b[39m   \u001b[36mconst\u001b[39m loginContainer \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-container'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 160 |\u001b[39m   \u001b[36mawait\u001b[39m expect(loginContainer)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 161 |\u001b[39m   \n \u001b[90m 162 |\u001b[39m   \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 163 |\u001b[39m   \u001b[36mconst\u001b[39m loginForm \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-form'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n\n\n\u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[90m// 检查登录容器\u001b[39m\n \u001b[90m 159 |\u001b[39m   \u001b[36mconst\u001b[39m loginContainer \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-container'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 160 |\u001b[39m   \u001b[36mawait\u001b[39m expect(loginContainer)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 161 |\u001b[39m   \n \u001b[90m 162 |\u001b[39m   \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 163 |\u001b[39m   \u001b[36mconst\u001b[39m loginForm \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-form'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at testLoginPageResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:160:32)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:93:17\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:17:09.951Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPhone-15-Pro-响应式测试-登录页面---响应式布局测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPhone-15-Pro-响应式测试-登录页面---响应式布局测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPhone-15-Pro-响应式测试-登录页面---响应式布局测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-64551ff61a3f30d04903", "file": "mobile-responsive.spec.js", "line": 83, "column": 7}, {"title": "触摸交互测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "passed", "duration": 1019, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:17:19.257Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d2030d89e881f596fec5-f5abfb20937115435a21", "file": "mobile-responsive.spec.js", "line": 109, "column": 7}, {"title": "横竖屏切换测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "failed", "duration": 7055, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n    at checkPortraitLayout (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:245:24)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:140:15", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 24, "line": 245}, "snippet": "\u001b[0m \u001b[90m 243 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m checkPortraitLayout(page) {\n \u001b[90m 244 |\u001b[39m   \u001b[36mconst\u001b[39m header \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.app-header'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 245 |\u001b[39m   \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 246 |\u001b[39m   \n \u001b[90m 247 |\u001b[39m   \u001b[90m// 竖屏模式下，导航可能折叠\u001b[39m\n \u001b[90m 248 |\u001b[39m   \u001b[36mconst\u001b[39m navItems \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.app-header__nav-item'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 24, "line": 245}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n\n\u001b[0m \u001b[90m 243 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m checkPortraitLayout(page) {\n \u001b[90m 244 |\u001b[39m   \u001b[36mconst\u001b[39m header \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.app-header'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 245 |\u001b[39m   \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 246 |\u001b[39m   \n \u001b[90m 247 |\u001b[39m   \u001b[90m// 竖屏模式下，导航可能折叠\u001b[39m\n \u001b[90m 248 |\u001b[39m   \u001b[36mconst\u001b[39m navItems \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.app-header__nav-item'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at checkPortraitLayout (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:245:24)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:140:15\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:17:20.590Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPhone-15-Pro-响应式测试-横竖屏切换测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPhone-15-Pro-响应式测试-横竖屏切换测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPhone-15-Pro-响应式测试-横竖屏切换测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 24, "line": 245}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-04ac743d5a22b61e286d", "file": "mobile-responsive.spec.js", "line": 128, "column": 7}]}, {"title": "Samsung Galaxy S24 响应式测试", "file": "mobile-responsive.spec.js", "line": 63, "column": 8, "specs": [{"title": "首页 - 响应式布局测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 8090, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n    at testUnifiedLayoutResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:192:26)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:96:17", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 192}, "snippet": "\u001b[0m \u001b[90m 190 |\u001b[39m   \u001b[36mif\u001b[39m (device\u001b[33m.\u001b[39misMobile) {\n \u001b[90m 191 |\u001b[39m     \u001b[90m// 移动端布局检查\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 192 |\u001b[39m     \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 193 |\u001b[39m     \n \u001b[90m 194 |\u001b[39m     \u001b[90m// 移动端侧边栏可能隐藏或折叠\u001b[39m\n \u001b[90m 195 |\u001b[39m     \u001b[36mconst\u001b[39m sidebarVisible \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m sidebar\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 192}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n\n\u001b[0m \u001b[90m 190 |\u001b[39m   \u001b[36mif\u001b[39m (device\u001b[33m.\u001b[39misMobile) {\n \u001b[90m 191 |\u001b[39m     \u001b[90m// 移动端布局检查\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 192 |\u001b[39m     \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 193 |\u001b[39m     \n \u001b[90m 194 |\u001b[39m     \u001b[90m// 移动端侧边栏可能隐藏或折叠\u001b[39m\n \u001b[90m 195 |\u001b[39m     \u001b[36mconst\u001b[39m sidebarVisible \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m sidebar\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at testUnifiedLayoutResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:192:26)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:96:17\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:17:28.644Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Samsung-Galaxy-S24-响应式测试-首页---响应式布局测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Samsung-Galaxy-S24-响应式测试-首页---响应式布局测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Samsung-Galaxy-S24-响应式测试-首页---响应式布局测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 192}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-cafa38019113e3caf552", "file": "mobile-responsive.spec.js", "line": 83, "column": 7}, {"title": "登录页面 - 响应式布局测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 8076, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n\n    at testLoginPageResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:160:32)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:93:17", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}, "snippet": "\u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[90m// 检查登录容器\u001b[39m\n \u001b[90m 159 |\u001b[39m   \u001b[36mconst\u001b[39m loginContainer \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-container'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 160 |\u001b[39m   \u001b[36mawait\u001b[39m expect(loginContainer)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 161 |\u001b[39m   \n \u001b[90m 162 |\u001b[39m   \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 163 |\u001b[39m   \u001b[36mconst\u001b[39m loginForm \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-form'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n\n\n\u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[90m// 检查登录容器\u001b[39m\n \u001b[90m 159 |\u001b[39m   \u001b[36mconst\u001b[39m loginContainer \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-container'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 160 |\u001b[39m   \u001b[36mawait\u001b[39m expect(loginContainer)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 161 |\u001b[39m   \n \u001b[90m 162 |\u001b[39m   \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 163 |\u001b[39m   \u001b[36mconst\u001b[39m loginForm \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-form'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at testLoginPageResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:160:32)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:93:17\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:17:37.982Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Samsung-Galaxy-S24-响应式测试-登录页面---响应式布局测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Samsung-Galaxy-S24-响应式测试-登录页面---响应式布局测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Samsung-Galaxy-S24-响应式测试-登录页面---响应式布局测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-c8c2968c55c5def16391", "file": "mobile-responsive.spec.js", "line": 83, "column": 7}, {"title": "触摸交互测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "passed", "duration": 1030, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:17:47.364Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d2030d89e881f596fec5-196d79d6181ee3220316", "file": "mobile-responsive.spec.js", "line": 109, "column": 7}, {"title": "横竖屏切换测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 7069, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n    at checkPortraitLayout (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:245:24)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:140:15", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 24, "line": 245}, "snippet": "\u001b[0m \u001b[90m 243 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m checkPortraitLayout(page) {\n \u001b[90m 244 |\u001b[39m   \u001b[36mconst\u001b[39m header \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.app-header'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 245 |\u001b[39m   \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 246 |\u001b[39m   \n \u001b[90m 247 |\u001b[39m   \u001b[90m// 竖屏模式下，导航可能折叠\u001b[39m\n \u001b[90m 248 |\u001b[39m   \u001b[36mconst\u001b[39m navItems \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.app-header__nav-item'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 24, "line": 245}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n\n\u001b[0m \u001b[90m 243 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m checkPortraitLayout(page) {\n \u001b[90m 244 |\u001b[39m   \u001b[36mconst\u001b[39m header \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.app-header'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 245 |\u001b[39m   \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 246 |\u001b[39m   \n \u001b[90m 247 |\u001b[39m   \u001b[90m// 竖屏模式下，导航可能折叠\u001b[39m\n \u001b[90m 248 |\u001b[39m   \u001b[36mconst\u001b[39m navItems \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.app-header__nav-item'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at checkPortraitLayout (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:245:24)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:140:15\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:17:48.652Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Samsung-Galaxy-S24-响应式测试-横竖屏切换测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Samsung-Galaxy-S24-响应式测试-横竖屏切换测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Samsung-Galaxy-S24-响应式测试-横竖屏切换测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 24, "line": 245}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-77aa2745ad13cce2df35", "file": "mobile-responsive.spec.js", "line": 128, "column": 7}]}, {"title": "iPad Pro 12.9 响应式测试", "file": "mobile-responsive.spec.js", "line": 63, "column": 8, "specs": [{"title": "首页 - 响应式布局测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 8322, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n    at testUnifiedLayoutResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:221:26)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:96:17", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 221}, "snippet": "\u001b[0m \u001b[90m 219 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 220 |\u001b[39m     \u001b[90m// 桌面端布局检查\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m expect(sidebar)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m expect(main)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 221}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n\n\u001b[0m \u001b[90m 219 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 220 |\u001b[39m     \u001b[90m// 桌面端布局检查\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m expect(sidebar)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m expect(main)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[0m\n\u001b[2m    at testUnifiedLayoutResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:221:26)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:96:17\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:17:56.634Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPad-Pro-12-9-响应式测试-首页---响应式布局测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPad-Pro-12-9-响应式测试-首页---响应式布局测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPad-Pro-12-9-响应式测试-首页---响应式布局测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 221}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-2023b9d4f0ac53e9a4b7", "file": "mobile-responsive.spec.js", "line": 83, "column": 7}, {"title": "登录页面 - 响应式布局测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "failed", "duration": 8253, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n\n    at testLoginPageResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:160:32)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:93:17", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}, "snippet": "\u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[90m// 检查登录容器\u001b[39m\n \u001b[90m 159 |\u001b[39m   \u001b[36mconst\u001b[39m loginContainer \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-container'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 160 |\u001b[39m   \u001b[36mawait\u001b[39m expect(loginContainer)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 161 |\u001b[39m   \n \u001b[90m 162 |\u001b[39m   \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 163 |\u001b[39m   \u001b[36mconst\u001b[39m loginForm \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-form'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n\n\n\u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[90m// 检查登录容器\u001b[39m\n \u001b[90m 159 |\u001b[39m   \u001b[36mconst\u001b[39m loginContainer \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-container'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 160 |\u001b[39m   \u001b[36mawait\u001b[39m expect(loginContainer)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 161 |\u001b[39m   \n \u001b[90m 162 |\u001b[39m   \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 163 |\u001b[39m   \u001b[36mconst\u001b[39m loginForm \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-form'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at testLoginPageResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:160:32)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:93:17\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:18:06.233Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPad-Pro-12-9-响应式测试-登录页面---响应式布局测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPad-Pro-12-9-响应式测试-登录页面---响应式布局测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-iPad-Pro-12-9-响应式测试-登录页面---响应式布局测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-78fc107a8e273414b1f2", "file": "mobile-responsive.spec.js", "line": 83, "column": 7}]}, {"title": "Desktop 1920x1080 响应式测试", "file": "mobile-responsive.spec.js", "line": 63, "column": 8, "specs": [{"title": "首页 - 响应式布局测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 0, "status": "failed", "duration": 8112, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n    at testUnifiedLayoutResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:221:26)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:96:17", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 221}, "snippet": "\u001b[0m \u001b[90m 219 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 220 |\u001b[39m     \u001b[90m// 桌面端布局检查\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m expect(sidebar)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m expect(main)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 221}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n\n\u001b[0m \u001b[90m 219 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 220 |\u001b[39m     \u001b[90m// 桌面端布局检查\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m expect(sidebar)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m expect(main)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[0m\n\u001b[2m    at testUnifiedLayoutResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:221:26)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:96:17\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:18:15.694Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1920x1080-响应式测试-首页---响应式布局测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1920x1080-响应式测试-首页---响应式布局测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1920x1080-响应式测试-首页---响应式布局测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 221}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-4907e49c145b81ee75af", "file": "mobile-responsive.spec.js", "line": 83, "column": 7}, {"title": "登录页面 - 响应式布局测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "failed", "duration": 8092, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n\n    at testLoginPageResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:160:32)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:93:17", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}, "snippet": "\u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[90m// 检查登录容器\u001b[39m\n \u001b[90m 159 |\u001b[39m   \u001b[36mconst\u001b[39m loginContainer \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-container'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 160 |\u001b[39m   \u001b[36mawait\u001b[39m expect(loginContainer)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 161 |\u001b[39m   \n \u001b[90m 162 |\u001b[39m   \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 163 |\u001b[39m   \u001b[36mconst\u001b[39m loginForm \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-form'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n\n\n\u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[90m// 检查登录容器\u001b[39m\n \u001b[90m 159 |\u001b[39m   \u001b[36mconst\u001b[39m loginContainer \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-container'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 160 |\u001b[39m   \u001b[36mawait\u001b[39m expect(loginContainer)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 161 |\u001b[39m   \n \u001b[90m 162 |\u001b[39m   \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 163 |\u001b[39m   \u001b[36mconst\u001b[39m loginForm \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-form'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at testLoginPageResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:160:32)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:93:17\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:18:25.075Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1920x1080-响应式测试-登录页面---响应式布局测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1920x1080-响应式测试-登录页面---响应式布局测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1920x1080-响应式测试-登录页面---响应式布局测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-3c75c5fa16a82486a0ac", "file": "mobile-responsive.spec.js", "line": 83, "column": 7}]}, {"title": "Desktop 1366x768 响应式测试", "file": "mobile-responsive.spec.js", "line": 63, "column": 8, "specs": [{"title": "首页 - 响应式布局测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "failed", "duration": 8058, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n    at testUnifiedLayoutResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:221:26)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:96:17", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 221}, "snippet": "\u001b[0m \u001b[90m 219 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 220 |\u001b[39m     \u001b[90m// 桌面端布局检查\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m expect(sidebar)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m expect(main)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 221}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.app-header')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.app-header')\u001b[22m\n\n\n\u001b[0m \u001b[90m 219 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 220 |\u001b[39m     \u001b[90m// 桌面端布局检查\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m expect(header)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m expect(sidebar)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m expect(main)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[0m\n\u001b[2m    at testUnifiedLayoutResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:221:26)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:96:17\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:18:34.375Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1366x768-响应式测试-首页---响应式布局测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1366x768-响应式测试-首页---响应式布局测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1366x768-响应式测试-首页---响应式布局测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 26, "line": 221}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-56f8bb87f287bccf121e", "file": "mobile-responsive.spec.js", "line": 83, "column": 7}, {"title": "登录页面 - 响应式布局测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 0, "status": "failed", "duration": 8073, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n\n    at testLoginPageResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:160:32)\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:93:17", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}, "snippet": "\u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[90m// 检查登录容器\u001b[39m\n \u001b[90m 159 |\u001b[39m   \u001b[36mconst\u001b[39m loginContainer \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-container'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 160 |\u001b[39m   \u001b[36mawait\u001b[39m expect(loginContainer)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 161 |\u001b[39m   \n \u001b[90m 162 |\u001b[39m   \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 163 |\u001b[39m   \u001b[36mconst\u001b[39m loginForm \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-form'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.login-container')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.login-container')\u001b[22m\n\n\n\u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[90m// 检查登录容器\u001b[39m\n \u001b[90m 159 |\u001b[39m   \u001b[36mconst\u001b[39m loginContainer \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-container'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 160 |\u001b[39m   \u001b[36mawait\u001b[39m expect(loginContainer)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 161 |\u001b[39m   \n \u001b[90m 162 |\u001b[39m   \u001b[90m// 检查表单元素\u001b[39m\n \u001b[90m 163 |\u001b[39m   \u001b[36mconst\u001b[39m loginForm \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.login-form'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at testLoginPageResponsive (D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:160:32)\u001b[22m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js:93:17\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T03:18:43.678Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1366x768-响应式测试-登录页面---响应式布局测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1366x768-响应式测试-登录页面---响应式布局测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\mobile-responsive-Desktop-1366x768-响应式测试-登录页面---响应式布局测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\mobile-responsive.spec.js", "column": 32, "line": 160}}], "status": "unexpected"}], "id": "d2030d89e881f596fec5-f27e9703773ea6e07d3b", "file": "mobile-responsive.spec.js", "line": 83, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-07-24T03:16:59.743Z", "duration": 112365.282, "expected": 2, "skipped": 0, "unexpected": 12, "flaky": 0}}