{"config": {"configFile": "D:\\二创\\二创短视频分发\\frontend\\playwright.config.ts", "rootDir": "D:/二创/二创短视频分发/frontend/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "basic.spec.ts", "file": "basic.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "基础功能测试", "file": "basic.spec.ts", "line": 3, "column": 6, "specs": [{"title": "应用程序可以正常加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2830, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T01:18:02.371Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-6036c35f33c2b9cd5113", "file": "basic.spec.ts", "line": 4, "column": 3}, {"title": "页面基本功能可用", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 2850, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T01:18:02.394Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-bf33774f233c0167cd33", "file": "basic.spec.ts", "line": 15, "column": 3}, {"title": "导航功能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 2903, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T01:18:02.406Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-8437a93a867db86f63ab", "file": "basic.spec.ts", "line": 32, "column": 3}, {"title": "权限页面可以在测试模式下访问", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 6925, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T01:18:02.435Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-7594893a3ce2457f5cc2", "file": "basic.spec.ts", "line": 50, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-24T01:18:01.163Z", "duration": 9390.707, "expected": 4, "skipped": 0, "unexpected": 0, "flaky": 0}}