{"config": {"configFile": "D:\\二创\\二创短视频分发\\frontend\\playwright.config.ts", "rootDir": "D:/二创/二创短视频分发/frontend/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "performance.spec.js", "file": "performance.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "性能优化检查", "file": "performance.spec.js", "line": 28, "column": 6, "specs": [{"title": "Core Web Vitals 测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9049, "errors": [], "stdout": [{"text": "Core Web Vitals: { FCP: \u001b[33m8.089999999850988\u001b[39m, TTI: \u001b[33m3222.109999999404\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:55:18.733Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0395ec5c307b4a8c0388-be6a819a55f2bac010d6", "file": "performance.spec.js", "line": 39, "column": 3}, {"title": "路由切换性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 8737, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m1000\u001b[39m\nReceived:   \u001b[31m1206\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m1000\u001b[39m\nReceived:   \u001b[31m1206\u001b[39m\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\performance.spec.js:146:31", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\performance.spec.js", "column": 31, "line": 146}, "snippet": "\u001b[0m \u001b[90m 144 |\u001b[39m     \n \u001b[90m 145 |\u001b[39m     \u001b[90m// 验证平均切换时间\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 146 |\u001b[39m     expect(avgTransitionTime)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLDS\u001b[39m\u001b[33m.\u001b[39mrouteTransition \u001b[33m*\u001b[39m \u001b[35m2\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 147 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 148 |\u001b[39m\n \u001b[90m 149 |\u001b[39m   test(\u001b[32m'布局重排性能测试'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\performance.spec.js", "column": 31, "line": 146}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m1000\u001b[39m\nReceived:   \u001b[31m1206\u001b[39m\n\n\u001b[0m \u001b[90m 144 |\u001b[39m     \n \u001b[90m 145 |\u001b[39m     \u001b[90m// 验证平均切换时间\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 146 |\u001b[39m     expect(avgTransitionTime)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLDS\u001b[39m\u001b[33m.\u001b[39mrouteTransition \u001b[33m*\u001b[39m \u001b[35m2\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 147 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 148 |\u001b[39m\n \u001b[90m 149 |\u001b[39m   test(\u001b[32m'布局重排性能测试'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\performance.spec.js:146:31\u001b[22m"}], "stdout": [{"text": "视频创作 路由切换时间: 1194ms\n"}, {"text": "计算引擎测试 路由切换时间: 1247ms\n"}, {"text": "个人中心 路由切换时间: 1214ms\n"}, {"text": "首页 路由切换时间: 1169ms\n"}, {"text": "平均路由切换时间: 1206ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:55:18.784Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\performance-性能优化检查-路由切换性能测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\performance-性能优化检查-路由切换性能测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\performance-性能优化检查-路由切换性能测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\performance.spec.js", "column": 31, "line": 146}}], "status": "unexpected"}], "id": "0395ec5c307b4a8c0388-a77db489f08dec1a2008", "file": "performance.spec.js", "line": 102, "column": 3}, {"title": "布局重排性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 7018, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m0.1\u001b[39m\nReceived:   \u001b[31m0.428226323445638\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m0.1\u001b[39m\nReceived:   \u001b[31m0.428226323445638\u001b[39m\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\performance.spec.js:203:24", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\performance.spec.js", "column": 24, "line": 203}, "snippet": "\u001b[0m \u001b[90m 201 |\u001b[39m     \n \u001b[90m 202 |\u001b[39m     \u001b[90m// 验证布局稳定性\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 203 |\u001b[39m     expect(totalShift)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLDS\u001b[39m\u001b[33m.\u001b[39m\u001b[33mCLS\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 204 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 205 |\u001b[39m\n \u001b[90m 206 |\u001b[39m   test(\u001b[32m'内存使用情况测试'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\performance.spec.js", "column": 24, "line": 203}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m0.1\u001b[39m\nReceived:   \u001b[31m0.428226323445638\u001b[39m\n\n\u001b[0m \u001b[90m 201 |\u001b[39m     \n \u001b[90m 202 |\u001b[39m     \u001b[90m// 验证布局稳定性\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 203 |\u001b[39m     expect(totalShift)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLDS\u001b[39m\u001b[33m.\u001b[39m\u001b[33mCLS\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 204 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 205 |\u001b[39m\n \u001b[90m 206 |\u001b[39m   test(\u001b[32m'内存使用情况测试'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\performance.spec.js:203:24\u001b[22m"}], "stdout": [{"text": "布局重排事件: [\n  {\n    value: \u001b[33m0.0021162312825520834\u001b[39m,\n    startTime: \u001b[33m4607.605000000447\u001b[39m,\n    sources: [ \u001b[36m[Object]\u001b[39m ]\n  },\n  {\n    value: \u001b[33m0.0027211791144476994\u001b[39m,\n    startTime: \u001b[33m4624.039999999106\u001b[39m,\n    sources: [ \u001b[36m[Object]\u001b[39m ]\n  },\n  {\n    value: \u001b[33m0.0032534953223334415\u001b[39m,\n    startTime: \u001b[33m4640.1899999994785\u001b[39m,\n    sources: [ \u001b[36m[Object]\u001b[39m ]\n  },\n  {\n    value: \u001b[33m0.003618573718600803\u001b[39m,\n    startTime: \u001b[33m4657.584999999031\u001b[39m,\n    sources: [ \u001b[36m[Object]\u001b[39m ]\n  },\n  {\n    value: \u001b[33m0.0037676080067952475\u001b[39m,\n    startTime: \u001b[33m4673.324999999255\u001b[39m,\n    sources: [ \u001b[36m[Object]\u001b[39m ]\n  },\n  {\n    value: \u001b[33m0.4044034461975098\u001b[39m,\n    startTime: \u001b[33m4691.0800000000745\u001b[39m,\n    sources: [ \u001b[36m[Object]\u001b[39m, \u001b[36m[Object]\u001b[39m ]\n  },\n  {\n    value: \u001b[33m0.0033283600277370876\u001b[39m,\n    startTime: \u001b[33m4706.769999999553\u001b[39m,\n    sources: [ \u001b[36m[Object]\u001b[39m ]\n  },\n  {\n    value: \u001b[33m0.002813022189670139\u001b[39m,\n    startTime: \u001b[33m4724.629999998957\u001b[39m,\n    sources: [ \u001b[36m[Object]\u001b[39m ]\n  },\n  {\n    value: \u001b[33m0.0022044075859917536\u001b[39m,\n    startTime: \u001b[33m4739.929999999702\u001b[39m,\n    sources: [ \u001b[36m[Object]\u001b[39m ]\n  }\n]\n"}, {"text": "总布局偏移: 0.428226323445638\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:55:18.834Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\performance-性能优化检查-布局重排性能测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\performance-性能优化检查-布局重排性能测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\performance-性能优化检查-布局重排性能测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\performance.spec.js", "column": 24, "line": 203}}], "status": "unexpected"}], "id": "0395ec5c307b4a8c0388-59c14e2f9d4bfd77659d", "file": "performance.spec.js", "line": 149, "column": 3}, {"title": "内存使用情况测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 4186, "errors": [], "stdout": [{"text": "初始内存使用: {\n  usedJSHeapSize: \u001b[33m53500000\u001b[39m,\n  totalJSHeapSize: \u001b[33m56800000\u001b[39m,\n  jsHeapSizeLimit: \u001b[33m3760000000\u001b[39m\n}\n"}, {"text": "最终内存使用: {\n  usedJSHeapSize: \u001b[33m53500000\u001b[39m,\n  totalJSHeapSize: \u001b[33m56800000\u001b[39m,\n  jsHeapSizeLimit: \u001b[33m3760000000\u001b[39m\n}\n"}, {"text": "内存增长: 0 bytes\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:55:18.833Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0395ec5c307b4a8c0388-66d3801def48ab6b6aa9", "file": "performance.spec.js", "line": 206, "column": 3}, {"title": "资源加载性能测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 3985, "errors": [], "stdout": [{"text": "总请求数: 1289\n"}, {"text": "总响应数: 1289\n"}, {"text": "大文件资源: [\n  {\n    url: \u001b[32m'http://localhost:3000/node_modules/.vite/deps/@sentry_vue.js?v=50cf79d0'\u001b[39m,\n    status: \u001b[33m200\u001b[39m,\n    size: \u001b[32m'1605369'\u001b[39m,\n    endTime: \u001b[33m1753325719906\u001b[39m\n  },\n  {\n    url: \u001b[32m'http://localhost:3000/node_modules/.vite/deps/lucide-vue-next.js?v=50cf79d0'\u001b[39m,\n    status: \u001b[33m200\u001b[39m,\n    size: \u001b[32m'1235031'\u001b[39m,\n    endTime: \u001b[33m1753325719950\u001b[39m\n  }\n]\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:55:18.873Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0395ec5c307b4a8c0388-4d382ba6855b1d3c982f", "file": "performance.spec.js", "line": 256, "column": 3}, {"title": "渲染性能测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 4925, "errors": [], "stdout": [{"text": "滚动性能: {\n  fps: \u001b[33m61.781468648535764\u001b[39m,\n  duration: \u001b[33m971.1649999991059\u001b[39m,\n  frameCount: \u001b[33m60\u001b[39m\n}\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T02:55:18.920Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0395ec5c307b4a8c0388-b442dbe3bcefc4097667", "file": "performance.spec.js", "line": 301, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-24T02:55:17.332Z", "duration": 10852.974, "expected": 4, "skipped": 0, "unexpected": 2, "flaky": 0}}