{"config": {"configFile": "D:\\二创\\二创短视频分发\\frontend\\playwright.config.ts", "rootDir": "D:/二创/二创短视频分发/frontend/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 3}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "chromium", "name": "chromium", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "firefox", "name": "firefox", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "webkit", "name": "webkit", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/二创/二创短视频分发/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/二创/二创短视频分发/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "basic.spec.ts", "file": "basic.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "基础功能测试", "file": "basic.spec.ts", "line": 3, "column": 6, "specs": [{"title": "应用程序可以正常加载", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 7099, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/AI视频内容创作系统/\u001b[39m\nReceived string:  \u001b[31m\"首页 - AI视频创作系统\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 5000ms\u001b[22m\n\u001b[2m    9 × unexpected value \"首页 - AI视频创作系统\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/AI视频内容创作系统/\u001b[39m\nReceived string:  \u001b[31m\"首页 - AI视频创作系统\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 5000ms\u001b[22m\n\u001b[2m    9 × unexpected value \"首页 - AI视频创作系统\"\u001b[22m\n\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts:8:24", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts", "column": 24, "line": 8}, "snippet": "\u001b[0m \u001b[90m  6 |\u001b[39m\n \u001b[90m  7 |\u001b[39m     \u001b[90m// 检查页面是否成功加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/AI视频内容创作系统/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  9 |\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 检查页面内容是否存在\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'AI视频内容创作系统'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts", "column": 24, "line": 8}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/AI视频内容创作系统/\u001b[39m\nReceived string:  \u001b[31m\"首页 - AI视频创作系统\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 5000ms\u001b[22m\n\u001b[2m    9 × unexpected value \"首页 - AI视频创作系统\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  6 |\u001b[39m\n \u001b[90m  7 |\u001b[39m     \u001b[90m// 检查页面是否成功加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/AI视频内容创作系统/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  9 |\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 检查页面内容是否存在\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'AI视频内容创作系统'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts:8:24\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T01:08:12.183Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\basic-基础功能测试-应用程序可以正常加载-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\basic-基础功能测试-应用程序可以正常加载-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\basic-基础功能测试-应用程序可以正常加载-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts", "column": 24, "line": 8}}], "status": "unexpected"}], "id": "8946741c555a0c4515d8-6036c35f33c2b9cd5113", "file": "basic.spec.ts", "line": 4, "column": 3}, {"title": "页面基本功能可用", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 7061, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=图片生成')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=图片生成')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=图片生成')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=图片生成')\u001b[22m\n\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts:23:45", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts", "column": 45, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \u001b[90m// 检查功能卡片\u001b[39m\n \u001b[90m 22 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=脚本生成'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=图片生成'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=视频合成'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[90m// 检查测试按钮\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts", "column": 45, "line": 23}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=图片生成')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=图片生成')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \u001b[90m// 检查功能卡片\u001b[39m\n \u001b[90m 22 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=脚本生成'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=图片生成'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=视频合成'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m\n \u001b[90m 26 |\u001b[39m     \u001b[90m// 检查测试按钮\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts:23:45\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T01:08:12.181Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\basic-基础功能测试-页面基本功能可用-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\basic-基础功能测试-页面基本功能可用-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\basic-基础功能测试-页面基本功能可用-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts", "column": 45, "line": 23}}], "status": "unexpected"}], "id": "8946741c555a0c4515d8-bf33774f233c0167cd33", "file": "basic.spec.ts", "line": 15, "column": 3}, {"title": "工作流页面可访问", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 11976, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=AI视频内容创作工作流') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=AI视频内容创作工作流') to be visible\u001b[22m\n\n    at D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts:34:16", "location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts", "column": 16, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m\n \u001b[90m 33 |\u001b[39m     \u001b[90m// 等待页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'text=AI视频内容创作工作流'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m     \u001b[90m// 检查工作流步骤 - 使用first()避免重复元素问题\u001b[39m\n \u001b[90m 37 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=视频上传与处理'\u001b[39m)\u001b[33m.\u001b[39mfirst())\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts", "column": 16, "line": 34}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=AI视频内容创作工作流') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 32 |\u001b[39m\n \u001b[90m 33 |\u001b[39m     \u001b[90m// 等待页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'text=AI视频内容创作工作流'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m     \u001b[90m// 检查工作流步骤 - 使用first()避免重复元素问题\u001b[39m\n \u001b[90m 37 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=视频上传与处理'\u001b[39m)\u001b[33m.\u001b[39mfirst())\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts:34:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T01:08:12.182Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\basic-基础功能测试-工作流页面可访问-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\basic-基础功能测试-工作流页面可访问-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\二创\\二创短视频分发\\frontend\\test-results\\basic-基础功能测试-工作流页面可访问-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\二创\\二创短视频分发\\frontend\\e2e\\basic.spec.ts", "column": 16, "line": 34}}], "status": "unexpected"}], "id": "8946741c555a0c4515d8-20bec977181ac39590a5", "file": "basic.spec.ts", "line": 30, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-24T01:08:11.005Z", "duration": 13476.84, "expected": 0, "skipped": 0, "unexpected": 3, "flaky": 0}}