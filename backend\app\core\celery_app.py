"""
Celery配置
"""

from celery import Celery

from app.core.config import settings

# 创建Celery实例
celery_app = Celery(
    "ai_video_system",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.REDIS_URL,
    include=["app.tasks.content_tasks", "app.tasks.distribution_tasks"],
)

# 简化Celery配置以避免兼容性问题
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=False,  # 禁用任务追踪以避免trace错误
    worker_prefetch_multiplier=1,
)
