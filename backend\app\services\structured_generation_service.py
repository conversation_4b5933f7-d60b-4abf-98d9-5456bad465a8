"""
结构化生成服务 - 基于outlines开源项目
GitHub: https://github.com/outlines-dev/outlines
用途: 确保LLM输出符合指定格式，提升生成质量和一致性
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List

import outlines
from pydantic import BaseModel, Field

# 导入缓存装饰器
from app.services.cache_service import cached


class ContentStructure(BaseModel):
    """内容结构化模式"""

    title: str = Field(description="标题")
    summary: str = Field(description="摘要")
    key_points: List[str] = Field(description="关键点列表")
    style: str = Field(description="风格类型")
    word_count: int = Field(description="字数")
    tags: List[str] = Field(description="标签列表")


class ComplianceResult(BaseModel):
    """合规检测结果模式"""

    is_compliant: bool = Field(description="是否合规")
    risk_level: str = Field(description="风险等级: low/medium/high")
    quality_score: int = Field(ge=1, le=10, description="质量评分1-10")
    issues: List[str] = Field(description="问题列表")
    suggestions: List[str] = Field(description="改进建议")


class VideoScript(BaseModel):
    """视频脚本结构"""

    scenes: List[Dict[str, str]] = Field(description="场景列表")
    narration: str = Field(description="旁白文本")
    duration_estimate: int = Field(description="预估时长(秒)")
    style_keywords: List[str] = Field(description="风格关键词")


class StructuredGenerationService:
    """基于outlines的结构化生成服务"""

    def __init__(self):
        """初始化结构化生成服务"""
        self.generators = {}
        self.models_loaded = False

        # 支持的结构化模式
        self.supported_schemas = {
            "content": ContentStructure,
            "compliance": ComplianceResult,
            "video_script": VideoScript,
        }

    async def initialize_generators(
        self, model_name: str = "microsoft/DialoGPT-medium"
    ):
        """
        初始化生成器

        Args:
            model_name: 模型名称
        """
        try:
            print(f"正在初始化结构化生成器: {model_name}")

            # 加载模型
            model = outlines.models.transformers(model_name)

            # 为每种模式创建生成器
            for schema_name, schema_class in self.supported_schemas.items():
                generator = outlines.generate.json(model, schema_class)
                self.generators[schema_name] = generator
                print(f"✅ {schema_name} 生成器初始化完成")

            self.models_loaded = True

            return {
                "success": True,
                "model": model_name,
                "schemas_loaded": list(self.supported_schemas.keys()),
                "message": "结构化生成器初始化完成",
            }

        except Exception as e:
            print(f"❌ 生成器初始化失败: {str(e)}")
            return {"success": False, "error": str(e), "message": "生成器初始化失败"}

    @cached(expiration=3600, service="structured_generation")
    async def generate_structured_content(
        self, prompt: str, schema_type: str = "content", max_retries: int = 3
    ) -> Dict[str, Any]:
        """
        生成结构化内容

        Args:
            prompt: 输入提示
            schema_type: 结构化模式类型
            max_retries: 最大重试次数

        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            if not self.models_loaded:
                init_result = await self.initialize_generators()
                if not init_result["success"]:
                    return {
                        "success": False,
                        "error": "模型未初始化",
                        "message": "请先初始化生成器",
                    }

            if schema_type not in self.generators:
                return {
                    "success": False,
                    "error": f"不支持的模式类型: {schema_type}",
                    "available_types": list(self.generators.keys()),
                }

            generator = self.generators[schema_type]

            # 生成结构化输出
            for attempt in range(max_retries):
                try:
                    result = generator(prompt)

                    # 验证结果
                    if isinstance(result, dict):
                        return {
                            "success": True,
                            "result": result,
                            "schema_type": schema_type,
                            "attempt": attempt + 1,
                            "timestamp": datetime.now().isoformat(),
                        }
                    else:
                        raise ValueError("生成结果格式不正确")

                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    print(f"第{attempt + 1}次尝试失败，重试中...")
                    await asyncio.sleep(1)

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "schema_type": schema_type,
                "message": "结构化生成失败",
            }

    async def rewrite_with_structure(
        self,
        original_content: str,
        target_style: str = "professional",
        include_analysis: bool = True,
    ) -> Dict[str, Any]:
        """
        使用结构化输出改写内容

        Args:
            original_content: 原始内容
            target_style: 目标风格
            include_analysis: 是否包含分析

        Returns:
            Dict[str, Any]: 改写结果
        """
        try:
            prompt = f"""
请将以下内容改写为{target_style}风格，并按照指定JSON格式输出：

原始内容：{original_content}

要求：
1. 保持核心信息不变
2. 调整语言风格为{target_style}
3. 提取3-5个关键点
4. 生成简洁的摘要
5. 统计字数
6. 添加相关标签

请严格按照JSON schema格式输出结果。
"""

            result = await self.generate_structured_content(prompt, "content")

            if result["success"]:
                structured_result = result["result"]

                # 添加额外分析
                if include_analysis:
                    analysis = self._analyze_content_quality(
                        original_content, structured_result
                    )
                    structured_result["analysis"] = analysis

                return {
                    "success": True,
                    "original_content": original_content,
                    "rewritten_content": structured_result,
                    "target_style": target_style,
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                return result

        except Exception as e:
            return {"success": False, "error": str(e), "message": "结构化改写失败"}

    async def generate_compliance_report(
        self, content: str, platform: str = "general"
    ) -> Dict[str, Any]:
        """
        生成合规检测报告

        Args:
            content: 待检测内容
            platform: 目标平台

        Returns:
            Dict[str, Any]: 合规报告
        """
        try:
            prompt = f"""
请分析以下内容在{platform}平台的合规性，并按照指定JSON格式输出：

内容：{content}

分析维度：
1. 是否包含敏感信息
2. 是否适合公开传播
3. 内容质量评分（1-10分）
4. 具体问题列表
5. 改进建议

请严格按照JSON schema格式输出结果。
"""

            result = await self.generate_structured_content(prompt, "compliance")

            if result["success"]:
                compliance_result = result["result"]

                return {
                    "success": True,
                    "content": content,
                    "platform": platform,
                    "compliance_report": compliance_result,
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                return result

        except Exception as e:
            return {"success": False, "error": str(e), "message": "合规检测失败"}

    async def generate_video_script(
        self, content: str, style: str = "engaging", duration_target: int = 60
    ) -> Dict[str, Any]:
        """
        生成视频脚本

        Args:
            content: 内容文本
            style: 视频风格
            duration_target: 目标时长(秒)

        Returns:
            Dict[str, Any]: 视频脚本
        """
        try:
            prompt = f"""
基于以下内容生成{style}风格的视频脚本，目标时长{duration_target}秒：

内容：{content}

要求：
1. 将内容分解为多个场景
2. 为每个场景提供描述和时长
3. 生成连贯的旁白文本
4. 估算总时长
5. 提供风格关键词

请严格按照JSON schema格式输出结果。
"""

            result = await self.generate_structured_content(prompt, "video_script")

            if result["success"]:
                script_result = result["result"]

                return {
                    "success": True,
                    "original_content": content,
                    "video_script": script_result,
                    "style": style,
                    "duration_target": duration_target,
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                return result

        except Exception as e:
            return {"success": False, "error": str(e), "message": "视频脚本生成失败"}

    def _analyze_content_quality(
        self, original: str, structured: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        分析内容质量

        Args:
            original: 原始内容
            structured: 结构化内容

        Returns:
            Dict[str, Any]: 质量分析
        """
        analysis = {
            "original_length": len(original),
            "processed_length": len(
                structured.get("title", "") + structured.get("summary", "")
            ),
            "key_points_count": len(structured.get("key_points", [])),
            "tags_count": len(structured.get("tags", [])),
            "structure_completeness": 0.0,
        }

        # 计算结构完整性
        required_fields = ["title", "summary", "key_points", "style", "tags"]
        completed_fields = sum(1 for field in required_fields if structured.get(field))
        completeness = completed_fields / len(required_fields)
        analysis["structure_completeness"] = completeness

        # 质量评估
        quality_factors = [
            analysis["structure_completeness"],
            min(analysis["key_points_count"] / 5, 1.0),  # 理想5个关键点
            min(analysis["tags_count"] / 3, 1.0),  # 理想3个标签
            min(analysis["processed_length"] / 200, 1.0),  # 理想200字
        ]

        analysis["quality_score"] = sum(quality_factors) / len(quality_factors)

        return analysis

    async def batch_generate(
        self, prompts: List[Dict[str, str]], max_concurrent: int = 3
    ) -> Dict[str, Any]:
        """
        批量生成结构化内容

        Args:
            prompts: 提示列表，格式：[{"prompt": "...", "schema_type": "..."}]
            max_concurrent: 最大并发数

        Returns:
            Dict[str, Any]: 批量生成结果
        """
        results = []
        successful = 0
        failed = 0

        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(max_concurrent)

        async def generate_single(prompt_data):
            async with semaphore:
                result = await self.generate_structured_content(
                    prompt_data["prompt"], prompt_data.get("schema_type", "content")
                )
                return {
                    "index": prompt_data.get("index", 0),
                    "prompt": prompt_data["prompt"],
                    **result,
                }

        # 添加索引
        for i, prompt_data in enumerate(prompts):
            prompt_data["index"] = i

        # 并发执行
        tasks = [generate_single(prompt_data) for prompt_data in prompts]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append({"success": False, "error": str(result)})
                failed += 1
            else:
                processed_results.append(result)
                if result.get("success"):
                    successful += 1
                else:
                    failed += 1

        return {
            "success": True,
            "results": processed_results,
            "total": len(prompts),
            "successful": successful,
            "failed": failed,
            "success_rate": successful / len(prompts) if prompts else 0,
            "timestamp": datetime.now().isoformat(),
        }

    async def check_system_status(self) -> Dict[str, Any]:
        """检查系统状态"""
        status = {
            "models_loaded": self.models_loaded,
            "available_generators": list(self.generators.keys()),
            "supported_schemas": list(self.supported_schemas.keys()),
        }

        # 检查依赖
        try:
            import outlines

            status["outlines_available"] = True
            status["outlines_version"] = getattr(outlines, "__version__", "unknown")
        except ImportError:
            status["outlines_available"] = False
            status["outlines_error"] = "outlines not installed"

        return {"status": status, "timestamp": datetime.now().isoformat()}


# 示例使用
async def test_structured_generation():
    """测试结构化生成服务"""
    service = StructuredGenerationService()

    # 检查系统状态
    status = await service.check_system_status()
    print("系统状态:", status)

    # 测试内容改写 (示例)
    # test_content = "这是一个关于AI技术发展的重要内容，需要进行专业化改写。"
    # result = await service.rewrite_with_structure(
    #     test_content, "professional"
    # )
    # print("改写结果:", result)


if __name__ == "__main__":
    asyncio.run(test_structured_generation())
