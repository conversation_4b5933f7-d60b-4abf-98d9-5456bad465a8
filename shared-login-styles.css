/* 
 * 统一登录页面样式
 * 前后端共享的登录界面设计
 */

/* 引入统一设计系统 */
@import url('/shared-ui-design.css');

/* ========== 登录页面布局 ========== */
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  padding: var(--space-4);
  position: relative;
  overflow: hidden;
}

.login-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.login-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-10);
  width: 100%;
  max-width: 420px;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ========== 登录头部 ========== */
.login-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.login-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.login-brand-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-xl);
  box-shadow: var(--shadow-md);
}

.login-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
}

.login-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: var(--space-2) 0 0 0;
}

/* ========== 用户类型选择 ========== */
.user-type-selector {
  display: flex;
  background: var(--gray-100);
  border-radius: var(--radius-lg);
  padding: var(--space-1);
  margin-bottom: var(--space-6);
  position: relative;
}

.user-type-option {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  text-align: center;
  border-radius: var(--radius-md);
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  z-index: 2;
  border: none;
  background: transparent;
  font-size: var(--text-sm);
}

.user-type-option.active {
  color: var(--primary-700);
  background: white;
  box-shadow: var(--shadow-sm);
}

.user-type-option:hover:not(.active) {
  color: var(--text-primary);
}

/* ========== 表单样式 ========== */
.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-label {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.form-label.required::after {
  content: ' *';
  color: var(--error-500);
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  color: var(--text-primary);
  background: white;
  transition: all var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled {
  background: var(--gray-50);
  color: var(--text-tertiary);
  cursor: not-allowed;
}

.form-input.error {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.password-toggle {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.password-toggle:hover {
  color: var(--text-primary);
}

/* ========== 错误提示 ========== */
.error-message {
  font-size: var(--text-sm);
  color: var(--error-500);
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.error-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.error-alert {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.error-alert .error-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.clear-error-btn {
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  font-size: var(--text-lg);
  padding: 0;
  margin-left: auto;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.clear-error-btn:hover {
  background: rgba(220, 38, 38, 0.1);
}

/* ========== 记住我选项 ========== */
.remember-me {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.remember-me input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  accent-color: var(--primary-500);
}

/* ========== 登录按钮 ========== */
.login-button {
  width: 100%;
  padding: var(--space-4);
  background: var(--primary-600);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  position: relative;
  overflow: hidden;
}

.login-button:hover:not(:disabled) {
  background: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  background: var(--gray-300);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.login-button.loading {
  color: transparent;
}

.login-button .loading-spinner {
  position: absolute;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========== 快速登录 ========== */
.quick-login {
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

.quick-login-title {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--space-3);
}

.quick-login-buttons {
  display: flex;
  gap: var(--space-2);
}

.quick-login-btn {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  background: var(--gray-100);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.quick-login-btn:hover {
  background: var(--gray-200);
  color: var(--text-primary);
}

/* ========== 页脚链接 ========== */
.login-footer {
  margin-top: var(--space-6);
  text-align: center;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.login-footer a {
  color: var(--primary-600);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.login-footer a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

/* ========== 响应式设计 ========== */
@media (max-width: 480px) {
  .login-container {
    padding: var(--space-6);
    margin: var(--space-4);
  }
  
  .login-title {
    font-size: var(--text-xl);
  }
  
  .user-type-selector {
    flex-direction: column;
    gap: var(--space-1);
  }
  
  .quick-login-buttons {
    flex-direction: column;
  }
}

/* ========== 安全指示器 ========== */
.security-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-top: var(--space-4);
  padding: var(--space-2);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.security-icon {
  width: 0.875rem;
  height: 0.875rem;
  color: var(--success-500);
}

/* ========== 动画效果 ========== */
.login-container {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-group {
  animation: fadeIn 0.3s ease-out;
  animation-fill-mode: both;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
