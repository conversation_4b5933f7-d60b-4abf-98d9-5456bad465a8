import { test, expect } from '@playwright/test';

test.describe('基础功能测试', () => {
  test('应用程序可以正常加载', async ({ page }) => {
    await page.goto('/?test=true');

    // 检查页面是否成功加载 - 更新为实际的页面标题
    await expect(page).toHaveTitle(/首页 - AI视频创作系统/);

    // 检查页面内容是否存在 - 使用更精确的选择器
    await expect(page.locator('main h1').first()).toContainText('AI视频创作系统');
    await expect(page.locator('text=基于先进的AI技术，为您提供专业的视频内容创作解决方案')).toBeVisible();
  });

  test('页面基本功能可用', async ({ page }) => {
    await page.goto('/?test=true');

    // 等待页面核心内容而不是网络空闲
    await page.waitForSelector('main h1', { timeout: 10000 });

    // 检查实际的功能卡片（使用更精确的选择器）
    await expect(page.locator('.feature-card__title:has-text("智能视频生成")')).toBeVisible();
    await expect(page.locator('.feature-card__title:has-text("计算引擎")')).toBeVisible();
    await expect(page.locator('.feature-card__title:has-text("AI增强")')).toBeVisible();

    // 检查功能按钮
    await expect(page.locator('text=开始创作')).toBeVisible();
    await expect(page.locator('text=测试引擎')).toBeVisible();
    await expect(page.locator('text=即将推出')).toBeVisible();
  });

  test('导航功能测试', async ({ page }) => {
    await page.goto('/?test=true');

    // 等待页面加载
    await page.waitForSelector('main h1', { timeout: 10000 });

    // 检查导航链接是否存在（使用更精确的选择器）
    await expect(page.locator('main a[href="/video-creation"]').first()).toBeVisible();
    await expect(page.locator('main a[href="/compute-test"]').first()).toBeVisible();

    // 检查页面响应式设计
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('main h1').first()).toBeVisible();

    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('main h1').first()).toBeVisible();
  });

  test('权限页面可以在测试模式下访问', async ({ page }) => {
    // 测试需要权限的页面在测试模式下可以访问
    await page.goto('/video-creation?test=true');

    // 等待页面加载，不应该重定向到登录页
    await page.waitForTimeout(2000);

    // 检查是否成功访问了视频创作页面而不是被重定向
    expect(page.url()).toContain('/video-creation');

    // 测试计算引擎页面
    await page.goto('/compute-test?test=true');
    await page.waitForTimeout(2000);
    expect(page.url()).toContain('/compute-test');
  });
});
