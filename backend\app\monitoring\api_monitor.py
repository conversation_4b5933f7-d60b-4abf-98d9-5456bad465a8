"""
API性能监控模块
追踪API响应时间、成功率和错误统计
"""

import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import logging
from functools import wraps

logger = logging.getLogger(__name__)


@dataclass
class APIMetrics:
    """API性能指标"""

    endpoint: str
    method: str
    timestamp: str
    response_time: float
    status_code: int
    success: bool
    error_message: Optional[str] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None


@dataclass
class EndpointStats:
    """端点统计信息"""

    endpoint: str
    total_requests: int
    success_count: int
    error_count: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    success_rate: float
    last_24h_requests: int
    error_rate_trend: str


class APIPerformanceMonitor:
    """API性能监控器"""

    def __init__(self):
        self.metrics_history = deque(maxlen=10000)  # 保持最近10000条记录
        self.endpoint_stats = defaultdict(list)
        self.alert_thresholds = {
            "response_time": 1000.0,  # 毫秒
            "error_rate": 5.0,  # 百分比
            "requests_per_minute": 1000,
        }

    def record_request(
        self,
        endpoint: str,
        method: str,
        response_time: float,
        status_code: int,
        error_message: Optional[str] = None,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
    ):
        """记录API请求"""

        metrics = APIMetrics(
            endpoint=endpoint,
            method=method,
            timestamp=datetime.now().isoformat(),
            response_time=response_time,
            status_code=status_code,
            success=200 <= status_code < 400,
            error_message=error_message,
            user_agent=user_agent,
            ip_address=ip_address,
        )

        self.metrics_history.append(metrics)
        self.endpoint_stats[endpoint].append(metrics)

        # 保持每个端点最近1000条记录
        if len(self.endpoint_stats[endpoint]) > 1000:
            self.endpoint_stats[endpoint] = self.endpoint_stats[endpoint][-1000:]

    def get_endpoint_statistics(self, endpoint: str, hours: int = 24) -> EndpointStats:
        """获取端点统计信息"""

        if endpoint not in self.endpoint_stats:
            return EndpointStats(
                endpoint=endpoint,
                total_requests=0,
                success_count=0,
                error_count=0,
                avg_response_time=0.0,
                min_response_time=0.0,
                max_response_time=0.0,
                success_rate=0.0,
                last_24h_requests=0,
                error_rate_trend="stable",
            )

        # 获取指定时间范围内的数据
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [
            m
            for m in self.endpoint_stats[endpoint]
            if datetime.fromisoformat(m.timestamp) > cutoff_time
        ]

        if not recent_metrics:
            return EndpointStats(
                endpoint=endpoint,
                total_requests=0,
                success_count=0,
                error_count=0,
                avg_response_time=0.0,
                min_response_time=0.0,
                max_response_time=0.0,
                success_rate=0.0,
                last_24h_requests=0,
                error_rate_trend="stable",
            )

        # 计算统计信息
        total_requests = len(recent_metrics)
        success_count = sum(1 for m in recent_metrics if m.success)
        error_count = total_requests - success_count

        response_times = [m.response_time for m in recent_metrics]
        avg_response_time = sum(response_times) / len(response_times)
        min_response_time = min(response_times)
        max_response_time = max(response_times)

        success_rate = (
            (success_count / total_requests) * 100 if total_requests > 0 else 0
        )

        # 计算错误率趋势
        error_rate_trend = self._calculate_error_trend(recent_metrics)

        return EndpointStats(
            endpoint=endpoint,
            total_requests=total_requests,
            success_count=success_count,
            error_count=error_count,
            avg_response_time=round(avg_response_time, 2),
            min_response_time=round(min_response_time, 2),
            max_response_time=round(max_response_time, 2),
            success_rate=round(success_rate, 2),
            last_24h_requests=total_requests,
            error_rate_trend=error_rate_trend,
        )

    def _calculate_error_trend(self, metrics: List[APIMetrics]) -> str:
        """计算错误率趋势"""
        if len(metrics) < 10:
            return "insufficient_data"

        # 分为前半部分和后半部分
        mid_point = len(metrics) // 2
        first_half = metrics[:mid_point]
        second_half = metrics[mid_point:]

        first_error_rate = (
            sum(1 for m in first_half if not m.success) / len(first_half)
        ) * 100
        second_error_rate = (
            sum(1 for m in second_half if not m.success) / len(second_half)
        ) * 100

        if second_error_rate > first_error_rate + 2:
            return "increasing"
        elif second_error_rate < first_error_rate - 2:
            return "decreasing"
        else:
            return "stable"

    def get_all_endpoints_summary(self) -> Dict[str, Any]:
        """获取所有端点的汇总信息"""
        summary = {}

        for endpoint in self.endpoint_stats.keys():
            stats = self.get_endpoint_statistics(endpoint)
            summary[endpoint] = asdict(stats)

        return summary

    def check_performance_alerts(self) -> List[str]:
        """检查性能告警"""
        alerts = []

        for endpoint in self.endpoint_stats.keys():
            stats = self.get_endpoint_statistics(endpoint, hours=1)  # 最近1小时

            # 检查响应时间
            if stats.avg_response_time > self.alert_thresholds["response_time"]:
                alerts.append(
                    f"端点 {endpoint} 平均响应时间过高: {stats.avg_response_time:.2f}ms"
                )

            # 检查错误率
            error_rate = (
                (
                    (stats.total_requests - stats.success_count)
                    / stats.total_requests
                    * 100
                )
                if stats.total_requests > 0
                else 0
            )
            if error_rate > self.alert_thresholds["error_rate"]:
                alerts.append(f"端点 {endpoint} 错误率过高: {error_rate:.2f}%")

            # 检查错误率趋势
            if stats.error_rate_trend == "increasing":
                alerts.append(f"端点 {endpoint} 错误率呈上升趋势")

        return alerts

    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""

        # 总体统计
        total_requests = len(self.metrics_history)
        if total_requests == 0:
            return {"error": "没有可用的API性能数据"}

        success_requests = sum(1 for m in self.metrics_history if m.success)
        avg_response_time = (
            sum(m.response_time for m in self.metrics_history) / total_requests
        )

        # 最近24小时统计
        cutoff_time = datetime.now() - timedelta(hours=24)
        recent_metrics = [
            m
            for m in self.metrics_history
            if datetime.fromisoformat(m.timestamp) > cutoff_time
        ]

        # 端点排行
        endpoint_requests = defaultdict(int)
        for metrics in recent_metrics:
            endpoint_requests[metrics.endpoint] += 1

        top_endpoints = sorted(
            endpoint_requests.items(), key=lambda x: x[1], reverse=True
        )[:10]

        # 错误统计
        error_metrics = [m for m in recent_metrics if not m.success]
        error_by_status = defaultdict(int)
        for error in error_metrics:
            error_by_status[error.status_code] += 1

        return {
            "report_time": datetime.now().isoformat(),
            "total_statistics": {
                "total_requests": total_requests,
                "success_requests": success_requests,
                "success_rate": round((success_requests / total_requests) * 100, 2),
                "avg_response_time": round(avg_response_time, 2),
            },
            "last_24h_statistics": {
                "total_requests": len(recent_metrics),
                "unique_endpoints": len(endpoint_requests),
                "avg_requests_per_hour": round(len(recent_metrics) / 24, 2),
            },
            "top_endpoints": top_endpoints,
            "error_statistics": dict(error_by_status),
            "alerts": self.check_performance_alerts(),
            "endpoint_details": self.get_all_endpoints_summary(),
        }

    def save_metrics(self, filepath: str = "metrics/api_metrics.json"):
        """保存指标到文件"""
        try:
            import os

            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            # 转换为可序列化的格式
            metrics_data = [asdict(m) for m in list(self.metrics_history)]

            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(metrics_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"保存API指标失败: {e}")


# 全局监控器实例
api_monitor = APIPerformanceMonitor()


def monitor_api_performance(func):
    """API性能监控装饰器"""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        endpoint = getattr(func, "__name__", "unknown")
        method = "GET"  # 默认方法，可以从request中获取

        try:
            # 执行原函数
            result = await func(*args, **kwargs)

            # 记录成功请求
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒
            api_monitor.record_request(
                endpoint=endpoint,
                method=method,
                response_time=response_time,
                status_code=200,
            )

            return result

        except Exception as e:
            # 记录失败请求
            response_time = (time.time() - start_time) * 1000
            api_monitor.record_request(
                endpoint=endpoint,
                method=method,
                response_time=response_time,
                status_code=500,
                error_message=str(e),
            )
            raise

    return wrapper


def get_api_performance_report():
    """获取API性能报告"""
    return api_monitor.generate_performance_report()


def get_endpoint_stats(endpoint: str):
    """获取特定端点统计"""
    return api_monitor.get_endpoint_statistics(endpoint)
