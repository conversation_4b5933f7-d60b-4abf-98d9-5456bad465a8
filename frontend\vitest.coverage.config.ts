/**
 * Vitest 覆盖率测试配置 - 100%覆盖率目标
 */

import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  test: {
    // 测试环境配置
    environment: 'jsdom',
    
    // 全局设置
    globals: true,
    
    // 设置文件
    setupFiles: ['./src/tests/setup.ts'],
    
    // 测试文件匹配模式 - 只运行覆盖率测试
    include: [
      'tests/unit/basic-coverage.test.ts'
    ],
    
    // 排除文件
    exclude: [
      'node_modules',
      'dist',
      'e2e'
    ],
    
    // 覆盖率配置 - 100%目标
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      
      // 包含所有源文件
      include: [
        'src/**/*.{js,ts,vue}',
        '!src/**/*.d.ts',
        '!src/tests/**',
        '!src/**/*.test.{js,ts}',
        '!src/**/*.spec.{js,ts}'
      ],
      
      // 排除文件
      exclude: [
        'node_modules/',
        'src/tests/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
        '**/dist/**',
        '**/e2e/**',
        'src/main.ts',
        'src/App.vue'
      ],
      
      // 100%覆盖率阈值
      thresholds: {
        global: {
          branches: 100,
          functions: 100,
          lines: 100,
          statements: 100
        }
      },
      
      // 强制覆盖率检查
      skipFull: false,
      all: true
    },
    
    // 测试超时
    testTimeout: 30000,
    hookTimeout: 30000,
    
    // 并发配置
    threads: false, // 禁用多线程以避免竞态条件
    
    // 监听模式配置
    watch: false,
    
    // 报告器配置
    reporters: ['verbose'],
    
    // 模拟配置
    deps: {
      inline: ['@vue', '@vueuse', 'vue-demi']
    },
    
    // 环境变量
    env: {
      NODE_ENV: 'test',
      VITE_APP_ENV: 'test'
    }
  },
  
  // 路径解析
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '~': resolve(__dirname, './src')
    }
  },
  
  // 定义全局变量
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
  }
})
