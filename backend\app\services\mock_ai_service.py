"""
模拟AI服务 - 用于测试和开发环境
当LocalAI服务不可用时提供模拟响应
"""

import asyncio
import random
from datetime import datetime
from typing import Any, Dict, List


class MockAIService:
    """模拟AI服务"""

    def __init__(self):
        self.available_models = [
            "deepseek-coder:7b",
            "deepseek-chat",
            "llama2-chat",
            "stable-video-diffusion",
            "bert-base-chinese",
        ]

    async def health_check(self) -> Dict[str, Any]:
        """模拟健康检查"""
        return {
            "status": "healthy",
            "available_models": self.available_models,
            "service": "MockAI",
            "timestamp": datetime.now().isoformat(),
        }

    async def chat_completion(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 2000,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """模拟聊天完成"""
        # 模拟处理时间
        await self._simulate_delay()

        # 获取用户输入
        user_message = messages[-1]["content"] if messages else ""

        # 生成模拟响应
        response_content = self._generate_mock_response(user_message, model)

        return {
            "success": True,
            "content": response_content,
            "model": model,
            "usage": {
                "prompt_tokens": len(user_message) // 4,
                "completion_tokens": len(response_content) // 4,
                "total_tokens": (len(user_message) + len(response_content)) // 4,
            },
            "finish_reason": "stop",
            "timestamp": datetime.now().isoformat(),
        }

    async def text_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
    ) -> Dict[str, Any]:
        """模拟文本补全"""
        # 模拟处理时间
        await self._simulate_delay()

        # 生成模拟响应
        response_text = self._generate_mock_response(prompt, model)

        return {
            "success": True,
            "text": response_text,
            "model": model,
            "usage": {
                "prompt_tokens": len(prompt) // 4,
                "completion_tokens": len(response_text) // 4,
                "total_tokens": (len(prompt) + len(response_text)) // 4,
            },
            "timestamp": datetime.now().isoformat(),
        }

    def _generate_mock_response(self, input_text: str, model: str) -> str:
        """生成模拟响应内容"""
        responses = {
            "code": [
                """```python
def process_video(video_path):
    \"\"\"处理视频文件\"\"\"
    import cv2
    import numpy as np

    # 读取视频
    cap = cv2.VideoCapture(video_path)
    frames = []

    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)

    cap.release()
    return frames
```""",
                """```javascript
function analyzeContent(content) {
    // 内容分析逻辑
    const keywords = content.split(' ').filter(word => word.length > 3);
    const sentiment = analyzeSentiment(content);

    return {
        keywords: keywords,
        sentiment: sentiment,
        length: content.length,
        wordCount: content.split(' ').length
    };
}
```""",
            ],
            "creative": [
                "🎬 短视频创意方案：\n\n1. 开场：温馨的室内场景，柔和的灯光营造科技感\n2. 主体：展示产品的核心功能，简洁明了\n3. 结尾：call-to-action，引导用户互动\n\n📝 文案建议：\n- 标题：「科技改变生活，温暖触手可及」\n- 描述：融合科技与人文关怀，打造智能生活新体验\n- 标签：#科技生活 #智能家居 #温馨时光",
                "✨ 创意短视频脚本：\n\n【场景1】室内温馨环境\n- 画面：暖色调灯光，现代简约装饰\n- 音效：轻柔的背景音乐\n- 时长：3-5秒\n\n【场景2】产品展示\n- 画面：产品特写，功能演示\n- 文字：核心卖点突出\n- 时长：10-15秒\n\n【场景3】生活场景\n- 画面：真实使用场景\n- 情感：温馨、便捷、科技感\n- 时长：8-12秒",
            ],
            "analysis": [
                "📊 内容分析报告：\n\n✅ 合规性检查：通过\n- 无违规内容\n- 符合平台规范\n- 适合目标受众\n\n📈 质量评估：\n- 内容原创性：85%\n- 观看完成度预估：78%\n- 互动潜力：中等偏上\n\n🎯 优化建议：\n- 增加互动元素\n- 优化开头3秒钩子\n- 加强情感共鸣点",
                "🔍 内容合规分析：\n\n✅ 安全检查：\n- 无敏感信息\n- 内容健康向上\n- 符合社区准则\n\n📝 内容特征：\n- 类型：教育/科技\n- 时长：适中\n- 质量：优秀\n\n💡 推荐指数：⭐⭐⭐⭐\n建议发布，预期表现良好",
            ],
            "general": [
                "感谢您的询问！作为AI助手，我很乐意为您提供帮助。请告诉我您需要什么样的协助，我会尽力为您提供准确、有用的信息。",
                "您好！我是AI助手，专门为短视频创作和内容分析提供支持。无论是创意策划、脚本编写还是内容审核，我都能为您提供专业的建议和解决方案。",
                "根据您的需求，我可以提供多种服务：\n\n🎥 视频创作：脚本编写、创意策划、文案优化\n🔍 内容分析：合规检查、质量评估、数据分析\n💻 技术支持：代码生成、问题解答、方案设计\n\n请告诉我您具体需要哪方面的帮助！",
            ],
        }

        # 根据输入内容类型选择响应
        if (
            "代码" in input_text
            or "函数" in input_text
            or "python" in input_text.lower()
        ):
            return random.choice(responses["code"])
        elif "创意" in input_text or "文案" in input_text or "脚本" in input_text:
            return random.choice(responses["creative"])
        elif "分析" in input_text or "合规" in input_text or "检查" in input_text:
            return random.choice(responses["analysis"])
        else:
            return random.choice(responses["general"])

    async def _simulate_delay(self):
        """模拟AI处理延迟"""
        delay = random.uniform(0.5, 2.0)  # 0.5-2秒随机延迟
        await asyncio.sleep(delay)


# 创建全局实例
mock_ai_service = MockAIService()
