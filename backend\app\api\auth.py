"""
认证API端点
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session

from app.core.auth import (
    auth_service,
    get_current_user_from_token,
    get_password_hash,
    verify_password,
)
from app.core.database import get_db
from app.models import User
from app.schemas import APIResponse, Token, UserCreate, UserResponse, UserUpdate
from app.services.sms_service import sms_service, SMSCodeRequest, SMSCodeVerification
from app.core.secure_exceptions import (  # 🔒 证据链: 导入安全异常处理
    secure_exception_handler,
    handle_exceptions,
    create_auth_error,
    create_validation_error,
    ErrorResponse,
)

# 创建路由器
router = APIRouter(prefix="/auth", tags=["认证"])

# HTTP Bearer认证
security = HTTPBearer()


def get_user_by_username(db: Session, username: str) -> User:
    """根据用户名获取用户"""
    return db.query(User).filter(User.username == username).first()


def get_user_by_email(db: Session, email: str) -> User:
    """根据邮箱获取用户"""
    return db.query(User).filter(User.email == email).first()


def get_user_by_phone(db: Session, phone_number: str) -> User:
    """根据手机号获取用户"""
    return db.query(User).filter(User.phone_number == phone_number).first()


def create_user(db: Session, user_create: UserCreate) -> User:
    """创建新用户"""
    # 检查用户名是否已存在
    if get_user_by_username(db, user_create.username):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="用户名已存在"
        )

    # 检查邮箱是否已存在
    if get_user_by_email(db, user_create.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="邮箱已存在"
        )

    # 检查手机号是否已存在（如果提供了手机号）
    if user_create.phone_number and get_user_by_phone(db, user_create.phone_number):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="手机号已存在"
        )

    # 创建用户
    hashed_password = get_password_hash(user_create.password)
    db_user = User(
        username=user_create.username,
        email=user_create.email,
        hashed_password=hashed_password,
        full_name=user_create.full_name,
        phone_number=user_create.phone_number,  # 添加手机号字段
        bio=user_create.bio,
        avatar_url=user_create.avatar_url,
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    return db_user


def authenticate_user(db: Session, username: str, password: str) -> User:
    """验证用户凭证"""
    user = get_user_by_username(db, username)
    if not user:
        return None

    if not verify_password(password, user.hashed_password):
        return None

    return user


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db),
) -> User:
    """获取当前登录用户"""
    try:
        # 从令牌中提取用户信息
        user_info = get_current_user_from_token(credentials.credentials)
        user_id = user_info["user_id"]

        # 从数据库获取用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="用户不存在"
            )

        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账号已禁用",
            )

        return user

    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.get("/me")
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
) -> UserResponse:
    """获取当前用户信息"""
    return UserResponse.from_orm(current_user)


@router.put("/me")
async def update_current_user(
    update_request: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> UserResponse:
    """更新当前用户信息"""
    try:
        updates = update_request.dict(exclude_unset=True)
        # 更新用户
        for key, value in updates.items():
            setattr(current_user, key, value)
        db.commit()
        db.refresh(current_user)
        return UserResponse.from_orm(current_user)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Update failed: {str(e)}")


@router.post("/register", response_model=APIResponse)
async def register(register_data: dict, db: Session = Depends(get_db)):
    """用户注册"""
    try:
        # 处理前端驼峰命名转换为后端下划线命名
        user_data = {
            "username": register_data.get("username"),
            "email": register_data.get("email"),
            "password": register_data.get("password"),
            "full_name": register_data.get("full_name"),
            "phone_number": register_data.get("phoneNumber"),  # 处理驼峰命名
            "bio": register_data.get("bio"),
            "avatar_url": register_data.get("avatar_url"),
        }

        # 移除None值
        user_data = {k: v for k, v in user_data.items() if v is not None}

        # 创建UserCreate对象
        user_create = UserCreate(**user_data)

        user = create_user(db, user_create)

        return APIResponse(
            success=True, message="注册成功", data=UserResponse.from_orm(user)
        )

    except HTTPException:
        raise
    except Exception as e:
        # 🔒 证据链: 使用安全异常处理，避免信息泄露
        incident_id = secure_exception_handler.log_security_incident(
            e, user_id="registration_attempt"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"注册失败，请稍后重试。事件ID: {incident_id}",
        )


@router.post("/login", response_model=Token)
async def login(login_data: dict, db: Session = Depends(get_db)):
    """用户登录 - 支持密码登录和短信登录"""
    login_method = login_data.get("loginMethod", "password")

    if login_method == "password":
        # 密码登录
        username = login_data.get("username")
        password = login_data.get("password")

        if not username or not password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="用户名和密码不能为空"
            )

        # 验证用户凭证
        user = authenticate_user(db, username, password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )

    elif login_method == "sms":
        # 短信登录
        phone_number = login_data.get("phoneNumber")
        sms_code = login_data.get("smsCode")

        if not phone_number or not sms_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="手机号和验证码不能为空"
            )

        # 验证短信验证码
        verification = SMSCodeVerification(phone_number=phone_number, code=sms_code)
        if not sms_service.verify_code(verification):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="验证码错误"
            )

        user = get_user_by_phone(db, phone_number)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="用户不存在"
            )

    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="不支持的登录方式"
        )

    # 生成访问令牌
    access_token = auth_service.create_access_token(user.id)
    refresh_token = auth_service.create_refresh_token(user.id)

    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
    )


@router.post("/sms-login", response_model=Token)
async def sms_login(sms_login_data: dict, db: Session = Depends(get_db)):
    """短信验证码登录"""
    phone_number = sms_login_data.get("phone_number")
    sms_code = sms_login_data.get("sms_code")

    if not phone_number or not sms_code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="手机号和验证码不能为空"
        )

    # 🔒 证据链: 使用真实短信验证服务替换硬编码验证码
    verification = SMSCodeVerification(
        phone_number=phone_number, code=sms_code, code_type="login"
    )

    verify_result = await sms_service.verify_code(verification)
    if not verify_result["success"]:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=verify_result.get("error", "验证码验证失败"),
        )

    # 查找用户
    user = db.query(User).filter(User.phone_number == phone_number).first()
    if not user:
        # 如果用户不存在，可以选择自动创建
        # 这里暂时返回错误
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="该手机号未注册"
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="账号已禁用"
        )

    # 创建访问令牌
    access_token = auth_service.create_user_token(
        user_id=user.id,
        username=user.username,
        email=user.email,
        role="admin" if user.is_superuser else "user",
    )

    # 更新最后登录时间
    from datetime import datetime

    user.last_login = datetime.utcnow()
    db.commit()

    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=auth_service.access_token_expire_minutes * 60,
        user=UserResponse.from_orm(user),
    )


@router.post("/send-sms")
async def send_sms_hyphen(sms_data: dict, db: Session = Depends(get_db)):
    """发送短信验证码（带连字符的端点）"""
    phone_number = sms_data.get("phoneNumber")
    sms_type = sms_data.get("type", "login")

    if not phone_number:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="手机号不能为空"
        )

    # 🔒 证据链: 使用真实短信服务发送验证码
    request = SMSCodeRequest(phone_number=phone_number, code_type=sms_type)

    result = await sms_service.send_verification_code(request)

    if result["success"]:
        return {
            "success": True,
            "message": result["message"],
            "data": {
                "phone_number": phone_number,
                "expires_in": result.get("expire_minutes", 5) * 60,
            },
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.get("error", "验证码发送失败"),
        )


@router.post("/send_sms")
async def send_sms(sms_data: dict, db: Session = Depends(get_db)):
    """发送短信验证码"""
    phone_number = sms_data.get("phone_number")
    sms_type = sms_data.get("type", "login")

    if not phone_number:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="手机号不能为空"
        )

    # 🔒 证据链: 使用真实短信服务发送验证码
    request = SMSCodeRequest(phone_number=phone_number, code_type=sms_type)

    result = await sms_service.send_verification_code(request)

    if result["success"]:
        return {
            "success": True,
            "message": result["message"],
            "data": {
                "phone_number": phone_number,
                "expires_in": result.get("expire_minutes", 5) * 60,
            },
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.get("error", "验证码发送失败"),
        )


@router.post("/logout", response_model=APIResponse)
async def logout(current_user: User = Depends(get_current_user)):
    """用户登出"""
    # 注意: 这里只是一个占位符
    # 实际的令牌失效需要使用黑名单或令牌存储机制
    return APIResponse(success=True, message="登出成功")


@router.post("/verify-token", response_model=APIResponse)
async def verify_token(current_user: User = Depends(get_current_user)):
    """验证令牌有效性"""
    return APIResponse(
        success=True,
        message="令牌有效",
        data={
            "user_id": current_user.id,
            "username": current_user.username,
            "is_active": current_user.is_active,
        },
    )
