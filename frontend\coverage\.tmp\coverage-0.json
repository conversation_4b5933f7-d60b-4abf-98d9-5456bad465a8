{"result": [{"scriptId": "866", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/tests/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49811, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49811, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 577, "endOffset": 946, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 981, "endOffset": 996, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 999, "endOffset": 1016, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1019, "endOffset": 1037, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 1175, "endOffset": 1190, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 1193, "endOffset": 1210, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1213, "endOffset": 1231, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3284, "endOffset": 3354, "count": 11}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3458, "endOffset": 4555, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4665, "endOffset": 6533, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7131, "endOffset": 7312, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7339, "endOffset": 8381, "count": 1}], "isBlockCoverage": true}, {"functionName": "getVoices", "ranges": [{"startOffset": 7400, "endOffset": 7745, "count": 2}], "isBlockCoverage": true}, {"functionName": "speak", "ranges": [{"startOffset": 7748, "endOffset": 8170, "count": 0}], "isBlockCoverage": false}, {"functionName": "cancel", "ranges": [{"startOffset": 8173, "endOffset": 8240, "count": 0}], "isBlockCoverage": false}, {"functionName": "pause", "ranges": [{"startOffset": 8243, "endOffset": 8280, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 8283, "endOffset": 8322, "count": 0}], "isBlockCoverage": false}, {"functionName": "addEventListener", "ranges": [{"startOffset": 8325, "endOffset": 8349, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEventListener", "ranges": [{"startOffset": 8352, "endOffset": 8379, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 8633, "endOffset": 9480, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 9703, "endOffset": 11003, "count": 0}], "isBlockCoverage": true}, {"functionName": "getTracks", "ranges": [{"startOffset": 11429, "endOffset": 11618, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAudioTracks", "ranges": [{"startOffset": 11638, "endOffset": 11833, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVideoTracks", "ranges": [{"startOffset": 11853, "endOffset": 11861, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTest", "ranges": [{"startOffset": 13502, "endOffset": 13920, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14018, "endOffset": 14043, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupTest", "ranges": [{"startOffset": 14047, "endOffset": 14164, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14264, "endOffset": 14291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14400, "endOffset": 14432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14542, "endOffset": 14576, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14678, "endOffset": 14704, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14810, "endOffset": 14840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14940, "endOffset": 14964, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15001, "endOffset": 15073, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15108, "endOffset": 15177, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "898", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/tests/integration.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 345, "endOffset": 1340, "count": 1}], "isBlockCoverage": true}, {"functionName": "dispose", "ranges": [{"startOffset": 663, "endOffset": 680, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1369, "endOffset": 1713, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1415, "endOffset": 1709, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1740, "endOffset": 1880, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2706, "endOffset": 2776, "count": 11}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2929, "endOffset": 3463, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3518, "endOffset": 13328, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3572, "endOffset": 3793, "count": 16}, {"startOffset": 3763, "endOffset": 3789, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3808, "endOffset": 3934, "count": 16}, {"startOffset": 3904, "endOffset": 3930, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3958, "endOffset": 4864, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3991, "endOffset": 4469, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4375, "endOffset": 4417, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4497, "endOffset": 4858, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4807, "endOffset": 4837, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4890, "endOffset": 6987, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4922, "endOffset": 5654, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5444, "endOffset": 5475, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5678, "endOffset": 6234, "count": 1}, {"startOffset": 6006, "endOffset": 6204, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6065, "endOffset": 6124, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6256, "endOffset": 6981, "count": 1}], "isBlockCoverage": true}, {"functionName": "computeStore.updateTask", "ranges": [{"startOffset": 6665, "endOffset": 6869, "count": 2}, {"startOffset": 6729, "endOffset": 6790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7013, "endOffset": 8558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7042, "endOffset": 7674, "count": 1}, {"startOffset": 7413, "endOffset": 7553, "count": 0}, {"startOffset": 7593, "endOffset": 7601, "count": 0}, {"startOffset": 7637, "endOffset": 7645, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7459, "endOffset": 7487, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7696, "endOffset": 8165, "count": 1}, {"startOffset": 7940, "endOffset": 8139, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7995, "endOffset": 8066, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8189, "endOffset": 8552, "count": 1}, {"startOffset": 8335, "endOffset": 8530, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8394, "endOffset": 8450, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8585, "endOffset": 9893, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8614, "endOffset": 9294, "count": 1}, {"startOffset": 9034, "endOffset": 9293, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9317, "endOffset": 9887, "count": 1}, {"startOffset": 9757, "endOffset": 9886, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9918, "endOffset": 10823, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9945, "endOffset": 10314, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10336, "endOffset": 10817, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10396, "endOffset": 10631, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10847, "endOffset": 11903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10878, "endOffset": 11282, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11304, "endOffset": 11897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11928, "endOffset": 13324, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11962, "endOffset": 12646, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12675, "endOffset": 13318, "count": 1}, {"startOffset": 12884, "endOffset": 13317, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13165, "endOffset": 13193, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13261, "endOffset": 13298, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "938", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/stores/compute.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33534, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33534, "count": 1}], "isBlockCoverage": true}, {"functionName": "state", "ranges": [{"startOffset": 623, "endOffset": 1115, "count": 16}], "isBlockCoverage": true}, {"functionName": "hasErrors", "ranges": [{"startOffset": 1166, "endOffset": 1200, "count": 6}], "isBlockCoverage": true}, {"functionName": "latestTask", "ranges": [{"startOffset": 1218, "endOffset": 1264, "count": 0}], "isBlockCoverage": false}, {"functionName": "isReady", "ranges": [{"startOffset": 1279, "endOffset": 1332, "count": 0}], "isBlockCoverage": false}, {"functionName": "completedTasks", "ranges": [{"startOffset": 1354, "endOffset": 1422, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1384, "endOffset": 1421, "count": 3}], "isBlockCoverage": true}, {"functionName": "failedTasks", "ranges": [{"startOffset": 1441, "endOffset": 1506, "count": 0}], "isBlockCoverage": false}, {"functionName": "processingTasks", "ranges": [{"startOffset": 1529, "endOffset": 1598, "count": 0}], "isBlockCoverage": false}, {"functionName": "successRate", "ranges": [{"startOffset": 1637, "endOffset": 1844, "count": 1}, {"startOffset": 1834, "endOffset": 1837, "count": 0}], "isBlockCoverage": true}, {"functionName": "recentLogs", "ranges": [{"startOffset": 1862, "endOffset": 1894, "count": 0}], "isBlockCoverage": false}, {"functionName": "addLog", "ranges": [{"startOffset": 1952, "endOffset": 2164, "count": 48}, {"startOffset": 2106, "endOffset": 2158, "count": 0}], "isBlockCoverage": true}, {"functionName": "initEngine", "ranges": [{"startOffset": 2191, "endOffset": 3016, "count": 4}, {"startOffset": 2737, "endOffset": 2792, "count": 0}, {"startOffset": 2801, "endOffset": 3010, "count": 0}], "isBlockCoverage": true}, {"functionName": "createTask", "ranges": [{"startOffset": 3042, "endOffset": 3338, "count": 15}], "isBlockCoverage": true}, {"functionName": "updateTask", "ranges": [{"startOffset": 3366, "endOffset": 4560, "count": 30}, {"startOffset": 3596, "endOffset": 4443, "count": 15}, {"startOffset": 3736, "endOffset": 3766, "count": 7}, {"startOffset": 3923, "endOffset": 3990, "count": 8}, {"startOffset": 3990, "endOffset": 4065, "count": 7}, {"startOffset": 4485, "endOffset": 4546, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3441, "endOffset": 3463, "count": 36}], "isBlockCoverage": true}, {"functionName": "processVideo", "ranges": [{"startOffset": 4588, "endOffset": 5952, "count": 10}, {"startOffset": 5196, "endOffset": 5502, "count": 8}, {"startOffset": 5502, "endOffset": 5572, "count": 2}, {"startOffset": 5549, "endOffset": 5560, "count": 0}, {"startOffset": 5581, "endOffset": 5946, "count": 2}, {"startOffset": 5665, "endOffset": 5677, "count": 0}], "isBlockCoverage": true}, {"functionName": "onProgress", "ranges": [{"startOffset": 4886, "endOffset": 4965, "count": 0}], "isBlockCoverage": false}, {"functionName": "onLog", "ranges": [{"startOffset": 4984, "endOffset": 5070, "count": 10}], "isBlockCoverage": true}, {"functionName": "runInference", "ranges": [{"startOffset": 5980, "endOffset": 7108, "count": 5}, {"startOffset": 6296, "endOffset": 6661, "count": 0}, {"startOffset": 6708, "endOffset": 6719, "count": 0}, {"startOffset": 6824, "endOffset": 6836, "count": 0}], "isBlockCoverage": true}, {"functionName": "extractVideoFrames", "ranges": [{"startOffset": 7135, "endOffset": 8063, "count": 0}], "isBlockCoverage": false}, {"functionName": "updatePerformanceMetrics", "ranges": [{"startOffset": 8091, "endOffset": 8272, "count": 0}], "isBlockCoverage": false}, {"functionName": "addError", "ranges": [{"startOffset": 8298, "endOffset": 8595, "count": 7}, {"startOffset": 8534, "endOffset": 8589, "count": 0}], "isBlockCoverage": true}, {"functionName": "clearErrors", "ranges": [{"startOffset": 8621, "endOffset": 8666, "count": 17}], "isBlockCoverage": true}, {"functionName": "clearTaskHistory", "ranges": [{"startOffset": 8694, "endOffset": 8798, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8752, "endOffset": 8790, "count": 15}], "isBlockCoverage": true}, {"functionName": "reset", "ranges": [{"startOffset": 8824, "endOffset": 9376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9488, "endOffset": 9519, "count": 18}], "isBlockCoverage": true}]}, {"scriptId": "939", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/compute/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37638, "count": 1}], "isBlockCoverage": true}, {"functionName": "updateMemoryUsage", "ranges": [{"startOffset": 1155, "endOffset": 1366, "count": 4}, {"startOffset": 1284, "endOffset": 1364, "count": 0}], "isBlockCoverage": true}, {"functionName": "getEngineStatus", "ranges": [{"startOffset": 1368, "endOffset": 1451, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1555, "endOffset": 1586, "count": 4}], "isBlockCoverage": true}, {"functionName": "initEngine", "ranges": [{"startOffset": 1590, "endOffset": 4020, "count": 9}, {"startOffset": 1654, "endOffset": 1676, "count": 8}, {"startOffset": 1676, "endOffset": 2564, "count": 1}, {"startOffset": 2637, "endOffset": 2666, "count": 1}, {"startOffset": 2736, "endOffset": 2763, "count": 1}, {"startOffset": 2837, "endOffset": 2866, "count": 1}, {"startOffset": 2952, "endOffset": 2987, "count": 1}, {"startOffset": 3044, "endOffset": 3076, "count": 1}, {"startOffset": 3077, "endOffset": 3108, "count": 0}, {"startOffset": 3109, "endOffset": 3142, "count": 0}, {"startOffset": 3143, "endOffset": 3182, "count": 0}, {"startOffset": 3209, "endOffset": 3675, "count": 1}, {"startOffset": 3675, "endOffset": 3760, "count": 0}, {"startOffset": 3765, "endOffset": 4018, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1926, "endOffset": 2033, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2173, "endOffset": 2183, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2191, "endOffset": 2300, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4119, "endOffset": 4145, "count": 4}], "isBlockCoverage": true}, {"functionName": "processVideoTask", "ranges": [{"startOffset": 4149, "endOffset": 5300, "count": 10}, {"startOffset": 4237, "endOffset": 4381, "count": 0}, {"startOffset": 4418, "endOffset": 4484, "count": 0}, {"startOffset": 4783, "endOffset": 4999, "count": 8}, {"startOffset": 4972, "endOffset": 4983, "count": 0}, {"startOffset": 5023, "endOffset": 5298, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5405, "endOffset": 5437, "count": 10}], "isBlockCoverage": true}, {"functionName": "runAIInference", "ranges": [{"startOffset": 5441, "endOffset": 6572, "count": 5}, {"startOffset": 5728, "endOffset": 5763, "count": 0}, {"startOffset": 5764, "endOffset": 5781, "count": 0}, {"startOffset": 5783, "endOffset": 6303, "count": 0}, {"startOffset": 6383, "endOffset": 6413, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6675, "endOffset": 6705, "count": 5}], "isBlockCoverage": true}, {"functionName": "extractFrames", "ranges": [{"startOffset": 6709, "endOffset": 7356, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7458, "endOffset": 7487, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateSpeech", "ranges": [{"startOffset": 7491, "endOffset": 8519, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8622, "endOffset": 8652, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateImage", "ranges": [{"startOffset": 8656, "endOffset": 9747, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9849, "endOffset": 9878, "count": 0}], "isBlockCoverage": false}, {"functionName": "composeVideo", "ranges": [{"startOffset": 9882, "endOffset": 10597, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10698, "endOffset": 10726, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupEngine", "ranges": [{"startOffset": 10730, "endOffset": 11474, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11576, "endOffset": 11605, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPerformanceMetrics", "ranges": [{"startOffset": 11609, "endOffset": 11823, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11933, "endOffset": 11970, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "940", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/compute/ai/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 39796, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateAIInput", "ranges": [{"startOffset": 317, "endOffset": 800, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMemoryUsage", "ranges": [{"startOffset": 825, "endOffset": 924, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadModel", "ranges": [{"startOffset": 990, "endOffset": 1963, "count": 0}], "isBlockCoverage": false}, {"functionName": "performImageClassification", "ranges": [{"startOffset": 2000, "endOffset": 3253, "count": 0}], "isBlockCoverage": false}, {"functionName": "performObjectDetection", "ranges": [{"startOffset": 3286, "endOffset": 5080, "count": 0}], "isBlockCoverage": false}, {"functionName": "performTextGeneration", "ranges": [{"startOffset": 5112, "endOffset": 5865, "count": 0}], "isBlockCoverage": false}, {"functionName": "performSentimentAnalysis", "ranges": [{"startOffset": 5900, "endOffset": 7059, "count": 0}], "isBlockCoverage": false}, {"functionName": "performToxicityDetection", "ranges": [{"startOffset": 7094, "endOffset": 8122, "count": 0}], "isBlockCoverage": false}, {"functionName": "initAIEngine", "ranges": [{"startOffset": 8124, "endOffset": 8712, "count": 1}, {"startOffset": 8380, "endOffset": 8442, "count": 0}, {"startOffset": 8608, "endOffset": 8710, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8813, "endOffset": 8841, "count": 1}], "isBlockCoverage": true}, {"functionName": "cleanupAIEngine", "ranges": [{"startOffset": 8845, "endOffset": 9396, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9500, "endOffset": 9531, "count": 0}], "isBlockCoverage": false}, {"functionName": "aiInference", "ranges": [{"startOffset": 9535, "endOffset": 11554, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11654, "endOffset": 11681, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "941", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/compute/video/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12214, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12214, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateVideoFile", "ranges": [{"startOffset": 417, "endOffset": 946, "count": 10}, {"startOffset": 504, "endOffset": 518, "count": 0}, {"startOffset": 598, "endOffset": 603, "count": 0}, {"startOffset": 644, "endOffset": 713, "count": 0}, {"startOffset": 810, "endOffset": 838, "count": 2}, {"startOffset": 838, "endOffset": 901, "count": 8}, {"startOffset": 901, "endOffset": 929, "count": 0}, {"startOffset": 929, "endOffset": 945, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 766, "endOffset": 807, "count": 26}], "isBlockCoverage": true}, {"functionName": "initVideoEngine", "ranges": [{"startOffset": 948, "endOffset": 1204, "count": 1}, {"startOffset": 1100, "endOffset": 1202, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1308, "endOffset": 1339, "count": 0}], "isBlockCoverage": false}, {"functionName": "videoProcess", "ranges": [{"startOffset": 1343, "endOffset": 2309, "count": 10}, {"startOffset": 1509, "endOffset": 1556, "count": 2}, {"startOffset": 1556, "endOffset": 1606, "count": 8}, {"startOffset": 1606, "endOffset": 1644, "count": 1}, {"startOffset": 1644, "endOffset": 1679, "count": 8}, {"startOffset": 1827, "endOffset": 2050, "count": 8}, {"startOffset": 2050, "endOffset": 2307, "count": 2}, {"startOffset": 2130, "endOffset": 2142, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2410, "endOffset": 2438, "count": 10}], "isBlockCoverage": true}, {"functionName": "extractVideoFrames", "ranges": [{"startOffset": 2442, "endOffset": 3188, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3295, "endOffset": 3329, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupVideoEngine", "ranges": [{"startOffset": 3333, "endOffset": 3570, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3677, "endOffset": 3711, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "942", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/engines/ffmpeg/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27103, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27103, "count": 1}], "isBlockCoverage": true}, {"functionName": "initFFmpeg", "ranges": [{"startOffset": 547, "endOffset": 1642, "count": 1}, {"startOffset": 593, "endOffset": 600, "count": 0}, {"startOffset": 618, "endOffset": 729, "count": 0}, {"startOffset": 1520, "endOffset": 1640, "count": 0}], "isBlockCoverage": true}, {"functionName": "isLoading", "ranges": [{"startOffset": 668, "endOffset": 705, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 896, "endOffset": 960, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 997, "endOffset": 1097, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1741, "endOffset": 1767, "count": 1}], "isBlockCoverage": true}, {"functionName": "convertVideo", "ranges": [{"startOffset": 1771, "endOffset": 2816, "count": 8}, {"startOffset": 1882, "endOffset": 1941, "count": 0}, {"startOffset": 2715, "endOffset": 2814, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2297, "endOffset": 2354, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2917, "endOffset": 2945, "count": 8}], "isBlockCoverage": true}, {"functionName": "extractThumbnail", "ranges": [{"startOffset": 2949, "endOffset": 3947, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4052, "endOffset": 4084, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVideoInfo", "ranges": [{"startOffset": 4088, "endOffset": 5772, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5873, "endOffset": 5901, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFFmpegLoaded", "ranges": [{"startOffset": 5905, "endOffset": 5953, "count": 8}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6056, "endOffset": 6086, "count": 8}], "isBlockCoverage": true}, {"functionName": "getFFmpegInstance", "ranges": [{"startOffset": 6090, "endOffset": 6147, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6253, "endOffset": 6286, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupFFmpeg", "ranges": [{"startOffset": 6290, "endOffset": 6554, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6656, "endOffset": 6685, "count": 0}], "isBlockCoverage": false}, {"functionName": "mergeVideos", "ranges": [{"startOffset": 6689, "endOffset": 8221, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8321, "endOffset": 8348, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "943", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/services/ttsService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21621, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21621, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 202, "endOffset": 5397, "count": 1}], "isBlockCoverage": true}, {"functionName": "TTSService", "ranges": [{"startOffset": 259, "endOffset": 352, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeVoices", "ranges": [{"startOffset": 380, "endOffset": 784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 430, "endOffset": 778, "count": 1}, {"startOffset": 671, "endOffset": 772, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadVoices", "ranges": [{"startOffset": 470, "endOffset": 589, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableVoices", "ranges": [{"startOffset": 814, "endOffset": 1119, "count": 1}, {"startOffset": 872, "endOffset": 916, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 944, "endOffset": 1113, "count": 2}], "isBlockCoverage": true}, {"functionName": "detectGender", "ranges": [{"startOffset": 1154, "endOffset": 1611, "count": 2}, {"startOffset": 1458, "endOffset": 1488, "count": 0}, {"startOffset": 1558, "endOffset": 1586, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1415, "endOffset": 1455, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1515, "endOffset": 1555, "count": 10}], "isBlockCoverage": true}, {"functionName": "generateSpeech", "ranges": [{"startOffset": 1646, "endOffset": 3023, "count": 0}], "isBlockCoverage": false}, {"functionName": "synthesizeToBlob", "ranges": [{"startOffset": 3056, "endOffset": 4106, "count": 0}], "isBlockCoverage": false}, {"functionName": "estimateDuration", "ranges": [{"startOffset": 4136, "endOffset": 4398, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopSpeech", "ranges": [{"startOffset": 4427, "endOffset": 4517, "count": 0}], "isBlockCoverage": false}, {"functionName": "pauseSpeech", "ranges": [{"startOffset": 4544, "endOffset": 4660, "count": 0}], "isBlockCoverage": false}, {"functionName": "resumeSpeech", "ranges": [{"startOffset": 4687, "endOffset": 4777, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStatus", "ranges": [{"startOffset": 4807, "endOffset": 5052, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateBatchSpeech", "ranges": [{"startOffset": 5090, "endOffset": 5395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5496, "endOffset": 5522, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5661, "endOffset": 5687, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "944", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/services/imageGenerationService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36821, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36821, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 214, "endOffset": 9564, "count": 1}], "isBlockCoverage": true}, {"functionName": "ImageGenerationService", "ranges": [{"startOffset": 278, "endOffset": 508, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableModels", "ranges": [{"startOffset": 540, "endOffset": 1631, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateImage", "ranges": [{"startOffset": 1656, "endOffset": 2556, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateWithDALLE", "ranges": [{"startOffset": 2589, "endOffset": 4193, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateWithStableDiffusion", "ranges": [{"startOffset": 4244, "endOffset": 5789, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatSize", "ranges": [{"startOffset": 5826, "endOffset": 6120, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateBatchImages", "ranges": [{"startOffset": 6147, "endOffset": 6445, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateVariations", "ranges": [{"startOffset": 6472, "endOffset": 7803, "count": 0}], "isBlockCoverage": false}, {"functionName": "editImage", "ranges": [{"startOffset": 7835, "endOffset": 9184, "count": 0}], "isBlockCoverage": false}, {"functionName": "setConfig", "ranges": [{"startOffset": 9212, "endOffset": 9325, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStatus", "ranges": [{"startOffset": 9352, "endOffset": 9562, "count": 1}, {"startOffset": 9440, "endOffset": 9478, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 9675, "endOffset": 9713, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9888, "endOffset": 9926, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "945", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/services/videoCompositionService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 40837, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 40837, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 485, "endOffset": 10266, "count": 1}], "isBlockCoverage": true}, {"functionName": "composeVideo", "ranges": [{"startOffset": 558, "endOffset": 2085, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateCompositionOptions", "ranges": [{"startOffset": 2112, "endOffset": 2981, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateSceneAssets", "ranges": [{"startOffset": 3008, "endOffset": 4591, "count": 0}], "isBlockCoverage": false}, {"functionName": "assembleVideo", "ranges": [{"startOffset": 4616, "endOffset": 5672, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderScenes", "ranges": [{"startOffset": 5700, "endOffset": 6763, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderFrame", "ranges": [{"startOffset": 6788, "endOffset": 7547, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderTextOverlay", "ranges": [{"startOffset": 7575, "endOffset": 8473, "count": 0}], "isBlockCoverage": false}, {"functionName": "applyTransitionEffects", "ranges": [{"startOffset": 8500, "endOffset": 9171, "count": 0}], "isBlockCoverage": false}, {"functionName": "applyTransition", "ranges": [{"startOffset": 9201, "endOffset": 9746, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadImageFromBlob", "ranges": [{"startOffset": 9776, "endOffset": 10006, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentProgress", "ranges": [{"startOffset": 10035, "endOffset": 10094, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCurrentlyProcessing", "ranges": [{"startOffset": 10123, "endOffset": 10182, "count": 0}], "isBlockCoverage": false}, {"functionName": "cancelProcessing", "ranges": [{"startOffset": 10209, "endOffset": 10264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10378, "endOffset": 10417, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10595, "endOffset": 10634, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "946", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/pages/ComputeTest.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 60292, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 60292, "count": 1}], "isBlockCoverage": true}, {"functionName": "setup", "ranges": [{"startOffset": 1425, "endOffset": 7189, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1841, "endOffset": 1918, "count": 2}, {"startOffset": 1873, "endOffset": 1918, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1977, "endOffset": 2042, "count": 2}, {"startOffset": 2010, "endOffset": 2042, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2101, "endOffset": 2147, "count": 2}, {"startOffset": 2130, "endOffset": 2138, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2204, "endOffset": 2281, "count": 2}, {"startOffset": 2236, "endOffset": 2281, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2346, "endOffset": 2379, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2443, "endOffset": 2517, "count": 2}, {"startOffset": 2475, "endOffset": 2517, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2580, "endOffset": 2642, "count": 2}, {"startOffset": 2613, "endOffset": 2642, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2705, "endOffset": 2751, "count": 2}, {"startOffset": 2734, "endOffset": 2742, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2809, "endOffset": 2883, "count": 2}, {"startOffset": 2841, "endOffset": 2883, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleVideoFileSelect", "ranges": [{"startOffset": 3291, "endOffset": 3456, "count": 1}, {"startOffset": 3390, "endOffset": 3450, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleImageFileSelect", "ranges": [{"startOffset": 3492, "endOffset": 3657, "count": 0}], "isBlockCoverage": false}, {"functionName": "testFFmpegLoad", "ranges": [{"startOffset": 3686, "endOffset": 3836, "count": 0}], "isBlockCoverage": false}, {"functionName": "testVideoConversion", "ranges": [{"startOffset": 3870, "endOffset": 4236, "count": 0}], "isBlockCoverage": false}, {"functionName": "testTensorFlowLoad", "ranges": [{"startOffset": 4269, "endOffset": 4423, "count": 0}], "isBlockCoverage": false}, {"functionName": "testImageClassification", "ranges": [{"startOffset": 4461, "endOffset": 5031, "count": 0}], "isBlockCoverage": false}, {"functionName": "testWebWorker", "ranges": [{"startOffset": 5059, "endOffset": 5475, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectSystemInfo", "ranges": [{"startOffset": 5506, "endOffset": 6039, "count": 2}, {"startOffset": 5767, "endOffset": 5771, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6077, "endOffset": 6116, "count": 2}], "isBlockCoverage": true}, {"functionName": "get VideoIcon", "ranges": [{"startOffset": 6598, "endOffset": 6663, "count": 4}], "isBlockCoverage": true}, {"functionName": "get BrainIcon", "ranges": [{"startOffset": 6665, "endOffset": 6730, "count": 8}], "isBlockCoverage": true}, {"functionName": "get CpuIcon", "ranges": [{"startOffset": 6732, "endOffset": 6793, "count": 8}], "isBlockCoverage": true}, {"functionName": "get RefreshCwIcon", "ranges": [{"startOffset": 6795, "endOffset": 6868, "count": 4}], "isBlockCoverage": true}, {"functionName": "get ScanIcon", "ranges": [{"startOffset": 6870, "endOffset": 6933, "count": 4}], "isBlockCoverage": true}, {"functionName": "get ZapIcon", "ranges": [{"startOffset": 6935, "endOffset": 6996, "count": 4}], "isBlockCoverage": true}, {"functionName": "get InfoIcon", "ranges": [{"startOffset": 6998, "endOffset": 7061, "count": 4}], "isBlockCoverage": true}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 10068, "endOffset": 28112, "count": 4}, {"startOffset": 10722, "endOffset": 11132, "count": 2}, {"startOffset": 11303, "endOffset": 11539, "count": 2}, {"startOffset": 12478, "endOffset": 12488, "count": 0}, {"startOffset": 12758, "endOffset": 12786, "count": 0}, {"startOffset": 12787, "endOffset": 12807, "count": 0}, {"startOffset": 13102, "endOffset": 13112, "count": 0}, {"startOffset": 13382, "endOffset": 13604, "count": 2}, {"startOffset": 13849, "endOffset": 13905, "count": 0}, {"startOffset": 13947, "endOffset": 15102, "count": 0}, {"startOffset": 15604, "endOffset": 16014, "count": 2}, {"startOffset": 16186, "endOffset": 16422, "count": 2}, {"startOffset": 17376, "endOffset": 17386, "count": 0}, {"startOffset": 17669, "endOffset": 17697, "count": 0}, {"startOffset": 17698, "endOffset": 17719, "count": 0}, {"startOffset": 18010, "endOffset": 18020, "count": 0}, {"startOffset": 18295, "endOffset": 18521, "count": 2}, {"startOffset": 18770, "endOffset": 18826, "count": 0}, {"startOffset": 18878, "endOffset": 20549, "count": 0}, {"startOffset": 21048, "endOffset": 21455, "count": 2}, {"startOffset": 22056, "endOffset": 22066, "count": 0}, {"startOffset": 22222, "endOffset": 22806, "count": 0}, {"startOffset": 23295, "endOffset": 23694, "count": 2}, {"startOffset": 23951, "endOffset": 24181, "count": 2}, {"startOffset": 24397, "endOffset": 24415, "count": 2}, {"startOffset": 24416, "endOffset": 24432, "count": 2}, {"startOffset": 24538, "endOffset": 24544, "count": 2}, {"startOffset": 24545, "endOffset": 24552, "count": 2}, {"startOffset": 24739, "endOffset": 24969, "count": 2}, {"startOffset": 25184, "endOffset": 25202, "count": 2}, {"startOffset": 25203, "endOffset": 25219, "count": 2}, {"startOffset": 25324, "endOffset": 25330, "count": 2}, {"startOffset": 25331, "endOffset": 25338, "count": 2}, {"startOffset": 25525, "endOffset": 25750, "count": 2}, {"startOffset": 25961, "endOffset": 25979, "count": 0}, {"startOffset": 26097, "endOffset": 26103, "count": 0}, {"startOffset": 26384, "endOffset": 26610, "count": 2}, {"startOffset": 26981, "endOffset": 27202, "count": 2}, {"startOffset": 27571, "endOffset": 27794, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19535, "endOffset": 20444, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "949", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/pages/ComputeTest.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 558, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "950", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/%00plugin-vue:export-helper", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_exports__.default", "ranges": [{"startOffset": 217, "endOffset": 355, "count": 1}, {"startOffset": 308, "endOffset": 336, "count": 3}], "isBlockCoverage": true}]}]}