{"result": [{"scriptId": "866", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/tests/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49811, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49811, "count": 3}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 577, "endOffset": 946, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 981, "endOffset": 996, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 999, "endOffset": 1016, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1019, "endOffset": 1037, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 1175, "endOffset": 1190, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 1193, "endOffset": 1210, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1213, "endOffset": 1231, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3284, "endOffset": 3354, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3458, "endOffset": 4555, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4665, "endOffset": 6533, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7131, "endOffset": 7312, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7339, "endOffset": 8381, "count": 1}], "isBlockCoverage": false}, {"functionName": "getVoices", "ranges": [{"startOffset": 7400, "endOffset": 7745, "count": 0}], "isBlockCoverage": false}, {"functionName": "speak", "ranges": [{"startOffset": 7748, "endOffset": 8170, "count": 0}], "isBlockCoverage": false}, {"functionName": "cancel", "ranges": [{"startOffset": 8173, "endOffset": 8240, "count": 0}], "isBlockCoverage": false}, {"functionName": "pause", "ranges": [{"startOffset": 8243, "endOffset": 8280, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 8283, "endOffset": 8322, "count": 0}], "isBlockCoverage": false}, {"functionName": "addEventListener", "ranges": [{"startOffset": 8325, "endOffset": 8349, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEventListener", "ranges": [{"startOffset": 8352, "endOffset": 8379, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 8633, "endOffset": 9480, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 9703, "endOffset": 11003, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTracks", "ranges": [{"startOffset": 11429, "endOffset": 11618, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAudioTracks", "ranges": [{"startOffset": 11638, "endOffset": 11833, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVideoTracks", "ranges": [{"startOffset": 11853, "endOffset": 11861, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTest", "ranges": [{"startOffset": 13502, "endOffset": 13920, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14018, "endOffset": 14043, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupTest", "ranges": [{"startOffset": 14047, "endOffset": 14164, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14264, "endOffset": 14291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14400, "endOffset": 14432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14542, "endOffset": 14576, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14678, "endOffset": 14704, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14810, "endOffset": 14840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14940, "endOffset": 14964, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15001, "endOffset": 15073, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15108, "endOffset": 15177, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "951", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/tests/unit/composables/useResponsiveLayout.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41543, "count": 1}], "isBlockCoverage": true}, {"functionName": "mockWindowSize", "ranges": [{"startOffset": 659, "endOffset": 913, "count": 64}], "isBlockCoverage": true}, {"functionName": "triggerResize", "ranges": [{"startOffset": 937, "endOffset": 991, "count": 1}], "isBlockCoverage": true}, {"functionName": "triggerOrientationChange", "ranges": [{"startOffset": 1026, "endOffset": 1091, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1147, "endOffset": 14589, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1205, "endOffset": 1321, "count": 32}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1358, "endOffset": 1435, "count": 32}, {"startOffset": 1383, "endOffset": 1431, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1481, "endOffset": 2486, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1532, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1865, "endOffset": 2150, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2196, "endOffset": 2480, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2530, "endOffset": 4586, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2581, "endOffset": 2875, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2921, "endOffset": 3215, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3261, "endOffset": 3555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3601, "endOffset": 3896, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3942, "endOffset": 4237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4284, "endOffset": 4580, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4630, "endOffset": 5660, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4689, "endOffset": 4973, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5027, "endOffset": 5312, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5367, "endOffset": 5654, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5708, "endOffset": 7843, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5761, "endOffset": 6067, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6116, "endOffset": 6425, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6473, "endOffset": 6785, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6834, "endOffset": 7149, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7196, "endOffset": 7492, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7542, "endOffset": 7837, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7890, "endOffset": 9818, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7942, "endOffset": 8448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8500, "endOffset": 8828, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8880, "endOffset": 9244, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9298, "endOffset": 9812, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9862, "endOffset": 11467, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9922, "endOffset": 10557, "count": 1}, {"startOffset": 10482, "endOffset": 10556, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10612, "endOffset": 11073, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11130, "endOffset": 11461, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11513, "endOffset": 12748, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11581, "endOffset": 11961, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12024, "endOffset": 12407, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12470, "endOffset": 12742, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12792, "endOffset": 13642, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12852, "endOffset": 13208, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13034, "endOffset": 13069, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13267, "endOffset": 13636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13460, "endOffset": 13497, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13695, "endOffset": 14585, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13747, "endOffset": 14313, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14359, "endOffset": 14579, "count": 1}, {"startOffset": 14484, "endOffset": 14573, "count": 5}], "isBlockCoverage": true}]}, {"scriptId": "952", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/composables/useResponsiveLayout.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18776, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18776, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 602, "endOffset": 629, "count": 7}], "isBlockCoverage": true}, {"functionName": "useResponsiveLayout", "ranges": [{"startOffset": 633, "endOffset": 5642, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 998, "endOffset": 1153, "count": 30}, {"startOffset": 1050, "endOffset": 1066, "count": 14}, {"startOffset": 1066, "endOffset": 1111, "count": 16}, {"startOffset": 1111, "endOffset": 1127, "count": 2}, {"startOffset": 1127, "endOffset": 1152, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1215, "endOffset": 1512, "count": 6}, {"startOffset": 1294, "endOffset": 1307, "count": 2}, {"startOffset": 1307, "endOffset": 1341, "count": 4}, {"startOffset": 1341, "endOffset": 1353, "count": 1}, {"startOffset": 1353, "endOffset": 1387, "count": 3}, {"startOffset": 1387, "endOffset": 1399, "count": 0}, {"startOffset": 1399, "endOffset": 1433, "count": 3}, {"startOffset": 1433, "endOffset": 1445, "count": 1}, {"startOffset": 1445, "endOffset": 1479, "count": 2}, {"startOffset": 1479, "endOffset": 1511, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1567, "endOffset": 1771, "count": 3}, {"startOffset": 1613, "endOffset": 1652, "count": 1}, {"startOffset": 1659, "endOffset": 1698, "count": 0}, {"startOffset": 1705, "endOffset": 1720, "count": 2}, {"startOffset": 1727, "endOffset": 1761, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1839, "endOffset": 1892, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1963, "endOffset": 2016, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2078, "endOffset": 2192, "count": 2}, {"startOffset": 2128, "endOffset": 2187, "count": 1}, {"startOffset": 2161, "endOffset": 2187, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2258, "endOffset": 2311, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2375, "endOffset": 2428, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2485, "endOffset": 2682, "count": 2}, {"startOffset": 2531, "endOffset": 2568, "count": 1}, {"startOffset": 2575, "endOffset": 2612, "count": 0}, {"startOffset": 2619, "endOffset": 2634, "count": 1}, {"startOffset": 2641, "endOffset": 2672, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2739, "endOffset": 2980, "count": 3}, {"startOffset": 2786, "endOffset": 2848, "count": 2}, {"startOffset": 2824, "endOffset": 2833, "count": 1}, {"startOffset": 2834, "endOffset": 2841, "count": 1}, {"startOffset": 2848, "endOffset": 2888, "count": 1}, {"startOffset": 2888, "endOffset": 2956, "count": 0}, {"startOffset": 2956, "endOffset": 2979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3038, "endOffset": 3532, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateWindowSize", "ranges": [{"startOffset": 3562, "endOffset": 3772, "count": 30}, {"startOffset": 3696, "endOffset": 3721, "count": 16}, {"startOffset": 3723, "endOffset": 3768, "count": 1}], "isBlockCoverage": true}, {"functionName": "toggleMobileMenu", "ranges": [{"startOffset": 3801, "endOffset": 4007, "count": 6}, {"startOffset": 3895, "endOffset": 3949, "count": 5}, {"startOffset": 3949, "endOffset": 4003, "count": 1}], "isBlockCoverage": true}, {"functionName": "closeMobileMenu", "ranges": [{"startOffset": 4035, "endOffset": 4121, "count": 1}], "isBlockCoverage": true}, {"functionName": "matchesBreakpoint", "ranges": [{"startOffset": 4151, "endOffset": 4393, "count": 4}, {"startOffset": 4268, "endOffset": 4389, "count": 2}], "isBlockCoverage": true}, {"functionName": "getMediaQuery", "ranges": [{"startOffset": 4419, "endOffset": 4664, "count": 2}, {"startOffset": 4536, "endOffset": 4660, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4700, "endOffset": 4898, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4937, "endOffset": 5118, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5750, "endOffset": 5785, "count": 30}], "isBlockCoverage": true}]}]}