<!DOCTYPE html><html lang="zh-CN"><head>
    <script type="module" src="/@vite/client"></script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二创短视频分发系统 - 二创短视频分发系统</title>
    <link rel="stylesheet" href="/shared-ui-design.css">
    <link rel="stylesheet" href="/shared-login-styles.css">
  <style type="text/css" data-vite-dev-id="D:/二创/二创短视频分发/frontend/src/components/layout/MobileNavigation.vue?vue&amp;type=style&amp;index=0&amp;scoped=6137f842&amp;lang.css">
/* 移动端导航样式 */
.mobile-navigation[data-v-6137f842] {
  position: relative;
  z-index: 1000;
}

/* 移动端顶部导航栏 */
.mobile-header[data-v-6137f842] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 56px;
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
  z-index: 1001;
}
.mobile-header__content[data-v-6137f842] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 16px;
}

/* 汉堡菜单按钮 */
.mobile-menu-toggle[data-v-6137f842] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}
.mobile-menu-toggle[data-v-6137f842]:hover {
  background-color: var(--color-background-soft);
}
.hamburger-icon[data-v-6137f842] {
  width: 24px;
  height: 18px;
  position: relative;
  transform: rotate(0deg);
  transition: 0.3s ease-in-out;
}
.hamburger-icon span[data-v-6137f842] {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: var(--color-text);
  border-radius: 1px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}
.hamburger-icon span[data-v-6137f842]:nth-child(1) {
  top: 0px;
}
.hamburger-icon span[data-v-6137f842]:nth-child(2) {
  top: 8px;
}
.hamburger-icon span[data-v-6137f842]:nth-child(3) {
  top: 16px;
}
.hamburger-icon.is-active span[data-v-6137f842]:nth-child(1) {
  top: 8px;
  transform: rotate(135deg);
}
.hamburger-icon.is-active span[data-v-6137f842]:nth-child(2) {
  opacity: 0;
  left: -60px;
}
.hamburger-icon.is-active span[data-v-6137f842]:nth-child(3) {
  top: 8px;
  transform: rotate(-135deg);
}

/* 品牌标识 */
.mobile-brand[data-v-6137f842] {
  flex: 1;
  display: flex;
  justify-content: center;
}
.brand-link[data-v-6137f842] {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--color-text);
}
.brand-logo[data-v-6137f842] {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}
.brand-text[data-v-6137f842] {
  font-size: 18px;
  font-weight: 600;
}

/* 右侧操作按钮 */
.mobile-actions[data-v-6137f842] {
  display: flex;
  gap: 8px;
}
.action-btn[data-v-6137f842] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}
.action-btn[data-v-6137f842]:hover {
  background-color: var(--color-background-soft);
}
.action-btn .icon[data-v-6137f842] {
  width: 20px;
  height: 20px;
}

/* 移动端侧滑菜单 */
.mobile-menu-overlay[data-v-6137f842] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1002;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.mobile-menu[data-v-6137f842] {
  position: absolute;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background: var(--color-background);
  box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15);
  overflow-y: auto;
  padding: 56px 0 0 0;
}

/* 用户信息 */
.mobile-user-info[data-v-6137f842] {
  display: flex;
  align-items: center;
  padding: 24px 20px;
  border-bottom: 1px solid var(--color-border);
}
.user-avatar img[data-v-6137f842] {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}
.user-details[data-v-6137f842] {
  margin-left: 12px;
}
.user-name[data-v-6137f842] {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}
.user-role[data-v-6137f842] {
  font-size: 14px;
  color: var(--color-text-soft);
  margin: 0;
}

/* 导航菜单 */
.mobile-nav-section[data-v-6137f842] {
  padding: 16px 0;
  border-bottom: 1px solid var(--color-border);
}
.nav-section-title[data-v-6137f842] {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--color-text-soft);
  margin: 0 0 12px 0;
  padding: 0 20px;
  letter-spacing: 0.5px;
}
.mobile-nav-list[data-v-6137f842] {
  list-style: none;
  margin: 0;
  padding: 0;
}
.mobile-nav-item[data-v-6137f842] {
  margin: 0;
}
.mobile-nav-link[data-v-6137f842] {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  text-decoration: none;
  color: var(--color-text);
  transition: background-color 0.2s ease;
  position: relative;
}
.mobile-nav-link[data-v-6137f842]:hover {
  background-color: var(--color-background-soft);
}
.mobile-nav-link.is-active[data-v-6137f842] {
  background-color: var(--color-primary-soft);
  color: var(--color-primary);
}
.nav-icon[data-v-6137f842] {
  width: 20px;
  height: 20px;
  margin-right: 12px;
}
.nav-text[data-v-6137f842] {
  flex: 1;
  font-size: 16px;
}
.nav-badge[data-v-6137f842] {
  width: 8px;
  height: 8px;
  color: var(--color-primary);
}

/* 菜单底部 */
.mobile-menu-footer[data-v-6137f842] {
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.footer-btn[data-v-6137f842] {
  display: flex;
  align-items: center;
  padding: 12px 0;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text);
  font-size: 16px;
  transition: color 0.2s ease;
}
.footer-btn[data-v-6137f842]:hover {
  color: var(--color-primary);
}
.footer-btn .icon[data-v-6137f842] {
  width: 20px;
  height: 20px;
  margin-right: 12px;
}

/* 底部导航 */
.mobile-bottom-nav[data-v-6137f842] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 64px;
  background: var(--color-background);
  border-top: 1px solid var(--color-border);
  z-index: 1001;
}
.bottom-nav-content[data-v-6137f842] {
  display: flex;
  height: 100%;
}
.bottom-nav-item[data-v-6137f842] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: var(--color-text-soft);
  transition: color 0.2s ease;
  padding: 8px 4px;
}
.bottom-nav-item[data-v-6137f842]:hover,
.bottom-nav-item.is-active[data-v-6137f842] {
  color: var(--color-primary);
}
.bottom-nav-icon[data-v-6137f842] {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}
.bottom-nav-text[data-v-6137f842] {
  font-size: 12px;
  font-weight: 500;
}

/* 过渡动画 */
.mobile-menu-enter-active[data-v-6137f842],
.mobile-menu-leave-active[data-v-6137f842] {
  transition: opacity 0.3s ease;
}
.mobile-menu-enter-active .mobile-menu[data-v-6137f842],
.mobile-menu-leave-active .mobile-menu[data-v-6137f842] {
  transition: transform 0.3s ease;
}
.mobile-menu-enter-from[data-v-6137f842],
.mobile-menu-leave-to[data-v-6137f842] {
  opacity: 0;
}
.mobile-menu-enter-from .mobile-menu[data-v-6137f842],
.mobile-menu-leave-to .mobile-menu[data-v-6137f842] {
  transform: translateX(-100%);
}
</style><style type="text/css" data-vite-dev-id="D:/二创/二创短视频分发/frontend/src/components/layout/UnifiedLayout.vue?vue&amp;type=style&amp;index=0&amp;scoped=4d0dd26f&amp;lang.css">
/* 引入统一设计系统 */
/* 
 * 二创短视频分发系统 - 统一UI设计规范
 * 前后端共享的设计系统
 */
/* ========== 设计令牌 (Design Tokens) ========== */
[data-v-4d0dd26f]:root {
  /* 主色调 */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 语义色彩 */
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  --info-500: #3b82f6;

  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* 文本色 */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;

  /* 边框色 */
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --border-focus: #3b82f6;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* 间距 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* 字体 */
  --font-family-sans: 'Inter', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Consolas', monospace;

  /* 字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  /* 布局尺寸 */
  --header-height: 4rem;
  --sidebar-width: 16rem;
  --sidebar-collapsed-width: 4rem;
  --content-max-width: 1200px;

  /* 动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}
/* ========== 基础重置 ========== */
[data-v-4d0dd26f] {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
body[data-v-4d0dd26f] {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* ========== 统一布局系统 ========== */
.app-layout[data-v-4d0dd26f] {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-secondary);
}
/* 顶部导航栏 */
.app-header[data-v-4d0dd26f] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 0 var(--space-6);
}
.app-header__brand[data-v-4d0dd26f] {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-weight: 600;
  font-size: var(--text-lg);
  color: var(--text-primary);
  text-decoration: none;
}
.app-header__brand-icon[data-v-4d0dd26f] {
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-lg);
}
.app-header__nav[data-v-4d0dd26f] {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  margin-left: auto;
}
.app-header__nav-item[data-v-4d0dd26f] {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}
.app-header__nav-item[data-v-4d0dd26f]:hover,
.app-header__nav-item.active[data-v-4d0dd26f] {
  color: var(--primary-600);
}
/* 主体内容区 */
.app-body[data-v-4d0dd26f] {
  display: flex;
  margin-top: var(--header-height);
  min-height: calc(100vh - var(--header-height));
}
/* 左侧功能区 */
.app-sidebar[data-v-4d0dd26f] {
  width: var(--sidebar-width);
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-primary);
  transition: width var(--transition-normal);
  overflow: hidden;
}
.app-sidebar.collapsed[data-v-4d0dd26f] {
  width: var(--sidebar-collapsed-width);
}
.app-sidebar__header[data-v-4d0dd26f] {
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}
.app-sidebar__title[data-v-4d0dd26f] {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
.app-sidebar__nav[data-v-4d0dd26f] {
  padding: var(--space-2);
}
.app-sidebar__nav-item[data-v-4d0dd26f] {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  margin-bottom: var(--space-1);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-weight: 500;
}
.app-sidebar__nav-item[data-v-4d0dd26f]:hover {
  background-color: var(--gray-100);
  color: var(--text-primary);
}
.app-sidebar__nav-item.active[data-v-4d0dd26f] {
  background-color: var(--primary-50);
  color: var(--primary-700);
  border-left: 3px solid var(--primary-500);
}
.app-sidebar__nav-icon[data-v-4d0dd26f] {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}
/* 中央工作区 */
.app-main[data-v-4d0dd26f] {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}
.app-main__header[data-v-4d0dd26f] {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-6);
}
.app-main__title[data-v-4d0dd26f] {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}
.app-main__subtitle[data-v-4d0dd26f] {
  color: var(--text-secondary);
  font-size: var(--text-base);
}
.app-main__content[data-v-4d0dd26f] {
  flex: 1;
  padding: var(--space-6);
  max-width: var(--content-max-width);
  margin: 0 auto;
  width: 100%;
}
/* ========== 通用组件样式 ========== */
.btn[data-v-4d0dd26f] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}
.btn-primary[data-v-4d0dd26f] {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}
.btn-primary[data-v-4d0dd26f]:hover {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
}
.btn-secondary[data-v-4d0dd26f] {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}
.btn-secondary[data-v-4d0dd26f]:hover {
  background-color: var(--gray-50);
  border-color: var(--border-secondary);
}
.card[data-v-4d0dd26f] {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}
.card-header[data-v-4d0dd26f] {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}
.card-body[data-v-4d0dd26f] {
  padding: var(--space-6);
}
.card-footer[data-v-4d0dd26f] {
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}
/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
.app-sidebar[data-v-4d0dd26f] {
    position: fixed;
    left: -100%;
    top: var(--header-height);
    height: calc(100vh - var(--header-height));
    z-index: 999;
    transition: left var(--transition-normal);
}
.app-sidebar.open[data-v-4d0dd26f] {
    left: 0;
}
.app-main[data-v-4d0dd26f] {
    margin-left: 0;
}
.app-main__content[data-v-4d0dd26f] {
    padding: var(--space-4);
}
}
/* 组件特定样式 */
.app-layout[data-v-4d0dd26f] {
  font-family: var(--font-family-sans);
}
.auth-layout[data-v-4d0dd26f] {
  /* 确保认证布局占满全屏且独立 */
  min-height: 100vh;
  width: 100%;
  font-family: var(--font-family-sans);
}
/* 移动端专用布局 */
.mobile-layout[data-v-4d0dd26f] {
  min-height: 100vh;
  font-family: var(--font-family-sans);
}
.mobile-main[data-v-4d0dd26f] {
  min-height: calc(100vh - 56px - 64px); /* 减去顶部导航和底部导航高度 */
  padding-top: 56px; /* 顶部导航高度 */
  padding-bottom: 64px; /* 底部导航高度 */
}
.mobile-page-header[data-v-4d0dd26f] {
  padding: 16px 0;
  border-bottom: 1px solid var(--color-border);
  margin-bottom: 16px;
}
.mobile-page-title[data-v-4d0dd26f] {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  color: var(--color-text);
}
.mobile-content[data-v-4d0dd26f] {
  flex: 1;
  padding-bottom: 24px;
}
/* 移动端适配 */
@media (max-width: 768px) {
.app-header__nav[data-v-4d0dd26f] {
    display: none;
}
.app-sidebar__title[data-v-4d0dd26f] {
    display: none;
}
}
</style><style type="text/css" data-vite-dev-id="D:/二创/二创短视频分发/frontend/src/App.vue?vue&amp;type=style&amp;index=0&amp;scoped=7a7a37b1&amp;lang.css">
/* 组件样式 */
</style><style type="text/css" data-vite-dev-id="D:/二创/二创短视频分发/frontend/src/style.css">*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.visible {
  visibility: visible;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.-right-1 {
  right: -0.25rem;
}
.-top-1 {
  top: -0.25rem;
}
.bottom-2 {
  bottom: 0.5rem;
}
.bottom-6 {
  bottom: 1.5rem;
}
.left-1\/2 {
  left: 50%;
}
.left-2 {
  left: 0.5rem;
}
.left-3 {
  left: 0.75rem;
}
.right-2 {
  right: 0.5rem;
}
.right-6 {
  right: 1.5rem;
}
.top-1\/2 {
  top: 50%;
}
.top-2 {
  top: 0.5rem;
}
.top-6 {
  top: 1.5rem;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.mx-6 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-auto {
  margin-top: auto;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.table {
  display: table;
}
.table-cell {
  display: table-cell;
}
.table-row {
  display: table-row;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-20 {
  height: 5rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-8 {
  height: 2rem;
}
.h-full {
  height: 100%;
}
.h-screen {
  height: 100vh;
}
.max-h-96 {
  max-height: 24rem;
}
.min-h-0 {
  min-height: 0px;
}
.min-h-screen {
  min-height: 100vh;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-full {
  width: 100%;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-\[220px\] {
  min-width: 220px;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-xl {
  max-width: 36rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink {
  flex-shrink: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-pointer {
  cursor: pointer;
}
.resize {
  resize: both;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: var(--radius);
}
.rounded-xl {
  border-radius: 0.75rem;
}
.border {
  border-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-l {
  border-left-width: 1px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-2 {
  border-right-width: 2px;
}
.border-t {
  border-top-width: 1px;
}
.border-border {
  border-color: hsl(var(--border));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.bg-accent {
  background-color: hsl(var(--accent));
}
.bg-background {
  background-color: hsl(var(--background));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/40 {
  background-color: rgb(0 0 0 / 0.4);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-card {
  background-color: hsl(var(--card));
}
.bg-destructive {
  background-color: hsl(var(--destructive));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-muted {
  background-color: hsl(var(--muted));
}
.bg-muted-foreground {
  background-color: hsl(var(--muted-foreground));
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-popover {
  background-color: hsl(var(--popover));
}
.bg-primary {
  background-color: hsl(var(--primary));
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-secondary {
  background-color: hsl(var(--secondary));
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}
.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pr-4 {
  padding-right: 1rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.text-accent-foreground {
  color: hsl(var(--accent-foreground));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-destructive {
  color: hsl(var(--destructive));
}
.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}
.text-foreground {
  color: hsl(var(--foreground));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}
.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-primary {
  color: hsl(var(--primary));
}
.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-75 {
  opacity: 0.75;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline {
  outline-style: solid;
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.duration-200 {
  animation-duration: 200ms;
}
.duration-300 {
  animation-duration: 300ms;
}
.running {
  animation-play-state: running;
}
.paused {
  animation-play-state: paused;
}

/* 移除浏览器默认样式 */
body {
  margin: 0;
  padding: 0;
}

/* 主题变量 */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

/* 基础样式 */
html, body {
  height: 100%;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-muted\/80:hover {
  background-color: hsl(var(--muted) / 0.8);
}

.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-primary\/80:hover {
  color: hsl(var(--primary) / 0.8);
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.dark\:bg-blue-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.dark\:bg-green-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}

.dark\:bg-red-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}

.dark\:bg-yellow-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));
}

.dark\:text-blue-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.dark\:text-green-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}

.dark\:text-red-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.dark\:text-yellow-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {

  .md\:block {
    display: block;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:p-6 {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {

  .lg\:block {
    display: block;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1280px) {

  .xl\:w-80 {
    width: 20rem;
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}</style><style type="text/css" data-vite-dev-id="D:/二创/二创短视频分发/frontend/src/pages/Home.vue?vue&amp;type=style&amp;index=0&amp;scoped=933e9cdf&amp;lang.css">
/* 引入统一设计系统 */
/* 
 * 二创短视频分发系统 - 统一UI设计规范
 * 前后端共享的设计系统
 */
/* ========== 设计令牌 (Design Tokens) ========== */
[data-v-933e9cdf]:root {
  /* 主色调 */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 语义色彩 */
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  --info-500: #3b82f6;

  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* 文本色 */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;

  /* 边框色 */
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --border-focus: #3b82f6;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* 间距 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* 字体 */
  --font-family-sans: 'Inter', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Consolas', monospace;

  /* 字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  /* 布局尺寸 */
  --header-height: 4rem;
  --sidebar-width: 16rem;
  --sidebar-collapsed-width: 4rem;
  --content-max-width: 1200px;

  /* 动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}
/* ========== 基础重置 ========== */
[data-v-933e9cdf] {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
body[data-v-933e9cdf] {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* ========== 统一布局系统 ========== */
.app-layout[data-v-933e9cdf] {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-secondary);
}
/* 顶部导航栏 */
.app-header[data-v-933e9cdf] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 0 var(--space-6);
}
.app-header__brand[data-v-933e9cdf] {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-weight: 600;
  font-size: var(--text-lg);
  color: var(--text-primary);
  text-decoration: none;
}
.app-header__brand-icon[data-v-933e9cdf] {
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-lg);
}
.app-header__nav[data-v-933e9cdf] {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  margin-left: auto;
}
.app-header__nav-item[data-v-933e9cdf] {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}
.app-header__nav-item[data-v-933e9cdf]:hover,
.app-header__nav-item.active[data-v-933e9cdf] {
  color: var(--primary-600);
}
/* 主体内容区 */
.app-body[data-v-933e9cdf] {
  display: flex;
  margin-top: var(--header-height);
  min-height: calc(100vh - var(--header-height));
}
/* 左侧功能区 */
.app-sidebar[data-v-933e9cdf] {
  width: var(--sidebar-width);
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-primary);
  transition: width var(--transition-normal);
  overflow: hidden;
}
.app-sidebar.collapsed[data-v-933e9cdf] {
  width: var(--sidebar-collapsed-width);
}
.app-sidebar__header[data-v-933e9cdf] {
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}
.app-sidebar__title[data-v-933e9cdf] {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
.app-sidebar__nav[data-v-933e9cdf] {
  padding: var(--space-2);
}
.app-sidebar__nav-item[data-v-933e9cdf] {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  margin-bottom: var(--space-1);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-weight: 500;
}
.app-sidebar__nav-item[data-v-933e9cdf]:hover {
  background-color: var(--gray-100);
  color: var(--text-primary);
}
.app-sidebar__nav-item.active[data-v-933e9cdf] {
  background-color: var(--primary-50);
  color: var(--primary-700);
  border-left: 3px solid var(--primary-500);
}
.app-sidebar__nav-icon[data-v-933e9cdf] {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}
/* 中央工作区 */
.app-main[data-v-933e9cdf] {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}
.app-main__header[data-v-933e9cdf] {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-6);
}
.app-main__title[data-v-933e9cdf] {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}
.app-main__subtitle[data-v-933e9cdf] {
  color: var(--text-secondary);
  font-size: var(--text-base);
}
.app-main__content[data-v-933e9cdf] {
  flex: 1;
  padding: var(--space-6);
  max-width: var(--content-max-width);
  margin: 0 auto;
  width: 100%;
}
/* ========== 通用组件样式 ========== */
.btn[data-v-933e9cdf] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}
.btn-primary[data-v-933e9cdf] {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}
.btn-primary[data-v-933e9cdf]:hover {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
}
.btn-secondary[data-v-933e9cdf] {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}
.btn-secondary[data-v-933e9cdf]:hover {
  background-color: var(--gray-50);
  border-color: var(--border-secondary);
}
.card[data-v-933e9cdf] {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}
.card-header[data-v-933e9cdf] {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}
.card-body[data-v-933e9cdf] {
  padding: var(--space-6);
}
.card-footer[data-v-933e9cdf] {
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}
/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
.app-sidebar[data-v-933e9cdf] {
    position: fixed;
    left: -100%;
    top: var(--header-height);
    height: calc(100vh - var(--header-height));
    z-index: 999;
    transition: left var(--transition-normal);
}
.app-sidebar.open[data-v-933e9cdf] {
    left: 0;
}
.app-main[data-v-933e9cdf] {
    margin-left: 0;
}
.app-main__content[data-v-933e9cdf] {
    padding: var(--space-4);
}
}
/* 首页特定样式 */
.home-dashboard[data-v-933e9cdf] {
  padding: 0;
}
.welcome-section[data-v-933e9cdf] {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  padding: var(--space-12) var(--space-6);
  margin: calc(-1 * var(--space-6));
  margin-bottom: var(--space-8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--radius-xl);
}
.welcome-content[data-v-933e9cdf] {
  flex: 1;
}
.welcome-title[data-v-933e9cdf] {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  line-height: 1.2;
}
.welcome-subtitle[data-v-933e9cdf] {
  font-size: var(--text-lg);
  opacity: 0.9;
  max-width: 600px;
}
.welcome-stats[data-v-933e9cdf] {
  display: flex;
  gap: var(--space-8);
}
.stat-item[data-v-933e9cdf] {
  text-align: center;
}
.stat-number[data-v-933e9cdf] {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-1);
}
.stat-label[data-v-933e9cdf] {
  font-size: var(--text-sm);
  opacity: 0.8;
}
.features-grid[data-v-933e9cdf] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}
.feature-card[data-v-933e9cdf] {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}
.feature-card[data-v-933e9cdf]:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-200);
}
.feature-card__header[data-v-933e9cdf] {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}
.feature-card__icon[data-v-933e9cdf] {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.feature-card__icon--primary[data-v-933e9cdf] {
  background: var(--primary-50);
  color: var(--primary-600);
}
.feature-card__title[data-v-933e9cdf] {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}
.feature-card__description[data-v-933e9cdf] {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}
.feature-card__actions[data-v-933e9cdf] {
  display: flex;
  gap: var(--space-3);
}
/* 响应式设计 */
@media (max-width: 768px) {
.welcome-section[data-v-933e9cdf] {
    flex-direction: column;
    text-align: center;
    gap: var(--space-6);
}
.welcome-stats[data-v-933e9cdf] {
    justify-content: center;
}
.features-grid[data-v-933e9cdf] {
    grid-template-columns: 1fr;
}
}
</style></head>
  <body>
    <div id="app" data-v-app=""><!-- 登录页面或特殊布局不使用统一布局 --><!-- 桌面端和平板端统一布局 --><div data-v-4d0dd26f="" data-v-7a7a37b1="" class="app-layout"><!-- 顶部导航栏 --><header data-v-4d0dd26f="" class="app-header"><a data-v-4d0dd26f="" aria-current="page" href="/" class="router-link-active router-link-exact-active app-header__brand"><div data-v-4d0dd26f="" class="app-header__brand-icon">🎬</div><span data-v-4d0dd26f="">二创短视频分发系统</span></a><nav data-v-4d0dd26f="" class="app-header__nav"><a data-v-4d0dd26f="" aria-current="page" href="/" class="router-link-active router-link-exact-active app-header__nav-item active"> 首页 </a><a data-v-4d0dd26f="" href="/video-creation" class="app-header__nav-item"> 视频创作 </a><a data-v-4d0dd26f="" href="/compute-test" class="app-header__nav-item"> 计算引擎 </a><a data-v-4d0dd26f="" href="/profile" class="app-header__nav-item"> 个人中心 </a></nav></header><!-- 主体内容区 --><div data-v-4d0dd26f="" class="app-body"><!-- 左侧功能区 --><aside data-v-4d0dd26f="" class="app-sidebar"><div data-v-4d0dd26f="" class="app-sidebar__header"><h3 data-v-4d0dd26f="" class="app-sidebar__title">功能导航</h3></div><nav data-v-4d0dd26f="" class="app-sidebar__nav"><a data-v-4d0dd26f="" aria-current="page" href="/" class="router-link-active router-link-exact-active app-sidebar__nav-item active"><svg data-v-4d0dd26f="" class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20"><path data-v-4d0dd26f="" d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path></svg><span data-v-4d0dd26f="">首页概览</span></a><a data-v-4d0dd26f="" href="/video-creation" class="app-sidebar__nav-item"><svg data-v-4d0dd26f="" class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20"><path data-v-4d0dd26f="" d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM5 8a1 1 0 000 2h8a1 1 0 100-2H5z"></path></svg><span data-v-4d0dd26f="">智能视频生成</span></a><a data-v-4d0dd26f="" href="/compute-test" class="app-sidebar__nav-item"><svg data-v-4d0dd26f="" class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20"><path data-v-4d0dd26f="" fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg><span data-v-4d0dd26f="">计算引擎测试</span></a><a data-v-4d0dd26f="" href="/content-analysis" class="app-sidebar__nav-item"><svg data-v-4d0dd26f="" class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20"><path data-v-4d0dd26f="" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path><path data-v-4d0dd26f="" fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2V6h2v1a1 1 0 102 0v1h1a1 1 0 110 2v1a1 1 0 11-2 0v1H7v-1a1 1 0 10-2 0V9a1 1 0 110-2h2z" clip-rule="evenodd"></path></svg><span data-v-4d0dd26f="">内容分析</span></a><a data-v-4d0dd26f="" href="/batch-processing" class="app-sidebar__nav-item"><svg data-v-4d0dd26f="" class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20"><path data-v-4d0dd26f="" fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"></path></svg><span data-v-4d0dd26f="">批量处理</span></a><a data-v-4d0dd26f="" href="/profile" class="app-sidebar__nav-item"><svg data-v-4d0dd26f="" class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20"><path data-v-4d0dd26f="" fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg><span data-v-4d0dd26f="">个人中心</span></a></nav></aside><!-- 中央工作区 --><main data-v-4d0dd26f="" class="app-main"><!--v-if--><div data-v-4d0dd26f="" class="app-main__content"><div data-v-933e9cdf="" data-v-7a7a37b1="" class="home-dashboard"><!-- 欢迎区域 --><div class="welcome-section" data-v-933e9cdf=""><div class="welcome-content" data-v-933e9cdf=""><h1 class="welcome-title" data-v-933e9cdf=""> 欢迎使用二创短视频分发系统 </h1><p class="welcome-subtitle" data-v-933e9cdf=""> 基于先进的AI技术，为您提供专业的视频内容创作解决方案 </p></div><div class="welcome-stats" data-v-933e9cdf=""><div class="stat-item" data-v-933e9cdf=""><div class="stat-number" data-v-933e9cdf="">1,234</div><div class="stat-label" data-v-933e9cdf="">视频已生成</div></div><div class="stat-item" data-v-933e9cdf=""><div class="stat-number" data-v-933e9cdf="">98.5%</div><div class="stat-label" data-v-933e9cdf="">成功率</div></div><div class="stat-item" data-v-933e9cdf=""><div class="stat-number" data-v-933e9cdf="">24/7</div><div class="stat-label" data-v-933e9cdf="">在线服务</div></div></div></div><!-- 功能卡片 --><div data-v-933e9cdf="" class="features-grid"><div data-v-933e9cdf="" class="feature-card"><div data-v-933e9cdf="" class="feature-card__header"><div data-v-933e9cdf="" class="feature-card__icon feature-card__icon--primary"><svg data-v-933e9cdf="" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-video-icon lucide-video w-6 h-6"><path d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"></path><rect x="2" y="6" width="14" height="12" rx="2"></rect></svg></div><h3 data-v-933e9cdf="" class="feature-card__title">智能视频生成</h3></div><p data-v-933e9cdf="" class="feature-card__description"> 利用AI技术自动生成高质量视频内容，支持多种风格和主题 </p><div data-v-933e9cdf="" class="feature-card__actions"><a data-v-933e9cdf="" href="/video-creation" class="btn btn-primary"> 开始创作 </a></div></div><div data-v-933e9cdf="" class="feature-card"><div data-v-933e9cdf="" class="feature-card__header"><div data-v-933e9cdf="" class="feature-card__icon feature-card__icon--success"><svg data-v-933e9cdf="" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cpu-icon lucide-cpu w-6 h-6"><path d="M12 20v2"></path><path d="M12 2v2"></path><path d="M17 20v2"></path><path d="M17 2v2"></path><path d="M2 12h2"></path><path d="M2 17h2"></path><path d="M2 7h2"></path><path d="M20 12h2"></path><path d="M20 17h2"></path><path d="M20 7h2"></path><path d="M7 20v2"></path><path d="M7 2v2"></path><rect x="4" y="4" width="16" height="16" rx="2"></rect><rect x="8" y="8" width="8" height="8" rx="1"></rect></svg></div><h3 data-v-933e9cdf="" class="feature-card__title">计算引擎</h3></div><p data-v-933e9cdf="" class="feature-card__description"> 强大的本地计算能力，支持FFmpeg、TensorFlow.js等核心技术 </p><div data-v-933e9cdf="" class="feature-card__actions"><a data-v-933e9cdf="" href="/compute-test" class="btn btn-primary"> 测试引擎 </a></div></div><div data-v-933e9cdf="" class="feature-card"><div data-v-933e9cdf="" class="feature-card__header"><div data-v-933e9cdf="" class="feature-card__icon feature-card__icon--warning"><svg data-v-933e9cdf="" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles-icon lucide-sparkles w-6 h-6"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d="M20 3v4"></path><path d="M22 5h-4"></path><path d="M4 17v2"></path><path d="M5 18H3"></path></svg></div><h3 data-v-933e9cdf="" class="feature-card__title">AI增强</h3></div><p data-v-933e9cdf="" class="feature-card__description"> 集成多种AI模型，提供智能内容分析和优化建议 </p><div data-v-933e9cdf="" class="feature-card__actions"><button data-v-933e9cdf="" class="btn btn-secondary" disabled=""> 即将推出 </button></div></div></div><!-- 技术特性 --><div data-v-933e9cdf="" class="bg-gray-50 rounded-xl p-8"><h2 data-v-933e9cdf="" class="text-2xl font-bold text-gray-900 mb-6 text-center"> 核心技术特性 </h2><div data-v-933e9cdf="" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"><div data-v-933e9cdf="" class="text-center"><div data-v-933e9cdf="" class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg data-v-933e9cdf="" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code-icon lucide-code w-8 h-8 text-blue-600"><path d="m16 18 6-6-6-6"></path><path d="m8 6-6 6 6 6"></path></svg></div><h3 data-v-933e9cdf="" class="font-semibold text-gray-900 mb-2">FFmpeg WASM</h3><p data-v-933e9cdf="" class="text-sm text-gray-600">浏览器内视频处理</p></div><div data-v-933e9cdf="" class="text-center"><div data-v-933e9cdf="" class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg data-v-933e9cdf="" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain-icon lucide-brain w-8 h-8 text-green-600"><path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d="M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d="M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d="M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d="M19.938 10.5a4 4 0 0 1 .585.396"></path><path d="M6 18a4 4 0 0 1-1.967-.516"></path><path d="M19.967 17.484A4 4 0 0 1 18 18"></path></svg></div><h3 data-v-933e9cdf="" class="font-semibold text-gray-900 mb-2">TensorFlow.js</h3><p data-v-933e9cdf="" class="text-sm text-gray-600">机器学习推理</p></div><div data-v-933e9cdf="" class="text-center"><div data-v-933e9cdf="" class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg data-v-933e9cdf="" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap-icon lucide-zap w-8 h-8 text-purple-600"><path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg></div><h3 data-v-933e9cdf="" class="font-semibold text-gray-900 mb-2">WebGPU</h3><p data-v-933e9cdf="" class="text-sm text-gray-600">GPU加速计算</p></div><div data-v-933e9cdf="" class="text-center"><div data-v-933e9cdf="" class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg data-v-933e9cdf="" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mic-icon lucide-mic w-8 h-8 text-orange-600"><path d="M12 19v3"></path><path d="M19 10v2a7 7 0 0 1-14 0v-2"></path><rect x="9" y="2" width="6" height="13" rx="3"></rect></svg></div><h3 data-v-933e9cdf="" class="font-semibold text-gray-900 mb-2">Web Speech</h3><p data-v-933e9cdf="" class="text-sm text-gray-600">语音识别合成</p></div></div></div></div></div></main></div></div></div>
    <script type="module" src="/src/main.ts"></script>
  
</body></html>