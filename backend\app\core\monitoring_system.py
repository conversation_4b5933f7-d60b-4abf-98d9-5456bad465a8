"""
完善的监控体系
"""

import asyncio
import json
import logging
import time
import psutil
import platform
from datetime import datetime
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum


class AlertLevel(Enum):
    """告警级别"""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class SystemMetrics:
    """系统指标"""

    timestamp: float
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    process_count: int
    load_average: List[float]

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class ApplicationMetrics:
    """应用指标"""

    timestamp: float
    active_connections: int
    request_count: int
    error_count: int
    avg_response_time: float
    cache_hit_rate: float
    database_connections: int
    queue_size: int

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class Alert:
    """告警"""

    id: str
    level: AlertLevel
    title: str
    message: str
    timestamp: float
    source: str
    resolved: bool = False
    resolved_at: Optional[float] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class MetricsCollector:
    """指标收集器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.system_metrics_history = []
        self.app_metrics_history = []
        self.max_history_size = 1000

    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # 磁盘使用率
            disk = psutil.disk_usage("/")
            disk_percent = disk.percent

            # 网络IO
            network = psutil.net_io_counters()
            network_io = {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv,
            }

            # 进程数量
            process_count = len(psutil.pids())

            # 负载平均值
            if platform.system() != "Windows":
                load_average = list(psutil.getloadavg())
            else:
                load_average = [0.0, 0.0, 0.0]  # Windows不支持

            metrics = SystemMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                network_io=network_io,
                process_count=process_count,
                load_average=load_average,
            )

            # 保存历史记录
            self.system_metrics_history.append(metrics)
            if len(self.system_metrics_history) > self.max_history_size:
                self.system_metrics_history.pop(0)

            return metrics

        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return None

    def collect_application_metrics(self) -> ApplicationMetrics:
        """收集应用指标"""
        try:
            # 这里需要从实际的应用中获取指标
            # 暂时使用模拟数据
            metrics = ApplicationMetrics(
                timestamp=time.time(),
                active_connections=10,  # 从连接池获取
                request_count=100,  # 从请求统计获取
                error_count=2,  # 从错误统计获取
                avg_response_time=0.15,  # 从性能统计获取
                cache_hit_rate=0.85,  # 从缓存统计获取
                database_connections=5,  # 从数据库连接池获取
                queue_size=0,  # 从任务队列获取
            )

            # 保存历史记录
            self.app_metrics_history.append(metrics)
            if len(self.app_metrics_history) > self.max_history_size:
                self.app_metrics_history.pop(0)

            return metrics

        except Exception as e:
            self.logger.error(f"收集应用指标失败: {e}")
            return None

    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """获取指标摘要"""
        cutoff_time = time.time() - (hours * 3600)

        # 过滤最近的指标
        recent_system = [
            m for m in self.system_metrics_history if m.timestamp > cutoff_time
        ]
        recent_app = [m for m in self.app_metrics_history if m.timestamp > cutoff_time]

        summary = {
            "period_hours": hours,
            "system_metrics": {
                "count": len(recent_system),
                "avg_cpu": (
                    sum(m.cpu_percent for m in recent_system) / len(recent_system)
                    if recent_system
                    else 0
                ),
                "avg_memory": (
                    sum(m.memory_percent for m in recent_system) / len(recent_system)
                    if recent_system
                    else 0
                ),
                "avg_disk": (
                    sum(m.disk_percent for m in recent_system) / len(recent_system)
                    if recent_system
                    else 0
                ),
            },
            "application_metrics": {
                "count": len(recent_app),
                "avg_response_time": (
                    sum(m.avg_response_time for m in recent_app) / len(recent_app)
                    if recent_app
                    else 0
                ),
                "avg_cache_hit_rate": (
                    sum(m.cache_hit_rate for m in recent_app) / len(recent_app)
                    if recent_app
                    else 0
                ),
                "total_requests": sum(m.request_count for m in recent_app),
                "total_errors": sum(m.error_count for m in recent_app),
            },
        }

        return summary


class AlertManager:
    """告警管理器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.alerts = []
        self.alert_rules = self._load_alert_rules()
        self.notification_channels = []

    def _load_alert_rules(self) -> List[Dict[str, Any]]:
        """加载告警规则"""
        return [
            {
                "name": "high_cpu_usage",
                "condition": lambda metrics: metrics.cpu_percent > 80,
                "level": AlertLevel.WARNING,
                "message": "CPU使用率过高: {cpu_percent:.1f}%",
            },
            {
                "name": "high_memory_usage",
                "condition": lambda metrics: metrics.memory_percent > 85,
                "level": AlertLevel.WARNING,
                "message": "内存使用率过高: {memory_percent:.1f}%",
            },
            {
                "name": "high_disk_usage",
                "condition": lambda metrics: metrics.disk_percent > 90,
                "level": AlertLevel.ERROR,
                "message": "磁盘使用率过高: {disk_percent:.1f}%",
            },
            {
                "name": "high_error_rate",
                "condition": lambda metrics: hasattr(metrics, "error_count")
                and metrics.error_count > 10,
                "level": AlertLevel.ERROR,
                "message": "错误率过高: {error_count} 个错误",
            },
            {
                "name": "slow_response_time",
                "condition": lambda metrics: hasattr(metrics, "avg_response_time")
                and metrics.avg_response_time > 1.0,
                "level": AlertLevel.WARNING,
                "message": "响应时间过慢: {avg_response_time:.2f}s",
            },
        ]

    def check_alerts(
        self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics
    ):
        """检查告警"""
        current_time = time.time()

        # 检查系统指标告警
        for rule in self.alert_rules:
            try:
                if rule["condition"](system_metrics):
                    self._create_alert(
                        rule["name"],
                        rule["level"],
                        rule["message"].format(**system_metrics.to_dict()),
                        "system",
                    )
            except Exception as e:
                self.logger.error(f"检查系统告警规则失败 {rule['name']}: {e}")

        # 检查应用指标告警
        for rule in self.alert_rules:
            try:
                if rule["condition"](app_metrics):
                    self._create_alert(
                        rule["name"],
                        rule["level"],
                        rule["message"].format(**app_metrics.to_dict()),
                        "application",
                    )
            except Exception as e:
                self.logger.error(f"检查应用告警规则失败 {rule['name']}: {e}")

    def _create_alert(self, name: str, level: AlertLevel, message: str, source: str):
        """创建告警"""
        # 检查是否已存在相同的未解决告警
        existing_alert = next(
            (a for a in self.alerts if a.title == name and not a.resolved), None
        )

        if existing_alert:
            return  # 避免重复告警

        alert = Alert(
            id=f"{name}_{int(time.time())}",
            level=level,
            title=name,
            message=message,
            timestamp=time.time(),
            source=source,
        )

        self.alerts.append(alert)
        self.logger.warning(f"新告警: {alert.title} - {alert.message}")

        # 发送通知
        self._send_notification(alert)

    def _send_notification(self, alert: Alert):
        """发送告警通知"""
        # 这里可以集成各种通知渠道
        # 例如：邮件、短信、Slack、钉钉等
        notification_data = {
            "alert_id": alert.id,
            "level": alert.level.value,
            "title": alert.title,
            "message": alert.message,
            "timestamp": datetime.fromtimestamp(alert.timestamp).isoformat(),
            "source": alert.source,
        }

        self.logger.info(
            f"发送告警通知: {json.dumps(notification_data, ensure_ascii=False)}"
        )

    def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        alert = next((a for a in self.alerts if a.id == alert_id), None)
        if alert and not alert.resolved:
            alert.resolved = True
            alert.resolved_at = time.time()
            self.logger.info(f"告警已解决: {alert.title}")
            return True
        return False

    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return [a for a in self.alerts if not a.resolved]

    def get_alert_history(self, hours: int = 24) -> List[Alert]:
        """获取告警历史"""
        cutoff_time = time.time() - (hours * 3600)
        return [a for a in self.alerts if a.timestamp > cutoff_time]


class HealthChecker:
    """健康检查器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.health_checks = []

    def register_health_check(
        self, name: str, check_func: callable, timeout: float = 5.0
    ):
        """注册健康检查"""
        self.health_checks.append(
            {"name": name, "check_func": check_func, "timeout": timeout}
        )

    async def run_health_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        results = {}
        overall_healthy = True

        for check in self.health_checks:
            try:
                start_time = time.time()

                # 运行健康检查（带超时）
                if asyncio.iscoroutinefunction(check["check_func"]):
                    result = await asyncio.wait_for(
                        check["check_func"](), timeout=check["timeout"]
                    )
                else:
                    result = await asyncio.wait_for(
                        asyncio.to_thread(check["check_func"]), timeout=check["timeout"]
                    )

                duration = time.time() - start_time

                results[check["name"]] = {
                    "status": "healthy" if result else "unhealthy",
                    "duration": round(duration * 1000, 2),  # 毫秒
                    "details": result if isinstance(result, dict) else None,
                }

                if not result:
                    overall_healthy = False

            except asyncio.TimeoutError:
                results[check["name"]] = {
                    "status": "timeout",
                    "duration": check["timeout"] * 1000,
                    "error": f"健康检查超时 ({check['timeout']}s)",
                }
                overall_healthy = False

            except Exception as e:
                results[check["name"]] = {"status": "error", "error": str(e)}
                overall_healthy = False

        return {
            "overall_status": "healthy" if overall_healthy else "unhealthy",
            "timestamp": time.time(),
            "checks": results,
        }


class MonitoringSystem:
    """监控系统主类"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager()
        self.health_checker = HealthChecker()
        self.running = False
        self.monitor_task = None

    async def start(self):
        """启动监控系统"""
        if self.running:
            return

        self.running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        self.logger.info("监控系统已启动")

    async def stop(self):
        """停止监控系统"""
        self.running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        self.logger.info("监控系统已停止")

    async def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 收集指标
                system_metrics = self.metrics_collector.collect_system_metrics()
                app_metrics = self.metrics_collector.collect_application_metrics()

                if system_metrics and app_metrics:
                    # 检查告警
                    self.alert_manager.check_alerts(system_metrics, app_metrics)

                # 等待下一次收集
                await asyncio.sleep(60)  # 每分钟收集一次

            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(60)

    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        return {
            "system_metrics": (
                self.metrics_collector.system_metrics_history[-1].to_dict()
                if self.metrics_collector.system_metrics_history
                else None
            ),
            "application_metrics": (
                self.metrics_collector.app_metrics_history[-1].to_dict()
                if self.metrics_collector.app_metrics_history
                else None
            ),
            "active_alerts": [
                a.to_dict() for a in self.alert_manager.get_active_alerts()
            ],
            "metrics_summary": self.metrics_collector.get_metrics_summary(),
            "timestamp": time.time(),
        }


# 全局监控系统实例
monitoring_system = MonitoringSystem()


def initialize_monitoring():
    """初始化监控系统"""
    logger = logging.getLogger(__name__)
    logger.info("初始化监控系统...")

    # 注册健康检查
    monitoring_system.health_checker.register_health_check(
        "database", lambda: True, timeout=5.0  # 这里应该是实际的数据库检查
    )

    monitoring_system.health_checker.register_health_check(
        "redis", lambda: True, timeout=3.0  # 这里应该是实际的Redis检查
    )

    logger.info("监控系统初始化完成")


if __name__ == "__main__":
    initialize_monitoring()
