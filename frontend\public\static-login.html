<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            width: 100%;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .quick-buttons {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .quick-buttons button {
            background: #28a745;
            width: auto;
            margin: 5px;
            padding: 8px 16px;
            font-size: 14px;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            text-align: left;
            font-family: monospace;
            font-size: 12px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>🎬 二创短视频分发系统</h1>
        <p>静态登录测试页面</p>
        
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin_user">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="Admin123!">
        </div>
        
        <button onclick="testLogin()">测试登录</button>
        
        <div class="quick-buttons">
            <button onclick="fillAdmin()">管理员账号</button>
            <button onclick="fillUser()">普通用户</button>
            <button onclick="testAPI()">测试API</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        function fillAdmin() {
            document.getElementById('username').value = 'admin_user';
            document.getElementById('password').value = 'Admin123!';
        }
        
        function fillUser() {
            document.getElementById('username').value = 'demo_user';
            document.getElementById('password').value = 'Demo123!';
        }
        
        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            result.innerHTML = '<pre>' + message + '</pre>';
            result.className = 'result ' + type;
        }
        
        async function testAPI() {
            showResult('测试API连接...', 'info');
            
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                showResult('API连接成功！\n' + JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                showResult('API连接失败：\n' + error.message, 'error');
            }
        }
        
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'error');
                return;
            }
            
            showResult('正在登录...', 'info');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('登录成功！\n' + JSON.stringify(data, null, 2), 'success');
                    
                    // 保存到localStorage
                    localStorage.setItem('user_info', JSON.stringify(data.user));
                    localStorage.setItem('access_token', 'simple-token');
                    
                    // 3秒后跳转
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 3000);
                } else {
                    showResult('登录失败：\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('登录请求失败：\n' + error.message, 'error');
            }
        }
        
        // 页面加载时自动测试API
        window.onload = function() {
            console.log('静态登录页面已加载');
            testAPI();
        };
    </script>
</body>
</html>
