#!/usr/bin/env python3
"""
系统健康检查API
提供系统状态监控接口
"""

from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.auth import get_current_user
from app.core.database import get_db
from app.services.health_check_service import HealthCheckService

router = APIRouter(prefix="/health", tags=["系统健康检查"])


@router.get("/check")
async def health_check(db: Session = Depends(get_db)):
    """执行系统健康检查"""
    try:
        health_service = HealthCheckService(db)
        health_report = health_service.perform_full_health_check()
        return health_report
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"健康检查失败: {str(e)}",
        )


@router.get("/check/quick")
async def quick_health_check():
    """快速健康检查（不需要数据库连接）"""
    try:
        health_service = HealthCheckService()

        # 只检查系统资源和服务可用性
        full_check = health_service.perform_full_health_check()
        health_report = {
            "timestamp": full_check["timestamp"],
            "status": "healthy",
            "checks": {
                "system_resources": health_service._check_system_resources(),
                "services": health_service._check_services_availability(),
                "disk_space": health_service._check_disk_space(),
            },
        }

        # 计算总体状态
        health_report["status"] = health_service._calculate_overall_status(
            health_report["checks"]
        )

        return health_report

    except Exception as e:
        return {"status": "critical", "error": str(e)}


@router.get("/history")
async def get_health_history(
    limit: int = 10,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取健康检查历史"""
    try:
        health_service = HealthCheckService(db)
        history = health_service.get_health_history(limit)
        return {"history": history, "total_records": len(history)}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取健康检查历史失败: {str(e)}",
        )


@router.get("/trends")
async def get_health_trends(
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取健康趋势分析"""
    try:
        health_service = HealthCheckService(db)
        trends = health_service.get_health_trends()
        return trends
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取健康趋势失败: {str(e)}",
        )


@router.get("/status")
async def get_system_status():
    """获取系统基本状态（公开接口）"""
    try:
        health_service = HealthCheckService()

        # 基本系统信息
        system_check = health_service._check_system_resources()
        disk_check = health_service._check_disk_space()

        full_check = health_service.perform_full_health_check()

        return {
            "status": "online",
            "timestamp": full_check["timestamp"],
            "basic_info": {
                "cpu_usage": system_check.get("cpu", {}).get("usage_percent", 0),
                "memory_usage": system_check.get("memory", {}).get("usage_percent", 0),
                "disk_usage": disk_check.get("usage_percent", 0),
            },
        }

    except Exception:
        return {"status": "error", "message": "系统状态检查失败"}
