"""
输入验证模块
🔒 证据链: 实现严格的输入验证，防止注入攻击和恶意输入
"""

import re
import urllib.parse
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator
from sqlalchemy import text
from sqlalchemy.orm import Session


class ValidationError(Exception):
    """验证错误异常"""

    def __init__(self, message: str, field: str = None):
        self.message = message
        self.field = field
        super().__init__(message)


class InputValidator:
    """增强的输入验证器
    提供全面的数据验证和安全检查功能
    """

    # 🔒 证据链: 安全的正则表达式模式
    PATTERNS = {
        "username": r"^[a-zA-Z0-9_]{3,20}$",
        "email": r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        "phone": r"^1[3-9]\d{9}$",
        "password": r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$",
        "url": r"^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$",
        "filename": r"^[a-zA-Z0-9._-]{1,255}$",
        "safe_string": r"^[a-zA-Z0-9\s\u4e00-\u9fff._-]{1,500}$",  # 支持中文
    }

    # 危险字符和SQL注入模式
    DANGEROUS_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        r"(--|#|/\*|\*/)",
        r"(\bOR\b.*=.*\bOR\b)",
        r"(\bAND\b.*=.*\bAND\b)",
        r'(\'.*\'|".*")',
        r"(<script|</script>|javascript:|vbscript:)",
        r"(\bon\w+\s*=)",
    ]

    @classmethod
    def validate_pattern(cls, value: str, pattern_name: str) -> bool:
        """验证字符串是否匹配指定模式"""
        if not value or not isinstance(value, str):
            return False

        pattern = cls.PATTERNS.get(pattern_name)
        if not pattern:
            return False

        return bool(re.match(pattern, value))

    @classmethod
    def check_sql_injection(cls, value: str) -> bool:
        """检查SQL注入攻击"""
        if not value or not isinstance(value, str):
            return False

        value_lower = value.lower()
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, value_lower, re.IGNORECASE):
                return True

        return False

    @classmethod
    def sanitize_string(cls, value: str, max_length: int = 500) -> str:
        """清理字符串"""
        if not value:
            return ""

        # 移除危险字符
        value = re.sub(r'[<>"\']', "", value)

        # 限制长度
        if len(value) > max_length:
            value = value[:max_length]

        return value.strip()

    @classmethod
    def validate_url(cls, url: str, allowed_schemes: List[str] = None) -> bool:
        """验证URL安全性"""
        if not url:
            return False

        if allowed_schemes is None:
            allowed_schemes = ["http", "https"]

        try:
            parsed = urllib.parse.urlparse(url)

            # 检查协议
            if parsed.scheme not in allowed_schemes:
                return False

            # 检查主机名
            if not parsed.netloc:
                return False

            # 防止内网访问
            if parsed.hostname in ["localhost", "127.0.0.1", "0.0.0.0"]:
                return False

            # 检查私有IP段
            if parsed.hostname:
                if (
                    parsed.hostname.startswith("192.168.")
                    or parsed.hostname.startswith("10.")
                    or parsed.hostname.startswith("172.")
                ):
                    return False

            return True

        except Exception:
            return False

    @classmethod
    def validate_file_path(cls, file_path: str) -> bool:
        """验证文件路径安全性"""
        if not file_path:
            return False

        # 防止路径遍历攻击
        dangerous_patterns = ["../", "..\\", "/etc/", "/proc/", "/sys/", "C:\\"]
        for pattern in dangerous_patterns:
            if pattern in file_path:
                return False

        return True

    @staticmethod
    def sanitize_html(text: str) -> str:
        """清理HTML内容"""
        if not text:
            return ""
        
        # 移除所有HTML标签
        clean_text = re.sub(r'<[^>]+>', '', text)
        
        # 移除多余的空白字符
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        
        return clean_text
    
    @staticmethod
    def validate_sql_injection(text: str) -> bool:
        """检查SQL注入攻击"""
        if not text:
            return True
        
        # SQL注入关键词
        sql_keywords = [
            'union', 'select', 'insert', 'update', 'delete', 'drop',
            'create', 'alter', 'exec', 'execute', 'script', 'declare',
            'cast', 'convert', 'char', 'nchar', 'varchar', 'nvarchar',
            'waitfor', 'delay', 'sp_', 'xp_', 'cmdshell'
        ]
        
        text_lower = text.lower()
        
        # 检查危险模式
        dangerous_patterns = [
            r"';.*--",  # 注释注入
            r"\bunion\s+select\b",  # UNION注入
            r"\bor\s+1\s*=\s*1\b",  # OR注入
            r"\band\s+1\s*=\s*1\b",  # AND注入
            r"\bdrop\s+table\b",  # 删除表
            r"\bexec\s*\(",  # 执行注入
            r"\bscript\s*>",  # 脚本注入
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return False
        
        # 检查关键词
        for keyword in sql_keywords:
            if keyword in text_lower:
                # 进一步检查上下文
                if re.search(rf'\b{keyword}\b', text_lower):
                    return False
        
        return True
    
    @staticmethod
    def validate_xss(text: str) -> bool:
        """检查XSS攻击"""
        if not text:
            return True
        
        # XSS危险模式
        xss_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'vbscript:',
            r'onload\s*=',
            r'onerror\s*=',
            r'onclick\s*=',
            r'onmouseover\s*=',
            r'onfocus\s*=',
            r'onblur\s*=',
            r'onchange\s*=',
            r'onsubmit\s*=',
            r'<iframe[^>]*>',
            r'<object[^>]*>',
            r'<embed[^>]*>',
            r'<link[^>]*>',
            r'<meta[^>]*>',
            r'eval\s*\(',
            r'expression\s*\(',
            r'url\s*\(',
            r'@import'
        ]
        
        text_lower = text.lower()
        
        for pattern in xss_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE | re.DOTALL):
                return False
        
        return True
    
    @staticmethod
    def validate_path_traversal(path: str) -> bool:
        """检查路径遍历攻击"""
        if not path:
            return True
        
        # 危险路径模式
        dangerous_patterns = [
            r'\.\.', # 父目录
            r'\\\.\.\\', # Windows路径遍历
            r'/\.\./', # Unix路径遍历
            r'%2e%2e', # URL编码的..
            r'%252e%252e', # 双重URL编码的..
            r'\\\\', # UNC路径
            r'/etc/', # 系统目录
            r'/proc/', # 进程目录
            r'/sys/', # 系统目录
            r'C:\\', # Windows系统盘
            r'D:\\', # Windows其他盘
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, path, re.IGNORECASE):
                return False
        
        return True
    
    @staticmethod
    def validate_command_injection(text: str) -> bool:
        """检查命令注入攻击"""
        if not text:
            return True
        
        # 命令注入危险字符
        dangerous_chars = ['|', '&', ';', '$', '`', '>', '<', '\n', '\r']
        
        for char in dangerous_chars:
            if char in text:
                return False
        
        # 危险命令
        dangerous_commands = [
            'rm', 'del', 'format', 'fdisk', 'mkfs', 'shutdown',
            'reboot', 'halt', 'poweroff', 'init', 'kill', 'killall',
            'ps', 'top', 'netstat', 'ifconfig', 'ping', 'nslookup',
            'wget', 'curl', 'nc', 'telnet', 'ssh', 'ftp', 'tftp',
            'cat', 'more', 'less', 'head', 'tail', 'grep', 'find',
            'locate', 'which', 'whereis', 'whoami', 'id', 'groups',
            'su', 'sudo', 'passwd', 'chown', 'chmod', 'chgrp'
        ]
        
        text_lower = text.lower()
        
        for cmd in dangerous_commands:
            if re.search(rf'\b{cmd}\b', text_lower):
                return False
        
        return True
    
    @staticmethod
    def validate_ldap_injection(text: str) -> bool:
        """检查LDAP注入攻击"""
        if not text:
            return True
        
        # LDAP注入危险字符
        dangerous_chars = ['(', ')', '*', '\\', '/', '+', '<', '>', '"', "'", ';', ',']
        
        for char in dangerous_chars:
            if char in text:
                return False
        
        return True
    
    @staticmethod
    def validate_nosql_injection(text: str) -> bool:
        """检查NoSQL注入攻击"""
        if not text:
            return True
        
        # NoSQL注入危险模式
        dangerous_patterns = [
            r'\$where',
            r'\$ne',
            r'\$gt',
            r'\$lt',
            r'\$gte',
            r'\$lte',
            r'\$in',
            r'\$nin',
            r'\$or',
            r'\$and',
            r'\$not',
            r'\$nor',
            r'\$exists',
            r'\$type',
            r'\$regex',
            r'\$options',
            r'\$elemMatch',
            r'\$size',
            r'\$all',
            r'\$slice',
            r'function\s*\(',
            r'this\.',
            r'db\.',
            r'ObjectId\s*\(',
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return False
        
        return True
    
    @staticmethod
    def validate_json_structure(data: dict, max_depth: int = 10, max_keys: int = 100) -> bool:
        """验证JSON结构安全性"""
        def check_depth(obj, current_depth=0):
            if current_depth > max_depth:
                return False
            
            if isinstance(obj, dict):
                if len(obj) > max_keys:
                    return False
                for value in obj.values():
                    if not check_depth(value, current_depth + 1):
                        return False
            elif isinstance(obj, list):
                if len(obj) > max_keys:
                    return False
                for item in obj:
                    if not check_depth(item, current_depth + 1):
                        return False
            
            return True
        
        return check_depth(data)
    
    @staticmethod
    def sanitize_input(text: str, max_length: int = 1000) -> str:
        """全面清理输入数据"""
        if not text:
            return ""
        
        # 限制长度
        if len(text) > max_length:
            text = text[:max_length]
        
        # 移除控制字符
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')
        
        # 清理HTML
        text = InputValidator.sanitize_html(text)
        
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    @staticmethod
    def validate_comprehensive(text: str, check_all: bool = True) -> dict:
        """综合安全验证
        
        Args:
            text: 要验证的文本
            check_all: 是否执行所有检查
        
        Returns:
            dict: 验证结果
        """
        results = {
            'is_safe': True,
            'issues': [],
            'sanitized_text': text
        }
        
        if not text:
            return results
        
        # SQL注入检查
        if not InputValidator.validate_sql_injection(text):
            results['is_safe'] = False
            results['issues'].append('SQL injection detected')
        
        # XSS检查
        if not InputValidator.validate_xss(text):
            results['is_safe'] = False
            results['issues'].append('XSS attack detected')
        
        # 路径遍历检查
        if not InputValidator.validate_path_traversal(text):
            results['is_safe'] = False
            results['issues'].append('Path traversal detected')
        
        # 命令注入检查
        if not InputValidator.validate_command_injection(text):
            results['is_safe'] = False
            results['issues'].append('Command injection detected')
        
        if check_all:
            # LDAP注入检查
            if not InputValidator.validate_ldap_injection(text):
                results['is_safe'] = False
                results['issues'].append('LDAP injection detected')
            
            # NoSQL注入检查
            if not InputValidator.validate_nosql_injection(text):
                results['is_safe'] = False
                results['issues'].append('NoSQL injection detected')
        
        # 清理文本
        results['sanitized_text'] = InputValidator.sanitize_input(text)
        
        return results


class SafeQueryBuilder:
    """安全查询构建器"""

    @staticmethod
    def build_safe_query(
        db: Session,
        base_query: str,
        params: Dict[str, Any],
        allowed_columns: List[str] = None,
    ):
        """构建安全的SQL查询"""
        # 🔒 证据链: 使用参数化查询防止SQL注入

        # 验证参数
        for key, value in params.items():
            if isinstance(value, str):
                if InputValidator.check_sql_injection(value):
                    raise ValidationError(f"Potential SQL injection detected in {key}")

        # 验证列名
        if allowed_columns:
            for key in params.keys():
                if key not in allowed_columns:
                    raise ValidationError(f"Column {key} not allowed")

        # 使用SQLAlchemy的text()和参数绑定
        return db.execute(text(base_query), params)


# Pydantic验证器
class UserCreateValidator(BaseModel):
    """用户创建验证器"""

    username: str = Field(..., min_length=3, max_length=20)
    email: str = Field(..., max_length=100)
    password: str = Field(..., min_length=8, max_length=128)
    full_name: Optional[str] = Field(None, max_length=100)
    phone_number: Optional[str] = Field(None, max_length=20)
    bio: Optional[str] = Field(None, max_length=500)

    @validator("username")
    def validate_username(cls, v):
        if not InputValidator.validate_pattern(v, "username"):
            raise ValueError("用户名只能包含字母、数字和下划线，长度3-20位")
        return v

    @validator("email")
    def validate_email(cls, v):
        if not InputValidator.validate_pattern(v, "email"):
            raise ValueError("邮箱格式不正确")
        return v

    @validator("password")
    def validate_password(cls, v):
        if not InputValidator.validate_pattern(v, "password"):
            raise ValueError("密码必须包含大小写字母、数字和特殊字符，长度至少8位")
        return v

    @validator("phone_number")
    def validate_phone(cls, v):
        if v and not InputValidator.validate_pattern(v, "phone"):
            raise ValueError("手机号格式不正确")
        return v

    @validator("bio")
    def validate_bio(cls, v):
        if v:
            if InputValidator.check_sql_injection(v):
                raise ValueError("简介包含不安全内容")
            return InputValidator.sanitize_string(v, 500)
        return v


class ContentCreateValidator(BaseModel):
    """内容创建验证器"""

    title: str = Field(..., min_length=1, max_length=200)
    content_type: str = Field(..., regex=r"^(text|video|image|audio)$")
    original_text: Optional[str] = Field(None, max_length=10000)
    project_id: Optional[int] = Field(None, ge=1)

    @validator("title")
    def validate_title(cls, v):
        if InputValidator.check_sql_injection(v):
            raise ValueError("标题包含不安全内容")
        return InputValidator.sanitize_string(v, 200)

    @validator("original_text")
    def validate_content(cls, v):
        if v:
            if InputValidator.check_sql_injection(v):
                raise ValueError("内容包含不安全内容")
            return InputValidator.sanitize_string(v, 10000)
        return v


class VideoProcessingValidator(BaseModel):
    """视频处理验证器"""

    url: str = Field(..., max_length=2000)
    quality: str = Field(default="720p", regex=r"^(360p|720p|1080p|1440p|best)$")
    format: str = Field(default="mp4", regex=r"^(mp4|avi|mov|mkv|webm)$")

    @validator("url")
    def validate_url(cls, v):
        if not InputValidator.validate_url(v, ["http", "https"]):
            raise ValueError("URL格式不正确或不安全")
        return v


class SearchQueryValidator(BaseModel):
    """搜索查询验证器"""

    query: str = Field(..., min_length=1, max_length=100)
    page: int = Field(default=1, ge=1, le=1000)
    size: int = Field(default=20, ge=1, le=100)
    sort_by: Optional[str] = Field(None, regex=r"^(created_at|updated_at|title|score)$")
    order: str = Field(default="desc", regex=r"^(asc|desc)$")

    @validator("query")
    def validate_search_query(cls, v):
        if InputValidator.check_sql_injection(v):
            raise ValueError("搜索查询包含不安全内容")
        return InputValidator.sanitize_string(v, 100)


# 验证装饰器
def validate_input(validator_class):
    """输入验证装饰器"""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 查找请求数据
            request_data = None
            for arg in args:
                if isinstance(arg, dict):
                    request_data = arg
                    break

            if request_data:
                try:
                    # 验证输入数据
                    validated_data = validator_class(**request_data)
                    # 替换原始数据
                    args = tuple(
                        validated_data.dict() if arg is request_data else arg
                        for arg in args
                    )
                except Exception as e:
                    raise ValidationError(f"输入验证失败: {str(e)}")

            return await func(*args, **kwargs)

        return wrapper

    return decorator


# 常用验证函数
def validate_id(value: Any) -> int:
    """验证ID参数"""
    try:
        id_value = int(value)
        if id_value <= 0:
            raise ValidationError("ID必须是正整数")
        return id_value
    except (ValueError, TypeError):
        raise ValidationError("ID格式不正确")


def validate_pagination(page: Any, size: Any) -> tuple:
    """验证分页参数"""
    try:
        page_num = int(page) if page else 1
        page_size = int(size) if size else 20

        if page_num < 1:
            page_num = 1
        if page_size < 1 or page_size > 100:
            page_size = 20

        return page_num, page_size
    except (ValueError, TypeError):
        return 1, 20


def validate_json_data(data: Any, max_size: int = 1024 * 1024) -> bool:
    """验证JSON数据"""
    import json

    try:
        if isinstance(data, str):
            json_str = data
        else:
            json_str = json.dumps(data)

        # 检查大小
        if len(json_str.encode("utf-8")) > max_size:
            raise ValidationError("JSON数据过大")

        # 检查是否包含危险内容
        if InputValidator.check_sql_injection(json_str):
            raise ValidationError("JSON数据包含不安全内容")

        return True

    except json.JSONDecodeError:
        raise ValidationError("JSON格式不正确")
    except Exception as e:
        raise ValidationError(f"JSON验证失败: {str(e)}")
