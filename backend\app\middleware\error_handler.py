"""统一错误处理中间件
提供全局异常处理和错误响应标准化
"""

import logging
import traceback
import time
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR

from app.core.config import settings

logger = logging.getLogger(__name__)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """统一错误处理中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        
        # 敏感信息模式（用于清理错误消息）
        self.sensitive_patterns = [
            'password', 'token', 'secret', 'key', 'auth',
            'credential', 'session', 'cookie', 'api_key'
        ]
        
        # 错误代码映射
        self.error_codes = {
            400: "BAD_REQUEST",
            401: "UNAUTHORIZED",
            403: "FORBIDDEN",
            404: "NOT_FOUND",
            405: "METHOD_NOT_ALLOWED",
            409: "CONFLICT",
            422: "VALIDATION_ERROR",
            429: "RATE_LIMIT_EXCEEDED",
            500: "INTERNAL_SERVER_ERROR",
            502: "BAD_GATEWAY",
            503: "SERVICE_UNAVAILABLE",
            504: "GATEWAY_TIMEOUT"
        }
    
    def sanitize_error_message(self, message: str) -> str:
        """清理错误消息中的敏感信息"""
        if not message:
            return "An error occurred"
        
        message_lower = message.lower()
        
        # 检查是否包含敏感信息
        for pattern in self.sensitive_patterns:
            if pattern in message_lower:
                return "An error occurred while processing your request"
        
        # 移除可能的SQL错误信息
        if any(sql_keyword in message_lower for sql_keyword in 
               ['mysql', 'postgresql', 'sqlite', 'database', 'table', 'column']):
            return "Database operation failed"
        
        # 移除文件路径信息
        if any(path_indicator in message for path_indicator in ['/', '\\', 'C:', 'D:']):
            return "File operation failed"
        
        return message
    
    def get_error_details(self, exc: Exception) -> Dict[str, Any]:
        """获取错误详细信息"""
        error_type = type(exc).__name__
        error_message = str(exc)
        
        # 清理错误消息
        sanitized_message = self.sanitize_error_message(error_message)
        
        details = {
            "type": error_type,
            "message": sanitized_message,
            "timestamp": time.time()
        }
        
        # 在开发环境中添加更多调试信息
        if settings.DEBUG and settings.ENVIRONMENT == "development":
            details.update({
                "original_message": error_message,
                "traceback": traceback.format_exc()
            })
        
        return details
    
    def create_error_response(
        self, 
        status_code: int, 
        error_code: str, 
        message: str, 
        details: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """创建标准化错误响应"""
        
        response_data = {
            "error": {
                "code": error_code,
                "message": message,
                "status_code": status_code,
                "timestamp": time.time()
            }
        }
        
        if details:
            response_data["error"]["details"] = details
        
        if request_id:
            response_data["error"]["request_id"] = request_id
        
        # 添加帮助信息
        if status_code == 400:
            response_data["error"]["help"] = "Please check your request parameters"
        elif status_code == 401:
            response_data["error"]["help"] = "Please provide valid authentication credentials"
        elif status_code == 403:
            response_data["error"]["help"] = "You don't have permission to access this resource"
        elif status_code == 404:
            response_data["error"]["help"] = "The requested resource was not found"
        elif status_code == 429:
            response_data["error"]["help"] = "Please wait before making more requests"
        elif status_code >= 500:
            response_data["error"]["help"] = "Please try again later or contact support"
        
        return JSONResponse(
            status_code=status_code,
            content=response_data
        )
    
    def log_error(
        self, 
        request: Request, 
        exc: Exception, 
        status_code: int,
        request_id: Optional[str] = None
    ) -> None:
        """记录错误日志"""
        
        # 构建日志消息
        log_data = {
            "method": request.method,
            "url": str(request.url),
            "status_code": status_code,
            "error_type": type(exc).__name__,
            "error_message": str(exc),
            "user_agent": request.headers.get("user-agent", "unknown"),
            "client_ip": request.client.host,
            "timestamp": time.time()
        }
        
        if request_id:
            log_data["request_id"] = request_id
        
        # 添加用户信息（如果可用）
        if hasattr(request.state, 'user_id'):
            log_data["user_id"] = request.state.user_id
        
        # 根据错误级别选择日志级别
        if status_code >= 500:
            logger.error(f"Server Error: {log_data}", exc_info=True)
        elif status_code >= 400:
            logger.warning(f"Client Error: {log_data}")
        else:
            logger.info(f"Request Error: {log_data}")
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """处理请求和异常"""
        
        # 生成请求ID
        request_id = f"{int(time.time() * 1000)}_{hash(str(request.url)) % 10000}"
        request.state.request_id = request_id
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 添加请求ID到响应头
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except HTTPException as exc:
            # 处理HTTP异常
            error_code = self.error_codes.get(exc.status_code, "HTTP_ERROR")
            
            # 记录日志
            self.log_error(request, exc, exc.status_code, request_id)
            
            # 获取错误详情
            details = self.get_error_details(exc)
            
            return self.create_error_response(
                status_code=exc.status_code,
                error_code=error_code,
                message=self.sanitize_error_message(exc.detail),
                details=details,
                request_id=request_id
            )
            
        except ValueError as exc:
            # 处理值错误
            self.log_error(request, exc, 400, request_id)
            
            return self.create_error_response(
                status_code=400,
                error_code="VALIDATION_ERROR",
                message="Invalid input data",
                details=self.get_error_details(exc),
                request_id=request_id
            )
            
        except PermissionError as exc:
            # 处理权限错误
            self.log_error(request, exc, 403, request_id)
            
            return self.create_error_response(
                status_code=403,
                error_code="PERMISSION_DENIED",
                message="Access denied",
                details=self.get_error_details(exc),
                request_id=request_id
            )
            
        except FileNotFoundError as exc:
            # 处理文件未找到错误
            self.log_error(request, exc, 404, request_id)
            
            return self.create_error_response(
                status_code=404,
                error_code="RESOURCE_NOT_FOUND",
                message="Resource not found",
                details=self.get_error_details(exc),
                request_id=request_id
            )
            
        except ConnectionError as exc:
            # 处理连接错误
            self.log_error(request, exc, 503, request_id)
            
            return self.create_error_response(
                status_code=503,
                error_code="SERVICE_UNAVAILABLE",
                message="Service temporarily unavailable",
                details=self.get_error_details(exc),
                request_id=request_id
            )
            
        except TimeoutError as exc:
            # 处理超时错误
            self.log_error(request, exc, 504, request_id)
            
            return self.create_error_response(
                status_code=504,
                error_code="TIMEOUT",
                message="Request timeout",
                details=self.get_error_details(exc),
                request_id=request_id
            )
            
        except Exception as exc:
            # 处理所有其他异常
            self.log_error(request, exc, 500, request_id)
            
            return self.create_error_response(
                status_code=500,
                error_code="INTERNAL_SERVER_ERROR",
                message="Internal server error",
                details=self.get_error_details(exc),
                request_id=request_id
            )


class CustomHTTPException(HTTPException):
    """自定义HTTP异常"""
    
    def __init__(
        self, 
        status_code: int, 
        detail: str, 
        error_code: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code


class ValidationError(CustomHTTPException):
    """验证错误"""
    
    def __init__(self, detail: str, field: Optional[str] = None):
        super().__init__(
            status_code=422,
            detail=detail,
            error_code="VALIDATION_ERROR"
        )
        self.field = field


class AuthenticationError(CustomHTTPException):
    """认证错误"""
    
    def __init__(self, detail: str = "Authentication required"):
        super().__init__(
            status_code=401,
            detail=detail,
            error_code="AUTHENTICATION_ERROR",
            headers={"WWW-Authenticate": "Bearer"}
        )


class AuthorizationError(CustomHTTPException):
    """授权错误"""
    
    def __init__(self, detail: str = "Access denied"):
        super().__init__(
            status_code=403,
            detail=detail,
            error_code="AUTHORIZATION_ERROR"
        )


class ResourceNotFoundError(CustomHTTPException):
    """资源未找到错误"""
    
    def __init__(self, resource: str = "Resource"):
        super().__init__(
            status_code=404,
            detail=f"{resource} not found",
            error_code="RESOURCE_NOT_FOUND"
        )


class ConflictError(CustomHTTPException):
    """冲突错误"""
    
    def __init__(self, detail: str = "Resource conflict"):
        super().__init__(
            status_code=409,
            detail=detail,
            error_code="CONFLICT_ERROR"
        )


class RateLimitError(CustomHTTPException):
    """频率限制错误"""
    
    def __init__(self, detail: str = "Rate limit exceeded", retry_after: int = 60):
        super().__init__(
            status_code=429,
            detail=detail,
            error_code="RATE_LIMIT_EXCEEDED",
            headers={"Retry-After": str(retry_after)}
        )


class ServiceUnavailableError(CustomHTTPException):
    """服务不可用错误"""
    
    def __init__(self, detail: str = "Service temporarily unavailable"):
        super().__init__(
            status_code=503,
            detail=detail,
            error_code="SERVICE_UNAVAILABLE"
        )


# 便捷函数
def create_error_handler_middleware():
    """创建错误处理中间件"""
    return ErrorHandlerMiddleware


def handle_validation_error(field: str, message: str) -> ValidationError:
    """创建验证错误"""
    return ValidationError(detail=f"{field}: {message}", field=field)


def handle_authentication_error(message: str = None) -> AuthenticationError:
    """创建认证错误"""
    return AuthenticationError(detail=message or "Authentication required")


def handle_authorization_error(message: str = None) -> AuthorizationError:
    """创建授权错误"""
    return AuthorizationError(detail=message or "Access denied")


def handle_not_found_error(resource: str = "Resource") -> ResourceNotFoundError:
    """创建资源未找到错误"""
    return ResourceNotFoundError(resource=resource)