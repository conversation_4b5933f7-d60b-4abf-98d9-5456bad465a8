{"timestamp": "2025-07-23T19:52:38.524480", "project_root": ".", "requirements_file": "requirements_secure.txt", "audit_results": {"pip_audit": {"status": "vulnerabilities_found", "vulnerabilities": []}, "safety_check": {"status": "vulnerabilities_found", "vulnerabilities": []}, "known_vulnerabilities": {"status": "vulnerabilities_found", "vulnerabilities": [{"package": "pyjwt", "version": "2.10.1", "vulnerability": "Known vulnerability in <2.4.0", "severity": "high"}]}, "high_risk_packages": {"status": "clean", "packages": []}, "outdated_packages": {"status": "outdated_found", "packages": [{"name": "aliyun-python-sdk-core", "version": "2.13.25", "latest_version": "2.16.0", "latest_filetype": "sdist"}, {"name": "aliyun-python-sdk-core-v3", "version": "2.13.11", "latest_version": "2.13.33", "latest_filetype": "sdist"}, {"name": "aliyun-python-sdk-kms", "version": "2.11.0", "latest_version": "2.16.5", "latest_filetype": "wheel"}, {"name": "altgraph", "version": "0.17", "latest_version": "0.17.4", "latest_filetype": "wheel"}, {"name": "asn1crypto", "version": "1.4.0", "latest_version": "1.5.1", "latest_filetype": "wheel"}, {"name": "bcrypt", "version": "3.1.7", "latest_version": "4.3.0", "latest_filetype": "wheel"}, {"name": "cachelib", "version": "0.1.1", "latest_version": "0.13.0", "latest_filetype": "wheel"}, {"name": "cachetools", "version": "4.1.1", "latest_version": "5.5.2", "latest_filetype": "wheel"}, {"name": "certifi", "version": "2020.6.20", "latest_version": "2025.7.14", "latest_filetype": "wheel"}, {"name": "cffi", "version": "1.14.1", "latest_version": "1.17.1", "latest_filetype": "wheel"}, {"name": "cfgv", "version": "3.2.0", "latest_version": "3.4.0", "latest_filetype": "wheel"}, {"name": "chardet", "version": "3.0.4", "latest_version": "5.2.0", "latest_filetype": "wheel"}, {"name": "colorama", "version": "0.4.3", "latest_version": "0.4.6", "latest_filetype": "wheel"}, {"name": "cos-python-sdk-v5", "version": "1.7.7", "latest_version": "1.9.38", "latest_filetype": "sdist"}, {"name": "cyclonedx-python-lib", "version": "7.6.2", "latest_version": "9.1.0", "latest_filetype": "wheel"}, {"name": "<PERSON><PERSON><PERSON>", "version": "0.29.21", "latest_version": "3.1.2", "latest_filetype": "wheel"}, {"name": "dicttoxml", "version": "1.7.4", "latest_version": "1.7.16", "latest_filetype": "wheel"}, {"name": "distlib", "version": "0.3.1", "latest_version": "0.4.0", "latest_filetype": "wheel"}, {"name": "dnspython", "version": "2.0.0", "latest_version": "2.6.1", "latest_filetype": "wheel"}, {"name": "Flask", "version": "2.0.3", "latest_version": "3.0.3", "latest_filetype": "wheel"}, {"name": "Flask-Session", "version": "0.3.2", "latest_version": "0.8.0", "latest_filetype": "wheel"}, {"name": "Flask-SocketIO", "version": "4.3.1", "latest_version": "5.5.1", "latest_filetype": "wheel"}, {"name": "Flask-SQLAlchemy", "version": "2.4.4", "latest_version": "3.1.1", "latest_filetype": "wheel"}, {"name": "future", "version": "0.18.2", "latest_version": "1.0.0", "latest_filetype": "wheel"}, {"name": "gevent", "version": "20.6.2", "latest_version": "24.2.1", "latest_filetype": "wheel"}, {"name": "google-api-core", "version": "1.22.0", "latest_version": "2.25.1", "latest_filetype": "wheel"}, {"name": "google-auth", "version": "1.20.0", "latest_version": "2.40.3", "latest_filetype": "wheel"}, {"name": "google-cloud-core", "version": "1.4.0", "latest_version": "2.4.3", "latest_filetype": "wheel"}, {"name": "googleapis-common-protos", "version": "1.52.0", "latest_version": "1.70.0", "latest_filetype": "wheel"}, {"name": "greenlet", "version": "0.4.16", "latest_version": "3.1.1", "latest_filetype": "wheel"}, {"name": "identify", "version": "1.4.25", "latest_version": "2.6.1", "latest_filetype": "wheel"}, {"name": "idna", "version": "2.10", "latest_version": "3.10", "latest_filetype": "wheel"}, {"name": "importlib-metadata", "version": "1.7.0", "latest_version": "8.5.0", "latest_filetype": "wheel"}, {"name": "importlib-resources", "version": "3.0.0", "latest_version": "6.4.5", "latest_filetype": "wheel"}, {"name": "IPy", "version": "1.0", "latest_version": "1.1", "latest_filetype": "sdist"}, {"name": "itsdangerous", "version": "2.1.1", "latest_version": "2.2.0", "latest_filetype": "wheel"}, {"name": "jmespath", "version": "0.10.0", "latest_version": "1.0.1", "latest_filetype": "wheel"}, {"name": "lxml", "version": "4.5.2", "latest_version": "6.0.0", "latest_filetype": "wheel"}, {"name": "MarkupSafe", "version": "2.1.0", "latest_version": "2.1.5", "latest_filetype": "wheel"}, {"name": "mysqlclient", "version": "2.0.1", "latest_version": "2.2.7", "latest_filetype": "sdist"}, {"name": "Naked", "version": "0.1.31", "latest_version": "0.1.32", "latest_filetype": "wheel"}, {"name": "nodeenv", "version": "1.4.0", "latest_version": "1.9.1", "latest_filetype": "wheel"}, {"name": "oss2", "version": "2.5.0", "latest_version": "2.19.1", "latest_filetype": "sdist"}, {"name": "packaging", "version": "24.2", "latest_version": "25.0", "latest_filetype": "wheel"}, {"name": "<PERSON><PERSON><PERSON>", "version": "2.7.1", "latest_version": "3.5.1", "latest_filetype": "wheel"}, {"name": "pefile", "version": "2019.4.18", "latest_version": "2024.8.26", "latest_filetype": "wheel"}, {"name": "Pillow", "version": "7.2.0", "latest_version": "10.4.0", "latest_filetype": "wheel"}, {"name": "pip", "version": "20.3.1", "latest_version": "25.0.1", "latest_filetype": "wheel"}, {"name": "pluggy", "version": "0.13.1", "latest_version": "1.5.0", "latest_filetype": "wheel"}, {"name": "pre-commit", "version": "2.6.0", "latest_version": "3.5.0", "latest_filetype": "wheel"}, {"name": "protobuf", "version": "3.12.4", "latest_version": "5.29.5", "latest_filetype": "wheel"}, {"name": "psutil", "version": "6.1.1", "latest_version": "7.0.0", "latest_filetype": "wheel"}, {"name": "py", "version": "1.9.0", "latest_version": "1.11.0", "latest_filetype": "wheel"}, {"name": "py-serializable", "version": "1.1.2", "latest_version": "2.1.0", "latest_filetype": "wheel"}, {"name": "py7zr", "version": "0.9.1", "latest_version": "0.22.0", "latest_filetype": "wheel"}, {"name": "pyasn1", "version": "0.4.8", "latest_version": "0.6.1", "latest_filetype": "wheel"}, {"name": "pyasn1-modules", "version": "0.2.8", "latest_version": "0.4.2", "latest_filetype": "wheel"}, {"name": "pyc<PERSON><PERSON>", "version": "2.20", "latest_version": "2.22", "latest_filetype": "wheel"}, {"name": "pycryptodome", "version": "3.9.8", "latest_version": "3.23.0", "latest_filetype": "wheel"}, {"name": "pydantic", "version": "2.9.2", "latest_version": "2.10.6", "latest_filetype": "wheel"}, {"name": "pydantic-core", "version": "2.23.4", "latest_version": "2.27.2", "latest_filetype": "wheel"}, {"name": "PyInstaller", "version": "3.6", "latest_version": "6.14.2", "latest_filetype": "wheel"}, {"name": "pymemcache", "version": "3.2.0", "latest_version": "4.0.0", "latest_filetype": "wheel"}, {"name": "pymongo", "version": "3.11.0", "latest_version": "4.10.1", "latest_filetype": "wheel"}, {"name": "pymssql", "version": "2.1.4", "latest_version": "2.3.4", "latest_filetype": "sdist"}, {"name": "PyMySQL", "version": "0.10.0", "latest_version": "1.1.1", "latest_filetype": "wheel"}, {"name": "PyNaCl", "version": "1.4.0", "latest_version": "1.5.0", "latest_filetype": "wheel"}, {"name": "pyOpenSSL", "version": "19.1.0", "latest_version": "25.1.0", "latest_filetype": "wheel"}, {"name": "pyotp", "version": "2.4.0", "latest_version": "2.9.0", "latest_filetype": "wheel"}, {"name": "pyparsing", "version": "2.4.7", "latest_version": "3.1.4", "latest_filetype": "wheel"}, {"name": "python-dateutil", "version": "2.8.2", "latest_version": "2.9.0.post0", "latest_filetype": "wheel"}, {"name": "python-engineio", "version": "3.13.1", "latest_version": "4.12.2", "latest_filetype": "wheel"}, {"name": "python-memcached", "version": "1.59", "latest_version": "1.62", "latest_filetype": "wheel"}, {"name": "python-socketio", "version": "4.6.0", "latest_version": "5.13.0", "latest_filetype": "wheel"}, {"name": "pytz", "version": "2020.1", "latest_version": "2025.2", "latest_filetype": "wheel"}, {"name": "pywin32", "version": "228", "latest_version": "311", "latest_filetype": "wheel"}, {"name": "pywin32-ctypes", "version": "0.2.0", "latest_version": "0.2.3", "latest_filetype": "wheel"}, {"name": "PyYAML", "version": "5.3.1", "latest_version": "6.0.2", "latest_filetype": "wheel"}, {"name": "qiniu", "version": "7.2.8", "latest_version": "7.17.0", "latest_filetype": "wheel"}, {"name": "rich", "version": "13.9.4", "latest_version": "14.0.0", "latest_filetype": "wheel"}, {"name": "rsa", "version": "4.6", "latest_version": "4.9.1", "latest_filetype": "wheel"}, {"name": "six", "version": "1.15.0", "latest_version": "1.17.0", "latest_filetype": "wheel"}, {"name": "SQLAlchemy", "version": "1.3.18", "latest_version": "2.0.41", "latest_filetype": "wheel"}, {"name": "tencentcloud-sdk-python", "version": "3.0.532", "latest_version": "3.0.1428", "latest_filetype": "wheel"}, {"name": "texttable", "version": "1.6.2", "latest_version": "1.7.0", "latest_filetype": "wheel"}, {"name": "tokenizers", "version": "0.20.3", "latest_version": "0.21.0", "latest_filetype": "sdist"}, {"name": "toml", "version": "0.10.1", "latest_version": "0.10.2", "latest_filetype": "wheel"}, {"name": "tox", "version": "3.18.1", "latest_version": "4.25.0", "latest_filetype": "wheel"}, {"name": "translate", "version": "3.5.0", "latest_version": "3.6.1", "latest_filetype": "wheel"}, {"name": "urllib3", "version": "1.25.10", "latest_version": "2.2.3", "latest_filetype": "wheel"}, {"name": "virtualenv", "version": "20.0.29", "latest_version": "20.32.0", "latest_filetype": "wheel"}, {"name": "watchdog", "version": "0.10.3", "latest_version": "4.0.2", "latest_filetype": "wheel"}, {"name": "websocket-client", "version": "1.2.1", "latest_version": "1.8.0", "latest_filetype": "wheel"}, {"name": "Werkzeug", "version": "2.0.3", "latest_version": "3.0.6", "latest_filetype": "wheel"}, {"name": "xmltodict", "version": "0.12.0", "latest_version": "0.14.2", "latest_filetype": "wheel"}, {"name": "zipp", "version": "3.1.0", "latest_version": "3.20.2", "latest_filetype": "wheel"}, {"name": "zope.event", "version": "4.4", "latest_version": "5.0", "latest_filetype": "wheel"}, {"name": "zope.interface", "version": "5.1.0", "latest_version": "7.2", "latest_filetype": "wheel"}]}}, "summary": {"total_vulnerabilities": 1, "total_high_risk_packages": 0, "total_outdated_packages": 98, "overall_risk_level": "critical", "recommendations": ["立即更新存在安全漏洞的包", "暂停生产环境部署直到漏洞修复", "进行安全评估和渗透测试", "定期运行安全审计", "使用依赖锁定文件", "监控安全公告和CVE数据库", "实施最小权限原则"]}}