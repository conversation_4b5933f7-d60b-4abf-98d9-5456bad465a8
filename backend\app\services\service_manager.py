"""
开源服务管理器 - 统一管理所有集成的开源项目服务
按照《后端开发指南.md》的开源项目集成模式设计
"""

from datetime import datetime
from typing import Any, Dict, List

from app.services.content_safety_service import content_safety_service
from app.services.local_ai_service import local_ai_service
from app.services.ollama_service import ollama_service
from app.services.speech_recognition_service import speech_recognition_service
from app.services.video_download_service import video_download_service


class OpenSourceServiceManager:
    """开源服务管理器"""

    def __init__(self):
        """初始化服务管理器"""
        self.services = {
            "local_ai": local_ai_service,
            "ollama": ollama_service,
            "video_download": video_download_service,
            "speech_recognition": speech_recognition_service,
            "content_safety": content_safety_service,
        }

        self.service_info = {
            "local_ai": {
                "name": "LocalAI服务",
                "description": "本地部署大语言模型，提供OpenAI兼容API",
                "github": "https://github.com/mudler/LocalAI",
                "category": "AI模型服务",
            },
            "ollama": {
                "name": "Ollama服务",
                "description": "简化的本地LLM管理工具",
                "github": "https://github.com/ollama/ollama",
                "category": "AI模型服务",
            },
            "video_download": {
                "name": "视频下载服务",
                "description": "基于yt-dlp的多平台视频下载",
                "github": "https://github.com/yt-dlp/yt-dlp",
                "category": "视频处理工具",
            },
            "speech_recognition": {
                "name": "语音识别服务",
                "description": "基于SenseVoice/FunASR的语音识别",
                "github": "https://github.com/FunAudioLLM/SenseVoice",
                "category": "语音处理工具",
            },
            "content_safety": {
                "name": "内容安全检测服务",
                "description": "基于detoxify/nudenet的内容安全检测",
                "github": "https://github.com/unitaryai/detoxify",
                "category": "内容安全检测",
            },
        }

    async def get_system_status(self) -> Dict[str, Any]:
        """获取所有服务的状态"""
        status_results = {}
        overall_healthy = True

        # 检查各服务状态
        for service_name, service in self.services.items():
            try:
                if hasattr(service, "health_check"):
                    status = await service.health_check()
                elif hasattr(service, "check_dependencies"):
                    status = await service.check_dependencies()
                else:
                    status = {
                        "status": "unknown",
                        "message": "No health check method available",
                    }

                status_results[service_name] = {
                    "service_info": self.service_info[service_name],
                    "status": status,
                }

                # 检查是否健康
                if status.get("status") != "healthy":
                    overall_healthy = False

            except Exception as e:
                status_results[service_name] = {
                    "service_info": self.service_info[service_name],
                    "status": {"status": "error", "error": str(e)},
                }
                overall_healthy = False

        return {
            "overall_status": "healthy" if overall_healthy else "degraded",
            "services": status_results,
            "total_services": len(self.services),
            "healthy_services": sum(
                1
                for result in status_results.values()
                if result["status"].get("status") == "healthy"
            ),
            "timestamp": datetime.now().isoformat(),
        }

    async def content_processing_pipeline(
        self, input_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        内容处理管道 - 整合多个开源服务

        Args:
            input_data: 输入数据，包含不同类型的内容

        Returns:
            Dict[str, Any]: 处理结果
        """
        pipeline_results = {}
        processing_steps = []

        try:
            # 步骤1: 内容安全检测
            if input_data.get("text") or input_data.get("image_path"):
                print("🔍 执行内容安全检测...")
                safety_result = (
                    await content_safety_service.comprehensive_content_check(
                        text=input_data.get("text"),
                        image_path=input_data.get("image_path"),
                        video_path=input_data.get("video_path"),
                    )
                )
                pipeline_results["safety_check"] = safety_result
                processing_steps.append("content_safety_check")

                # 如果内容不安全，停止处理
                if not safety_result.get("overall_safe", True):
                    return {
                        "success": False,
                        "message": "内容安全检测未通过",
                        "results": pipeline_results,
                        "stopped_at": "safety_check",
                    }

            # 步骤2: 视频下载（如果提供URL）
            if input_data.get("video_url"):
                print("📥 执行视频下载...")
                download_result = await video_download_service.download_video(
                    input_data["video_url"]
                )
                pipeline_results["video_download"] = download_result
                processing_steps.append("video_download")

            # 步骤3: 语音识别（如果有音频/视频）
            if input_data.get("audio_path") or input_data.get("video_path"):
                print("🎙️ 执行语音识别...")
                speech_result = await speech_recognition_service.transcribe_audio_mock(
                    input_data.get("audio_path") or input_data.get("video_path")
                )
                pipeline_results["speech_recognition"] = speech_result
                processing_steps.append("speech_recognition")

            # 步骤4: AI内容生成/改写
            if input_data.get("generate_content"):
                print("🤖 执行AI内容生成...")
                # 优先使用LocalAI，如果不可用则使用Ollama
                try:
                    ai_result = await local_ai_service.content_generation(
                        topic=input_data.get("topic", "默认主题"),
                        content_type=input_data.get("content_type", "文章"),
                        style=input_data.get("style", "专业"),
                    )
                    pipeline_results["ai_generation"] = ai_result
                    processing_steps.append("ai_generation_localai")
                except Exception:
                    # 备选方案：使用Ollama
                    ai_result = await ollama_service.content_optimization(
                        content=input_data.get("text", "请生成内容"),
                        optimization_type="改写",
                    )
                    pipeline_results["ai_generation"] = ai_result
                    processing_steps.append("ai_generation_ollama")

            # 步骤5: 内容改写优化
            if input_data.get("rewrite_content") and input_data.get("text"):
                print("✨ 执行内容改写...")
                rewrite_result = await local_ai_service.content_rewrite(
                    input_data["text"], input_data.get("target_style", "专业")
                )
                pipeline_results["content_rewrite"] = rewrite_result
                processing_steps.append("content_rewrite")

            return {
                "success": True,
                "message": "内容处理管道执行完成",
                "processing_steps": processing_steps,
                "results": pipeline_results,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"处理管道执行失败: {str(e)}",
                "processing_steps": processing_steps,
                "results": pipeline_results,
            }

    async def batch_content_processing(
        self, batch_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        批量内容处理

        Args:
            batch_data: 批量输入数据

        Returns:
            Dict[str, Any]: 批量处理结果
        """
        results = []
        success_count = 0

        for i, data in enumerate(batch_data, 1):
            print(f"处理批次 {i}/{len(batch_data)}")

            result = await self.content_processing_pipeline(data)
            results.append(result)

            if result["success"]:
                success_count += 1
                print(f"✅ 批次 {i} 处理完成")
            else:
                print(f"❌ 批次 {i} 处理失败: {result.get('error', 'unknown')}")

        return {
            "success": success_count == len(batch_data),
            "total_batches": len(batch_data),
            "success_count": success_count,
            "failed_count": len(batch_data) - success_count,
            "success_rate": round(success_count / len(batch_data) * 100, 2),
            "results": results,
            "timestamp": datetime.now().isoformat(),
        }

    async def service_diagnostics(self) -> Dict[str, Any]:
        """服务诊断信息"""
        diagnostics = {}

        for service_name, service in self.services.items():
            try:
                if hasattr(service, "check_dependencies"):
                    deps = await service.check_dependencies()
                    diagnostics[service_name] = {
                        "dependencies": deps,
                        "service_type": self.service_info[service_name]["category"],
                    }
                else:
                    diagnostics[service_name] = {
                        "message": "No diagnostic method available",
                        "service_type": self.service_info[service_name]["category"],
                    }
            except Exception as e:
                diagnostics[service_name] = {
                    "error": str(e),
                    "service_type": self.service_info[service_name]["category"],
                }

        return {
            "diagnostics": diagnostics,
            "timestamp": datetime.now().isoformat(),
        }

    def get_service_catalog(self) -> Dict[str, Any]:
        """获取服务目录"""
        return {
            "services": self.service_info,
            "total_services": len(self.service_info),
            "categories": list(
                set(info["category"] for info in self.service_info.values())
            ),
            "integration_model": "开源项目集成",
            "timestamp": datetime.now().isoformat(),
        }


# 全局服务管理器实例
service_manager = OpenSourceServiceManager()


# 快捷函数
async def get_all_services_status() -> Dict[str, Any]:
    """获取所有服务状态"""
    return await service_manager.get_system_status()


async def process_content(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """快捷内容处理函数"""
    return await service_manager.content_processing_pipeline(input_data)
