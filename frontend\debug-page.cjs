/**
 * 页面调试脚本 - 检查前端页面加载状态
 */

const puppeteer = require('puppeteer');

async function debugPage() {
  console.log('🔍 开始调试前端页面...');
  
  let browser;
  try {
    browser = await puppeteer.launch({
      headless: 'new', // 使用新的headless模式
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`📝 [${type.toUpperCase()}] ${text}`);
    });
    
    // 监听页面错误
    page.on('pageerror', error => {
      console.error('❌ 页面错误:', error.message);
    });
    
    // 监听请求失败
    page.on('requestfailed', request => {
      console.error('🚫 请求失败:', request.url(), request.failure().errorText);
    });
    
    // 监听响应
    page.on('response', response => {
      if (!response.ok()) {
        console.error(`⚠️  响应错误: ${response.url()} - ${response.status()}`);
      }
    });
    
    console.log('🌐 正在访问 http://localhost:3001/');
    await page.goto('http://localhost:3001/', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    // 等待Vue应用挂载
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 检查页面内容
    const title = await page.title();
    console.log('📄 页面标题:', title);
    
    // 检查#app元素
    const appElement = await page.$('#app');
    if (appElement) {
      const appContent = await page.evaluate(() => {
        const app = document.getElementById('app');
        return {
          hasContent: app && app.innerHTML.trim().length > 0,
          innerHTML: app ? app.innerHTML.substring(0, 500) : 'null',
          childrenCount: app ? app.children.length : 0,
          classList: app ? Array.from(app.classList) : []
        };
      });
      
      console.log('🎯 #app元素状态:', appContent);
      
      if (!appContent.hasContent) {
        console.error('❌ #app元素为空，Vue应用可能未正确挂载');
        
        // 检查是否有错误信息
        const errorInfo = await page.evaluate(() => {
          const errors = [];
          if (window.console && window.console.error) {
            // 检查是否有全局错误
          }
          return {
            hasVue: typeof window.Vue !== 'undefined',
            hasRouter: typeof window.$router !== 'undefined',
            documentReady: document.readyState,
            scriptsLoaded: document.querySelectorAll('script').length
          };
        });
        
        console.log('🔧 环境检查:', errorInfo);
        
      } else {
        console.log('✅ #app元素有内容，Vue应用已挂载');
      }
    } else {
      console.error('❌ 未找到#app元素');
    }
    
    // 检查网络请求
    const networkRequests = await page.evaluate(() => {
      return {
        scripts: Array.from(document.querySelectorAll('script')).map(s => s.src || 'inline'),
        stylesheets: Array.from(document.querySelectorAll('link[rel="stylesheet"]')).map(l => l.href),
        images: Array.from(document.querySelectorAll('img')).map(i => i.src)
      };
    });
    
    console.log('🌐 网络资源:', networkRequests);
    
    // 检查当前路径
    const currentPath = await page.evaluate(() => window.location.pathname);
    console.log('🛣️  当前路径:', currentPath);
    
    // 检查可见元素数量
    const elementStats = await page.evaluate(() => {
      const all = document.querySelectorAll('*');
      const visible = document.querySelectorAll('*:not([style*="display: none"]):not([style*="visibility: hidden"])');
      return {
        total: all.length,
        visible: visible.length,
        bodyChildren: document.body.children.length,
        appChildren: document.getElementById('app') ? document.getElementById('app').children.length : 0
      };
    });
    
    console.log('👁️  元素统计:', elementStats);
    
    // 截图保存
    await page.screenshot({ 
      path: 'debug-screenshot.png', 
      fullPage: true,
      type: 'png'
    });
    console.log('📸 已保存截图: debug-screenshot.png');
    
    // 保存页面HTML
    const html = await page.content();
    require('fs').writeFileSync('debug-page.html', html);
    console.log('💾 已保存页面HTML: debug-page.html');
    
  } catch (error) {
    console.error('💥 调试过程中出现错误:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// 运行调试
debugPage().catch(console.error);
