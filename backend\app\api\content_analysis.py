#!/usr/bin/env python3
"""
AI视频内容创作系统 - 内容分析API
提供内容分析、合规检测、推荐等功能的REST API接口
"""

from typing import List, Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from ..services.content_analysis_service import (
    ComplianceLevel,
    ContentAnalysisResult,
    ContentType,
    content_analysis_service,
)

router = APIRouter(prefix="/api/v1/content", tags=["内容分析"])


class ContentAnalysisRequest(BaseModel):
    """内容分析请求"""

    content_type: ContentType
    title: str
    description: Optional[str] = ""
    tags: Optional[List[str]] = []
    duration: Optional[float] = None


class ContentAnalysisResponse(BaseModel):
    """内容分析响应"""

    result: ContentAnalysisResult
    message: str


@router.post("/analyze")
async def analyze_content(
    request: ContentAnalysisRequest,
) -> ContentAnalysisResponse:
    """分析内容"""
    try:
        result = content_analysis_service.analyze_content(
            content_type=request.content_type,
            title=request.title,
            description=request.description or "",
            tags=request.tags or [],
            duration=request.duration,
        )

        return ContentAnalysisResponse(
            result=result, message=f"内容分析完成: {result.content_id}"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/analysis/{content_id}")
async def get_analysis(content_id: str) -> ContentAnalysisResult:
    """获取分析结果"""
    result = content_analysis_service.get_analysis_by_id(content_id)
    if not result:
        raise HTTPException(status_code=404, detail=f"分析结果不存在: {content_id}")
    return result


@router.get("/analyses")
async def list_analyses() -> List[ContentAnalysisResult]:
    """列出所有分析结果"""
    return content_analysis_service.get_all_analyses()


@router.get("/stats")
async def get_analysis_stats():
    """获取分析统计信息"""
    return content_analysis_service.get_service_stats()


@router.delete("/cache")
async def clear_analysis_cache():
    """清空分析缓存"""
    content_analysis_service.clear_cache()
    return {"message": "分析缓存已清空"}


@router.get("/compliance/check")
async def check_compliance(title: str, description: str = "") -> dict:
    """快速合规检查"""
    try:
        # 创建临时分析来检查合规性
        result = content_analysis_service.analyze_content(
            content_type=ContentType.TEXT, title=title, description=description
        )

        return {
            "compliance_level": result.compliance_level,
            "compliance_score": result.compliance_score,
            "compliance_reasons": result.compliance_reasons,
            "is_safe": result.compliance_level == ComplianceLevel.SAFE,
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/recommend/platforms")
async def recommend_platforms(
    content_type: ContentType,
    tags: List[str] = [],
    duration: Optional[float] = None,
) -> dict:
    """推荐发布平台"""
    try:
        # 创建临时分析来获取平台推荐
        result = content_analysis_service.analyze_content(
            content_type=content_type,
            title="临时内容",  # 临时标题
            tags=tags,
            duration=duration,
        )

        return {
            "recommended_platforms": result.recommended_platforms,
            "optimal_publish_time": result.optimal_publish_time,
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 健康检查端点
@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        stats = content_analysis_service.get_service_stats()

        return {
            "status": "healthy",
            "service_info": {
                "total_analyses": stats["total_analyses"],
                "cache_size": len(content_analysis_service.analysis_cache),
                "service_type": "content_analysis",
            },
            "timestamp": "2025-07-07T12:00:00Z",
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2025-07-07T12:00:00Z",
        }
