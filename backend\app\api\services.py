"""
开源服务API路由 - 提供统一的开源服务接口
按照《后端开发指南.md》的开源项目集成模式设计
"""

import os
import tempfile
from typing import Any, Dict

from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, status

from app.core.auth import get_current_user
from app.services.content_safety_service import content_safety_service
from app.services.local_ai_service import local_ai_service
from app.services.ollama_service import ollama_service
from app.services.service_manager import service_manager
from app.services.speech_recognition_service import speech_recognition_service
from app.services.video_download_service import video_download_service

router = APIRouter(prefix="/services", tags=["开源服务集成"])


# 服务状态和管理接口


@router.get("/status")
async def get_services_status(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取所有开源服务的状态"""
    try:
        status = await service_manager.get_system_status()
        return {"success": True, "data": status, "message": "服务状态获取成功"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务状态失败: {str(e)}",
        )


@router.get("/catalog")
async def get_services_catalog():
    """获取服务目录（公开接口）"""
    try:
        catalog = service_manager.get_service_catalog()
        return {
            "success": True,
            "data": catalog,
            "message": "服务目录获取成功",
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务目录失败: {str(e)}",
        )


@router.get("/diagnostics")
async def get_services_diagnostics(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取服务诊断信息"""
    try:
        diagnostics = await service_manager.service_diagnostics()
        return {
            "success": True,
            "data": diagnostics,
            "message": "服务诊断完成",
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务诊断失败: {str(e)}",
        )


# AI内容生成服务


@router.post("/ai/generate")
async def generate_content(
    request_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """AI内容生成"""
    try:
        topic = request_data.get("topic", "")
        content_type = request_data.get("content_type", "文章")
        style = request_data.get("style", "专业")
        model_service = request_data.get("service", "local_ai")

        if not topic:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="主题不能为空"
            )

        if model_service == "local_ai":
            result = await local_ai_service.content_generation(
                topic=topic, content_type=content_type, style=style
            )
        elif model_service == "ollama":
            result = await ollama_service.content_optimization(
                content=f"请生成关于'{topic}'的{content_type}内容",
                optimization_type="生成",
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的AI服务: {model_service}",
            )

        return {"success": True, "data": result, "message": "内容生成完成"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"内容生成失败: {str(e)}",
        )


@router.post("/ai/rewrite")
async def rewrite_content(
    request_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """AI内容改写"""
    try:
        content = request_data.get("content", "")
        target_style = request_data.get("target_style", "专业")
        model_service = request_data.get("service", "local_ai")

        if not content:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="内容不能为空"
            )

        if model_service == "local_ai":
            result = await local_ai_service.content_rewrite(
                original_content=content, target_style=target_style
            )
        elif model_service == "ollama":
            result = await ollama_service.content_optimization(
                content=content, optimization_type="改写"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的AI服务: {model_service}",
            )

        return {"success": True, "data": result, "message": "内容改写完成"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"内容改写失败: {str(e)}",
        )


# 视频下载服务


@router.post("/video/download")
async def download_video(
    request_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """视频下载"""
    try:
        url = request_data.get("url", "")
        quality = request_data.get("quality", "best")
        extract_audio = request_data.get("extract_audio", False)

        if not url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="视频URL不能为空",
            )

        result = await video_download_service.download_video(
            url=url, quality=quality, extract_audio=extract_audio
        )

        return {
            "success": True,
            "data": result,
            "message": "视频下载请求已提交",
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"视频下载失败: {str(e)}",
        )


@router.post("/video/info")
async def get_video_info(
    request_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取视频信息"""
    try:
        url = request_data.get("url", "")

        if not url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="视频URL不能为空",
            )

        result = await video_download_service.get_video_info(url)

        return {"success": True, "data": result, "message": "视频信息获取完成"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取视频信息失败: {str(e)}",
        )


# 语音识别服务


@router.post("/speech/transcribe")
async def transcribe_audio(
    audio_file: UploadFile = File(...),
    language: str = "auto",
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """音频转文本"""
    try:
        # 保存上传的音频文件
        with tempfile.NamedTemporaryFile(
            delete=False, suffix=os.path.splitext(audio_file.filename)[1]
        ) as temp_file:
            content = await audio_file.read()
            temp_file.write(content)
            temp_path = temp_file.name

        # 执行语音识别
        result = await speech_recognition_service.transcribe_audio_mock(
            audio_path=temp_path, language=language
        )

        # 清理临时文件
        os.unlink(temp_path)

        return {"success": True, "data": result, "message": "语音识别完成"}

    except HTTPException:
        raise
    except Exception as e:
        # 确保清理临时文件
        if "temp_path" in locals() and os.path.exists(temp_path):
            os.unlink(temp_path)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"语音识别失败: {str(e)}",
        )


@router.post("/speech/video-to-text")
async def video_to_text(
    video_file: UploadFile = File(...),
    language: str = "auto",
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """视频转文本（提取音频后识别）"""
    try:
        # 保存上传的视频文件
        with tempfile.NamedTemporaryFile(
            delete=False, suffix=os.path.splitext(video_file.filename)[1]
        ) as temp_file:
            content = await video_file.read()
            temp_file.write(content)
            temp_path = temp_file.name

        # 执行视频转文本
        result = await speech_recognition_service.video_to_text(
            video_path=temp_path, language=language
        )

        # 清理临时文件
        os.unlink(temp_path)

        return {"success": True, "data": result, "message": "视频转文本完成"}

    except HTTPException:
        raise
    except Exception as e:
        # 确保清理临时文件
        if "temp_path" in locals() and os.path.exists(temp_path):
            os.unlink(temp_path)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"视频转文本失败: {str(e)}",
        )


# 内容安全检测服务


@router.post("/safety/check-text")
async def check_text_safety(
    request_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """文本安全检测"""
    try:
        text = request_data.get("text", "")

        if not text:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文本内容不能为空",
            )

        result = await content_safety_service.check_text_safety_mock(text)

        return {"success": True, "data": result, "message": "文本安全检测完成"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文本安全检测失败: {str(e)}",
        )


@router.post("/safety/check-image")
async def check_image_safety(
    image_file: UploadFile = File(...),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """图像安全检测"""
    try:
        # 保存上传的图像文件
        with tempfile.NamedTemporaryFile(
            delete=False, suffix=os.path.splitext(image_file.filename)[1]
        ) as temp_file:
            content = await image_file.read()
            temp_file.write(content)
            temp_path = temp_file.name

        # 执行图像安全检测
        result = await content_safety_service.check_image_safety_mock(temp_path)

        # 清理临时文件
        os.unlink(temp_path)

        return {"success": True, "data": result, "message": "图像安全检测完成"}

    except HTTPException:
        raise
    except Exception as e:
        # 确保清理临时文件
        if "temp_path" in locals() and os.path.exists(temp_path):
            os.unlink(temp_path)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"图像安全检测失败: {str(e)}",
        )


# 综合内容处理管道


@router.post("/pipeline/process")
async def process_content_pipeline(
    request_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """综合内容处理管道"""
    try:
        result = await service_manager.content_processing_pipeline(request_data)

        return {
            "success": True,
            "data": result,
            "message": "内容处理管道执行完成",
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"内容处理管道执行失败: {str(e)}",
        )


@router.post("/pipeline/batch")
async def batch_process_content(
    request_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """批量内容处理"""
    try:
        batch_data = request_data.get("batch_data", [])

        if not batch_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="批量数据不能为空",
            )

        result = await service_manager.batch_content_processing(batch_data)

        return {"success": True, "data": result, "message": "批量内容处理完成"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量内容处理失败: {str(e)}",
        )
