# Page snapshot

```yaml
- text: "[plugin:vite:import-analysis] Failed to resolve import \"/logo.svg\" from \"src/components/layout/MobileNavigation.vue\". Does the file exist? D:/二创/二创短视频分发/frontend/src/components/layout/MobileNavigation.vue:28:32 95 | }); 96 | import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, resolveDynamicComponent as _resolveDynamicComponent, createBlock as _createBlock, withModifiers as _withModifiers, Transition as _Transition, createStaticVNode as _createStaticVNode } from \"vue\"; 97 | import _imports_0 from \"/logo.svg\"; | ^ 98 | const _hoisted_1 = { class: \"mobile-navigation\" }; 99 | const _hoisted_2 = { class: \"mobile-header\" }; at TransformPluginContext._formatError (file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49258:41) at TransformPluginContext.error (file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49253:16) at normalizeUrl (file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64291:23) at process.processTicksAndRejections (node:internal/process/task_queues:105:5) at async file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64423:39 at async Promise.all (index 7) at async TransformPluginContext.transform (file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:64350:7) at async PluginContainer.transform (file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:49099:18) at async loadAndTransform (file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:51977:27) at async viteTransformMiddleware (file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:62105:24 Click outside, press Esc key, or fix the code to dismiss. You can also disable this overlay by setting"
- code: server.hmr.overlay
- text: to
- code: "false"
- text: in
- code: vite.config.ts
- text: .
```