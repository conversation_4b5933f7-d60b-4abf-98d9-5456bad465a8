<template>
  <div class="fixed top-6 right-6 z-50 space-y-2">
    <transition-group name="toast-fade" tag="div">
      <div v-for="toast in toasts" :key="toast.id" class="bg-gray-900 text-white px-4 py-3 rounded shadow flex items-center gap-2 min-w-[220px]">
        <span>{{ toast.message }}</span>
        <button class="ml-auto text-gray-300 hover:text-white" @click="remove(toast.id)">×</button>
      </div>
    </transition-group>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

interface Toast {
  id: number
  message: string
}
const toasts = ref<Toast[]>([])
let id = 0

function show(message: string, duration = 3000) {
  const toast = { id: ++id, message }
  toasts.value.push(toast)
  setTimeout(() => remove(toast.id), duration)
}
function remove(id: number) {
  toasts.value = toasts.value.filter(t => t.id !== id)
}

defineExpose({ show })
</script>
<style scoped>
.toast-fade-enter-active, .toast-fade-leave-active { transition: opacity 0.3s; }
.toast-fade-enter-from, .toast-fade-leave-to { opacity: 0; }
</style>