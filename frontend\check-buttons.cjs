const puppeteer = require('puppeteer');

async function checkButtons() {
  const browser = await puppeteer.launch({ headless: 'new' });
  const page = await browser.newPage();
  
  await page.goto('http://localhost:3001/');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 获取所有按钮和链接
  const elements = await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button, a[href]'));
    return buttons.map(el => ({
      tag: el.tagName.toLowerCase(),
      text: el.textContent.trim(),
      href: el.href || null,
      className: el.className,
      visible: el.offsetParent !== null
    })).filter(el => el.visible && el.text);
  });
  
  console.log('找到的按钮和链接:');
  elements.forEach((el, i) => {
    console.log(`${i + 1}. [${el.tag}] "${el.text}" ${el.href ? `-> ${el.href}` : ''}`);
  });
  
  await browser.close();
}

checkButtons().catch(console.error);
