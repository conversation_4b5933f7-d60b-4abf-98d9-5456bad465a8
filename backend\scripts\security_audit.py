#!/usr/bin/env python3
"""
依赖安全审计脚本
🔒 证据链: 定期检查依赖包安全漏洞，确保系统安全
"""

import json
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List


class SecurityAuditor:
    """安全审计器"""

    def __init__(self, project_root: str = None):
        self.project_root = (
            Path(project_root) if project_root else Path(__file__).parent.parent
        )
        self.requirements_file = self.project_root / "requirements_secure.txt"
        self.audit_report_dir = self.project_root / "security_reports"
        self.audit_report_dir.mkdir(exist_ok=True)

        # 🔒 证据链: 已知安全漏洞的包版本
        self.known_vulnerabilities = {
            "pillow": ["<10.0.0"],
            "requests": ["<2.31.0"],
            "urllib3": ["<2.0.0"],
            "cryptography": ["<41.0.0"],
            "pyjwt": ["<2.4.0"],
            "sqlalchemy": ["<1.4.46"],
            "fastapi": ["<0.100.0"],
            "starlette": ["<0.27.0"],
            "uvicorn": ["<0.23.0"],
        }

        # 高风险包列表
        self.high_risk_packages = [
            "eval",
            "exec",
            "pickle",
            "marshal",
            "subprocess",
            "os",
            "sys",
        ]

    def run_pip_audit(self) -> Dict:
        """运行pip-audit安全检查"""
        print("🔍 运行pip-audit安全检查...")

        try:
            # 安装pip-audit如果不存在
            subprocess.run(
                [sys.executable, "-m", "pip", "install", "pip-audit"],
                capture_output=True,
                check=True,
            )

            # 运行安全审计
            result = subprocess.run(
                [
                    sys.executable,
                    "-m",
                    "pip_audit",
                    "--requirement",
                    str(self.requirements_file),
                    "--format",
                    "json",
                    "--output",
                    str(
                        self.audit_report_dir
                        / f"pip_audit_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    ),
                ],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                print("✅ pip-audit检查完成，未发现安全漏洞")
                return {"status": "clean", "vulnerabilities": []}
            else:
                vulnerabilities = json.loads(result.stdout) if result.stdout else []
                print(f"⚠️ pip-audit发现 {len(vulnerabilities)} 个安全漏洞")
                return {
                    "status": "vulnerabilities_found",
                    "vulnerabilities": vulnerabilities,
                }

        except subprocess.CalledProcessError as e:
            print(f"❌ pip-audit执行失败: {e}")
            return {"status": "error", "error": str(e)}
        except Exception as e:
            print(f"❌ pip-audit检查异常: {e}")
            return {"status": "error", "error": str(e)}

    def run_safety_check(self) -> Dict:
        """运行safety安全检查"""
        print("🔍 运行safety安全检查...")

        try:
            # 安装safety如果不存在
            subprocess.run(
                [sys.executable, "-m", "pip", "install", "safety"],
                capture_output=True,
                check=True,
            )

            # 运行safety检查
            result = subprocess.run(
                [
                    sys.executable,
                    "-m",
                    "safety",
                    "check",
                    "--requirement",
                    str(self.requirements_file),
                    "--json",
                ],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                print("✅ safety检查完成，未发现安全漏洞")
                return {"status": "clean", "vulnerabilities": []}
            else:
                vulnerabilities = json.loads(result.stdout) if result.stdout else []
                print(f"⚠️ safety发现 {len(vulnerabilities)} 个安全漏洞")
                return {
                    "status": "vulnerabilities_found",
                    "vulnerabilities": vulnerabilities,
                }

        except subprocess.CalledProcessError as e:
            print(f"❌ safety执行失败: {e}")
            return {"status": "error", "error": str(e)}
        except Exception as e:
            print(f"❌ safety检查异常: {e}")
            return {"status": "error", "error": str(e)}

    def check_known_vulnerabilities(self) -> Dict:
        """检查已知漏洞"""
        print("🔍 检查已知安全漏洞...")

        vulnerabilities = []

        try:
            with open(self.requirements_file, "r", encoding="utf-8") as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if line and not line.startswith("#") and "==" in line:
                    package_name, version = line.split("==")
                    package_name = package_name.lower()

                    if package_name in self.known_vulnerabilities:
                        vulnerable_versions = self.known_vulnerabilities[package_name]
                        for vuln_version in vulnerable_versions:
                            if self._version_matches_pattern(version, vuln_version):
                                vulnerabilities.append(
                                    {
                                        "package": package_name,
                                        "version": version,
                                        "vulnerability": f"Known vulnerability in {vuln_version}",
                                        "severity": "high",
                                    }
                                )

            if vulnerabilities:
                print(f"⚠️ 发现 {len(vulnerabilities)} 个已知漏洞")
                return {
                    "status": "vulnerabilities_found",
                    "vulnerabilities": vulnerabilities,
                }
            else:
                print("✅ 未发现已知漏洞")
                return {"status": "clean", "vulnerabilities": []}

        except Exception as e:
            print(f"❌ 已知漏洞检查异常: {e}")
            return {"status": "error", "error": str(e)}

    def check_high_risk_packages(self) -> Dict:
        """检查高风险包"""
        print("🔍 检查高风险包...")

        high_risk_found = []

        try:
            with open(self.requirements_file, "r", encoding="utf-8") as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if line and not line.startswith("#") and "==" in line:
                    package_name = line.split("==")[0].lower()

                    if package_name in self.high_risk_packages:
                        high_risk_found.append(
                            {
                                "package": package_name,
                                "risk": "High risk package that may execute arbitrary code",
                                "recommendation": "Review usage and consider alternatives",
                            }
                        )

            if high_risk_found:
                print(f"⚠️ 发现 {len(high_risk_found)} 个高风险包")
                return {"status": "high_risk_found", "packages": high_risk_found}
            else:
                print("✅ 未发现高风险包")
                return {"status": "clean", "packages": []}

        except Exception as e:
            print(f"❌ 高风险包检查异常: {e}")
            return {"status": "error", "error": str(e)}

    def check_outdated_packages(self) -> Dict:
        """检查过时的包"""
        print("🔍 检查过时的包...")

        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "list", "--outdated", "--format=json"],
                capture_output=True,
                text=True,
                check=True,
            )

            outdated_packages = json.loads(result.stdout)

            if outdated_packages:
                print(f"⚠️ 发现 {len(outdated_packages)} 个过时的包")
                return {"status": "outdated_found", "packages": outdated_packages}
            else:
                print("✅ 所有包都是最新版本")
                return {"status": "up_to_date", "packages": []}

        except subprocess.CalledProcessError as e:
            print(f"❌ 过时包检查失败: {e}")
            return {"status": "error", "error": str(e)}
        except Exception as e:
            print(f"❌ 过时包检查异常: {e}")
            return {"status": "error", "error": str(e)}

    def _version_matches_pattern(self, version: str, pattern: str) -> bool:
        """检查版本是否匹配漏洞模式"""
        # 简单的版本比较，实际应该使用packaging库
        if pattern.startswith("<"):
            target_version = pattern[1:]
            return version < target_version
        elif pattern.startswith("<="):
            target_version = pattern[2:]
            return version <= target_version
        elif pattern.startswith(">"):
            target_version = pattern[1:]
            return version > target_version
        elif pattern.startswith(">="):
            target_version = pattern[2:]
            return version >= target_version
        else:
            return version == pattern

    def generate_security_report(self, audit_results: Dict) -> str:
        """生成安全报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.audit_report_dir / f"security_report_{timestamp}.json"

        report = {
            "timestamp": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "requirements_file": str(self.requirements_file),
            "audit_results": audit_results,
            "summary": self._generate_summary(audit_results),
        }

        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"📄 安全报告已生成: {report_file}")
        return str(report_file)

    def _generate_summary(self, audit_results: Dict) -> Dict:
        """生成审计摘要"""
        total_vulnerabilities = 0
        total_high_risk = 0
        total_outdated = 0

        for tool, result in audit_results.items():
            if result.get("status") == "vulnerabilities_found":
                total_vulnerabilities += len(result.get("vulnerabilities", []))
            elif result.get("status") == "high_risk_found":
                total_high_risk += len(result.get("packages", []))
            elif result.get("status") == "outdated_found":
                total_outdated += len(result.get("packages", []))

        risk_level = "low"
        if total_vulnerabilities > 0:
            risk_level = "critical"
        elif total_high_risk > 0:
            risk_level = "high"
        elif total_outdated > 5:
            risk_level = "medium"

        return {
            "total_vulnerabilities": total_vulnerabilities,
            "total_high_risk_packages": total_high_risk,
            "total_outdated_packages": total_outdated,
            "overall_risk_level": risk_level,
            "recommendations": self._get_recommendations(risk_level),
        }

    def _get_recommendations(self, risk_level: str) -> List[str]:
        """获取安全建议"""
        recommendations = []

        if risk_level == "critical":
            recommendations.extend(
                [
                    "立即更新存在安全漏洞的包",
                    "暂停生产环境部署直到漏洞修复",
                    "进行安全评估和渗透测试",
                ]
            )
        elif risk_level == "high":
            recommendations.extend(
                ["尽快更新高风险包", "审查高风险包的使用方式", "考虑使用替代方案"]
            )
        elif risk_level == "medium":
            recommendations.extend(["定期更新过时的包", "建立自动化依赖更新流程"])

        recommendations.extend(
            [
                "定期运行安全审计",
                "使用依赖锁定文件",
                "监控安全公告和CVE数据库",
                "实施最小权限原则",
            ]
        )

        return recommendations

    def run_full_audit(self) -> Dict:
        """运行完整的安全审计"""
        print("🔒 开始完整安全审计...")
        print("=" * 50)

        audit_results = {}

        # 运行各种安全检查
        audit_results["pip_audit"] = self.run_pip_audit()
        audit_results["safety_check"] = self.run_safety_check()
        audit_results["known_vulnerabilities"] = self.check_known_vulnerabilities()
        audit_results["high_risk_packages"] = self.check_high_risk_packages()
        audit_results["outdated_packages"] = self.check_outdated_packages()

        # 生成报告
        report_file = self.generate_security_report(audit_results)

        # 打印摘要
        summary = audit_results.get("summary", {})
        print("\n" + "=" * 50)
        print("🔒 安全审计摘要")
        print(f"安全漏洞: {summary.get('total_vulnerabilities', 0)}")
        print(f"高风险包: {summary.get('total_high_risk_packages', 0)}")
        print(f"过时包: {summary.get('total_outdated_packages', 0)}")
        print(f"整体风险等级: {summary.get('overall_risk_level', 'unknown').upper()}")

        return audit_results


def main():
    """主函数"""
    auditor = SecurityAuditor()
    results = auditor.run_full_audit()

    # 根据结果设置退出码
    summary = results.get("summary", {})
    risk_level = summary.get("overall_risk_level", "low")

    if risk_level == "critical":
        print("\n❌ 发现严重安全问题，请立即处理！")
        sys.exit(1)
    elif risk_level == "high":
        print("\n⚠️ 发现高风险问题，建议尽快处理")
        sys.exit(1)
    elif risk_level == "medium":
        print("\n⚠️ 发现中等风险问题，建议及时处理")
        sys.exit(0)
    else:
        print("\n✅ 安全审计通过！")
        sys.exit(0)


if __name__ == "__main__":
    main()
