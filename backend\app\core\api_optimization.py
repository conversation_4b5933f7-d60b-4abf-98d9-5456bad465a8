"""
API性能优化模块
"""

import asyncio
import time
from functools import wraps
from typing import Any, Callable, Dict, Optional

from fastapi import Request, Response
from fastapi.concurrency import run_in_threadpool
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.cache_optimization import api_cache
from app.core.config import settings


class APIPerformanceMiddleware(BaseHTTPMiddleware):
    """API性能监控中间件"""

    def __init__(self, app):
        super().__init__(app)
        self.request_stats = {}
        self.slow_requests = []

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()

        # 记录请求开始
        request_id = f"{request.method}:{request.url.path}"

        try:
            response = await call_next(request)

            # 计算响应时间
            process_time = time.time() - start_time

            # 记录统计信息
            self._record_request_stats(request_id, process_time, response.status_code)

            # 添加性能头部
            response.headers["X-Process-Time"] = str(round(process_time * 1000, 2))

            return response

        except Exception as e:
            process_time = time.time() - start_time
            self._record_request_stats(request_id, process_time, 500, str(e))
            raise

    def _record_request_stats(
        self,
        request_id: str,
        process_time: float,
        status_code: int,
        error: Optional[str] = None,
    ):
        """记录请求统计信息"""
        if request_id not in self.request_stats:
            self.request_stats[request_id] = {
                "count": 0,
                "total_time": 0,
                "avg_time": 0,
                "min_time": float("inf"),
                "max_time": 0,
                "errors": 0,
            }

        stats = self.request_stats[request_id]
        stats["count"] += 1
        stats["total_time"] += process_time
        stats["avg_time"] = stats["total_time"] / stats["count"]
        stats["min_time"] = min(stats["min_time"], process_time)
        stats["max_time"] = max(stats["max_time"], process_time)

        if error:
            stats["errors"] += 1

        # 记录慢请求
        if process_time > 1.0:  # 超过1秒的请求
            self.slow_requests.append(
                {
                    "request_id": request_id,
                    "process_time": process_time,
                    "status_code": status_code,
                    "error": error,
                    "timestamp": time.time(),
                }
            )

            # 保持最近100个慢请求
            if len(self.slow_requests) > 100:
                self.slow_requests = self.slow_requests[-100:]

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            "request_stats": self.request_stats,
            "slow_requests": self.slow_requests[-10:],  # 最近10个慢请求
            "total_requests": sum(
                stats["count"] for stats in self.request_stats.values()
            ),
            "avg_response_time": (
                sum(stats["avg_time"] for stats in self.request_stats.values())
                / len(self.request_stats)
                if self.request_stats
                else 0
            ),
        }


def async_cache(ttl: int = 300, key_prefix: str = ""):
    """异步缓存装饰器"""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"

            # 尝试从缓存获取
            cached_result = await api_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

            # 执行函数
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = await run_in_threadpool(func, *args, **kwargs)

            # 缓存结果
            await api_cache.set(cache_key, result, ttl)

            return result

        return wrapper

    return decorator


def rate_limit(max_requests: int = 100, window_seconds: int = 60):
    """速率限制装饰器"""
    request_counts = {}

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            client_ip = request.client.host
            current_time = time.time()

            # 清理过期记录
            cutoff_time = current_time - window_seconds
            request_counts[client_ip] = [
                req_time
                for req_time in request_counts.get(client_ip, [])
                if req_time > cutoff_time
            ]

            # 检查速率限制
            if len(request_counts.get(client_ip, [])) >= max_requests:
                from fastapi import HTTPException

                raise HTTPException(status_code=429, detail="Rate limit exceeded")

            # 记录请求
            if client_ip not in request_counts:
                request_counts[client_ip] = []
            request_counts[client_ip].append(current_time)

            # 执行函数
            return await func(request, *args, **kwargs)

        return wrapper

    return decorator


class ConnectionPool:
    """连接池管理"""

    def __init__(self, max_connections: int = 100):
        self.max_connections = max_connections
        self.active_connections = 0
        self.semaphore = asyncio.Semaphore(max_connections)
        self.connection_stats = {"total_created": 0, "active": 0, "peak": 0}

    async def acquire(self):
        """获取连接"""
        await self.semaphore.acquire()
        self.active_connections += 1
        self.connection_stats["active"] = self.active_connections
        self.connection_stats["total_created"] += 1

        if self.active_connections > self.connection_stats["peak"]:
            self.connection_stats["peak"] = self.active_connections

    def release(self):
        """释放连接"""
        self.active_connections -= 1
        self.connection_stats["active"] = self.active_connections
        self.semaphore.release()

    def get_stats(self) -> Dict[str, Any]:
        """获取连接统计"""
        return self.connection_stats.copy()


class BatchProcessor:
    """批处理器"""

    def __init__(self, batch_size: int = 10, max_wait_time: float = 1.0):
        self.batch_size = batch_size
        self.max_wait_time = max_wait_time
        self.pending_items = []
        self.pending_futures = []
        self.last_batch_time = time.time()

    async def add_item(self, item: Any, processor: Callable) -> Any:
        """添加项目到批处理队列"""
        future = asyncio.Future()
        self.pending_items.append(item)
        self.pending_futures.append(future)

        # 检查是否需要处理批次
        should_process = (
            len(self.pending_items) >= self.batch_size
            or time.time() - self.last_batch_time >= self.max_wait_time
        )

        if should_process:
            await self._process_batch(processor)

        return await future

    async def _process_batch(self, processor: Callable):
        """处理批次"""
        if not self.pending_items:
            return

        items = self.pending_items.copy()
        futures = self.pending_futures.copy()

        self.pending_items.clear()
        self.pending_futures.clear()
        self.last_batch_time = time.time()

        try:
            # 批量处理
            results = await processor(items)

            # 设置结果
            for future, result in zip(futures, results):
                if not future.done():
                    future.set_result(result)

        except Exception as e:
            # 设置异常
            for future in futures:
                if not future.done():
                    future.set_exception(e)


class ResponseCompression:
    """响应压缩"""

    @staticmethod
    def should_compress(response: Response, min_size: int = 1024) -> bool:
        """判断是否应该压缩"""
        content_type = response.headers.get("content-type", "")
        content_length = int(response.headers.get("content-length", 0))

        # 检查内容类型
        compressible_types = [
            "application/json",
            "text/html",
            "text/css",
            "text/javascript",
            "application/javascript",
        ]

        is_compressible = any(ct in content_type for ct in compressible_types)
        is_large_enough = content_length >= min_size

        return is_compressible and is_large_enough


# 全局实例
performance_middleware = None
connection_pool = ConnectionPool(max_connections=settings.DB_POOL_SIZE * 2)
batch_processor = BatchProcessor()


def setup_api_optimization(app):
    """设置API优化"""
    global performance_middleware

    # 添加性能监控中间件
    performance_middleware = APIPerformanceMiddleware(app)
    app.add_middleware(APIPerformanceMiddleware)

    # 添加压缩中间件
    from fastapi.middleware.gzip import GZipMiddleware

    app.add_middleware(GZipMiddleware, minimum_size=1000)


def get_api_performance_stats() -> Dict[str, Any]:
    """获取API性能统计"""
    if performance_middleware:
        return performance_middleware.get_performance_stats()
    return {}


async def optimize_database_queries():
    """优化数据库查询"""
    # 这里可以添加数据库查询优化逻辑
    # 例如：预加载关联数据、使用连接池等


async def preload_cache():
    """预加载缓存"""
    # 这里可以添加缓存预热逻辑
    # 例如：预加载热点数据到缓存


def initialize_api_optimization():
    """初始化API优化"""
    import logging

    logger = logging.getLogger(__name__)

    logger.info("初始化API性能优化...")

    # 这里可以添加初始化逻辑
    # 例如：设置连接池、预热缓存等

    logger.info("API性能优化初始化完成")


if __name__ == "__main__":
    initialize_api_optimization()
