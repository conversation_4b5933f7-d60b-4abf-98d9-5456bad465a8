<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 登录功能测试</h1>
        
        <div class="form-group">
            <label for="username">用户名:</label>
            <select id="username">
                <option value="admin_user">admin_user (管理员)</option>
                <option value="content_creator">content_creator (创作者)</option>
                <option value="business_user">business_user (商业用户)</option>
                <option value="demo_user">demo_user (普通用户)</option>
                <option value="test_developer">test_developer (开发者)</option>
                <option value="marketing_team">marketing_team (营销团队)</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="Admin123!" placeholder="请输入密码">
        </div>
        
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testAPI()">测试API连接</button>
        <button onclick="clearResult()">清除结果</button>
        
        <div id="result"></div>
    </div>

    <script>
        // 更新密码
        document.getElementById('username').addEventListener('change', function() {
            const passwords = {
                'admin_user': 'Admin123!',
                'content_creator': 'Creator123!',
                'business_user': 'Business123!',
                'demo_user': 'Demo123!',
                'test_developer': 'Dev123!',
                'marketing_team': 'Marketing123!'
            };
            document.getElementById('password').value = passwords[this.value] || '';
        });

        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
        }

        async function testAPI() {
            showResult('测试API连接...', 'info');
            
            try {
                const response = await fetch('http://localhost:8001/health');
                const data = await response.json();
                
                showResult(`API连接成功！
状态: ${data.status}
数据库: ${data.database}
用户数: ${data.users}
时间: ${data.timestamp}`, 'success');
                
            } catch (error) {
                showResult(`API连接失败: ${error.message}
请确保后端服务器正在运行在 http://localhost:8001`, 'error');
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            showResult(`正在测试登录: ${username}...`, 'info');
            
            try {
                const response = await fetch('http://localhost:8001/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult(`登录成功！
用户名: ${data.user.username}
邮箱: ${data.user.email}
角色: ${data.user.role}
ID: ${data.user.id}`, 'success');
                } else {
                    showResult(`登录失败: ${data.detail || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                showResult(`登录请求失败: ${error.message}
可能的原因:
1. 后端服务器未启动
2. CORS跨域问题
3. 网络连接问题`, 'error');
            }
        }

        function clearResult() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = '';
        }

        // 页面加载时自动测试API连接
        window.onload = function() {
            testAPI();
        };
    </script>
</body>
</html>
