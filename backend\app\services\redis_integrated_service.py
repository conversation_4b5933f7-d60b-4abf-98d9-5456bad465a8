#!/usr/bin/env python3
"""
Redis集成升级 - 开源项目集成服务
将现有内存缓存升级为Redis分布式缓存
"""

import logging
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

# 导入Redis缓存服务
from .redis_cache_service import RedisCache


class ProjectStatus(str, Enum):
    """项目状态枚举"""

    NOT_INSTALLED = "not_installed"
    INSTALLING = "installing"
    INSTALLED = "installed"
    AVAILABLE = "available"
    ERROR = "error"
    UPDATING = "updating"


class OpenSourceProject(BaseModel):
    """开源项目配置模型"""

    name: str = Field(..., description="项目名称")
    github_url: str = Field(..., description="GitHub仓库地址")
    project_type: str = Field(..., description="项目类型")
    package_names: List[str] = Field(default_factory=list, description="Python包名")
    version: Optional[str] = Field(None, description="版本要求")
    description: str = Field(..., description="项目描述")
    integration_method: str = Field(..., description="集成方式")
    dependencies: List[str] = Field(default_factory=list, description="依赖项")
    config_required: bool = Field(False, description="是否需要配置")
    status: ProjectStatus = Field(default=ProjectStatus.NOT_INSTALLED)
    last_updated: Optional[datetime] = None
    error_message: Optional[str] = None


class RedisIntegratedService:
    """Redis集成的开源项目服务"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.projects: Dict[str, OpenSourceProject] = {}

        # 使用Redis缓存替代内存缓存
        self.cache = RedisCache("integration_service")
        self.project_cache = RedisCache("projects")
        self.metrics_cache = RedisCache("metrics")

        # 初始化项目配置
        self._load_project_configs()

    async def _cache_project_status(self, project: OpenSourceProject):
        """缓存项目状态到Redis"""
        cache_key = f"project_status:{project.name}"
        await self.project_cache.set(cache_key, project.dict(), ttl=300)  # 5分钟TTL

    async def _get_cached_project_status(
        self, project_name: str
    ) -> Optional[OpenSourceProject]:
        """从Redis获取缓存的项目状态"""
        cache_key = f"project_status:{project_name}"
        cached_data = await self.project_cache.get(cache_key)

        if cached_data:
            return OpenSourceProject.parse_obj(cached_data)
        return None

    async def check_project_status(self, project_name: str) -> ProjectStatus:
        """检查项目状态（优先从Redis缓存读取）"""
        try:
            # 先从Redis缓存获取
            cached_project = await self._get_cached_project_status(project_name)
            if cached_project:
                self.logger.debug(f"从缓存获取项目状态: {project_name}")
                return cached_project.status

            # 缓存未命中，执行实际检查
            if project_name in self.projects:
                project = self.projects[project_name]

                # 检查包是否可导入
                status = await self._check_package_availability(project)

                # 更新项目状态
                project.status = status
                project.last_updated = datetime.now()

                # 缓存到Redis
                await self._cache_project_status(project)

                return status

            return ProjectStatus.NOT_INSTALLED

        except Exception as e:
            self.logger.error(f"检查项目状态失败: {e}")
            return ProjectStatus.ERROR

    async def _check_package_availability(
        self, project: OpenSourceProject
    ) -> ProjectStatus:
        """检查包的可用性"""
        try:
            # 检查各个包名
            for package_name in project.package_names:
                try:
                    __import__(package_name)
                    self.logger.debug(f"包 {package_name} 可用")
                except ImportError:
                    self.logger.debug(f"包 {package_name} 不可用")
                    return ProjectStatus.NOT_INSTALLED

            return ProjectStatus.AVAILABLE

        except Exception as e:
            self.logger.error(f"检查包可用性失败: {e}")
            return ProjectStatus.ERROR

    async def get_all_project_status(self) -> Dict[str, Any]:
        """获取所有项目状态"""
        try:
            status_dict = {}

            for project_name in self.projects:
                status = await self.check_project_status(project_name)
                status_dict[project_name] = {
                    "status": status.value,
                    "last_updated": datetime.now().isoformat(),
                }

            # 缓存整体状态
            await self.cache.set("all_project_status", status_dict, ttl=180)  # 3分钟TTL

            return status_dict

        except Exception as e:
            self.logger.error(f"获取项目状态失败: {e}")
            return {"error": str(e)}

    async def get_service_health(self) -> Dict[str, Any]:
        """获取服务健康状态"""
        try:
            # 获取缓存指标
            cache_metrics = await self.cache.get_metrics()
            project_cache_metrics = await self.project_cache.get_metrics()

            # 获取项目总数和可用数
            total_projects = len(self.projects)
            available_count = 0

            for project_name in self.projects:
                status = await self.check_project_status(project_name)
                if status == ProjectStatus.AVAILABLE:
                    available_count += 1

            health_info = {
                "service_status": "healthy",
                "total_projects": total_projects,
                "available_projects": available_count,
                "availability_rate": (
                    f"{(available_count/total_projects)*100:.1f}%"
                    if total_projects > 0
                    else "0%"
                ),
                "cache_metrics": {
                    "main_cache": cache_metrics.dict(),
                    "project_cache": project_cache_metrics.dict(),
                },
                "redis_status": "connected",
            }

            # 缓存健康信息
            await self.metrics_cache.set(
                "service_health", health_info, ttl=60  # 1分钟TTL
            )

            return health_info

        except Exception as e:
            self.logger.error(f"获取服务健康状态失败: {e}")
            return {"service_status": "unhealthy", "error": str(e)}

    def _load_project_configs(self):
        """加载项目配置"""
        # 核心开源项目配置
        self.projects = {
            "yt-dlp": OpenSourceProject(
                name="yt-dlp",
                github_url="https://github.com/yt-dlp/yt-dlp",
                project_type="video_download",
                package_names=["yt_dlp"],
                description="强大的视频下载工具",
                integration_method="python_package",
                dependencies=["python>=3.7"],
            ),
            "moviepy": OpenSourceProject(
                name="moviepy",
                github_url="https://github.com/Zulko/moviepy",
                project_type="video_processing",
                package_names=["moviepy"],
                description="Python视频编辑库",
                integration_method="python_package",
                dependencies=["imageio", "decorator"],
            ),
            "fastapi": OpenSourceProject(
                name="fastapi",
                github_url="https://github.com/tiangolo/fastapi",
                project_type="web_framework",
                package_names=["fastapi"],
                description="现代、快速的Web框架",
                integration_method="python_package",
                dependencies=["pydantic", "starlette"],
            ),
            "redis": OpenSourceProject(
                name="redis-py",
                github_url="https://github.com/redis/redis-py",
                project_type="cache_database",
                package_names=["redis"],
                description="Redis Python客户端",
                integration_method="python_package",
                dependencies=[],
            ),
        }

        self.logger.info(f"加载了 {len(self.projects)} 个项目配置")


# 创建全局服务实例
redis_integrated_service = RedisIntegratedService()
