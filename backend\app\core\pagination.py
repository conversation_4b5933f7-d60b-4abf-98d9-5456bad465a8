# -*- coding: utf-8 -*-
"""
API分页机制
提供统一的分页功能和响应格式
"""

from typing import Any, Dict, List, Optional, TypeVar, Generic, Union
from math import ceil
from pydantic import BaseModel, Field, validator
from sqlalchemy.orm import Query
from sqlalchemy import func, text
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T')

class PaginationParams(BaseModel):
    """分页参数"""
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")
    sort_by: Optional[str] = Field(default=None, description="排序字段")
    sort_order: str = Field(default="desc", regex="^(asc|desc)$", description="排序方向")
    
    @validator('size')
    def validate_size(cls, v):
        if v > 100:
            return 100
        return v
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size
    
    @property
    def limit(self) -> int:
        """获取限制数量"""
        return self.size

class PaginationMeta(BaseModel):
    """分页元数据"""
    page: int = Field(description="当前页码")
    size: int = Field(description="每页大小")
    total: int = Field(description="总记录数")
    pages: int = Field(description="总页数")
    has_prev: bool = Field(description="是否有上一页")
    has_next: bool = Field(description="是否有下一页")
    prev_page: Optional[int] = Field(description="上一页页码")
    next_page: Optional[int] = Field(description="下一页页码")

class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应"""
    data: List[T] = Field(description="数据列表")
    meta: PaginationMeta = Field(description="分页元数据")
    success: bool = Field(default=True, description="是否成功")
    message: str = Field(default="查询成功", description="响应消息")

class Paginator:
    """分页器"""
    
    def __init__(self, params: PaginationParams):
        self.params = params
    
    def paginate_query(self, query: Query, count_query: Query = None) -> PaginatedResponse:
        """对SQLAlchemy查询进行分页
        
        Args:
            query: 数据查询
            count_query: 计数查询(可选)
        
        Returns:
            PaginatedResponse: 分页响应
        """
        try:
            # 获取总数
            if count_query:
                total = count_query.scalar()
            else:
                # 使用子查询计数以提高性能
                total = query.count()
            
            # 计算分页信息
            pages = ceil(total / self.params.size) if total > 0 else 0
            has_prev = self.params.page > 1
            has_next = self.params.page < pages
            prev_page = self.params.page - 1 if has_prev else None
            next_page = self.params.page + 1 if has_next else None
            
            # 应用分页
            paginated_query = query.offset(self.params.offset).limit(self.params.limit)
            
            # 执行查询
            data = paginated_query.all()
            
            # 构建元数据
            meta = PaginationMeta(
                page=self.params.page,
                size=self.params.size,
                total=total,
                pages=pages,
                has_prev=has_prev,
                has_next=has_next,
                prev_page=prev_page,
                next_page=next_page
            )
            
            return PaginatedResponse(
                data=data,
                meta=meta
            )
            
        except Exception as e:
            logger.error(f"分页查询失败: {e}")
            return PaginatedResponse(
                data=[],
                meta=PaginationMeta(
                    page=self.params.page,
                    size=self.params.size,
                    total=0,
                    pages=0,
                    has_prev=False,
                    has_next=False,
                    prev_page=None,
                    next_page=None
                ),
                success=False,
                message=f"查询失败: {str(e)}"
            )
    
    def paginate_list(self, data_list: List[T]) -> PaginatedResponse[T]:
        """对列表进行分页
        
        Args:
            data_list: 数据列表
        
        Returns:
            PaginatedResponse: 分页响应
        """
        try:
            total = len(data_list)
            pages = ceil(total / self.params.size) if total > 0 else 0
            has_prev = self.params.page > 1
            has_next = self.params.page < pages
            prev_page = self.params.page - 1 if has_prev else None
            next_page = self.params.page + 1 if has_next else None
            
            # 计算切片范围
            start = self.params.offset
            end = start + self.params.size
            
            # 获取分页数据
            paginated_data = data_list[start:end]
            
            # 构建元数据
            meta = PaginationMeta(
                page=self.params.page,
                size=self.params.size,
                total=total,
                pages=pages,
                has_prev=has_prev,
                has_next=has_next,
                prev_page=prev_page,
                next_page=next_page
            )
            
            return PaginatedResponse(
                data=paginated_data,
                meta=meta
            )
            
        except Exception as e:
            logger.error(f"列表分页失败: {e}")
            return PaginatedResponse(
                data=[],
                meta=PaginationMeta(
                    page=self.params.page,
                    size=self.params.size,
                    total=0,
                    pages=0,
                    has_prev=False,
                    has_next=False,
                    prev_page=None,
                    next_page=None
                ),
                success=False,
                message=f"分页失败: {str(e)}"
            )

class CursorPaginationParams(BaseModel):
    """游标分页参数"""
    cursor: Optional[str] = Field(default=None, description="游标")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")
    direction: str = Field(default="next", regex="^(next|prev)$", description="分页方向")
    
    @validator('size')
    def validate_size(cls, v):
        if v > 100:
            return 100
        return v

class CursorPaginationMeta(BaseModel):
    """游标分页元数据"""
    size: int = Field(description="每页大小")
    has_prev: bool = Field(description="是否有上一页")
    has_next: bool = Field(description="是否有下一页")
    prev_cursor: Optional[str] = Field(description="上一页游标")
    next_cursor: Optional[str] = Field(description="下一页游标")

class CursorPaginatedResponse(BaseModel, Generic[T]):
    """游标分页响应"""
    data: List[T] = Field(description="数据列表")
    meta: CursorPaginationMeta = Field(description="分页元数据")
    success: bool = Field(default=True, description="是否成功")
    message: str = Field(default="查询成功", description="响应消息")

class CursorPaginator:
    """游标分页器
    
    适用于大数据集的高性能分页，特别是时间序列数据
    """
    
    def __init__(self, params: CursorPaginationParams, cursor_field: str = "id"):
        self.params = params
        self.cursor_field = cursor_field
    
    def paginate_query(self, query: Query, cursor_value_func=None) -> CursorPaginatedResponse:
        """对查询进行游标分页
        
        Args:
            query: 数据查询
            cursor_value_func: 游标值提取函数
        
        Returns:
            CursorPaginatedResponse: 游标分页响应
        """
        try:
            # 应用游标条件
            if self.params.cursor:
                if self.params.direction == "next":
                    query = query.filter(text(f"{self.cursor_field} > :cursor")).params(cursor=self.params.cursor)
                else:
                    query = query.filter(text(f"{self.cursor_field} < :cursor")).params(cursor=self.params.cursor)
            
            # 获取数据(多取一条用于判断是否有下一页)
            data = query.limit(self.params.size + 1).all()
            
            # 判断是否有更多数据
            has_more = len(data) > self.params.size
            if has_more:
                data = data[:-1]  # 移除多取的一条
            
            # 计算游标
            prev_cursor = None
            next_cursor = None
            
            if data:
                if cursor_value_func:
                    first_cursor = cursor_value_func(data[0])
                    last_cursor = cursor_value_func(data[-1])
                else:
                    first_cursor = getattr(data[0], self.cursor_field)
                    last_cursor = getattr(data[-1], self.cursor_field)
                
                if self.params.direction == "next":
                    prev_cursor = str(first_cursor) if self.params.cursor else None
                    next_cursor = str(last_cursor) if has_more else None
                else:
                    prev_cursor = str(last_cursor) if has_more else None
                    next_cursor = str(first_cursor) if self.params.cursor else None
            
            # 构建元数据
            meta = CursorPaginationMeta(
                size=self.params.size,
                has_prev=self.params.cursor is not None and self.params.direction == "next",
                has_next=has_more if self.params.direction == "next" else (self.params.cursor is not None),
                prev_cursor=prev_cursor,
                next_cursor=next_cursor
            )
            
            return CursorPaginatedResponse(
                data=data,
                meta=meta
            )
            
        except Exception as e:
            logger.error(f"游标分页查询失败: {e}")
            return CursorPaginatedResponse(
                data=[],
                meta=CursorPaginationMeta(
                    size=self.params.size,
                    has_prev=False,
                    has_next=False,
                    prev_cursor=None,
                    next_cursor=None
                ),
                success=False,
                message=f"查询失败: {str(e)}"
            )

# 便捷函数
def paginate_query(query: Query, params: PaginationParams, count_query: Query = None) -> PaginatedResponse:
    """分页查询便捷函数"""
    paginator = Paginator(params)
    return paginator.paginate_query(query, count_query)

def paginate_list(data_list: List[T], params: PaginationParams) -> PaginatedResponse[T]:
    """列表分页便捷函数"""
    paginator = Paginator(params)
    return paginator.paginate_list(data_list)

def cursor_paginate_query(query: Query, params: CursorPaginationParams, 
                         cursor_field: str = "id", cursor_value_func=None) -> CursorPaginatedResponse:
    """游标分页查询便捷函数"""
    paginator = CursorPaginator(params, cursor_field)
    return paginator.paginate_query(query, cursor_value_func)

# 分页装饰器
def paginated_endpoint(default_size: int = 20, max_size: int = 100):
    """分页端点装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 从请求参数中提取分页参数
            page = kwargs.get('page', 1)
            size = min(kwargs.get('size', default_size), max_size)
            sort_by = kwargs.get('sort_by')
            sort_order = kwargs.get('sort_order', 'desc')
            
            # 创建分页参数
            params = PaginationParams(
                page=page,
                size=size,
                sort_by=sort_by,
                sort_order=sort_order
            )
            
            # 将分页参数添加到函数参数中
            kwargs['pagination'] = params
            
            return func(*args, **kwargs)
        return wrapper
    return decorator