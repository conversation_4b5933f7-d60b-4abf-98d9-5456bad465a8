"""
MySQL性能监控模块
实时监控数据库性能指标
"""

import pymysql
import psutil
import json
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)


@dataclass
class MySQLMetrics:
    """MySQL性能指标"""

    timestamp: str
    connections_active: int
    connections_total: int
    queries_per_second: float
    slow_queries: int
    innodb_buffer_pool_hit_ratio: float
    table_locks_waited: int
    query_cache_hit_ratio: float
    cpu_usage: float
    memory_usage: float
    disk_io_read: float
    disk_io_write: float


class MySQLPerformanceMonitor:
    """MySQL性能监控器"""

    def __init__(self):
        from app.core.config import settings
        self.mysql_config = {
            "host": settings.DB_HOST,
            "port": settings.DB_PORT,
            "user": settings.DB_USER,
            "password": settings.DB_PASSWORD,
            "database": settings.DB_NAME,
            "charset": "utf8mb4",
        }
        self.metrics_history = []
        self.alert_thresholds = {
            "cpu_usage": 80.0,
            "memory_usage": 85.0,
            "slow_queries_per_minute": 10,
            "connections_usage": 80.0,
            "buffer_pool_hit_ratio": 95.0,
        }

    def collect_mysql_metrics(self) -> Dict[str, Any]:
        """收集MySQL性能指标"""
        try:
            conn = pymysql.connect(**self.mysql_config)
            cursor = conn.cursor()

            metrics = {}

            # 连接数统计
            cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
            metrics["connections_active"] = int(cursor.fetchone()[1])

            cursor.execute("SHOW STATUS LIKE 'Connections'")
            metrics["connections_total"] = int(cursor.fetchone()[1])

            # 查询统计
            cursor.execute("SHOW STATUS LIKE 'Questions'")
            metrics["total_queries"] = int(cursor.fetchone()[1])

            cursor.execute("SHOW STATUS LIKE 'Slow_queries'")
            metrics["slow_queries"] = int(cursor.fetchone()[1])

            # InnoDB缓冲池命中率
            cursor.execute("SHOW STATUS LIKE 'Innodb_buffer_pool_read_requests'")
            read_requests = int(cursor.fetchone()[1])

            cursor.execute("SHOW STATUS LIKE 'Innodb_buffer_pool_reads'")
            disk_reads = int(cursor.fetchone()[1])

            if read_requests > 0:
                metrics["innodb_buffer_pool_hit_ratio"] = (
                    (read_requests - disk_reads) / read_requests
                ) * 100
            else:
                metrics["innodb_buffer_pool_hit_ratio"] = 100.0

            # 表锁等待
            cursor.execute("SHOW STATUS LIKE 'Table_locks_waited'")
            metrics["table_locks_waited"] = int(cursor.fetchone()[1])

            # 查询缓存命中率
            cursor.execute("SHOW STATUS LIKE 'Qcache_hits'")
            cache_hits = int(cursor.fetchone()[1])

            cursor.execute("SHOW STATUS LIKE 'Com_select'")
            selects = int(cursor.fetchone()[1])

            if (cache_hits + selects) > 0:
                metrics["query_cache_hit_ratio"] = (
                    cache_hits / (cache_hits + selects)
                ) * 100
            else:
                metrics["query_cache_hit_ratio"] = 0.0

            conn.close()
            return metrics

        except Exception as e:
            logger.error(f"MySQL指标收集失败: {e}")
            return {}

    def collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统性能指标"""
        try:
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=1)

            # 内存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent

            # 磁盘I/O
            disk_io = psutil.disk_io_counters()
            disk_io_read = disk_io.read_bytes / (1024 * 1024)  # MB
            disk_io_write = disk_io.write_bytes / (1024 * 1024)  # MB

            return {
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage,
                "disk_io_read": disk_io_read,
                "disk_io_write": disk_io_write,
            }

        except Exception as e:
            logger.error(f"系统指标收集失败: {e}")
            return {}

    def calculate_qps(
        self, current_queries: int, previous_queries: int, time_diff: float
    ) -> float:
        """计算每秒查询数"""
        if time_diff > 0:
            return (current_queries - previous_queries) / time_diff
        return 0.0

    def collect_all_metrics(self) -> MySQLMetrics:
        """收集所有性能指标"""
        timestamp = datetime.now().isoformat()

        mysql_metrics = self.collect_mysql_metrics()
        system_metrics = self.collect_system_metrics()

        # 计算QPS
        qps = 0.0
        if len(self.metrics_history) > 0:
            last_metrics = self.metrics_history[-1]
            time_diff = (
                datetime.fromisoformat(timestamp)
                - datetime.fromisoformat(last_metrics.timestamp)
            ).total_seconds()

            if "total_queries" in mysql_metrics:
                qps = self.calculate_qps(
                    mysql_metrics["total_queries"],
                    getattr(last_metrics, "total_queries", 0),
                    time_diff,
                )

        metrics = MySQLMetrics(
            timestamp=timestamp,
            connections_active=mysql_metrics.get("connections_active", 0),
            connections_total=mysql_metrics.get("connections_total", 0),
            queries_per_second=qps,
            slow_queries=mysql_metrics.get("slow_queries", 0),
            innodb_buffer_pool_hit_ratio=mysql_metrics.get(
                "innodb_buffer_pool_hit_ratio", 0.0
            ),
            table_locks_waited=mysql_metrics.get("table_locks_waited", 0),
            query_cache_hit_ratio=mysql_metrics.get("query_cache_hit_ratio", 0.0),
            cpu_usage=system_metrics.get("cpu_usage", 0.0),
            memory_usage=system_metrics.get("memory_usage", 0.0),
            disk_io_read=system_metrics.get("disk_io_read", 0.0),
            disk_io_write=system_metrics.get("disk_io_write", 0.0),
        )

        # 保存到历史记录
        self.metrics_history.append(metrics)

        # 保持最近100条记录
        if len(self.metrics_history) > 100:
            self.metrics_history = self.metrics_history[-100:]

        return metrics

    def check_alerts(self, metrics: MySQLMetrics) -> List[str]:
        """检查告警条件"""
        alerts = []

        if metrics.cpu_usage > self.alert_thresholds["cpu_usage"]:
            alerts.append(f"CPU使用率过高: {metrics.cpu_usage:.1f}%")

        if metrics.memory_usage > self.alert_thresholds["memory_usage"]:
            alerts.append(f"内存使用率过高: {metrics.memory_usage:.1f}%")

        if (
            metrics.innodb_buffer_pool_hit_ratio
            < self.alert_thresholds["buffer_pool_hit_ratio"]
        ):
            alerts.append(
                f"InnoDB缓冲池命中率过低: {metrics.innodb_buffer_pool_hit_ratio:.1f}%"
            )

        # 检查慢查询增长
        if len(self.metrics_history) > 1:
            prev_metrics = self.metrics_history[-2]
            slow_query_increase = metrics.slow_queries - prev_metrics.slow_queries
            time_diff = (
                datetime.fromisoformat(metrics.timestamp)
                - datetime.fromisoformat(prev_metrics.timestamp)
            ).total_seconds() / 60

            if time_diff > 0:
                slow_queries_per_minute = slow_query_increase / time_diff
                if (
                    slow_queries_per_minute
                    > self.alert_thresholds["slow_queries_per_minute"]
                ):
                    alerts.append(f"慢查询增长过快: {slow_queries_per_minute:.1f}/分钟")

        return alerts

    def save_metrics(
        self, metrics: MySQLMetrics, filepath: str = "metrics/mysql_metrics.json"
    ):
        """保存指标到文件"""
        try:
            import os

            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            # 读取现有数据
            existing_data = []
            if os.path.exists(filepath):
                with open(filepath, "r", encoding="utf-8") as f:
                    existing_data = json.load(f)

            # 添加新指标
            existing_data.append(asdict(metrics))

            # 保持最近1000条记录
            if len(existing_data) > 1000:
                existing_data = existing_data[-1000:]

            # 保存到文件
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(existing_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"保存指标失败: {e}")

    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        if not self.metrics_history:
            return {"error": "没有可用的性能数据"}

        recent_metrics = self.metrics_history[-10:]  # 最近10条记录

        # 计算平均值
        avg_cpu = sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_usage for m in recent_metrics) / len(recent_metrics)
        avg_qps = sum(m.queries_per_second for m in recent_metrics) / len(
            recent_metrics
        )
        avg_buffer_hit = sum(
            m.innodb_buffer_pool_hit_ratio for m in recent_metrics
        ) / len(recent_metrics)

        return {
            "report_time": datetime.now().isoformat(),
            "metrics_count": len(self.metrics_history),
            "averages": {
                "cpu_usage": round(avg_cpu, 2),
                "memory_usage": round(avg_memory, 2),
                "queries_per_second": round(avg_qps, 2),
                "buffer_pool_hit_ratio": round(avg_buffer_hit, 2),
            },
            "current": (
                asdict(self.metrics_history[-1]) if self.metrics_history else None
            ),
            "alerts": (
                self.check_alerts(self.metrics_history[-1])
                if self.metrics_history
                else []
            ),
        }


# 全局监控器实例
mysql_monitor = MySQLPerformanceMonitor()


def start_monitoring():
    """启动性能监控"""
    logger.info("MySQL性能监控已启动")
    return mysql_monitor


def get_current_metrics():
    """获取当前性能指标"""
    return mysql_monitor.collect_all_metrics()


def get_performance_report():
    """获取性能报告"""
    return mysql_monitor.generate_performance_report()
