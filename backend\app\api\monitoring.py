#!/usr/bin/env python3
"""
系统监控API - Prometheus指标端点
"""

import logging
from typing import Any, Dict

from fastapi import APIRouter, HTTPException, Response

router = APIRouter(prefix="/monitoring", tags=["monitoring"])
logger = logging.getLogger(__name__)

# 全局监控服务实例
monitoring_service = None


def init_monitoring_service():
    """初始化监控服务"""
    global monitoring_service
    try:
        from app.services.system_monitoring_service import (
            SystemMonitoringService,
        )

        monitoring_service = SystemMonitoringService(collection_interval=10.0)
        monitoring_service.start_monitoring()
        logger.info("监控服务初始化成功")
        return True
    except Exception as e:
        logger.error(f"监控服务初始化失败: {e}")
        return False


@router.get("/metrics")
async def get_prometheus_metrics():
    """
    获取Prometheus格式的指标数据
    用于Prometheus服务器抓取
    """
    if not monitoring_service:
        if not init_monitoring_service():
            raise HTTPException(status_code=503, detail="监控服务不可用")

    try:
        metrics_data = monitoring_service.get_metrics()
        return Response(
            content=metrics_data,
            media_type="text/plain; version=0.0.4; charset=utf-8",
        )
    except Exception as e:
        logger.error(f"获取Prometheus指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"指标获取失败: {str(e)}")


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    健康检查端点
    """
    if not monitoring_service:
        if not init_monitoring_service():
            return {
                "status": "unhealthy",
                "monitoring_service": "unavailable",
                "message": "监控服务初始化失败",
            }

    try:
        summary = monitoring_service.get_metrics_summary()
        return {
            "status": "healthy",
            "monitoring_service": "available",
            "system_summary": summary,
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {"status": "unhealthy", "error": str(e)}


@router.get("/summary")
async def get_metrics_summary() -> Dict[str, Any]:
    """
    获取指标摘要
    """
    if not monitoring_service:
        if not init_monitoring_service():
            raise HTTPException(status_code=503, detail="监控服务不可用")

    try:
        return monitoring_service.get_metrics_summary()
    except Exception as e:
        logger.error(f"获取指标摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"指标摘要获取失败: {str(e)}")


@router.post("/record/http")
async def record_http_request(
    method: str, endpoint: str, status_code: int, duration: float
):
    """
    记录HTTP请求指标
    """
    if not monitoring_service:
        return {
            "status": "ignored",
            "reason": "monitoring service unavailable",
        }

    try:
        monitoring_service.record_http_request(
            method,
            endpoint,
            status_code,
            duration,
        )
        return {"status": "recorded"}
    except Exception as e:
        logger.error(f"记录HTTP请求指标失败: {e}")
        return {"status": "error", "error": str(e)}


@router.post("/record/task")
async def record_task_execution(task_type: str, status: str, duration: float):
    """
    记录任务执行指标
    """
    if not monitoring_service:
        return {
            "status": "ignored",
            "reason": "monitoring service unavailable",
        }

    try:
        monitoring_service.record_task_execution(
            task_type,
            status,
            duration,
        )
        return {"status": "recorded"}
    except Exception as e:
        logger.error(f"记录任务执行指标失败: {e}")
        return {"status": "error", "error": str(e)}


@router.post("/record/video")
async def record_video_processing(operation: str, status: str, duration: float):
    """
    记录视频处理指标
    """
    if not monitoring_service:
        return {
            "status": "ignored",
            "reason": "monitoring service unavailable",
        }

    try:
        monitoring_service.record_video_processing(
            operation,
            status,
            duration,
        )
        return {"status": "recorded"}
    except Exception as e:
        logger.error(f"记录视频处理指标失败: {e}")
        return {"status": "error", "error": str(e)}


@router.post("/record/user")
async def record_user_activity(action: str, user_count: int = None):
    """
    记录用户活动指标
    """
    if not monitoring_service:
        return {
            "status": "ignored",
            "reason": "monitoring service unavailable",
        }

    try:
        monitoring_service.record_user_activity(action, user_count)
        return {"status": "recorded"}
    except Exception as e:
        logger.error(f"记录用户活动指标失败: {e}")
        return {"status": "error", "error": str(e)}


@router.get("/status")
async def get_monitoring_status() -> Dict[str, Any]:
    """
    获取监控服务状态
    """
    status = {
        "monitoring_service_initialized": monitoring_service is not None,
        "monitoring_active": False,
        "prometheus_available": False,
    }

    if monitoring_service:
        status.update(
            {
                "monitoring_active": monitoring_service.running,
                "collection_interval": monitoring_service.collection_interval,
                "prometheus_available": hasattr(monitoring_service, "registry"),
            }
        )

    return status
