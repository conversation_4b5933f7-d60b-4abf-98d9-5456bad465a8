"""
分发管理API
按照《后端开发指南.md》要求，实现多平台内容分发、发布计划、发布状态追踪等功能
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import case, func
from sqlalchemy.orm import Session

from app.core.auth import get_current_user
from app.core.database import get_db
from app.models import Distribution
from app.schemas import (
    DistributionCreate,
    DistributionResponse,
    DistributionUpdate,
)

router = APIRouter(prefix="/distribution", tags=["分发管理"])


@router.post("/", response_model=DistributionResponse)
async def create_distribution(
    distribution: DistributionCreate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """创建分发任务"""
    try:
        # 创建分发任务记录
        db_distribution = Distribution(
            content_id=distribution.content_id,
            platform=distribution.platform,
            creator_id=current_user["user_id"],
            title=distribution.title,
            description=distribution.description,
            scheduled_time=distribution.scheduled_time,
            status="pending",
            platform_config=(
                json.dumps(distribution.platform_config)
                if distribution.platform_config
                else None
            ),
        )

        db.add(db_distribution)
        db.commit()
        db.refresh(db_distribution)

        # 如果是立即发布，启动后台任务
        if (
            not distribution.scheduled_time
            or distribution.scheduled_time <= datetime.utcnow()
        ):
            # 这里应该调用Celery任务，暂时使用同步方式
            await _process_distribution_async(db_distribution.id, db)

        return DistributionResponse(
            id=db_distribution.id,
            content_id=db_distribution.content_id,
            platform=db_distribution.platform,
            creator_id=db_distribution.creator_id,
            title=db_distribution.title,
            description=db_distribution.description,
            scheduled_time=db_distribution.scheduled_time,
            status=db_distribution.status,
            platform_config=(
                json.loads(db_distribution.platform_config)
                if db_distribution.platform_config
                else {}
            ),
            result_data=(
                json.loads(db_distribution.result_data)
                if db_distribution.result_data
                else {}
            ),
            created_at=db_distribution.created_at,
            updated_at=db_distribution.updated_at,
            published_at=db_distribution.published_at,
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建分发任务失败: {str(e)}",
        )


@router.get("/", response_model=List[DistributionResponse])
async def list_distributions(
    content_id: Optional[int] = None,
    platform: Optional[str] = None,
    status_filter: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取分发任务列表"""
    query = db.query(Distribution).filter(
        Distribution.creator_id == current_user["user_id"]
    )

    if content_id:
        query = query.filter(Distribution.content_id == content_id)
    if platform:
        query = query.filter(Distribution.platform == platform)
    if status_filter:
        query = query.filter(Distribution.status == status_filter)

    distributions = (
        query.order_by(Distribution.created_at.desc()).offset(skip).limit(limit).all()
    )

    return [
        DistributionResponse(
            id=dist.id,
            content_id=dist.content_id,
            platform=dist.platform,
            creator_id=dist.creator_id,
            title=dist.title,
            description=dist.description,
            scheduled_time=dist.scheduled_time,
            status=dist.status,
            platform_config=(
                json.loads(dist.platform_config) if dist.platform_config else {}
            ),
            result_data=(json.loads(dist.result_data) if dist.result_data else {}),
            error_message=dist.error_message,
            created_at=dist.created_at,
            updated_at=dist.updated_at,
            published_at=dist.published_at,
        )
        for dist in distributions
    ]


@router.get("/{distribution_id}", response_model=DistributionResponse)
async def get_distribution(
    distribution_id: int,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取单个分发任务详情"""
    distribution = (
        db.query(Distribution)
        .filter(
            Distribution.id == distribution_id,
            Distribution.creator_id == current_user["user_id"],
        )
        .first()
    )

    if not distribution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="分发任务不存在"
        )

    return DistributionResponse(
        id=distribution.id,
        content_id=distribution.content_id,
        platform=distribution.platform,
        creator_id=distribution.creator_id,
        title=distribution.title,
        description=distribution.description,
        scheduled_time=distribution.scheduled_time,
        status=distribution.status,
        platform_config=(
            json.loads(distribution.platform_config)
            if distribution.platform_config
            else {}
        ),
        result_data=(
            json.loads(distribution.result_data) if distribution.result_data else {}
        ),
        error_message=distribution.error_message,
        created_at=distribution.created_at,
        updated_at=distribution.updated_at,
        published_at=distribution.published_at,
    )


@router.put("/{distribution_id}", response_model=DistributionResponse)
async def update_distribution(
    distribution_id: int,
    distribution_update: DistributionUpdate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """更新分发任务"""
    distribution = (
        db.query(Distribution)
        .filter(
            Distribution.id == distribution_id,
            Distribution.creator_id == current_user["user_id"],
        )
        .first()
    )

    if not distribution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="分发任务不存在"
        )

    # 检查是否可以更新
    if distribution.status in ["processing", "completed"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="正在处理或已完成的任务不能修改",
        )

    # 更新字段
    update_data = distribution_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == "platform_config" and value is not None:
            setattr(distribution, field, json.dumps(value))
        else:
            setattr(distribution, field, value)

    distribution.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(distribution)

    return DistributionResponse(
        id=distribution.id,
        content_id=distribution.content_id,
        platform=distribution.platform,
        creator_id=distribution.creator_id,
        title=distribution.title,
        description=distribution.description,
        scheduled_time=distribution.scheduled_time,
        status=distribution.status,
        platform_config=(
            json.loads(distribution.platform_config)
            if distribution.platform_config
            else {}
        ),
        result_data=(
            json.loads(distribution.result_data) if distribution.result_data else {}
        ),
        error_message=distribution.error_message,
        created_at=distribution.created_at,
        updated_at=distribution.updated_at,
        published_at=distribution.published_at,
    )


@router.delete("/{distribution_id}")
async def delete_distribution(
    distribution_id: int,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """删除分发任务"""
    distribution = (
        db.query(Distribution)
        .filter(
            Distribution.id == distribution_id,
            Distribution.creator_id == current_user["user_id"],
        )
        .first()
    )

    if not distribution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="分发任务不存在"
        )

    # 检查是否可以删除
    if distribution.status == "processing":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="正在处理的任务不能删除",
        )

    db.delete(distribution)
    db.commit()

    return {"message": "分发任务删除成功"}


@router.post("/{distribution_id}/publish")
async def publish_distribution(
    distribution_id: int,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """立即发布分发任务"""
    distribution = (
        db.query(Distribution)
        .filter(
            Distribution.id == distribution_id,
            Distribution.creator_id == current_user["user_id"],
        )
        .first()
    )

    if not distribution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="分发任务不存在"
        )

    if distribution.status != "pending":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只有待发布的任务可以立即发布",
        )

    try:
        # 启动发布过程
        await _process_distribution_async(distribution.id, db)

        db.refresh(distribution)

        return {
            "success": True,
            "message": "发布任务已启动",
            "distribution_id": distribution.id,
            "status": distribution.status,
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发布失败: {str(e)}",
        )


@router.post("/{distribution_id}/cancel")
async def cancel_distribution(
    distribution_id: int,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """取消分发任务"""
    distribution = (
        db.query(Distribution)
        .filter(
            Distribution.id == distribution_id,
            Distribution.creator_id == current_user["user_id"],
        )
        .first()
    )

    if not distribution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="分发任务不存在"
        )

    if distribution.status not in ["pending", "processing"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只有待处理或正在处理的任务可以取消",
        )

    distribution.status = "cancelled"
    distribution.updated_at = datetime.utcnow()
    db.commit()

    return {"message": "分发任务已取消"}


@router.get("/platforms/supported")
async def get_supported_platforms():
    """获取支持的平台列表"""
    platforms = {
        "douyin": {
            "name": "抖音",
            "description": "字节跳动旗下短视频平台",
            "supported_types": ["video", "image"],
            "config_required": ["access_token", "app_id"],
            "max_duration": 180,  # 秒
            "max_file_size": 1024 * 1024 * 100,  # 100MB
        },
        "xiaohongshu": {
            "name": "小红书",
            "description": "生活方式平台",
            "supported_types": ["image", "video"],
            "config_required": ["access_token"],
            "max_duration": 60,
            "max_file_size": 1024 * 1024 * 50,  # 50MB
        },
        "weibo": {
            "name": "微博",
            "description": "社交媒体平台",
            "supported_types": ["text", "image", "video"],
            "config_required": ["access_token", "app_key"],
            "max_duration": 300,
            "max_file_size": 1024 * 1024 * 200,  # 200MB
        },
        "bilibili": {
            "name": "哔哩哔哩",
            "description": "视频弹幕网站",
            "supported_types": ["video"],
            "config_required": ["sessdata", "bili_jct", "buvid3"],
            "max_duration": 3600,  # 1小时
            "max_file_size": 1024 * 1024 * 1024,  # 1GB
        },
    }

    return {"success": True, "platforms": platforms, "total": len(platforms)}


@router.get("/statistics/summary")
async def get_distribution_statistics(
    days: int = 30,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取分发统计信息"""

    # 计算时间范围
    start_date = datetime.utcnow() - timedelta(days=days)

    # 总体统计
    total_query = db.query(Distribution).filter(
        Distribution.creator_id == current_user["user_id"],
        Distribution.created_at >= start_date,
    )

    total_distributions = total_query.count()
    completed_distributions = total_query.filter(
        Distribution.status == "completed"
    ).count()
    failed_distributions = total_query.filter(Distribution.status == "failed").count()
    pending_distributions = total_query.filter(Distribution.status == "pending").count()

    # 按平台统计
    platform_stats = (
        db.query(
            Distribution.platform,
            func.count(Distribution.id).label("count"),
            func.sum(case([(Distribution.status == "completed", 1)], else_=0)).label(
                "completed"
            ),
            func.sum(case([(Distribution.status == "failed", 1)], else_=0)).label(
                "failed"
            ),
        )
        .filter(
            Distribution.creator_id == current_user["user_id"],
            Distribution.created_at >= start_date,
        )
        .group_by(Distribution.platform)
        .all()
    )

    # 按日期统计
    daily_stats = (
        db.query(
            func.date(Distribution.created_at).label("date"),
            func.count(Distribution.id).label("total"),
            func.sum(case([(Distribution.status == "completed", 1)], else_=0)).label(
                "completed"
            ),
        )
        .filter(
            Distribution.creator_id == current_user["user_id"],
            Distribution.created_at >= start_date,
        )
        .group_by(func.date(Distribution.created_at))
        .all()
    )

    return {
        "success": True,
        "period": f"最近{days}天",
        "summary": {
            "total": total_distributions,
            "completed": completed_distributions,
            "failed": failed_distributions,
            "pending": pending_distributions,
            "success_rate": (
                round(completed_distributions / total_distributions * 100, 2)
                if total_distributions > 0
                else 0
            ),
        },
        "by_platform": [
            {
                "platform": stat.platform,
                "total": stat.count,
                "completed": stat.completed,
                "failed": stat.failed,
                "success_rate": (
                    round(stat.completed / stat.count * 100, 2) if stat.count > 0 else 0
                ),
            }
            for stat in platform_stats
        ],
        "daily_trend": [
            {
                "date": stat.date.isoformat(),
                "total": stat.total,
                "completed": stat.completed,
            }
            for stat in daily_stats
        ],
    }


async def _process_distribution_async(distribution_id: int, db: Session):
    """异步处理分发任务（应该用Celery替代）"""
    distribution = (
        db.query(Distribution).filter(Distribution.id == distribution_id).first()
    )
    if not distribution:
        return

    try:
        # 更新状态为处理中
        distribution.status = "processing"
        distribution.updated_at = datetime.utcnow()
        db.commit()

        # 模拟发布过程
        await asyncio.sleep(2)  # 模拟网络请求

        # 这里应该调用具体的平台发布API
        # 目前使用模拟结果
        result_data = {
            "platform_id": f"{distribution.platform}_{distribution.id}",
            "published_url": (
                f"https://{distribution.platform}.com/post/" f"mock_{distribution.id}"
            ),
            "published_time": datetime.utcnow().isoformat(),
            "engagement": {"views": 0, "likes": 0, "comments": 0, "shares": 0},
        }

        # 更新为完成状态
        distribution.status = "completed"
        distribution.result_data = json.dumps(result_data)
        distribution.published_at = datetime.utcnow()
        distribution.updated_at = datetime.utcnow()
        db.commit()

    except Exception as e:
        # 更新为失败状态
        distribution.status = "failed"
        distribution.error_message = str(e)
        distribution.updated_at = datetime.utcnow()
        db.commit()
