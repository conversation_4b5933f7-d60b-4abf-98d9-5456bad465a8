import { Script } from 'vm';

export default new Script(`
this.ArrayBuffer = globalThis.ArrayBuffer;
this.Boolean = globalThis.Boolean;
this.DataView = globalThis.DataView;
this.Date = globalThis.Date;
this.Error = globalThis.Error;
this.EvalError = globalThis.EvalError;
this.Float32Array = globalThis.Float32Array;
this.Float64Array = globalThis.Float64Array;
this.GLOBAL = globalThis.GLOBAL;
this.Infinity = globalThis.Infinity;
this.Int16Array = globalThis.Int16Array;
this.Int32Array = globalThis.Int32Array;
this.Int8Array = globalThis.Int8Array;
this.Intl = globalThis.Intl;
this.JSON = globalThis.JSON;
this.Map = globalThis.Map;
this.Math = globalThis.Math;
this.NaN = globalThis.NaN;
this.Number = globalThis.Number;
this.Promise = globalThis.Promise;
this.RangeError = globalThis.RangeError;
this.ReferenceError = globalThis.ReferenceError;
this.RegExp = globalThis.RegExp;
this.Reflect = globalThis.Reflect;
this.Set = globalThis.Set;
this.Symbol = globalThis.Symbol;
this.SyntaxError = globalThis.SyntaxError;
this.String = globalThis.String;
this.TypeError = globalThis.TypeError;
this.URIError = globalThis.URIError;
this.Uint16Array = globalThis.Uint16Array;
this.Uint32Array = globalThis.Uint32Array;
this.Uint8Array = globalThis.Uint8Array;
this.Uint8ClampedArray = globalThis.Uint8ClampedArray;
this.WeakMap = globalThis.WeakMap;
this.WeakSet = globalThis.WeakSet;
this.decodeURI = globalThis.decodeURI;
this.decodeURIComponent = globalThis.decodeURIComponent;
this.encodeURI = globalThis.encodeURI;
this.encodeURIComponent = globalThis.encodeURIComponent;
this.eval = globalThis.eval;
this.escape = globalThis.escape;
this.global = globalThis.global;
this.isFinite = globalThis.isFinite;
this.isNaN = globalThis.isNaN;
this.parseFloat = globalThis.parseFloat;
this.parseInt = globalThis.parseInt;
this.root = globalThis.root;
this.undefined = globalThis.undefined;
this.unescape = globalThis.unescape;
this.gc = globalThis.gc;
this.v8debug = globalThis.v8debug;
this.AbortController = globalThis.AbortController;
this.AbortSignal = globalThis.AbortSignal;
this.Array = globalThis.Array;
this.Object = globalThis.Object;
this.Function = globalThis.Function;
`);
