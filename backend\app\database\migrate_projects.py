"""
项目表结构迁移脚本
将现有的projects表升级为增强版本
"""

import sqlite3
from datetime import datetime
from pathlib import Path

# 数据库路径
DB_PATH = Path(__file__).parent.parent.parent / "ai_video_system.db"


def backup_database():
    """备份现有数据库"""
    backup_path = DB_PATH.with_suffix(
        f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    )

    # 复制数据库文件
    import shutil

    shutil.copy2(DB_PATH, backup_path)
    print(f"✅ 数据库已备份到: {backup_path}")
    return backup_path


def migrate_projects_table():
    """迁移projects表结构"""

    print("🔄 开始迁移projects表结构...")

    # 备份数据库
    backup_path = backup_database()

    try:
        conn = sqlite3.connect(DB_PATH)

        # 🔧 修复: 启用WAL模式和事务安全
        conn.execute("PRAGMA journal_mode = WAL")
        conn.execute("PRAGMA synchronous = FULL")
        conn.execute("PRAGMA foreign_keys = ON")

        cursor = conn.cursor()

        # 🔧 修复: 开始事务保护
        cursor.execute("BEGIN IMMEDIATE TRANSACTION")

        try:
            # 1. 检查现有表结构
            cursor.execute("PRAGMA table_info(projects)")
            existing_columns = {row[1]: row[2] for row in cursor.fetchall()}
            print(f"📋 现有列: {list(existing_columns.keys())}")

            # 2. 创建新的projects表（临时）- 添加约束检查
            cursor.execute(
                """
                CREATE TABLE projects_new (
                    id INTEGER PRIMARY KEY,
                    name VARCHAR(200) NOT NULL CHECK (length(name) > 0),
                    description TEXT,
                    project_type VARCHAR(50) DEFAULT 'video_creation' NOT NULL
                        CHECK (project_type IN ('video_creation', 'content_distribution', 'ai_analysis', 'automation', 'integration', 'research')),
                    owner_id INTEGER NOT NULL CHECK (owner_id > 0),
                    created_by INTEGER NOT NULL CHECK (created_by > 0),
                    status VARCHAR(20) DEFAULT 'draft' NOT NULL
                        CHECK (status IN ('draft', 'planning', 'in_progress', 'review', 'completed', 'archived', 'cancelled')),
                    priority VARCHAR(10) DEFAULT 'medium' NOT NULL
                        CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
                    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
                    estimated_hours INTEGER CHECK (estimated_hours > 0),
                    actual_hours INTEGER DEFAULT 0 CHECK (actual_hours >= 0),
                    start_date DATETIME,
                    due_date DATETIME,
                    completed_at DATETIME,
                    config JSON,
                    tags JSON,
                    task_count INTEGER DEFAULT 0 CHECK (task_count >= 0),
                    completed_task_count INTEGER DEFAULT 0 CHECK (completed_task_count >= 0),
                    member_count INTEGER DEFAULT 1 CHECK (member_count > 0),
                    is_active BOOLEAN DEFAULT 1,
                    is_template BOOLEAN DEFAULT 0,
                    is_public BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                    updated_at DATETIME,
                    notes TEXT,
                    last_activity TEXT,

                    -- 🔧 修复: 添加数据一致性约束
                    CHECK (completed_task_count <= task_count)
                )
            """
            )

            # 3. 迁移现有数据
            cursor.execute(
                """
            INSERT INTO projects_new (
                id, name, description, owner_id, created_by, status, 
                config, created_at, updated_at
            )
            SELECT 
                id, 
                name, 
                description, 
                owner_id,
                owner_id as created_by,  -- 假设创建者就是所有者
                CASE 
                    WHEN status = 'active' THEN 'in_progress'
                    WHEN status = 'paused' THEN 'draft'
                    ELSE status
                END as status,
                config,
                created_at,
                updated_at
            FROM projects
        """
            )

            # 4. 删除旧表，重命名新表
            cursor.execute("DROP TABLE projects")
            cursor.execute("ALTER TABLE projects_new RENAME TO projects")

            # 5. 创建优化的索引
            indexes = [
                "CREATE INDEX idx_project_status_type ON projects(status, project_type)",
                "CREATE INDEX idx_project_owner_status ON projects(owner_id, status)",
                "CREATE INDEX idx_project_created_desc ON projects(created_at DESC)",
                "CREATE INDEX idx_project_priority ON projects(priority)",
            ]

            for index_sql in indexes:
                cursor.execute(index_sql)

            # 6. 创建关联表
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS project_members (
                    id INTEGER PRIMARY KEY,
                    project_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    role VARCHAR(50) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member', 'viewer')),
                    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
                    UNIQUE(project_id, user_id)
                )
            """
            )

            cursor.execute(
                """
                CREATE INDEX idx_project_member_composite ON project_members(project_id, user_id, is_active)
            """
            )

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS project_history (
                    id INTEGER PRIMARY KEY,
                    project_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    action VARCHAR(50) NOT NULL,
                    description TEXT,
                    old_value JSON,
                    new_value JSON,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
                )
            """
            )

            cursor.execute(
                """
                CREATE INDEX idx_project_history_composite ON project_history(project_id, created_at DESC)
            """
            )

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS project_templates (
                    id INTEGER PRIMARY KEY,
                    name VARCHAR(200) NOT NULL,
                    description TEXT,
                    project_type VARCHAR(50) NOT NULL,
                    template_config JSON,
                    is_active BOOLEAN DEFAULT 1,
                    created_by INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME
                )
            """
            )

            # 7. 为现有项目添加默认成员记录
            cursor.execute(
                """
                INSERT INTO project_members (project_id, user_id, role)
                SELECT id, owner_id, 'owner'
                FROM projects
                WHERE id NOT IN (SELECT project_id FROM project_members)
            """
            )

            # 🔧 修复: 提交事务
            conn.commit()
            print("✅ projects表结构迁移完成")

        except Exception as inner_e:
            # 🔧 修复: 事务回滚
            conn.rollback()
            raise inner_e

        # 8. 验证迁移结果
        cursor.execute("SELECT COUNT(*) FROM projects")
        project_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM project_members")
        member_count = cursor.fetchone()[0]

        print(f"📊 迁移结果:")
        print(f"   - 项目数量: {project_count}")
        print(f"   - 成员记录: {member_count}")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ 迁移失败: {e}")

        # 恢复备份
        import shutil

        shutil.copy2(backup_path, DB_PATH)
        print(f"🔄 已从备份恢复数据库")

        return False


def add_sample_data():
    """添加示例数据"""

    print("📝 添加示例项目数据...")

    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查是否已有足够的示例数据
        cursor.execute("SELECT COUNT(*) FROM projects WHERE project_type != 'video'")
        if cursor.fetchone()[0] >= 3:
            print("ℹ️ 示例数据已存在，跳过添加")
            return

        # 添加示例项目
        sample_projects = [
            (
                "AI视频内容创作平台",
                "基于人工智能的短视频内容自动生成平台，支持文本到视频的转换",
                "video_creation",
                1,
                1,
                "in_progress",
                "high",
                75,
                '{"ai_model": "gpt-4", "video_quality": "1080p"}',
                '["AI", "视频", "创作"]',
            ),
            (
                "多平台内容分发系统",
                "自动化的内容分发系统，支持抖音、快手、小红书等多个平台",
                "content_distribution",
                1,
                1,
                "planning",
                "medium",
                25,
                '{"platforms": ["douyin", "kuaishou", "xiaohongshu"]}',
                '["分发", "自动化", "多平台"]',
            ),
            (
                "用户行为分析项目",
                "基于大数据的用户行为分析和内容推荐系统",
                "ai_analysis",
                1,
                1,
                "draft",
                "low",
                10,
                '{"analysis_type": "behavior", "data_source": "user_logs"}',
                '["分析", "大数据", "推荐"]',
            ),
            (
                "内容审核自动化",
                "AI驱动的内容审核和合规检查系统",
                "automation",
                1,
                1,
                "completed",
                "high",
                100,
                '{"ai_model": "content_moderator", "accuracy": 0.95}',
                '["审核", "AI", "合规"]',
            ),
            (
                "第三方API集成",
                "集成各种第三方服务API，包括支付、存储、通知等",
                "integration",
                1,
                1,
                "review",
                "medium",
                90,
                '{"apis": ["payment", "storage", "notification"]}',
                '["集成", "API", "服务"]',
            ),
        ]

        cursor.executemany(
            """
            INSERT INTO projects (
                name, description, project_type, owner_id, created_by, 
                status, priority, progress_percentage, config, tags
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            sample_projects,
        )

        conn.commit()
        conn.close()

        print("✅ 示例数据添加完成")

    except Exception as e:
        print(f"❌ 添加示例数据失败: {e}")


if __name__ == "__main__":
    print("🚀 开始项目表结构迁移...")

    if migrate_projects_table():
        add_sample_data()
        print("🎉 项目管理功能数据库升级完成！")
    else:
        print("💥 迁移失败，请检查错误信息")
