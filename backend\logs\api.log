{"timestamp": "2025-07-14T08:23:42.090630", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 149}
{"timestamp": "2025-07-14T08:23:42.090630", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.156375", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.157375", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.160374", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.162372", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.164376", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.164376", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.166376", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.166376", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.168430", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.168430", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.170427", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.170427", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.172428", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.172428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.173430", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.174428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.175427", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.176429", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.177427", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.178428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.179428", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.180428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.181427", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.182429", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.183428", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.184428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.185428", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.186430", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.187428", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.187428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.189427", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.190429", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.191428", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.191428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.193428", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.193428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.194429", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.195428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.196429", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.197426", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.199429", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.199429", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.200426", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.201428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.202429", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.203429", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.204428", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.205428", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.206427", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.207417", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.208420", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.209420", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.210419", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.210419", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.212420", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.212420", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.213419", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.214420", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.216420", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.216420", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.218421", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.218421", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.220421", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.220421", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.221420", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.222420", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.223419", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.223419", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.225418", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.225418", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.227419", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.227419", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.229419", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.229419", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.231420", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.231420", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.233419", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.233419", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.235419", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.235419", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.237421", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.237421", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.239420", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.239420", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.241421", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.241421", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.242420", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.243420", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.244420", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.244420", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.246418", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.247418", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.249419", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.249419", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.412419", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.412419", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.413418", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.413418", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.414418", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.414418", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
{"timestamp": "2025-07-14T10:05:19.415418", "level": "INFO", "logger": "api", "message": "系统健康检查请求", "module": "logging", "function": "_log", "line": 147}
{"timestamp": "2025-07-14T10:05:19.416418", "level": "INFO", "logger": "api", "message": "系统健康检查完成", "module": "", "function": null, "line": 0, "overall_status": "critical"}
