"""应用程序配置
基于最佳实践的配置管理
"""

import os
from pathlib import Path
from typing import List, Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    def __init__(self):
        # 基础配置
        self.PROJECT_NAME = "二创短视频分发系统"
        self.VERSION = "2.0.0"
        self.API_V1_STR = "/api/v1"
        self.HOST = "0.0.0.0"
        self.PORT = 8000
        self.RELOAD = os.getenv("RELOAD", "true").lower() == "true"
        self.DEBUG = os.getenv("DEBUG", "true").lower() == "true"
        self.BASE_DIR = Path(__file__).parent.parent.parent.absolute()

    # 安全配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "dev-secret-key-for-testing-only")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "15"))
    REFRESH_TOKEN_EXPIRE_DAYS: int = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

    # 环境配置
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")

    # 数据库配置
    DB_HOST: str = os.getenv("DB_HOST", "localhost")
    DB_PORT: int = int(os.getenv("DB_PORT", "3306"))
    DB_USER: str = os.getenv("DB_USER", "root")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "123456")
    DB_NAME: str = os.getenv("DB_NAME", "video_creation")
    
    @property
    def DATABASE_URL(self) -> str:
        """动态生成数据库连接URL"""
        return (
            f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@"
            f"{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}?charset=utf8mb4"
        )
    
    # 数据库连接池配置
    DB_POOL_SIZE: int = int(os.getenv("DB_POOL_SIZE", "20"))
    DB_MAX_OVERFLOW: int = int(os.getenv("DB_MAX_OVERFLOW", "30"))
    DB_POOL_TIMEOUT: int = int(os.getenv("DB_POOL_TIMEOUT", "30"))
    DB_POOL_RECYCLE: int = int(os.getenv("DB_POOL_RECYCLE", "3600"))
    DB_POOL_PRE_PING: bool = os.getenv("DB_POOL_PRE_PING", "true").lower() == "true"
    
    # 数据库连接超时配置
    DB_CONNECT_TIMEOUT: int = int(os.getenv("DB_CONNECT_TIMEOUT", "10"))
    DB_READ_TIMEOUT: int = int(os.getenv("DB_READ_TIMEOUT", "30"))
    DB_WRITE_TIMEOUT: int = int(os.getenv("DB_WRITE_TIMEOUT", "30"))

    # CORS配置
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
    ]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]

    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "info")
    LOG_FILE: str = os.getenv("LOG_FILE", "logs/app.log")

    # AI服务配置
    AI_SERVICE_TIMEOUT: int = int(os.getenv("AI_SERVICE_TIMEOUT", "30"))
    CONTENT_COMPLIANCE_MODEL: str = os.getenv("CONTENT_COMPLIANCE_MODEL", "content-safety-v1")
    TEXT_TO_VIDEO_MODEL: str = os.getenv("TEXT_TO_VIDEO_MODEL", "text2video-v1")

    # Redis配置
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_PASSWORD: Optional[str] = os.getenv("REDIS_PASSWORD")
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    REDIS_MAX_CONNECTIONS: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "20"))
    REDIS_SOCKET_TIMEOUT: float = float(os.getenv("REDIS_SOCKET_TIMEOUT", "5.0"))
    REDIS_SOCKET_CONNECT_TIMEOUT: float = float(os.getenv("REDIS_SOCKET_CONNECT_TIMEOUT", "5.0"))
    REDIS_RETRY_ON_TIMEOUT: bool = os.getenv("REDIS_RETRY_ON_TIMEOUT", "true").lower() == "true"
    
    @property
    def REDIS_URL(self) -> str:
        """动态生成Redis连接URL"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    @property
    def CELERY_BROKER_URL(self) -> str:
        """Celery代理URL"""
        return self.REDIS_URL

    # 文件存储配置
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "uploads")
    MAX_FILE_SIZE: str = os.getenv("MAX_FILE_SIZE", "100MB")

    # 缓存配置
    CACHE_DEFAULT_TTL: int = int(os.getenv("CACHE_DEFAULT_TTL", "3600"))
    CACHE_KEY_PREFIX: str = os.getenv("CACHE_KEY_PREFIX", "video_system:")

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"  # 允许额外的字段
        extra = "allow"


# 创建全局配置实例
settings = Settings()


# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    dirs_to_create = [
        Path(settings.UPLOAD_DIR),
        Path("logs"),
        Path("temp"),
        Path("static"),
    ]

    for directory in dirs_to_create:
        directory.mkdir(exist_ok=True, parents=True)


# 应用启动时创建目录
ensure_directories()
