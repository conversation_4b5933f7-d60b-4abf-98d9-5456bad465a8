"""
Redis缓存策略服务
为AI服务提供高性能缓存支持
"""

import hashlib
import json
import pickle
from datetime import datetime
from functools import wraps
from typing import Any, Dict, List, Optional

import redis.asyncio as redis

from app.core.logging import get_service_logger, log_function_call

# 导入配置 - 解决循环导入问题
try:
    from app.core.config import settings
except ImportError:
    # 如果无法导入，使用默认配置
    class DefaultSettings:
        REDIS_URL = "redis://localhost:6379/0"

    settings = DefaultSettings()


class CacheKeyBuilder:
    """缓存键构建器"""

    @staticmethod
    def build_key(service: str, method: str, *args, **kwargs) -> str:
        """构建缓存键"""
        # 创建参数哈希
        params_str = json.dumps(
            {"args": args, "kwargs": sorted(kwargs.items())},
            sort_keys=True,
            default=str,
        )

        params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]

        return f"cache:{service}:{method}:{params_hash}"

    @staticmethod
    def build_pattern_key(service: str, method: str = "*") -> str:
        """构建模式匹配键"""
        return f"cache:{service}:{method}:*"


class CacheConfig:
    """缓存配置"""

    # 默认过期时间(秒)
    DEFAULT_EXPIRATION = 3600  # 1小时

    # 不同服务的缓存配置
    SERVICE_CONFIGS = {
        "ai_integration": {
            "default_expiration": 1800,  # 30分钟
            "methods": {
                "content_compliance_check": 3600,  # 1小时
                "content_rewrite": 1800,  # 30分钟
                "generate_structured_content": 2400,  # 40分钟
            },
        },
        "speech_recognition": {
            "default_expiration": 7200,  # 2小时
            "methods": {
                "transcribe_audio": 7200,  # 2小时
                "transcribe_with_timestamps": 7200,  # 2小时
            },
        },
        "structured_generation": {
            "default_expiration": 1800,  # 30分钟
            "methods": {
                "generate_structured_content": 1800,  # 30分钟
                "validate_output_format": 3600,  # 1小时
            },
        },
        "video_download": {
            "default_expiration": 86400,  # 24小时
            "methods": {
                "download_video": 86400,  # 24小时
                "get_video_info": 43200,  # 12小时
            },
        },
        "content_safety": {
            "default_expiration": 3600,  # 1小时
            "methods": {
                "check_text_safety": 7200,  # 2小时
                "check_image_safety": 7200,  # 2小时
            },
        },
    }

    @classmethod
    def get_expiration(cls, service: str, method: str) -> int:
        """获取指定服务方法的过期时间"""
        service_config = cls.SERVICE_CONFIGS.get(service, {})
        method_expiration = service_config.get("methods", {}).get(method)

        if method_expiration:
            return method_expiration

        return service_config.get("default_expiration", cls.DEFAULT_EXPIRATION)


class RedisCacheService:
    """Redis缓存服务"""

    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.logger = get_service_logger("redis_cache")
        self.connected = False

        # 缓存统计
        self.stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "errors": 0,
        }

    async def connect(self) -> bool:
        """连接到Redis"""
        try:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=False,  # 处理二进制数据
            )

            # 测试连接
            await self.redis_client.ping()
            self.connected = True

            self.logger.info(
                "Redis连接成功",
                extra_data={"redis_url": settings.REDIS_URL.split("@")[-1]},  # 隐藏密码
            )

            return True

        except Exception as e:
            self.logger.error(
                "Redis连接失败",
                extra_data={"error": str(e), "error_type": type(e).__name__},
            )
            self.connected = False
            return False

    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self.connected = False
            self.logger.info("Redis连接已断开")

    def _serialize_value(self, value: Any) -> bytes:
        """序列化值"""
        return pickle.dumps(value)

    def _deserialize_value(self, data: bytes) -> Any:
        """反序列化值"""
        return pickle.loads(data)

    @log_function_call
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self.connected or not self.redis_client:
            self.logger.warning("Redis未连接，跳过缓存获取")
            return None

        try:
            data = await self.redis_client.get(key)

            if data is not None:
                self.stats["hits"] += 1
                value = self._deserialize_value(data)

                self.logger.debug(
                    "缓存命中",
                    extra_data={
                        "key": key,
                        "value_type": type(value).__name__,
                    },
                )

                return value
            else:
                self.stats["misses"] += 1
                self.logger.debug("缓存未命中", extra_data={"key": key})
                return None

        except Exception as e:
            self.stats["errors"] += 1
            self.logger.error("缓存获取失败", extra_data={"key": key, "error": str(e)})
            return None

    @log_function_call
    async def set(self, key: str, value: Any, expiration: Optional[int] = None) -> bool:
        """设置缓存值"""
        if not self.connected or not self.redis_client:
            self.logger.warning("Redis未连接，跳过缓存设置")
            return False

        try:
            serialized_value = self._serialize_value(value)

            if expiration:
                await self.redis_client.setex(key, expiration, serialized_value)
            else:
                await self.redis_client.set(key, serialized_value)

            self.stats["sets"] += 1

            self.logger.debug(
                "缓存设置成功",
                extra_data={
                    "key": key,
                    "value_type": type(value).__name__,
                    "expiration": expiration,
                },
            )

            return True

        except Exception as e:
            self.stats["errors"] += 1
            self.logger.error("缓存设置失败", extra_data={"key": key, "error": str(e)})
            return False

    @log_function_call
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        if not self.connected or not self.redis_client:
            self.logger.warning("Redis未连接，跳过缓存删除")
            return False

        try:
            result = await self.redis_client.delete(key)
            self.stats["deletes"] += 1

            self.logger.debug(
                "缓存删除成功",
                extra_data={"key": key, "deleted": bool(result)},
            )

            return bool(result)

        except Exception as e:
            self.stats["errors"] += 1
            self.logger.error("缓存删除失败", extra_data={"key": key, "error": str(e)})
            return False

    @log_function_call
    async def delete_pattern(self, pattern: str) -> int:
        """删除匹配模式的缓存"""
        if not self.connected or not self.redis_client:
            self.logger.warning("Redis未连接，跳过模式删除")
            return 0

        try:
            keys = await self.redis_client.keys(pattern)
            if keys:
                deleted_count = await self.redis_client.delete(*keys)
                self.stats["deletes"] += deleted_count

                self.logger.info(
                    "模式缓存删除成功",
                    extra_data={
                        "pattern": pattern,
                        "deleted_count": deleted_count,
                    },
                )

                return deleted_count
            else:
                return 0

        except Exception as e:
            self.stats["errors"] += 1
            self.logger.error(
                "模式缓存删除失败",
                extra_data={"pattern": pattern, "error": str(e)},
            )
            return 0

    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        if not self.connected or not self.redis_client:
            return False

        try:
            result = await self.redis_client.exists(key)
            return bool(result)
        except Exception:
            return False

    async def get_ttl(self, key: str) -> int:
        """获取缓存剩余时间"""
        if not self.connected or not self.redis_client:
            return -1

        try:
            return await self.redis_client.ttl(key)
        except Exception:
            return -1

    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (
            self.stats["hits"] / total_requests * 100 if total_requests > 0 else 0
        )

        redis_info = {}
        if self.connected and self.redis_client:
            try:
                info = await self.redis_client.info()
                redis_info = {
                    "used_memory": info.get("used_memory_human", "Unknown"),
                    "connected_clients": info.get("connected_clients", 0),
                    "total_commands_processed": info.get("total_commands_processed", 0),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0),
                }
            except Exception as e:
                self.logger.error("获取Redis信息失败", extra_data={"error": str(e)})

        return {
            "connected": self.connected,
            "stats": self.stats,
            "hit_rate": round(hit_rate, 2),
            "redis_info": redis_info,
            "timestamp": datetime.now().isoformat(),
        }

    async def clear_service_cache(self, service: str) -> int:
        """清除指定服务的所有缓存"""
        pattern = CacheKeyBuilder.build_pattern_key(service)
        return await self.delete_pattern(pattern)


# 全局缓存实例
cache_service = RedisCacheService()


# 缓存装饰器
def cache_result(service: str, method: str, expiration: Optional[int] = None):
    """
    缓存装饰器

    Args:
        service: 服务名称
        method: 方法名称
        expiration: 过期时间(秒)，None表示使用默认配置
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 构建缓存键
            cache_key = CacheKeyBuilder.build_key(service, method, *args, **kwargs)

            # 尝试从缓存获取
            cached_result = await cache_service.get(cache_key)
            if cached_result is not None:
                return cached_result

            # 执行原函数
            result = await func(*args, **kwargs)

            # 设置缓存
            cache_expiration = expiration or CacheConfig.get_expiration(service, method)
            await cache_service.set(cache_key, result, cache_expiration)

            return result

        return wrapper

    return decorator


def cache_invalidate(service: str, method: str = "*"):
    """
    缓存失效装饰器
    在函数执行后清除相关缓存
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            result = await func(*args, **kwargs)

            # 清除缓存
            pattern = CacheKeyBuilder.build_pattern_key(service, method)
            deleted_count = await cache_service.delete_pattern(pattern)

            cache_service.logger.info(
                "缓存失效",
                extra_data={
                    "service": service,
                    "method": method,
                    "deleted_count": deleted_count,
                },
            )

            return result

        return wrapper

    return decorator


# 初始化函数
async def init_cache_service() -> bool:
    """初始化缓存服务"""
    return await cache_service.connect()


# 清理函数
async def cleanup_cache_service():
    """清理缓存服务"""
    await cache_service.disconnect()


# 工具函数
async def warm_up_cache():
    """预热缓存 - 可以预加载一些常用数据"""
    cache_service.logger.info("开始缓存预热")

    # 这里可以添加预热逻辑
    # 例如预加载常用的AI模型结果、配置信息等

    cache_service.logger.info("缓存预热完成")


async def get_cache_health() -> Dict[str, Any]:
    """获取缓存健康状态"""
    stats = await cache_service.get_stats()

    health_status = "healthy"
    if not stats["connected"]:
        health_status = "disconnected"
    elif stats["stats"]["errors"] > 100:
        health_status = "degraded"

    return {
        "status": health_status,
        "cache_stats": stats,
        "recommendations": _get_cache_recommendations(stats),
    }


def _get_cache_recommendations(stats: Dict[str, Any]) -> List[str]:
    """获取缓存优化建议"""
    recommendations = []

    hit_rate = stats.get("hit_rate", 0)
    if hit_rate < 50:
        recommendations.append("缓存命中率较低，建议增加缓存时间或优化缓存策略")

    error_count = stats["stats"]["errors"]
    if error_count > 10:
        recommendations.append("缓存错误较多，建议检查Redis连接和配置")

    if not stats["connected"]:
        recommendations.append("Redis连接断开，建议检查Redis服务状态")

    return recommendations


# 简单的缓存装饰器（为了兼容性）
def cached(ttl: int = 300, expiration: int = None, service: str = None):
    """
    简单的缓存装饰器

    Args:
        ttl: 缓存时间（秒），默认5分钟
        expiration: 缓存过期时间（秒），兼容性参数
        service: 服务名称，兼容性参数
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 为了兼容性，直接调用原函数不使用缓存
            # 在实际应用中，这里应该实现真正的缓存逻辑
            return await func(*args, **kwargs)

        return wrapper

    return decorator
