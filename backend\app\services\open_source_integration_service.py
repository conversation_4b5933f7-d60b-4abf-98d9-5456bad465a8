#!/usr/bin/env python3
"""
核心开源项目集成服务
负责管理和协调所有开源项目的集成和使用
"""

import importlib
import logging
import subprocess
import sys
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional

import requests
from pydantic import BaseModel, Field


class PerformanceMetrics(BaseModel):
    """性能指标模型"""

    operation: str
    execution_time: float
    memory_usage: Optional[float] = None
    success: bool
    timestamp: datetime
    details: Optional[Dict[str, Any]] = None


class CacheEntry(BaseModel):
    """缓存条目模型"""

    value: Any
    timestamp: datetime
    ttl: int  # 存活时间（秒）

    def is_expired(self) -> bool:
        return datetime.now() > self.timestamp + timedelta(seconds=self.ttl)


class ProjectCache:
    """项目状态缓存管理器"""

    def __init__(self, default_ttl: int = 300):  # 5分钟默认TTL
        self.cache: Dict[str, CacheEntry] = {}
        self.default_ttl = default_ttl
        self.lock = threading.Lock()

        # 缓存统计
        self.hit_count = 0
        self.miss_count = 0
        self.total_requests = 0

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            self.total_requests += 1

            if key in self.cache:
                entry = self.cache[key]
                if not entry.is_expired():
                    self.hit_count += 1
                    return entry.value
                else:
                    del self.cache[key]

            self.miss_count += 1
            return None

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        with self.lock:
            self.cache[key] = CacheEntry(
                value=value,
                timestamp=datetime.now(),
                ttl=ttl or self.default_ttl,
            )

    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()

    def reset_stats(self) -> None:
        """重置统计信息"""
        with self.lock:
            self.hit_count = 0
            self.miss_count = 0
            self.total_requests = 0

    def get_hit_rate(self) -> float:
        """获取缓存命中率"""
        with self.lock:
            if self.total_requests == 0:
                return 0.0
            return (self.hit_count / self.total_requests) * 100

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            # 直接计算命中率，避免递归调用
            hit_rate = 0.0
            if self.total_requests > 0:
                hit_rate = (self.hit_count / self.total_requests) * 100

            # 计算过期条目数
            expired_count = 0
            try:
                expired_count = sum(
                    1 for entry in self.cache.values() if entry.is_expired()
                )
            except Exception:
                expired_count = 0  # 如果计算失败，设为0

            return {
                "total_requests": self.total_requests,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count,
                "hit_rate": hit_rate,
                "cache_size": len(self.cache),
                "expired_entries": expired_count,
            }


class ProjectType(str, Enum):
    """开源项目类型枚举"""

    VIDEO_DOWNLOAD = "video_download"
    SPEECH_RECOGNITION = "speech_recognition"
    LOCAL_AI = "local_ai"
    VIDEO_PROCESSING = "video_processing"
    CONTENT_SAFETY = "content_safety"
    UI_COMPONENTS = "ui_components"
    DESKTOP_APP = "desktop_app"
    TEXT_PROCESSING = "text_processing"
    IMAGE_PROCESSING = "image_processing"


class ProjectStatus(str, Enum):
    """项目状态枚举"""

    NOT_INSTALLED = "not_installed"
    INSTALLING = "installing"
    INSTALLED = "installed"
    AVAILABLE = "available"
    ERROR = "error"
    UPDATING = "updating"


class OpenSourceProject(BaseModel):
    """开源项目配置模型"""

    name: str = Field(..., description="项目名称")
    github_url: str = Field(..., description="GitHub仓库地址")
    project_type: ProjectType = Field(..., description="项目类型")
    package_names: List[str] = Field(default_factory=list, description="Python包名")
    version: Optional[str] = Field(None, description="版本要求")
    description: str = Field(..., description="项目描述")
    integration_method: str = Field(..., description="集成方式")
    dependencies: List[str] = Field(default_factory=list, description="依赖项")
    config_required: bool = Field(False, description="是否需要配置")
    status: ProjectStatus = Field(default=ProjectStatus.NOT_INSTALLED)
    last_updated: Optional[datetime] = None
    error_message: Optional[str] = None


class IntegrationResult(BaseModel):
    """集成结果模型"""

    success: bool
    project_name: str
    status: ProjectStatus
    message: str
    execution_time: float
    details: Optional[Dict[str, Any]] = None


class OpenSourceIntegrationService:
    """核心开源项目集成服务"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.projects: Dict[str, OpenSourceProject] = {}
        self.integration_history: List[IntegrationResult] = []
        self.performance_metrics: List[PerformanceMetrics] = []
        self.cache = ProjectCache(default_ttl=300)  # 5分钟缓存
        # 使用同步方式，避免线程池导致程序挂起
        # self.executor = ThreadPoolExecutor(max_workers=4)
        self._load_project_configs()

        # 不启动后台清理任务，改为手动清理
        # self._start_cache_cleanup()

    def _start_cache_cleanup(self):
        """启动后台缓存清理任务"""

        def cleanup_worker():
            while True:
                try:
                    time.sleep(60)  # 每分钟清理一次
                    self.cache.cleanup_expired()
                    self.logger.debug("缓存清理完成")
                except Exception as e:
                    self.logger.error(f"缓存清理失败: {e}")
                    # 继续运行，不退出线程

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        self.logger.info("缓存清理线程已启动")

    def _measure_performance(self, operation: str):
        """性能测量装饰器上下文管理器"""

        class PerformanceMeasurer:
            def __init__(self, service_instance, operation_name):
                self.service = service_instance
                self.operation = operation_name
                self.start_time = None

            def __enter__(self):
                self.start_time = time.time()
                return self

            def __exit__(self, exc_type, exc_val, exc_tb):
                execution_time = time.time() - self.start_time
                success = exc_type is None

                metric = PerformanceMetrics(
                    operation=self.operation,
                    execution_time=execution_time,
                    success=success,
                    timestamp=datetime.now(),
                    details={"error": str(exc_val) if exc_val else None},
                )

                self.service.performance_metrics.append(metric)

                # 保持最近1000条记录
                if len(self.service.performance_metrics) > 1000:
                    self.service.performance_metrics = self.service.performance_metrics[
                        -1000:
                    ]

        return PerformanceMeasurer(self, operation)

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if not self.performance_metrics:
            return {"message": "暂无性能数据"}

        # 计算各项统计
        total_operations = len(self.performance_metrics)
        success_count = sum(1 for m in self.performance_metrics if m.success)
        success_rate = (success_count / total_operations) * 100

        execution_times = [m.execution_time for m in self.performance_metrics]
        avg_time = sum(execution_times) / len(execution_times)
        max_time = max(execution_times)
        min_time = min(execution_times)

        # 按操作类型统计
        operation_stats = {}
        for metric in self.performance_metrics:
            op = metric.operation
            if op not in operation_stats:
                operation_stats[op] = {
                    "count": 0,
                    "success_count": 0,
                    "total_time": 0.0,
                    "avg_time": 0.0,
                }

            operation_stats[op]["count"] += 1
            operation_stats[op]["total_time"] += metric.execution_time
            if metric.success:
                operation_stats[op]["success_count"] += 1

        # 计算每个操作的平均时间
        for op_stats in operation_stats.values():
            op_stats["avg_time"] = op_stats["total_time"] / op_stats["count"]
            op_stats["success_rate"] = (
                op_stats["success_count"] / op_stats["count"] * 100
            )

        return {
            "summary": {
                "total_operations": total_operations,
                "success_rate": round(success_rate, 2),
                "avg_execution_time": round(avg_time, 3),
                "max_execution_time": round(max_time, 3),
                "min_execution_time": round(min_time, 3),
            },
            "by_operation": operation_stats,
            "cache_stats": {
                "total_entries": len(self.cache.cache),
                "cache_hit_rate": "需要实现命中率统计",
            },
        }

    def _load_project_configs(self):
        """加载开源项目配置"""
        # 视频下载类项目
        self.projects["yt-dlp"] = OpenSourceProject(
            name="yt-dlp",
            github_url="https://github.com/yt-dlp/yt-dlp",
            project_type=ProjectType.VIDEO_DOWNLOAD,
            package_names=["yt_dlp"],
            version=">=2025.6.30",
            description="通用视频下载工具，支持1000+网站",
            integration_method="Python包直接调用",
            dependencies=["ffmpeg"],
            config_required=False,
        )

        self.projects["gallery-dl"] = OpenSourceProject(
            name="gallery-dl",
            github_url="https://github.com/mikf/gallery-dl",
            project_type=ProjectType.VIDEO_DOWNLOAD,
            package_names=["gallery_dl"],
            version=">=1.26.8",
            description="图像和视频画廊下载器",
            integration_method="Python包直接调用",
            dependencies=[],
            config_required=True,
        )

        # 语音识别类项目
        self.projects["sensevoice"] = OpenSourceProject(
            name="SenseVoice",
            github_url="https://github.com/FunAudioLLM/SenseVoice",
            project_type=ProjectType.SPEECH_RECOGNITION,
            package_names=["funasr", "modelscope"],
            description="阿里巴巴多语言语音理解模型",
            integration_method="ModelScope API集成",
            dependencies=["torch", "transformers"],
            config_required=True,
        )

        self.projects["funasr"] = OpenSourceProject(
            name="FunASR",
            github_url="https://github.com/alibaba-damo-academy/FunASR",
            project_type=ProjectType.SPEECH_RECOGNITION,
            package_names=["funasr"],
            description="全功能语音识别工具包",
            integration_method="API服务调用",
            dependencies=["torch", "onnxruntime"],
            config_required=True,
        )

        # 本地AI项目
        self.projects["ollama"] = OpenSourceProject(
            name="Ollama",
            github_url="https://github.com/ollama/ollama",
            project_type=ProjectType.LOCAL_AI,
            package_names=["ollama"],
            description="本地大语言模型运行环境",
            integration_method="HTTP API调用",
            dependencies=[],
            config_required=True,
            status=ProjectStatus.INSTALLED,  # 已安装
        )

        self.projects["localai"] = OpenSourceProject(
            name="LocalAI",
            github_url="https://github.com/mudler/LocalAI",
            project_type=ProjectType.LOCAL_AI,
            package_names=[],
            description="本地AI模型推理服务",
            integration_method="Docker容器部署",
            dependencies=["docker"],
            config_required=True,
        )

        # 视频处理项目
        self.projects["moviepy"] = OpenSourceProject(
            name="MoviePy",
            github_url="https://github.com/Zulko/moviepy",
            project_type=ProjectType.VIDEO_PROCESSING,
            package_names=["moviepy"],
            version=">=2.2.1",
            description="Python视频编辑库",
            integration_method="Python包直接调用",
            dependencies=["ffmpeg"],
            config_required=False,
            status=ProjectStatus.INSTALLED,
        )

        self.projects["opencv"] = OpenSourceProject(
            name="OpenCV",
            github_url="https://github.com/opencv/opencv-python",
            project_type=ProjectType.VIDEO_PROCESSING,
            package_names=["cv2"],
            version=">=********",
            description="计算机视觉库",
            integration_method="Python包直接调用",
            dependencies=[],
            config_required=False,
            status=ProjectStatus.INSTALLED,
        )

        # 内容安全项目
        self.projects["detoxify"] = OpenSourceProject(
            name="Detoxify",
            github_url="https://github.com/unitaryai/detoxify",
            project_type=ProjectType.CONTENT_SAFETY,
            package_names=["detoxify"],
            version=">=0.5.2",
            description="文本毒性检测模型",
            integration_method="Python包直接调用",
            dependencies=["transformers", "torch"],
            config_required=False,
            status=ProjectStatus.INSTALLED,
        )

        self.projects["nudenet"] = OpenSourceProject(
            name="NudeNet",
            github_url="https://github.com/notAI-tech/NudeNet",
            project_type=ProjectType.CONTENT_SAFETY,
            package_names=["nudenet"],
            version=">=3.4.2",
            description="图像内容安全检测",
            integration_method="Python包直接调用",
            dependencies=["tensorflow", "opencv-python"],
            config_required=False,
            status=ProjectStatus.INSTALLED,
        )

        # 文本处理项目
        self.projects["jieba"] = OpenSourceProject(
            name="Jieba",
            github_url="https://github.com/fxsjy/jieba",
            project_type=ProjectType.TEXT_PROCESSING,
            package_names=["jieba"],
            version=">=0.42.1",
            description="中文分词库",
            integration_method="Python包直接调用",
            dependencies=[],
            config_required=False,
            status=ProjectStatus.INSTALLED,
        )

        self.projects["spacy"] = OpenSourceProject(
            name="spaCy",
            github_url="https://github.com/explosion/spaCy",
            project_type=ProjectType.TEXT_PROCESSING,
            package_names=["spacy"],
            version=">=3.8.7",
            description="自然语言处理库",
            integration_method="Python包直接调用",
            dependencies=[],
            config_required=True,
            status=ProjectStatus.INSTALLED,
        )

    def check_project_status(self, project_name: str) -> ProjectStatus:
        """检查项目安装状态"""
        if project_name not in self.projects:
            raise ValueError(f"未知项目: {project_name}")

        project = self.projects[project_name]

        # 先从缓存中获取状态
        cached_status = self.cache.get(f"project_status_{project_name}")
        if cached_status:
            project.status = cached_status
            self.logger.info(f"🔄 使用缓存的状态: {project_name} - {project.status}")
            return project.status

        try:
            # 检查Python包是否已安装
            for package_name in project.package_names:
                try:
                    importlib.import_module(package_name)
                    self.logger.info(f"✅ {package_name} 已安装")
                except ImportError:
                    self.logger.warning(f"⚠️ {package_name} 未安装")
                    project.status = ProjectStatus.NOT_INSTALLED
                    self.cache.set(f"project_status_{project_name}", project.status)
                    return project.status

            # 如果是外部服务，检查服务状态
            if project.integration_method == "HTTP API调用":
                service_status = self._check_service_status(project_name)
                project.status = service_status
            else:
                project.status = ProjectStatus.INSTALLED

            # 更新缓存
            self.cache.set(f"project_status_{project_name}", project.status)
            return project.status

        except Exception as e:
            self.logger.error(f"检查项目状态失败 {project_name}: {e}")
            project.status = ProjectStatus.ERROR
            project.error_message = str(e)
            self.cache.set(f"project_status_{project_name}", project.status)
            return project.status

    def _check_service_status(self, project_name: str) -> ProjectStatus:
        """检查外部服务状态"""
        if project_name == "ollama":
            try:
                url = "http://127.0.0.1:11434/api/tags"
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    return ProjectStatus.AVAILABLE
                else:
                    return ProjectStatus.ERROR
            except Exception:
                return ProjectStatus.NOT_INSTALLED

        return ProjectStatus.UNKNOWN

    def check_external_service_status(self, service_name: str) -> ProjectStatus:
        """检查外部服务状态的公共方法"""
        return self._check_service_status(service_name)

    def install_project(self, project_name: str) -> IntegrationResult:
        """安装指定的开源项目"""
        start_time = datetime.now()

        if project_name not in self.projects:
            return IntegrationResult(
                success=False,
                project_name=project_name,
                status=ProjectStatus.ERROR,
                message=f"未知项目: {project_name}",
                execution_time=0.0,
            )

        project = self.projects[project_name]
        project.status = ProjectStatus.INSTALLING

        try:
            # 安装Python包
            for package_name in project.package_names:
                if project.version:
                    package_spec = f"{package_name}{project.version}"
                else:
                    package_spec = package_name

                self.logger.info(f"安装包: {package_spec}")
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", package_spec],
                    capture_output=True,
                    text=True,
                )

                if result.returncode != 0:
                    raise Exception(f"安装失败: {result.stderr}")

            # 安装依赖
            for dep in project.dependencies:
                self.logger.info(f"安装依赖: {dep}")
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", dep],
                    capture_output=True,
                    text=True,
                )

                if result.returncode != 0:
                    self.logger.warning(f"依赖安装可能失败: {dep}")

            project.status = ProjectStatus.INSTALLED
            project.last_updated = datetime.now()
            project.error_message = None

            execution_time = (datetime.now() - start_time).total_seconds()

            result = IntegrationResult(
                success=True,
                project_name=project_name,
                status=project.status,
                message=f"项目 {project_name} 安装成功",
                execution_time=execution_time,
                details={
                    "packages_installed": project.package_names,
                    "dependencies": project.dependencies,
                },
            )

            self.integration_history.append(result)
            return result

        except Exception as e:
            self.logger.error(f"安装项目失败 {project_name}: {e}")
            project.status = ProjectStatus.ERROR
            project.error_message = str(e)

            execution_time = (datetime.now() - start_time).total_seconds()

            result = IntegrationResult(
                success=False,
                project_name=project_name,
                status=project.status,
                message=f"项目 {project_name} 安装失败: {str(e)}",
                execution_time=execution_time,
            )

            self.integration_history.append(result)
            return result

    def update_project(self, project_name: str) -> IntegrationResult:
        """更新指定的开源项目"""
        start_time = datetime.now()

        if project_name not in self.projects:
            return IntegrationResult(
                success=False,
                project_name=project_name,
                status=ProjectStatus.ERROR,
                message=f"未知项目: {project_name}",
                execution_time=0.0,
            )

        project = self.projects[project_name]
        project.status = ProjectStatus.UPDATING

        try:
            # 更新Python包
            for package_name in project.package_names:
                self.logger.info(f"更新包: {package_name}")
                result = subprocess.run(
                    [
                        sys.executable,
                        "-m",
                        "pip",
                        "install",
                        "--upgrade",
                        package_name,
                    ],
                    capture_output=True,
                    text=True,
                )

                if result.returncode != 0:
                    raise Exception(f"更新失败: {result.stderr}")

            project.status = ProjectStatus.INSTALLED
            project.last_updated = datetime.now()
            project.error_message = None

            execution_time = (datetime.now() - start_time).total_seconds()

            result = IntegrationResult(
                success=True,
                project_name=project_name,
                status=project.status,
                message=f"项目 {project_name} 更新成功",
                execution_time=execution_time,
            )

            self.integration_history.append(result)
            return result

        except Exception as e:
            self.logger.error(f"更新项目失败 {project_name}: {e}")
            project.status = ProjectStatus.ERROR
            project.error_message = str(e)

            execution_time = (datetime.now() - start_time).total_seconds()

            result = IntegrationResult(
                success=False,
                project_name=project_name,
                status=project.status,
                message=f"项目 {project_name} 更新失败: {str(e)}",
                execution_time=execution_time,
            )

            self.integration_history.append(result)
            return result

    def get_projects_by_type(
        self, project_type: ProjectType
    ) -> List[OpenSourceProject]:
        """根据类型获取项目列表"""
        return [
            project
            for project in self.projects.values()
            if project.project_type == project_type
        ]

    def get_all_projects(self):
        """获取所有项目"""
        return self.projects

    def get_project_status_summary(self) -> Dict[str, Any]:
        """获取项目状态摘要"""
        status_counts = {}
        for status in ProjectStatus:
            status_counts[status.value] = 0

        for project in self.projects.values():
            status_counts[project.status.value] += 1

        return {
            "total_projects": len(self.projects),
            "status_distribution": status_counts,
            "by_type": {
                project_type.value: len(self.get_projects_by_type(project_type))
                for project_type in ProjectType
            },
            "last_update": max(
                [
                    project.last_updated
                    for project in self.projects.values()
                    if project.last_updated
                ],
                default=None,
            ),
        }

    def bulk_install(self, project_names: List[str]) -> List[IntegrationResult]:
        """批量安装项目"""
        results = []
        for project_name in project_names:
            result = self.install_project(project_name)
            results.append(result)
        return results

    def bulk_update(
        self, project_names: Optional[List[str]] = None
    ) -> List[IntegrationResult]:
        """批量更新项目"""
        if project_names is None:
            project_names = list(self.projects.keys())

        results = []
        for project_name in project_names:
            if self.projects[project_name].status == ProjectStatus.INSTALLED:
                result = self.update_project(project_name)
                results.append(result)
        return results

    def check_all_statuses(self) -> Dict[str, ProjectStatus]:
        """检查所有项目状态"""
        statuses = {}
        for project_name in self.projects.keys():
            statuses[project_name] = self.check_project_status(project_name)
        return statuses

    def get_integration_history(self, limit: int = 50) -> List[IntegrationResult]:
        """获取集成历史"""
        return self.integration_history[-limit:]

    def generate_integration_report(self) -> Dict[str, Any]:
        """生成集成报告"""
        status_summary = self.get_project_status_summary()
        all_statuses = self.check_all_statuses()

        installed_projects = [
            name
            for name, status in all_statuses.items()
            if status in [ProjectStatus.INSTALLED, ProjectStatus.AVAILABLE]
        ]

        failed_projects = [
            name
            for name, status in all_statuses.items()
            if status == ProjectStatus.ERROR
        ]

        return {
            "report_generated": datetime.now(),
            "status_summary": status_summary,
            "installed_projects": installed_projects,
            "failed_projects": failed_projects,
            "installation_success_rate": len(installed_projects)
            / len(self.projects)
            * 100,
            "recent_history": self.get_integration_history(10),
        }

    def check_all_projects_parallel(self) -> Dict[str, ProjectStatus]:
        """检查所有项目状态（同步版本，避免线程挂起）"""
        with self._measure_performance("check_all_projects_parallel"):
            results = {}

            # 改为同步方式，避免线程池导致程序挂起
            for name in self.projects.keys():
                try:
                    status = self.check_project_status(name)
                    results[name] = status
                except Exception as e:
                    self.logger.error(f"检查项目失败 {name}: {e}")
                    results[name] = ProjectStatus.ERROR

            return results

    def bulk_install_parallel(
        self, project_names: List[str]
    ) -> List[IntegrationResult]:
        """并行批量安装项目"""
        with self._measure_performance("bulk_install_parallel"):

            def install_single_project(name):
                try:
                    return self.install_project(name)
                except Exception as e:
                    return IntegrationResult(
                        success=False,
                        project_name=name,
                        status=ProjectStatus.ERROR,
                        message=f"并行安装失败: {str(e)}",
                        execution_time=0.0,
                    )

            results = []
            with ThreadPoolExecutor(max_workers=min(len(project_names), 4)) as executor:
                future_to_name = {
                    executor.submit(install_single_project, name): name
                    for name in project_names
                }

                for future in future_to_name:
                    try:
                        result = future.result(timeout=300)  # 5分钟超时
                        results.append(result)
                    except Exception as e:
                        name = future_to_name[future]
                        self.logger.error(f"并行安装超时 {name}: {e}")
                        results.append(
                            IntegrationResult(
                                success=False,
                                project_name=name,
                                status=ProjectStatus.ERROR,
                                message=f"安装超时: {str(e)}",
                                execution_time=300.0,
                            )
                        )

            return results

    def get_system_health_report(self) -> Dict[str, Any]:
        """获取系统健康状况报告"""
        with self._measure_performance("system_health_report"):
            # 检查所有项目状态
            project_statuses = self.check_all_projects_parallel()

            # 统计各种状态
            status_counts = {}
            for status in ProjectStatus:
                status_counts[status.value] = 0

            for status in project_statuses.values():
                status_counts[status.value] += 1

            # 计算健康分数
            total_projects = len(project_statuses)
            healthy_projects = status_counts.get("installed", 0) + status_counts.get(
                "available", 0
            )
            health_score = (
                (healthy_projects / total_projects * 100) if total_projects > 0 else 0
            )

            # 获取性能统计
            perf_stats = self.get_performance_stats()

            # 检查关键服务
            critical_services = {"ollama": self.check_external_service_status("ollama")}

            return {
                "timestamp": datetime.now().isoformat(),
                "health_score": round(health_score, 2),
                "project_status_summary": {
                    "total": total_projects,
                    "healthy": healthy_projects,
                    "status_breakdown": status_counts,
                },
                "critical_services": {
                    name: status.value for name, status in critical_services.items()
                },
                "performance_overview": {
                    "total_operations": perf_stats.get("summary", {}).get(
                        "total_operations", 0
                    ),
                    "success_rate": perf_stats.get("summary", {}).get(
                        "success_rate", 0
                    ),
                    "avg_response_time": perf_stats.get("summary", {}).get(
                        "avg_execution_time", 0
                    ),
                },
                "cache_efficiency": {
                    "total_entries": len(self.cache.cache),
                    "cache_utilization": (
                        "良好" if len(self.cache.cache) > 0 else "未使用"
                    ),
                },
                "recommendations": self._generate_health_recommendations(
                    status_counts, health_score, critical_services
                ),
            }

    def _generate_health_recommendations(
        self,
        status_counts: Dict[str, int],
        health_score: float,
        critical_services: Dict[str, ProjectStatus],
    ) -> List[str]:
        """生成健康状况改善建议"""
        recommendations = []

        if health_score < 70:
            recommendations.append("🚨 系统健康分数较低，建议立即检查和修复项目状态")

        if status_counts.get("not_installed", 0) > 0:
            recommendations.append(
                f"📦 有 {status_counts['not_installed']} 个项目未安装，建议运行批量安装"
            )

        if status_counts.get("error", 0) > 0:
            recommendations.append(
                f"❌ 有 {status_counts['error']} 个项目处于错误状态，需要人工检查"
            )

        # 检查关键服务
        for service_name, status in critical_services.items():
            if status != ProjectStatus.AVAILABLE:
                recommendations.append(
                    f"🔧 关键服务 {service_name} 不可用，建议检查服务状态"
                )

        if health_score >= 90:
            recommendations.append("✅ 系统状态良好，继续保持")

        return recommendations

    def get_health_score(self) -> float:
        """获取系统健康分数"""
        try:
            health_report = self.get_system_health_report()
            return health_report.get("health_score", 0.0)
        except Exception as e:
            self.logger.error(f"获取健康分数失败: {e}")
            return 0.0

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标摘要"""
        return self.get_performance_stats()

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        cache_stats = self.cache.get_stats()

        # 计算缓存效率等级
        hit_rate = cache_stats["hit_rate"]
        if hit_rate >= 80:
            efficiency = "优秀"
        elif hit_rate >= 60:
            efficiency = "良好"
        elif hit_rate >= 40:
            efficiency = "一般"
        elif hit_rate > 0:
            efficiency = "需要优化"
        else:
            efficiency = "未使用"

        active_entries = cache_stats["cache_size"] - cache_stats["expired_entries"]

        return {
            "total_entries": cache_stats["cache_size"],
            "expired_entries": cache_stats["expired_entries"],
            "active_entries": active_entries,
            "cache_efficiency": efficiency,
            "hit_rate": f"{hit_rate:.1f}%",
            "total_requests": cache_stats["total_requests"],
            "hit_count": cache_stats["hit_count"],
            "miss_count": cache_stats["miss_count"],
        }

    def clear_cache(self) -> Dict[str, Any]:
        """手动清理缓存并返回清理结果"""
        try:
            # 获取清理前的统计
            old_stats = self.cache.get_stats()

            # 清理过期缓存
            self.cache.cleanup_expired()

            # 获取清理后的统计
            new_stats = self.cache.get_stats()

            cleaned_count = old_stats["expired_entries"]

            return {
                "success": True,
                "cleaned_entries": cleaned_count,
                "remaining_entries": new_stats["cache_size"],
                "message": f"成功清理 {cleaned_count} 个过期缓存条目",
            }
        except Exception as e:
            self.logger.error(f"手动缓存清理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"缓存清理失败: {e}",
            }

    def reset_cache_stats(self) -> Dict[str, Any]:
        """重置缓存统计信息"""
        try:
            self.cache.reset_stats()
            return {"success": True, "message": "缓存统计信息已重置"}
        except Exception as e:
            self.logger.error(f"重置缓存统计失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"重置缓存统计失败: {e}",
            }


# 全局实例
open_source_integration_service = OpenSourceIntegrationService()
