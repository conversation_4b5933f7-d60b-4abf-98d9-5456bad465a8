{"timestamp": "2025-07-23T16:10:28.585128", "project_root": "d:\\二创\\二创短视频分发\\backend", "requirements_file": "d:\\二创\\二创短视频分发\\backend\\requirements_secure.txt", "audit_results": {"pip_audit": {"status": "vulnerabilities_found", "vulnerabilities": []}, "safety_check": {"status": "vulnerabilities_found", "vulnerabilities": []}, "known_vulnerabilities": {"status": "error", "error": "[Errno 2] No such file or directory: 'd:\\\\二创\\\\二创短视频分发\\\\backend\\\\requirements_secure.txt'"}, "high_risk_packages": {"status": "error", "error": "[Errno 2] No such file or directory: 'd:\\\\二创\\\\二创短视频分发\\\\backend\\\\requirements_secure.txt'"}, "outdated_packages": {"status": "outdated_found", "packages": [{"name": "absl-py", "version": "2.3.0", "latest_version": "2.3.1", "latest_filetype": "wheel"}, {"name": "aiofiles", "version": "23.2.1", "latest_version": "24.1.0", "latest_filetype": "wheel"}, {"name": "aiosqlite", "version": "0.19.0", "latest_version": "0.21.0", "latest_filetype": "wheel"}, {"name": "airportsdata", "version": "20250622", "latest_version": "20250706", "latest_filetype": "wheel"}, {"name": "alembic", "version": "1.16.2", "latest_version": "1.16.4", "latest_filetype": "wheel"}, {"name": "anthropic", "version": "0.55.0", "latest_version": "0.58.2", "latest_filetype": "wheel"}, {"name": "antlr4-python3-runtime", "version": "4.9.3", "latest_version": "4.13.2", "latest_filetype": "wheel"}, {"name": "anyio", "version": "3.7.1", "latest_version": "4.9.0", "latest_filetype": "wheel"}, {"name": "apispec", "version": "6.8.0", "latest_version": "6.8.2", "latest_filetype": "wheel"}, {"name": "apispec-webframeworks", "version": "1.0.0", "latest_version": "1.2.0", "latest_filetype": "wheel"}, {"name": "asyncpg", "version": "0.29.0", "latest_version": "0.30.0", "latest_filetype": "wheel"}, {"name": "bce-python-sdk", "version": "0.9.35", "latest_version": "0.9.41", "latest_filetype": "wheel"}, {"name": "bcrypt", "version": "4.1.2", "latest_version": "4.3.0", "latest_filetype": "wheel"}, {"name": "celery", "version": "5.3.4", "latest_version": "5.5.3", "latest_filetype": "wheel"}, {"name": "certifi", "version": "2025.6.15", "latest_version": "2025.7.14", "latest_filetype": "wheel"}, {"name": "click-plugins", "version": "1.1.1", "latest_version": "1.1.1.2", "latest_filetype": "wheel"}, {"name": "cryptography", "version": "41.0.7", "latest_version": "45.0.5", "latest_filetype": "wheel"}, {"name": "cyclonedx-python-lib", "version": "9.1.0", "latest_version": "11.0.0", "latest_filetype": "wheel"}, {"name": "fastapi-cloud-cli", "version": "0.1.2", "latest_version": "0.1.4", "latest_filetype": "wheel"}, {"name": "filelock", "version": "3.16.1", "latest_version": "3.18.0", "latest_filetype": "wheel"}, {"name": "Flask", "version": "2.3.3", "latest_version": "3.1.1", "latest_filetype": "wheel"}, {"name": "Flask-Cors", "version": "4.0.1", "latest_version": "6.0.1", "latest_filetype": "wheel"}, {"name": "Flask-JWT-Extended", "version": "4.6.0", "latest_version": "4.7.1", "latest_filetype": "wheel"}, {"name": "Flask-SQLAlchemy", "version": "3.0.5", "latest_version": "3.1.1", "latest_filetype": "wheel"}, {"name": "fonttools", "version": "4.58.4", "latest_version": "4.59.0", "latest_filetype": "wheel"}, {"name": "fsspec", "version": "2025.5.1", "latest_version": "2025.7.0", "latest_filetype": "wheel"}, {"name": "gallery_dl", "version": "1.29.7", "latest_version": "1.30.0", "latest_filetype": "wheel"}, {"name": "gunicorn", "version": "21.2.0", "latest_version": "23.0.0", "latest_filetype": "wheel"}, {"name": "huggingface-hub", "version": "0.33.2", "latest_version": "0.33.4", "latest_filetype": "wheel"}, {"name": "hvac", "version": "1.2.1", "latest_version": "2.3.0", "latest_filetype": "wheel"}, {"name": "jmespath", "version": "0.10.0", "latest_version": "1.0.1", "latest_filetype": "wheel"}, {"name": "joblib", "version": "1.4.2", "latest_version": "1.5.1", "latest_filetype": "wheel"}, {"name": "jsonschema", "version": "4.24.0", "latest_version": "4.25.0", "latest_filetype": "wheel"}, {"name": "marshmallow", "version": "3.20.1", "latest_version": "4.0.0", "latest_filetype": "wheel"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "3.8.4", "latest_version": "3.10.3", "latest_filetype": "wheel"}, {"name": "modelscope", "version": "1.27.1", "latest_version": "1.28.0", "latest_filetype": "wheel"}, {"name": "multidict", "version": "6.6.0", "latest_version": "6.6.3", "latest_filetype": "wheel"}, {"name": "mypy", "version": "1.16.1", "latest_version": "1.17.0", "latest_filetype": "wheel"}, {"name": "onnxruntime", "version": "1.22.0", "latest_version": "1.22.1", "latest_filetype": "wheel"}, {"name": "openai", "version": "1.93.0", "latest_version": "1.97.1", "latest_filetype": "wheel"}, {"name": "opencv-python", "version": "*********", "latest_version": "*********", "latest_filetype": "wheel"}, {"name": "opencv-python-headless", "version": "*********", "latest_version": "*********", "latest_filetype": "wheel"}, {"name": "<PERSON><PERSON><PERSON>", "version": "3.9.10", "latest_version": "3.11.0", "latest_filetype": "wheel"}, {"name": "outlines", "version": "1.0.4", "latest_version": "1.1.1", "latest_filetype": "wheel"}, {"name": "outlines_core", "version": "0.1.26", "latest_version": "0.2.11", "latest_filetype": "wheel"}, {"name": "pandas", "version": "2.2.2", "latest_version": "2.3.1", "latest_filetype": "wheel"}, {"name": "pip", "version": "23.2.1", "latest_version": "25.1.1", "latest_filetype": "wheel"}, {"name": "prometheus-client", "version": "0.19.0", "latest_version": "0.22.1", "latest_filetype": "wheel"}, {"name": "protobuf", "version": "5.29.5", "latest_version": "6.31.1", "latest_filetype": "wheel"}, {"name": "psutil", "version": "6.1.1", "latest_version": "7.0.0", "latest_filetype": "wheel"}, {"name": "psycopg2-binary", "version": "2.9.9", "latest_version": "2.9.10", "latest_filetype": "wheel"}, {"name": "pydantic", "version": "2.9.2", "latest_version": "2.11.7", "latest_filetype": "wheel"}, {"name": "pydantic_core", "version": "2.14.1", "latest_version": "2.35.2", "latest_filetype": "wheel"}, {"name": "PyMySQL", "version": "1.1.0", "latest_version": "1.1.1", "latest_filetype": "wheel"}, {"name": "pytest", "version": "8.3.2", "latest_version": "8.4.1", "latest_filetype": "wheel"}, {"name": "pytest-asyncio", "version": "1.0.0", "latest_version": "1.1.0", "latest_filetype": "wheel"}, {"name": "qrcode", "version": "7.3.1", "latest_version": "8.2", "latest_filetype": "wheel"}, {"name": "rignore", "version": "0.5.1", "latest_version": "0.6.4", "latest_filetype": "wheel"}, {"name": "scikit-learn", "version": "1.7.0", "latest_version": "1.7.1", "latest_filetype": "wheel"}, {"name": "scipy", "version": "1.13.1", "latest_version": "1.16.0", "latest_filetype": "wheel"}, {"name": "selenium", "version": "4.34.0", "latest_version": "4.34.2", "latest_filetype": "wheel"}, {"name": "sentry-sdk", "version": "2.32.0", "latest_version": "2.33.2", "latest_filetype": "wheel"}, {"name": "smart_open", "version": "7.3.0", "latest_version": "7.3.0.post1", "latest_filetype": "wheel"}, {"name": "starlette", "version": "0.46.2", "latest_version": "0.47.2", "latest_filetype": "wheel"}, {"name": "structlog", "version": "23.2.0", "latest_version": "25.4.0", "latest_filetype": "wheel"}, {"name": "tenacity", "version": "8.5.0", "latest_version": "9.1.2", "latest_filetype": "wheel"}, {"name": "tensorboard", "version": "2.19.0", "latest_version": "2.20.0", "latest_filetype": "wheel"}, {"name": "thinc", "version": "8.3.6", "latest_version": "9.1.1", "latest_filetype": "wheel"}, {"name": "transformers", "version": "4.53.1", "latest_version": "4.53.3", "latest_filetype": "wheel"}, {"name": "typing_extensions", "version": "4.14.0", "latest_version": "4.14.1", "latest_filetype": "wheel"}, {"name": "urllib3", "version": "2.4.0", "latest_version": "2.5.0", "latest_filetype": "wheel"}, {"name": "yt-dlp", "version": "2025.6.30", "latest_version": "2025.7.21", "latest_filetype": "wheel"}]}}, "summary": {"total_vulnerabilities": 0, "total_high_risk_packages": 0, "total_outdated_packages": 72, "overall_risk_level": "medium", "recommendations": ["定期更新过时的包", "建立自动化依赖更新流程", "定期运行安全审计", "使用依赖锁定文件", "监控安全公告和CVE数据库", "实施最小权限原则"]}}