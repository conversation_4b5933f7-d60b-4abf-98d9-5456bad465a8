/**
 * 前端页面统一布局测试脚本
 * 测试所有页面是否正确应用了统一UI布局
 */

const puppeteer = require('puppeteer');

// 测试页面列表
const testPages = [
  { path: '/', name: '首页', shouldHaveLayout: true },
  { path: '/video-creation', name: '视频创作', shouldHaveLayout: true },
  { path: '/compute-test', name: '计算引擎测试', shouldHaveLayout: true },
  { path: '/profile', name: '个人中心', shouldHaveLayout: true },
  { path: '/login', name: '登录页面', shouldHaveLayout: false }
];

// 布局元素选择器
const layoutSelectors = {
  header: '.app-header',
  sidebar: '.app-sidebar',
  main: '.app-main',
  brand: '.app-header__brand',
  nav: '.app-header__nav',
  sidebarNav: '.app-sidebar__nav'
};

// 登录页面特有元素
const loginSelectors = {
  loginContainer: '.login-container',
  userTypeSelector: '.user-type-selector',
  loginForm: '.login-form'
};

async function testPageLayout(browser, page) {
  const results = {
    name: page.name,
    path: page.path,
    shouldHaveLayout: page.shouldHaveLayout,
    passed: false,
    issues: [],
    elements: {}
  };

  try {
    const browserPage = await browser.newPage();
    await browserPage.goto(`http://localhost:3000${page.path}`, {
      waitUntil: 'networkidle0',
      timeout: 15000
    });

    // 等待Vue应用加载完成
    await new Promise(resolve => setTimeout(resolve, 3000));

    if (page.shouldHaveLayout) {
      // 测试统一布局元素
      for (const [key, selector] of Object.entries(layoutSelectors)) {
        try {
          const element = await browserPage.$(selector);
          results.elements[key] = !!element;
          
          if (!element) {
            results.issues.push(`缺少布局元素: ${key} (${selector})`);
          }
        } catch (error) {
          results.elements[key] = false;
          results.issues.push(`检查布局元素失败: ${key} - ${error.message}`);
        }
      }

      // 检查页面标题
      try {
        const title = await browserPage.title();
        if (!title.includes('二创短视频分发系统')) {
          results.issues.push(`页面标题不包含系统名称: ${title}`);
        }
      } catch (error) {
        results.issues.push(`获取页面标题失败: ${error.message}`);
      }

      // 检查导航链接
      try {
        const navLinks = await browserPage.$$('.app-header__nav-item');
        if (navLinks.length === 0) {
          results.issues.push('顶部导航链接为空');
        }
      } catch (error) {
        results.issues.push(`检查导航链接失败: ${error.message}`);
      }

      // 检查侧边栏导航
      try {
        const sidebarLinks = await browserPage.$$('.app-sidebar__nav-item');
        if (sidebarLinks.length === 0) {
          results.issues.push('侧边栏导航链接为空');
        }
      } catch (error) {
        results.issues.push(`检查侧边栏导航失败: ${error.message}`);
      }

    } else {
      // 测试登录页面特有元素
      for (const [key, selector] of Object.entries(loginSelectors)) {
        try {
          const element = await browserPage.$(selector);
          results.elements[key] = !!element;
          
          if (!element) {
            results.issues.push(`缺少登录页面元素: ${key} (${selector})`);
          }
        } catch (error) {
          results.elements[key] = false;
          results.issues.push(`检查登录页面元素失败: ${key} - ${error.message}`);
        }
      }

      // 确保登录页面没有统一布局元素
      for (const [key, selector] of Object.entries(layoutSelectors)) {
        try {
          const element = await browserPage.$(selector);
          if (element) {
            results.issues.push(`登录页面不应包含布局元素: ${key} (${selector})`);
          }
        } catch (error) {
          // 这里出错是正常的，因为登录页面不应该有这些元素
        }
      }
    }

    // 检查CSS样式是否加载
    try {
      const hasSharedStyles = await browserPage.evaluate(() => {
        const links = document.querySelectorAll('link[rel="stylesheet"]');
        return Array.from(links).some(link => 
          link.href.includes('shared-ui-design.css') || 
          link.href.includes('shared-login-styles.css')
        );
      });
      
      if (!hasSharedStyles) {
        results.issues.push('未加载共享样式文件');
      }
    } catch (error) {
      results.issues.push(`检查样式文件失败: ${error.message}`);
    }

    // 判断测试是否通过
    results.passed = results.issues.length === 0;

    await browserPage.close();
  } catch (error) {
    results.issues.push(`页面访问失败: ${error.message}`);
    results.passed = false;
  }

  return results;
}

async function runLayoutTests() {
  console.log('🎬 前端页面统一布局测试');
  console.log('=' .repeat(60));

  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const results = [];
    let passedTests = 0;

    for (const page of testPages) {
      console.log(`\n📋 测试页面: ${page.name} (${page.path})`);
      
      const result = await testPageLayout(browser, page);
      results.push(result);

      if (result.passed) {
        console.log(`✅ ${page.name} - 布局测试通过`);
        passedTests++;
      } else {
        console.log(`❌ ${page.name} - 布局测试失败`);
        result.issues.forEach(issue => {
          console.log(`   - ${issue}`);
        });
      }

      // 显示检测到的元素
      if (page.shouldHaveLayout) {
        const layoutElements = Object.entries(result.elements)
          .filter(([key]) => layoutSelectors[key])
          .map(([key, found]) => `${key}: ${found ? '✅' : '❌'}`)
          .join(', ');
        console.log(`   布局元素: ${layoutElements}`);
      } else {
        const loginElements = Object.entries(result.elements)
          .filter(([key]) => loginSelectors[key])
          .map(([key, found]) => `${key}: ${found ? '✅' : '❌'}`)
          .join(', ');
        console.log(`   登录元素: ${loginElements}`);
      }
    }

    // 输出测试总结
    console.log('\n' + '='.repeat(60));
    console.log('📊 测试结果汇总:');
    console.log(`✅ 通过测试: ${passedTests}/${testPages.length}`);
    console.log(`❌ 失败测试: ${testPages.length - passedTests}/${testPages.length}`);
    console.log(`📈 成功率: ${Math.round((passedTests / testPages.length) * 100)}%`);

    // 详细分析
    console.log('\n🔍 详细分析:');
    
    const layoutPages = results.filter(r => r.shouldHaveLayout);
    const loginPages = results.filter(r => !r.shouldHaveLayout);
    
    console.log(`\n📄 统一布局页面 (${layoutPages.length}个):`);
    layoutPages.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`  ${status} ${result.name} - ${result.issues.length} 个问题`);
    });

    console.log(`\n🔐 登录页面 (${loginPages.length}个):`);
    loginPages.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`  ${status} ${result.name} - ${result.issues.length} 个问题`);
    });

    // 生成修复建议
    if (passedTests < testPages.length) {
      console.log('\n🔧 修复建议:');
      results.filter(r => !r.passed).forEach(result => {
        console.log(`\n${result.name} (${result.path}):`);
        result.issues.forEach(issue => {
          console.log(`  - ${issue}`);
        });
      });
    } else {
      console.log('\n🎉 所有页面布局测试通过！');
      console.log('✨ 前端统一UI布局应用完整，用户体验一致');
    }

    return passedTests === testPages.length;

  } catch (error) {
    console.error('❌ 测试执行失败:', error);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// 运行测试
if (require.main === module) {
  runLayoutTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试运行出错:', error);
      process.exit(1);
    });
}

module.exports = { runLayoutTests };
