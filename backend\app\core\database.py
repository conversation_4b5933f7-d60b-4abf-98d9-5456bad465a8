"""
数据库连接和会话管理
"""

import logging
from typing import Generator

from sqlalchemy import MetaData, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker

from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 🔒 证据链: MySQL专用连接池配置，提高性能和稳定性
# 强制使用MySQL，移除SQLite支持
engine = create_engine(
    settings.DATABASE_URL,
    pool_size=getattr(settings, "DB_POOL_SIZE", 20),  # 连接池大小
    max_overflow=getattr(settings, "DB_MAX_OVERFLOW", 30),  # 最大溢出连接
    pool_timeout=30,  # 获取连接超时时间
    pool_recycle=3600,  # 连接回收时间（1小时）
    pool_pre_ping=True,  # 连接前ping检查
    echo=settings.DEBUG,
    connect_args={
        "connect_timeout": 10,  # 连接超时
        "read_timeout": 30,  # 读取超时
        "write_timeout": 30,  # 写入超时
    },
)

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 声明基类
Base = declarative_base()

# 元数据对象
metadata = MetaData()


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话

    Yields:
        Session: 数据库会话对象
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话错误: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def create_tables():
    """创建所有数据表"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("数据表创建成功")
    except Exception as e:
        logger.error(f"数据表创建失败: {e}")
        raise


def drop_tables():
    """删除所有数据表"""
    try:
        Base.metadata.drop_all(bind=engine)
        logger.info("数据表删除成功")
    except Exception as e:
        logger.error(f"数据表删除失败: {e}")
        raise


def reset_database():
    """重置数据库（删除并重新创建）"""
    logger.warning("正在重置数据库...")
    drop_tables()
    create_tables()
    logger.info("数据库重置完成")


# 🔒 证据链: 连接池状态监控
def get_connection_pool_status() -> dict:
    """获取连接池状态信息"""
    try:
        pool = engine.pool
        pool_class_name = type(pool).__name__

        # 根据连接池类型返回不同的状态信息
        if pool_class_name == "StaticPool":
            # SQLite使用StaticPool，API不同
            return {
                "pool_type": "StaticPool",
                "pool_status": "healthy",
                "note": "SQLite使用静态连接池，无需监控连接数",
            }
        else:
            # 其他数据库的连接池
            return {
                "pool_type": pool_class_name,
                "pool_size": getattr(pool, "size", lambda: 0)(),
                "checked_in": getattr(pool, "checkedin", lambda: 0)(),
                "checked_out": getattr(pool, "checkedout", lambda: 0)(),
                "overflow": getattr(pool, "overflow", lambda: 0)(),
                "invalid": getattr(pool, "invalid", lambda: 0)(),
                "pool_status": "healthy",
            }
    except Exception as e:
        logger.error(f"获取连接池状态失败: {e}")
        return {"error": str(e), "pool_status": "unknown", "pool_type": "unknown"}


# 数据库健康检查
def check_database_health() -> dict:
    """
    检查数据库连接健康状态

    Returns:
        dict: 包含详细健康状态信息的字典
    """
    try:
        from sqlalchemy import text

        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))

        # 🔒 证据链: 添加连接池状态到健康检查
        pool_status = get_connection_pool_status()

        return {
            "status": "healthy",
            "database_url": (
                settings.DATABASE_URL.split("@")[-1]
                if "@" in settings.DATABASE_URL
                else settings.DATABASE_URL
            ),
            "connection_test": "passed",
            "pool_status": pool_status,
            "engine_info": {
                "pool_class": str(type(engine.pool).__name__),
                "echo": engine.echo,
                "url": (
                    str(engine.url).split("@")[-1]
                    if "@" in str(engine.url)
                    else str(engine.url)
                ),
            },
        }
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "database_url": (
                settings.DATABASE_URL.split("@")[-1]
                if "@" in settings.DATABASE_URL
                else settings.DATABASE_URL
            ),
            "connection_test": "failed",
            "error": str(e),
            "pool_status": get_connection_pool_status(),
        }


# 数据库初始化
def init_database():
    """初始化数据库"""
    try:
        logger.info("正在初始化数据库...")

        # 检查连接
        if not check_database_health():
            raise Exception("数据库连接失败")

        # 创建表格
        create_tables()

        logger.info("数据库初始化完成")
        return True

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False
