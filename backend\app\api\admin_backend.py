"""
管理界面后端API实现
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, List
import json
import io
import pandas as pd
import pymysql
from datetime import datetime
from pathlib import Path
from contextlib import contextmanager
import threading

router = APIRouter(tags=["管理后端API"])

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'video_user',
    'password': 'VideoSystem2024!@#',
    'database': 'video_system',
    'charset': 'utf8mb4'
}

# 数据库连接池
_db_lock = threading.Lock()

@contextmanager
def get_db_connection():
    """获取MySQL数据库连接的上下文管理器"""
    conn = None
    try:
        with _db_lock:
            conn = pymysql.connect(**MYSQL_CONFIG)
            yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"数据库连接失败: {str(e)}")
    finally:
        if conn:
            conn.close()

# 数据模型
class UserCreate(BaseModel):
    username: str
    email: str
    password: str
    role: str = "user"

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    status: Optional[str] = None

# 用户管理API
@router.get("/users")
async def get_users():
    """获取用户列表"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT id, username, email, status, role, created_at FROM users ORDER BY id")
            rows = cursor.fetchall()

            users = []
            for row in rows:
                users.append({
                    "id": row[0],
                    "username": row[1],
                    "email": row[2],
                    "status": row[3],
                    "role": row[4],
                    "created_at": row[5]
                })

            return {"users": users, "total": len(users)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")

@router.post("/users")
async def create_user(user: UserCreate):
    """创建新用户"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO users (username, email, status, role) VALUES (%s, %s, %s, %s)",
                (user.username, user.email, "active", user.role)
            )
            user_id = cursor.lastrowid
            conn.commit()

            # 获取创建的用户信息
            cursor.execute("SELECT id, username, email, status, role, created_at FROM users WHERE id = %s", (user_id,))
            row = cursor.fetchone()

            new_user = {
                "id": row[0],
                "username": row[1],
                "email": row[2],
                "status": row[3],
                "role": row[4],
                "created_at": row[5]
            }

            return {"message": "用户创建成功", "user": new_user}

    except pymysql.IntegrityError as e:
        if "username" in str(e):
            raise HTTPException(status_code=400, detail="用户名已存在")
        elif "email" in str(e):
            raise HTTPException(status_code=400, detail="邮箱已存在")
        else:
            raise HTTPException(status_code=400, detail="创建用户失败")

@router.put("/users/{user_id}")
async def update_user(user_id: int, user: UserUpdate):
    """更新用户信息"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 检查用户是否存在
            cursor.execute("SELECT id FROM users WHERE id = %s", (user_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="用户不存在")

            # 构建更新语句
            update_fields = []
            params = []
            
            if user.username is not None:
                update_fields.append("username = %s")
                params.append(user.username)
            if user.email is not None:
                update_fields.append("email = %s")
                params.append(user.email)
            if user.status is not None:
                update_fields.append("status = %s")
                params.append(user.status)

            if update_fields:
                params.append(user_id)
                sql = f"UPDATE users SET {', '.join(update_fields)} WHERE id = %s"
                cursor.execute(sql, params)
                conn.commit()

            # 获取更新后的用户信息
            cursor.execute("SELECT id, username, email, status, role, created_at FROM users WHERE id = %s", (user_id,))
            row = cursor.fetchone()

            updated_user = {
                "id": row[0],
                "username": row[1],
                "email": row[2],
                "status": row[3],
                "role": row[4],
                "created_at": row[5]
            }

            return {"message": "用户信息已更新", "user": updated_user}

    except pymysql.IntegrityError as e:
        if "username" in str(e):
            raise HTTPException(status_code=400, detail="用户名已存在")
        elif "email" in str(e):
            raise HTTPException(status_code=400, detail="邮箱已存在")
        else:
            raise HTTPException(status_code=400, detail="更新用户失败")

@router.delete("/users/{user_id}")
async def delete_user(user_id: int):
    """删除用户"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 获取要删除的用户信息
            cursor.execute("SELECT id, username, email, status, role FROM users WHERE id = %s", (user_id,))
            row = cursor.fetchone()

            if not row:
                raise HTTPException(status_code=404, detail="用户不存在")

            deleted_user = {
                "id": row[0],
                "username": row[1],
                "email": row[2],
                "status": row[3],
                "role": row[4]
            }

            # 删除用户
            cursor.execute("DELETE FROM users WHERE id = %s", (user_id,))
            conn.commit()

            return {"message": "用户已删除", "user": deleted_user}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")