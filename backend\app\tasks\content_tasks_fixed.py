"""
内容处理任务
"""

import json
import time
from typing import Any, Dict

from celery import current_task

from app.core.celery_app import celery_app
from app.core.database import SessionLocal
from app.models import Content


@celery_app.task(bind=True)
def process_content_compliance(self, content_id: int, user_id: int):
    """
    处理内容合规检测任务
    """
    try:
        # 更新任务状态
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "message": "开始合规检测"},
        )

        # 获取数据库会话
        db = SessionLocal()
        try:
            content = db.query(Content).filter(Content.id == content_id).first()

            if not content:
                raise Exception(f"Content with id {content_id} not found")

            # 模拟合规检测过程
            steps = [
                "文本预处理",
                "关键词检测",
                "语义分析",
                "风险评估",
                "生成报告",
            ]

            compliance_issues = []
            compliance_score = 0.95  # 默认高分

            for i, step in enumerate(steps):
                current_task.update_state(
                    state="PROGRESS",
                    meta={
                        "current": (i + 1) * 20,
                        "total": 100,
                        "message": f"正在进行: {step}",
                    },
                )

                # 模拟处理时间
                time.sleep(1)

                # 模拟发现问题
                if i == 2 and "敏感" in (content.original_text or ""):
                    compliance_issues.append(
                        {
                            "type": "sensitive_content",
                            "message": "检测到敏感内容",
                            "severity": "medium",
                        }
                    )
                    compliance_score = 0.7

            # 更新数据库中的内容
            content.compliance_score = compliance_score
            content.compliance_issues = json.dumps(
                compliance_issues, ensure_ascii=False
            )
            content.status = "approved" if compliance_score >= 0.8 else "rejected"

            db.commit()

            return {
                "content_id": content_id,
                "compliance_score": compliance_score,
                "compliance_issues": compliance_issues,
                "status": content.status,
                "message": "合规检测完成",
            }

        finally:
            db.close()

    except Exception as e:
        current_task.update_state(state="FAILURE", meta={"error": str(e)})
        raise


@celery_app.task(bind=True)
def rewrite_content(self, content_id: int, rewrite_prompt: str = None):
    """
    内容AI改写任务
    """
    try:
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "message": "开始内容改写"},
        )

        # 获取数据库会话
        db = SessionLocal()
        try:
            content = db.query(Content).filter(Content.id == content_id).first()

            if not content:
                raise Exception(f"Content with id {content_id} not found")

            original_text = content.original_text or ""

            # 模拟AI改写过程
            steps = [
                "文本分析",
                "语义理解",
                "内容重组",
                "风格调整",
                "质量检查",
            ]

            for i, step in enumerate(steps):
                current_task.update_state(
                    state="PROGRESS",
                    meta={
                        "current": (i + 1) * 20,
                        "total": 100,
                        "message": f"正在进行: {step}",
                    },
                )
                time.sleep(1)

            # 模拟改写结果（实际应该调用AI服务）
            rewritten_text = f"【改写版本】{original_text}"
            if rewrite_prompt:
                rewritten_text += f" (按照要求: {rewrite_prompt})"

            # 更新数据库
            content.rewritten_text = rewritten_text
            content.rewrite_metadata = json.dumps(
                {
                    "rewrite_prompt": rewrite_prompt,
                    "rewrite_time": time.time(),
                    "ai_model": "mock-rewriter-v1",
                },
                ensure_ascii=False,
            )

            db.commit()

            return {
                "content_id": content_id,
                "original_text": original_text,
                "rewritten_text": rewritten_text,
                "message": "内容改写完成",
            }

        finally:
            db.close()

    except Exception as e:
        current_task.update_state(state="FAILURE", meta={"error": str(e)})
        raise


@celery_app.task(bind=True)
def generate_video_from_text(
    self, content_id: int, video_config: Dict[str, Any] = None
):
    """
    文本转视频任务
    """
    try:
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "message": "开始视频生成"},
        )

        # 获取数据库会话
        db = SessionLocal()
        try:
            content = db.query(Content).filter(Content.id == content_id).first()

            if not content:
                raise Exception(f"Content with id {content_id} not found")

            content.rewritten_text or content.original_text or ""

            # 模拟视频生成过程
            steps = [
                "文本分析",
                "场景规划",
                "视觉生成",
                "音频合成",
                "视频合成",
                "后期处理",
            ]

            for i, step in enumerate(steps):
                current_task.update_state(
                    state="PROGRESS",
                    meta={
                        "current": int((i + 1) * 100 / len(steps)),
                        "total": 100,
                        "message": f"正在进行: {step}",
                    },
                )
                # 视频生成需要更长时间
                time.sleep(2)

            # 模拟生成的视频文件路径
            video_path = f"videos/content_{content_id}_{int(time.time())}.mp4"
            thumbnail_path = f"thumbnails/content_{content_id}_{int(time.time())}.jpg"

            # 更新数据库
            media_files = json.loads(content.media_files or "[]")
            media_files.append(
                {
                    "type": "video",
                    "path": video_path,
                    "thumbnail": thumbnail_path,
                    "generated_at": time.time(),
                    "config": video_config or {},
                }
            )

            content.media_files = json.dumps(media_files, ensure_ascii=False)
            content.content_type = "video"

            db.commit()

            return {
                "content_id": content_id,
                "video_path": video_path,
                "thumbnail_path": thumbnail_path,
                "duration": 30,  # 模拟30秒视频
                "message": "视频生成完成",
            }

        finally:
            db.close()

    except Exception as e:
        current_task.update_state(state="FAILURE", meta={"error": str(e)})
        raise
