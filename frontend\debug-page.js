/**
 * 页面调试脚本 - 检查前端页面加载状态
 */

const puppeteer = require('puppeteer');

async function debugPage() {
  console.log('🔍 开始调试前端页面...');
  
  const browser = await puppeteer.launch({
    headless: false, // 显示浏览器窗口
    devtools: true,  // 打开开发者工具
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    console.log(`📝 [${type.toUpperCase()}] ${text}`);
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.error('❌ 页面错误:', error.message);
  });
  
  // 监听请求失败
  page.on('requestfailed', request => {
    console.error('🚫 请求失败:', request.url(), request.failure().errorText);
  });
  
  // 监听响应
  page.on('response', response => {
    if (!response.ok()) {
      console.error(`⚠️  响应错误: ${response.url()} - ${response.status()}`);
    }
  });
  
  try {
    console.log('🌐 正在访问 http://localhost:3001/');
    await page.goto('http://localhost:3001/', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    // 等待Vue应用挂载
    await page.waitForTimeout(3000);
    
    // 检查页面内容
    const title = await page.title();
    console.log('📄 页面标题:', title);
    
    // 检查#app元素
    const appElement = await page.$('#app');
    if (appElement) {
      const appContent = await page.evaluate(() => {
        const app = document.getElementById('app');
        return {
          hasContent: app && app.innerHTML.trim().length > 0,
          innerHTML: app ? app.innerHTML.substring(0, 200) + '...' : 'null',
          childrenCount: app ? app.children.length : 0
        };
      });
      
      console.log('🎯 #app元素状态:', appContent);
      
      if (!appContent.hasContent) {
        console.error('❌ #app元素为空，Vue应用可能未正确挂载');
      } else {
        console.log('✅ #app元素有内容，Vue应用已挂载');
      }
    } else {
      console.error('❌ 未找到#app元素');
    }
    
    // 检查Vue实例
    const vueInstance = await page.evaluate(() => {
      return typeof window.Vue !== 'undefined' || 
             document.querySelector('[data-v-]') !== null ||
             document.querySelector('.v-') !== null;
    });
    
    console.log('🔧 Vue实例检测:', vueInstance ? '已找到' : '未找到');
    
    // 检查路由
    const currentPath = await page.evaluate(() => window.location.pathname);
    console.log('🛣️  当前路径:', currentPath);
    
    // 检查可见元素
    const visibleElements = await page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      let visibleCount = 0;
      let totalCount = elements.length;
      
      elements.forEach(el => {
        const style = window.getComputedStyle(el);
        if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
          visibleCount++;
        }
      });
      
      return { visibleCount, totalCount };
    });
    
    console.log('👁️  可见元素:', `${visibleElements.visibleCount}/${visibleElements.totalCount}`);
    
    // 截图
    await page.screenshot({ path: 'debug-screenshot.png', fullPage: true });
    console.log('📸 已保存截图: debug-screenshot.png');
    
    // 保持浏览器打开以便手动检查
    console.log('🔍 浏览器将保持打开状态，请手动检查页面...');
    console.log('按 Ctrl+C 退出');
    
    // 等待用户手动关闭
    await new Promise(() => {});
    
  } catch (error) {
    console.error('💥 调试过程中出现错误:', error.message);
  } finally {
    // await browser.close();
  }
}

// 检查是否安装了puppeteer
try {
  debugPage().catch(console.error);
} catch (error) {
  console.error('❌ 请先安装puppeteer: npm install puppeteer');
  console.log('或者使用简化版本的调试...');
  
  // 简化版本的调试
  const https = require('http');
  
  const req = https.request('http://localhost:3001/', (res) => {
    console.log('📡 HTTP状态码:', res.statusCode);
    console.log('📋 响应头:', res.headers);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('📄 页面内容长度:', data.length);
      console.log('🔍 是否包含#app:', data.includes('id="app"'));
      console.log('🔍 是否包含main.ts:', data.includes('main.ts'));
    });
  });
  
  req.on('error', (error) => {
    console.error('❌ 请求错误:', error.message);
  });
  
  req.end();
}
