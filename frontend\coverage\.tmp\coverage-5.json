{"result": [{"scriptId": "866", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/tests/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49811, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49811, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 577, "endOffset": 946, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 981, "endOffset": 996, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 999, "endOffset": 1016, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1019, "endOffset": 1037, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 1175, "endOffset": 1190, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 1193, "endOffset": 1210, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1213, "endOffset": 1231, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3284, "endOffset": 3354, "count": 38}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3458, "endOffset": 4555, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4665, "endOffset": 6533, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7131, "endOffset": 7312, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7339, "endOffset": 8381, "count": 1}], "isBlockCoverage": true}, {"functionName": "getVoices", "ranges": [{"startOffset": 7400, "endOffset": 7745, "count": 2}], "isBlockCoverage": true}, {"functionName": "speak", "ranges": [{"startOffset": 7748, "endOffset": 8170, "count": 0}], "isBlockCoverage": false}, {"functionName": "cancel", "ranges": [{"startOffset": 8173, "endOffset": 8240, "count": 0}], "isBlockCoverage": false}, {"functionName": "pause", "ranges": [{"startOffset": 8243, "endOffset": 8280, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 8283, "endOffset": 8322, "count": 0}], "isBlockCoverage": false}, {"functionName": "addEventListener", "ranges": [{"startOffset": 8325, "endOffset": 8349, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEventListener", "ranges": [{"startOffset": 8352, "endOffset": 8379, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 8633, "endOffset": 9480, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 9703, "endOffset": 11003, "count": 0}], "isBlockCoverage": true}, {"functionName": "getTracks", "ranges": [{"startOffset": 11429, "endOffset": 11618, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAudioTracks", "ranges": [{"startOffset": 11638, "endOffset": 11833, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVideoTracks", "ranges": [{"startOffset": 11853, "endOffset": 11861, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTest", "ranges": [{"startOffset": 13502, "endOffset": 13920, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14018, "endOffset": 14043, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupTest", "ranges": [{"startOffset": 14047, "endOffset": 14164, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14264, "endOffset": 14291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14400, "endOffset": 14432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14542, "endOffset": 14576, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14678, "endOffset": 14704, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14810, "endOffset": 14840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14940, "endOffset": 14964, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15001, "endOffset": 15073, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15108, "endOffset": 15177, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "898", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/tests/performance.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49967, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49967, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 345, "endOffset": 1410, "count": 1}], "isBlockCoverage": true}, {"functionName": "dispose", "ranges": [{"startOffset": 733, "endOffset": 750, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1439, "endOffset": 1907, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1643, "endOffset": 1756, "count": 36}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1676, "endOffset": 1748, "count": 36}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1934, "endOffset": 2062, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2575, "endOffset": 3418, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3479, "endOffset": 4232, "count": 12}], "isBlockCoverage": true}, {"functionName": "start", "ranges": [{"startOffset": 3524, "endOffset": 3608, "count": 7}], "isBlockCoverage": true}, {"functionName": "snapshot", "ranges": [{"startOffset": 3611, "endOffset": 3731, "count": 2}], "isBlockCoverage": true}, {"functionName": "end", "ranges": [{"startOffset": 3734, "endOffset": 4230, "count": 5}, {"startOffset": 3856, "endOffset": 3937, "count": 1}, {"startOffset": 3938, "endOffset": 3941, "count": 4}, {"startOffset": 4042, "endOffset": 4077, "count": 1}, {"startOffset": 4078, "endOffset": 4081, "count": 4}, {"startOffset": 4136, "endOffset": 4215, "count": 1}, {"startOffset": 4216, "endOffset": 4219, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4166, "endOffset": 4181, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4250, "endOffset": 14293, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4319, "endOffset": 4534, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4549, "endOffset": 4629, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4653, "endOffset": 6762, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4688, "endOffset": 5542, "count": 1}, {"startOffset": 5113, "endOffset": 5228, "count": 2}, {"startOffset": 5186, "endOffset": 5220, "count": 0}, {"startOffset": 5471, "endOffset": 5536, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4771, "endOffset": 5078, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5570, "endOffset": 6207, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5630, "endOffset": 5866, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6242, "endOffset": 6756, "count": 1}, {"startOffset": 6621, "endOffset": 6755, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6404, "endOffset": 6587, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6786, "endOffset": 9347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6817, "endOffset": 7571, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6910, "endOffset": 7152, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7601, "endOffset": 8394, "count": 1}, {"startOffset": 7988, "endOffset": 8279, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7661, "endOffset": 7902, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8421, "endOffset": 9341, "count": 1}, {"startOffset": 9036, "endOffset": 9340, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8509, "endOffset": 8746, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8797, "endOffset": 8980, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9162, "endOffset": 9187, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9245, "endOffset": 9267, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9371, "endOffset": 11232, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9402, "endOffset": 10388, "count": 1}, {"startOffset": 10084, "endOffset": 10387, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9492, "endOffset": 9729, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9784, "endOffset": 10022, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10413, "endOffset": 11226, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11257, "endOffset": 12696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11291, "endOffset": 12101, "count": 1}, {"startOffset": 11410, "endOffset": 11788, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11838, "endOffset": 11853, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12127, "endOffset": 12690, "count": 1}, {"startOffset": 12201, "endOffset": 12532, "count": 8}, {"startOffset": 12470, "endOffset": 12524, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12720, "endOffset": 14289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12751, "endOffset": 13508, "count": 1}, {"startOffset": 13345, "endOffset": 13378, "count": 3}], "isBlockCoverage": true}, {"functionName": "global.Worker", "ranges": [{"startOffset": 12909, "endOffset": 12988, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13058, "endOffset": 13308, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13530, "endOffset": 14283, "count": 1}, {"startOffset": 13612, "endOffset": 14086, "count": 2}, {"startOffset": 14044, "endOffset": 14078, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "938", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/stores/compute.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33534, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33534, "count": 1}], "isBlockCoverage": true}, {"functionName": "state", "ranges": [{"startOffset": 623, "endOffset": 1115, "count": 12}], "isBlockCoverage": true}, {"functionName": "hasErrors", "ranges": [{"startOffset": 1166, "endOffset": 1200, "count": 0}], "isBlockCoverage": false}, {"functionName": "latestTask", "ranges": [{"startOffset": 1218, "endOffset": 1264, "count": 0}], "isBlockCoverage": false}, {"functionName": "isReady", "ranges": [{"startOffset": 1279, "endOffset": 1332, "count": 0}], "isBlockCoverage": false}, {"functionName": "completedTasks", "ranges": [{"startOffset": 1354, "endOffset": 1422, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1384, "endOffset": 1421, "count": 3}], "isBlockCoverage": true}, {"functionName": "failedTasks", "ranges": [{"startOffset": 1441, "endOffset": 1506, "count": 0}], "isBlockCoverage": false}, {"functionName": "processingTasks", "ranges": [{"startOffset": 1529, "endOffset": 1598, "count": 0}], "isBlockCoverage": false}, {"functionName": "successRate", "ranges": [{"startOffset": 1637, "endOffset": 1844, "count": 1}, {"startOffset": 1834, "endOffset": 1837, "count": 0}], "isBlockCoverage": true}, {"functionName": "recentLogs", "ranges": [{"startOffset": 1862, "endOffset": 1894, "count": 0}], "isBlockCoverage": false}, {"functionName": "addLog", "ranges": [{"startOffset": 1952, "endOffset": 2164, "count": 124}, {"startOffset": 2106, "endOffset": 2158, "count": 0}], "isBlockCoverage": true}, {"functionName": "initEngine", "ranges": [{"startOffset": 2191, "endOffset": 3016, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTask", "ranges": [{"startOffset": 3042, "endOffset": 3338, "count": 43}], "isBlockCoverage": true}, {"functionName": "updateTask", "ranges": [{"startOffset": 3366, "endOffset": 4560, "count": 86}, {"startOffset": 3596, "endOffset": 4443, "count": 43}, {"startOffset": 3736, "endOffset": 3766, "count": 7}, {"startOffset": 3923, "endOffset": 3990, "count": 36}, {"startOffset": 3990, "endOffset": 4065, "count": 7}, {"startOffset": 4485, "endOffset": 4546, "count": 36}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3441, "endOffset": 3463, "count": 204}], "isBlockCoverage": true}, {"functionName": "processVideo", "ranges": [{"startOffset": 4588, "endOffset": 5952, "count": 38}, {"startOffset": 5196, "endOffset": 5502, "count": 36}, {"startOffset": 5502, "endOffset": 5572, "count": 2}, {"startOffset": 5549, "endOffset": 5560, "count": 0}, {"startOffset": 5581, "endOffset": 5946, "count": 2}, {"startOffset": 5665, "endOffset": 5677, "count": 0}], "isBlockCoverage": true}, {"functionName": "onProgress", "ranges": [{"startOffset": 4886, "endOffset": 4965, "count": 0}], "isBlockCoverage": false}, {"functionName": "onLog", "ranges": [{"startOffset": 4984, "endOffset": 5070, "count": 38}], "isBlockCoverage": true}, {"functionName": "runInference", "ranges": [{"startOffset": 5980, "endOffset": 7108, "count": 5}, {"startOffset": 6296, "endOffset": 6661, "count": 0}, {"startOffset": 6708, "endOffset": 6719, "count": 0}, {"startOffset": 6824, "endOffset": 6836, "count": 0}], "isBlockCoverage": true}, {"functionName": "extractVideoFrames", "ranges": [{"startOffset": 7135, "endOffset": 8063, "count": 0}], "isBlockCoverage": false}, {"functionName": "updatePerformanceMetrics", "ranges": [{"startOffset": 8091, "endOffset": 8272, "count": 0}], "isBlockCoverage": false}, {"functionName": "addError", "ranges": [{"startOffset": 8298, "endOffset": 8595, "count": 7}, {"startOffset": 8534, "endOffset": 8589, "count": 0}], "isBlockCoverage": true}, {"functionName": "clearErrors", "ranges": [{"startOffset": 8621, "endOffset": 8666, "count": 12}], "isBlockCoverage": true}, {"functionName": "clearTaskHistory", "ranges": [{"startOffset": 8694, "endOffset": 8798, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8752, "endOffset": 8790, "count": 43}], "isBlockCoverage": true}, {"functionName": "reset", "ranges": [{"startOffset": 8824, "endOffset": 9376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9488, "endOffset": 9519, "count": 12}], "isBlockCoverage": true}]}, {"scriptId": "939", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/compute/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37638, "count": 1}], "isBlockCoverage": true}, {"functionName": "updateMemoryUsage", "ranges": [{"startOffset": 1155, "endOffset": 1366, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEngineStatus", "ranges": [{"startOffset": 1368, "endOffset": 1451, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1555, "endOffset": 1586, "count": 0}], "isBlockCoverage": false}, {"functionName": "initEngine", "ranges": [{"startOffset": 1590, "endOffset": 4020, "count": 7}, {"startOffset": 1654, "endOffset": 1676, "count": 5}, {"startOffset": 1676, "endOffset": 2564, "count": 2}, {"startOffset": 2637, "endOffset": 2666, "count": 2}, {"startOffset": 2736, "endOffset": 2763, "count": 2}, {"startOffset": 2837, "endOffset": 2866, "count": 2}, {"startOffset": 2952, "endOffset": 2987, "count": 2}, {"startOffset": 3044, "endOffset": 3076, "count": 2}, {"startOffset": 3077, "endOffset": 3108, "count": 0}, {"startOffset": 3109, "endOffset": 3142, "count": 0}, {"startOffset": 3143, "endOffset": 3182, "count": 0}, {"startOffset": 3209, "endOffset": 3675, "count": 2}, {"startOffset": 3675, "endOffset": 3760, "count": 0}, {"startOffset": 3765, "endOffset": 4018, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1926, "endOffset": 2033, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2173, "endOffset": 2183, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2191, "endOffset": 2300, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4119, "endOffset": 4145, "count": 0}], "isBlockCoverage": false}, {"functionName": "processVideoTask", "ranges": [{"startOffset": 4149, "endOffset": 5300, "count": 38}, {"startOffset": 4237, "endOffset": 4381, "count": 2}, {"startOffset": 4309, "endOffset": 4375, "count": 0}, {"startOffset": 4418, "endOffset": 4484, "count": 0}, {"startOffset": 4783, "endOffset": 4999, "count": 36}, {"startOffset": 4972, "endOffset": 4983, "count": 0}, {"startOffset": 5023, "endOffset": 5298, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5405, "endOffset": 5437, "count": 38}], "isBlockCoverage": true}, {"functionName": "runAIInference", "ranges": [{"startOffset": 5441, "endOffset": 6572, "count": 5}, {"startOffset": 5728, "endOffset": 5763, "count": 0}, {"startOffset": 5764, "endOffset": 5781, "count": 0}, {"startOffset": 5783, "endOffset": 6303, "count": 0}, {"startOffset": 6383, "endOffset": 6413, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6675, "endOffset": 6705, "count": 5}], "isBlockCoverage": true}, {"functionName": "extractFrames", "ranges": [{"startOffset": 6709, "endOffset": 7356, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7458, "endOffset": 7487, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateSpeech", "ranges": [{"startOffset": 7491, "endOffset": 8519, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8622, "endOffset": 8652, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateImage", "ranges": [{"startOffset": 8656, "endOffset": 9747, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9849, "endOffset": 9878, "count": 0}], "isBlockCoverage": false}, {"functionName": "composeVideo", "ranges": [{"startOffset": 9882, "endOffset": 10597, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10698, "endOffset": 10726, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupEngine", "ranges": [{"startOffset": 10730, "endOffset": 11474, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11576, "endOffset": 11605, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPerformanceMetrics", "ranges": [{"startOffset": 11609, "endOffset": 11823, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11933, "endOffset": 11970, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "940", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/compute/ai/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 39796, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateAIInput", "ranges": [{"startOffset": 317, "endOffset": 800, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMemoryUsage", "ranges": [{"startOffset": 825, "endOffset": 924, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadModel", "ranges": [{"startOffset": 990, "endOffset": 1963, "count": 0}], "isBlockCoverage": false}, {"functionName": "performImageClassification", "ranges": [{"startOffset": 2000, "endOffset": 3253, "count": 0}], "isBlockCoverage": false}, {"functionName": "performObjectDetection", "ranges": [{"startOffset": 3286, "endOffset": 5080, "count": 0}], "isBlockCoverage": false}, {"functionName": "performTextGeneration", "ranges": [{"startOffset": 5112, "endOffset": 5865, "count": 0}], "isBlockCoverage": false}, {"functionName": "performSentimentAnalysis", "ranges": [{"startOffset": 5900, "endOffset": 7059, "count": 0}], "isBlockCoverage": false}, {"functionName": "performToxicityDetection", "ranges": [{"startOffset": 7094, "endOffset": 8122, "count": 0}], "isBlockCoverage": false}, {"functionName": "initAIEngine", "ranges": [{"startOffset": 8124, "endOffset": 8712, "count": 2}, {"startOffset": 8380, "endOffset": 8442, "count": 0}, {"startOffset": 8608, "endOffset": 8710, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8813, "endOffset": 8841, "count": 2}], "isBlockCoverage": true}, {"functionName": "cleanupAIEngine", "ranges": [{"startOffset": 8845, "endOffset": 9396, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9500, "endOffset": 9531, "count": 0}], "isBlockCoverage": false}, {"functionName": "aiInference", "ranges": [{"startOffset": 9535, "endOffset": 11554, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11654, "endOffset": 11681, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "941", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/compute/video/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12214, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12214, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateVideoFile", "ranges": [{"startOffset": 417, "endOffset": 946, "count": 38}, {"startOffset": 504, "endOffset": 518, "count": 0}, {"startOffset": 598, "endOffset": 603, "count": 0}, {"startOffset": 644, "endOffset": 713, "count": 0}, {"startOffset": 810, "endOffset": 838, "count": 2}, {"startOffset": 838, "endOffset": 901, "count": 36}, {"startOffset": 901, "endOffset": 929, "count": 0}, {"startOffset": 929, "endOffset": 945, "count": 36}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 766, "endOffset": 807, "count": 54}], "isBlockCoverage": true}, {"functionName": "initVideoEngine", "ranges": [{"startOffset": 948, "endOffset": 1204, "count": 2}, {"startOffset": 1100, "endOffset": 1202, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1308, "endOffset": 1339, "count": 0}], "isBlockCoverage": false}, {"functionName": "videoProcess", "ranges": [{"startOffset": 1343, "endOffset": 2309, "count": 38}, {"startOffset": 1509, "endOffset": 1556, "count": 2}, {"startOffset": 1556, "endOffset": 1606, "count": 36}, {"startOffset": 1606, "endOffset": 1644, "count": 2}, {"startOffset": 1644, "endOffset": 1679, "count": 36}, {"startOffset": 1827, "endOffset": 2050, "count": 36}, {"startOffset": 2050, "endOffset": 2307, "count": 2}, {"startOffset": 2130, "endOffset": 2142, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2410, "endOffset": 2438, "count": 38}], "isBlockCoverage": true}, {"functionName": "extractVideoFrames", "ranges": [{"startOffset": 2442, "endOffset": 3188, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3295, "endOffset": 3329, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupVideoEngine", "ranges": [{"startOffset": 3333, "endOffset": 3570, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3677, "endOffset": 3711, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "942", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/engines/ffmpeg/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27103, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27103, "count": 1}], "isBlockCoverage": true}, {"functionName": "initFFmpeg", "ranges": [{"startOffset": 547, "endOffset": 1642, "count": 2}, {"startOffset": 593, "endOffset": 600, "count": 0}, {"startOffset": 618, "endOffset": 1520, "count": 1}, {"startOffset": 1520, "endOffset": 1640, "count": 0}], "isBlockCoverage": true}, {"functionName": "isLoading", "ranges": [{"startOffset": 668, "endOffset": 705, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 896, "endOffset": 960, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 997, "endOffset": 1097, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1741, "endOffset": 1767, "count": 2}], "isBlockCoverage": true}, {"functionName": "convertVideo", "ranges": [{"startOffset": 1771, "endOffset": 2816, "count": 36}, {"startOffset": 1882, "endOffset": 1941, "count": 0}, {"startOffset": 2715, "endOffset": 2814, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2297, "endOffset": 2354, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2917, "endOffset": 2945, "count": 36}], "isBlockCoverage": true}, {"functionName": "extractThumbnail", "ranges": [{"startOffset": 2949, "endOffset": 3947, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4052, "endOffset": 4084, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVideoInfo", "ranges": [{"startOffset": 4088, "endOffset": 5772, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5873, "endOffset": 5901, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFFmpegLoaded", "ranges": [{"startOffset": 5905, "endOffset": 5953, "count": 36}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6056, "endOffset": 6086, "count": 36}], "isBlockCoverage": true}, {"functionName": "getFFmpegInstance", "ranges": [{"startOffset": 6090, "endOffset": 6147, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6253, "endOffset": 6286, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupFFmpeg", "ranges": [{"startOffset": 6290, "endOffset": 6554, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6656, "endOffset": 6685, "count": 0}], "isBlockCoverage": false}, {"functionName": "mergeVideos", "ranges": [{"startOffset": 6689, "endOffset": 8221, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8321, "endOffset": 8348, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "943", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/services/ttsService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21621, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21621, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 202, "endOffset": 5397, "count": 1}], "isBlockCoverage": true}, {"functionName": "TTSService", "ranges": [{"startOffset": 259, "endOffset": 352, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeVoices", "ranges": [{"startOffset": 380, "endOffset": 784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 430, "endOffset": 778, "count": 1}, {"startOffset": 671, "endOffset": 772, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadVoices", "ranges": [{"startOffset": 470, "endOffset": 589, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableVoices", "ranges": [{"startOffset": 814, "endOffset": 1119, "count": 2}, {"startOffset": 872, "endOffset": 916, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 944, "endOffset": 1113, "count": 4}], "isBlockCoverage": true}, {"functionName": "detectGender", "ranges": [{"startOffset": 1154, "endOffset": 1611, "count": 4}, {"startOffset": 1458, "endOffset": 1488, "count": 0}, {"startOffset": 1558, "endOffset": 1586, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1415, "endOffset": 1455, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1515, "endOffset": 1555, "count": 20}], "isBlockCoverage": true}, {"functionName": "generateSpeech", "ranges": [{"startOffset": 1646, "endOffset": 3023, "count": 0}], "isBlockCoverage": false}, {"functionName": "synthesizeToBlob", "ranges": [{"startOffset": 3056, "endOffset": 4106, "count": 0}], "isBlockCoverage": false}, {"functionName": "estimateDuration", "ranges": [{"startOffset": 4136, "endOffset": 4398, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopSpeech", "ranges": [{"startOffset": 4427, "endOffset": 4517, "count": 0}], "isBlockCoverage": false}, {"functionName": "pauseSpeech", "ranges": [{"startOffset": 4544, "endOffset": 4660, "count": 0}], "isBlockCoverage": false}, {"functionName": "resumeSpeech", "ranges": [{"startOffset": 4687, "endOffset": 4777, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStatus", "ranges": [{"startOffset": 4807, "endOffset": 5052, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateBatchSpeech", "ranges": [{"startOffset": 5090, "endOffset": 5395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5496, "endOffset": 5522, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5661, "endOffset": 5687, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "944", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/services/imageGenerationService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36821, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36821, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 214, "endOffset": 9564, "count": 1}], "isBlockCoverage": true}, {"functionName": "ImageGenerationService", "ranges": [{"startOffset": 278, "endOffset": 508, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAvailableModels", "ranges": [{"startOffset": 540, "endOffset": 1631, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateImage", "ranges": [{"startOffset": 1656, "endOffset": 2556, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateWithDALLE", "ranges": [{"startOffset": 2589, "endOffset": 4193, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateWithStableDiffusion", "ranges": [{"startOffset": 4244, "endOffset": 5789, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatSize", "ranges": [{"startOffset": 5826, "endOffset": 6120, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateBatchImages", "ranges": [{"startOffset": 6147, "endOffset": 6445, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateVariations", "ranges": [{"startOffset": 6472, "endOffset": 7803, "count": 0}], "isBlockCoverage": false}, {"functionName": "editImage", "ranges": [{"startOffset": 7835, "endOffset": 9184, "count": 0}], "isBlockCoverage": false}, {"functionName": "setConfig", "ranges": [{"startOffset": 9212, "endOffset": 9325, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStatus", "ranges": [{"startOffset": 9352, "endOffset": 9562, "count": 2}, {"startOffset": 9440, "endOffset": 9478, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 9675, "endOffset": 9713, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9888, "endOffset": 9926, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "945", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/services/videoCompositionService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 40837, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 40837, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 485, "endOffset": 10266, "count": 1}], "isBlockCoverage": true}, {"functionName": "composeVideo", "ranges": [{"startOffset": 558, "endOffset": 2085, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateCompositionOptions", "ranges": [{"startOffset": 2112, "endOffset": 2981, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateSceneAssets", "ranges": [{"startOffset": 3008, "endOffset": 4591, "count": 0}], "isBlockCoverage": false}, {"functionName": "assembleVideo", "ranges": [{"startOffset": 4616, "endOffset": 5672, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderScenes", "ranges": [{"startOffset": 5700, "endOffset": 6763, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderFrame", "ranges": [{"startOffset": 6788, "endOffset": 7547, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderTextOverlay", "ranges": [{"startOffset": 7575, "endOffset": 8473, "count": 0}], "isBlockCoverage": false}, {"functionName": "applyTransitionEffects", "ranges": [{"startOffset": 8500, "endOffset": 9171, "count": 0}], "isBlockCoverage": false}, {"functionName": "applyTransition", "ranges": [{"startOffset": 9201, "endOffset": 9746, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadImageFromBlob", "ranges": [{"startOffset": 9776, "endOffset": 10006, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentProgress", "ranges": [{"startOffset": 10035, "endOffset": 10094, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCurrentlyProcessing", "ranges": [{"startOffset": 10123, "endOffset": 10182, "count": 0}], "isBlockCoverage": false}, {"functionName": "cancelProcessing", "ranges": [{"startOffset": 10209, "endOffset": 10264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10378, "endOffset": 10417, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10595, "endOffset": 10634, "count": 0}], "isBlockCoverage": false}]}]}