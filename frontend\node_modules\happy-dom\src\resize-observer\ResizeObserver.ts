/**
 * The ResizeObserver interface reports changes to the dimensions of an Element's content or border box, or the bounding box of an SVGElement.
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/API/ResizeObserver
 */
export default class ResizeObserver {
	/**
	 * Starts observing.
	 *
	 * Not implemented.
	 */
	public observe(): void {
		// TODO: Not implemented
	}

	/**
	 * Stops observing.
	 *
	 * Not implemented.
	 */
	public unobserve(): void {
		// TODO: Not implemented
	}

	/**
	 * Disconnects.
	 *
	 * Not implemented.
	 */
	public disconnect(): void {
		// TODO: Not implemented
	}
}
