{"timestamp": "2025-07-23T18:15:53.727376", "level": "INFO", "logger": "app.main", "message": "🚀 启动应用程序...", "module": "main", "function": "lifespan", "line": 53}
{"timestamp": "2025-07-23T18:15:54.143128", "level": "INFO", "logger": "app.main", "message": "✅ 数据库表创建成功", "module": "main", "function": "lifespan", "line": 58}
{"timestamp": "2025-07-23T18:16:08.971377", "level": "INFO", "logger": "app.main", "message": "🚀 启动应用程序...", "module": "main", "function": "lifespan", "line": 53}
{"timestamp": "2025-07-23T18:16:09.036161", "level": "INFO", "logger": "app.main", "message": "✅ 数据库表创建成功", "module": "main", "function": "lifespan", "line": 58}
{"timestamp": "2025-07-23T18:50:52.725257", "level": "INFO", "logger": "app.main", "message": "🛑 关闭应用程序...", "module": "main", "function": "lifespan", "line": 64}
{"timestamp": "2025-07-23T18:50:57.701808", "level": "INFO", "logger": "app.main", "message": "🚀 启动应用程序...", "module": "main", "function": "lifespan", "line": 53}
{"timestamp": "2025-07-23T18:50:57.746865", "level": "INFO", "logger": "app.main", "message": "✅ 数据库表创建成功", "module": "main", "function": "lifespan", "line": 58}
{"timestamp": "2025-07-23T18:51:06.006865", "level": "INFO", "logger": "app.main", "message": "🛑 关闭应用程序...", "module": "main", "function": "lifespan", "line": 64}
{"timestamp": "2025-07-23T18:51:10.847020", "level": "INFO", "logger": "app.main", "message": "🚀 启动应用程序...", "module": "main", "function": "lifespan", "line": 53}
{"timestamp": "2025-07-23T18:51:10.889108", "level": "INFO", "logger": "app.main", "message": "✅ 数据库表创建成功", "module": "main", "function": "lifespan", "line": 58}
{"timestamp": "2025-07-23T18:51:23.373345", "level": "INFO", "logger": "app.main", "message": "🛑 关闭应用程序...", "module": "main", "function": "lifespan", "line": 64}
{"timestamp": "2025-07-23T18:51:28.263996", "level": "INFO", "logger": "app.main", "message": "🚀 启动应用程序...", "module": "main", "function": "lifespan", "line": 53}
{"timestamp": "2025-07-23T18:51:28.263996", "level": "ERROR", "logger": "app.main", "message": "❌ 数据库表创建失败: (pymysql.err.OperationalError) (1045, \"Access denied for user 'video_user'@'localhost' (using password: YES)\")\n(Background on this error at: http://sqlalche.me/e/13/e3q8)", "module": "main", "function": "lifespan", "line": 60}
{"timestamp": "2025-07-23T18:51:45.637456", "level": "INFO", "logger": "app.main", "message": "🛑 关闭应用程序...", "module": "main", "function": "lifespan", "line": 64}
{"timestamp": "2025-07-23T18:51:50.266137", "level": "INFO", "logger": "app.main", "message": "🚀 启动应用程序...", "module": "main", "function": "lifespan", "line": 53}
{"timestamp": "2025-07-23T18:51:50.268663", "level": "ERROR", "logger": "app.main", "message": "❌ 数据库表创建失败: (pymysql.err.OperationalError) (1045, \"Access denied for user 'video_user'@'localhost' (using password: YES)\")\n(Background on this error at: http://sqlalche.me/e/13/e3q8)", "module": "main", "function": "lifespan", "line": 60}
{"timestamp": "2025-07-23T19:48:53.476696", "level": "INFO", "logger": "app.main", "message": "🛑 关闭应用程序...", "module": "main", "function": "lifespan", "line": 64}
