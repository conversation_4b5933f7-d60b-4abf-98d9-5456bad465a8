import pyotp

from app.core.config import settings


def generate_otp_secret() -> str:
    """生成一个新的OTP密钥"""
    return pyotp.random_base32()


def get_otp_provisioning_uri(email: str, secret: str) -> str:
    """
    为给定的用户和密钥生成OTP供应URI，用于生成二维码
    """
    return pyotp.totp.TOTP(secret).provisioning_uri(
        name=email, issuer_name=settings.PROJECT_NAME
    )


def verify_otp(secret: str, otp: str) -> bool:
    """
    验证给定密钥的一次性密码(OTP)
    """
    totp = pyotp.totp.TOTP(secret)
    return totp.verify(otp)
