#!/usr/bin/env python3
"""
AI视频内容创作系统 - 视频处理API
提供视频下载、编辑、格式转换等功能的REST API接口
"""

from typing import List

from fastapi import APIRouter, BackgroundTasks, HTTPException
from pydantic import BaseModel

from ..services.video_processing_service import (
    ProcessingTask,
    VideoFormat,
    VideoInfo,
    VideoQuality,
    video_processing_service,
)

router = APIRouter(prefix="/api/v1/video", tags=["视频处理"])


class VideoDownloadRequest(BaseModel):
    """视频下载请求"""

    url: str
    quality: VideoQuality = VideoQuality.HIGH
    format: VideoFormat = VideoFormat.MP4


class TaskResponse(BaseModel):
    """任务响应"""

    task_id: str
    message: str


@router.get("/info")
async def get_video_info(url: str) -> VideoInfo:
    """获取视频信息"""
    try:
        video_info = video_processing_service.get_video_info(url)
        if not video_info:
            raise HTTPException(status_code=404, detail=f"无法获取视频信息: {url}")
        return video_info
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/download")
async def create_download_task(
    request: VideoDownloadRequest, background_tasks: BackgroundTasks
) -> TaskResponse:
    """创建视频下载任务"""
    try:
        task_id = video_processing_service.create_processing_task(
            url=request.url,
            target_format=request.format,
            target_quality=request.quality,
        )

        # TODO: 在后台任务中执行实际下载
        # background_tasks.add_task(process_video_download, task_id)

        return TaskResponse(task_id=task_id, message=f"下载任务已创建: {task_id}")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/tasks/{task_id}")
async def get_task_status(task_id: str) -> ProcessingTask:
    """获取任务状态"""
    task = video_processing_service.get_task_status(task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"任务不存在: {task_id}")
    return task


@router.get("/tasks")
async def list_all_tasks() -> List[ProcessingTask]:
    """列出所有任务"""
    return video_processing_service.list_tasks()


@router.get("/status")
async def get_service_status():
    """获取视频处理服务状态"""
    return video_processing_service.get_service_status()


@router.delete("/tasks/{task_id}")
async def delete_task(task_id: str) -> dict:
    """删除任务"""
    if task_id in video_processing_service.tasks:
        del video_processing_service.tasks[task_id]
        return {"message": f"任务已删除: {task_id}"}
    else:
        raise HTTPException(status_code=404, detail=f"任务不存在: {task_id}")


# 健康检查端点
@router.get("/health")
async def health_check():
    """健康检查"""
    status = video_processing_service.get_service_status()

    # 检查关键依赖
    health_status = "healthy"
    issues = []

    if not status["yt_dlp_available"]:
        issues.append("yt-dlp 不可用")
        health_status = "degraded"

    if not status["moviepy_available"]:
        issues.append("moviepy 不可用")
        health_status = "degraded"

    if len(issues) >= 2:
        health_status = "unhealthy"

    return {
        "status": health_status,
        "issues": issues,
        "service_info": status,
        "timestamp": "2025-07-07T12:00:00Z",
    }
