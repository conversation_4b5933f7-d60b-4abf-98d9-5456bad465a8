/**
 * 响应式布局组合式函数 - 2025年最佳实践
 * 提供移动端专用布局模式和响应式控制
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'

// 断点配置 - 基于2025年主流设备
export const BREAKPOINTS = {
  xs: 0,     // 超小屏幕
  sm: 576,   // 小屏幕 (手机)
  md: 768,   // 中等屏幕 (平板)
  lg: 992,   // 大屏幕 (桌面)
  xl: 1200,  // 超大屏幕
  xxl: 1400  // 超超大屏幕
} as const

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop'
export type BreakpointKey = keyof typeof BREAKPOINTS

// 布局模式
export type LayoutMode = 'mobile' | 'tablet' | 'desktop'

export function useResponsiveLayout() {
  // 响应式状态
  const windowWidth = ref(0)
  const windowHeight = ref(0)
  const isMobileMenuOpen = ref(false)
  const isTabletMode = ref(false)
  const isMobileMode = ref(false)
  
  // 计算当前设备类型
  const deviceType = computed<DeviceType>(() => {
    if (windowWidth.value < BREAKPOINTS.md) return 'mobile'
    if (windowWidth.value < BREAKPOINTS.lg) return 'tablet'
    return 'desktop'
  })
  
  // 计算当前断点
  const currentBreakpoint = computed<BreakpointKey>(() => {
    const width = windowWidth.value
    if (width >= BREAKPOINTS.xxl) return 'xxl'
    if (width >= BREAKPOINTS.xl) return 'xl'
    if (width >= BREAKPOINTS.lg) return 'lg'
    if (width >= BREAKPOINTS.md) return 'md'
    if (width >= BREAKPOINTS.sm) return 'sm'
    return 'xs'
  })
  
  // 计算布局模式
  const layoutMode = computed<LayoutMode>(() => {
    switch (deviceType.value) {
      case 'mobile':
        return 'mobile'
      case 'tablet':
        return 'tablet'
      case 'desktop':
      default:
        return 'desktop'
    }
  })
  
  // 是否显示汉堡菜单
  const shouldShowHamburgerMenu = computed(() => {
    return deviceType.value === 'mobile'
  })
  
  // 是否显示底部导航
  const shouldShowBottomNavigation = computed(() => {
    return deviceType.value === 'mobile'
  })
  
  // 是否显示侧边栏
  const shouldShowSidebar = computed(() => {
    return deviceType.value === 'desktop' || 
           (deviceType.value === 'tablet' && !isMobileMenuOpen.value)
  })
  
  // 侧边栏是否应该折叠
  const shouldCollapseSidebar = computed(() => {
    return deviceType.value === 'tablet'
  })
  
  // 是否使用全屏模式
  const shouldUseFullscreen = computed(() => {
    return deviceType.value === 'mobile'
  })
  
  // 导航栏高度
  const navbarHeight = computed(() => {
    switch (deviceType.value) {
      case 'mobile':
        return '56px' // 移动端标准高度
      case 'tablet':
        return '64px' // 平板端高度
      case 'desktop':
      default:
        return '72px' // 桌面端高度
    }
  })
  
  // 侧边栏宽度
  const sidebarWidth = computed(() => {
    if (deviceType.value === 'mobile') {
      return isMobileMenuOpen.value ? '280px' : '0px'
    }
    if (deviceType.value === 'tablet') {
      return shouldCollapseSidebar.value ? '72px' : '240px'
    }
    return '280px' // 桌面端默认宽度
  })
  
  // 内容区域边距
  const contentMargin = computed(() => {
    const topMargin = navbarHeight.value
    const leftMargin = shouldShowSidebar.value ? sidebarWidth.value : '0px'
    const bottomMargin = shouldShowBottomNavigation.value ? '64px' : '0px'
    
    return {
      marginTop: topMargin,
      marginLeft: deviceType.value === 'mobile' ? '0px' : leftMargin,
      marginBottom: bottomMargin,
      paddingLeft: deviceType.value === 'mobile' ? '16px' : '24px',
      paddingRight: deviceType.value === 'mobile' ? '16px' : '24px'
    }
  })
  
  // 更新窗口尺寸
  const updateWindowSize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
    
    // 自动关闭移动端菜单（当切换到桌面端时）
    if (deviceType.value !== 'mobile' && isMobileMenuOpen.value) {
      isMobileMenuOpen.value = false
    }
  }
  
  // 切换移动端菜单
  const toggleMobileMenu = () => {
    isMobileMenuOpen.value = !isMobileMenuOpen.value
    
    // 防止背景滚动
    if (isMobileMenuOpen.value) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
  }
  
  // 关闭移动端菜单
  const closeMobileMenu = () => {
    isMobileMenuOpen.value = false
    document.body.style.overflow = ''
  }
  
  // 检查是否匹配断点
  const matchesBreakpoint = (breakpoint: BreakpointKey, direction: 'up' | 'down' = 'up') => {
    const breakpointValue = BREAKPOINTS[breakpoint]
    if (direction === 'up') {
      return windowWidth.value >= breakpointValue
    } else {
      return windowWidth.value < breakpointValue
    }
  }
  
  // 获取CSS媒体查询字符串
  const getMediaQuery = (breakpoint: BreakpointKey, direction: 'up' | 'down' = 'up') => {
    const breakpointValue = BREAKPOINTS[breakpoint]
    if (direction === 'up') {
      return `(min-width: ${breakpointValue}px)`
    } else {
      return `(max-width: ${breakpointValue - 1}px)`
    }
  }
  
  // 生命周期钩子
  onMounted(() => {
    updateWindowSize()
    window.addEventListener('resize', updateWindowSize)
    
    // 监听方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(updateWindowSize, 100) // 延迟更新以确保获取正确尺寸
    })
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateWindowSize)
    window.removeEventListener('orientationchange', updateWindowSize)
    
    // 清理样式
    document.body.style.overflow = ''
  })
  
  return {
    // 状态
    windowWidth,
    windowHeight,
    isMobileMenuOpen,
    isTabletMode,
    isMobileMode,
    
    // 计算属性
    deviceType,
    currentBreakpoint,
    layoutMode,
    shouldShowHamburgerMenu,
    shouldShowBottomNavigation,
    shouldShowSidebar,
    shouldCollapseSidebar,
    shouldUseFullscreen,
    navbarHeight,
    sidebarWidth,
    contentMargin,
    
    // 方法
    toggleMobileMenu,
    closeMobileMenu,
    matchesBreakpoint,
    getMediaQuery,
    updateWindowSize,
    
    // 常量
    BREAKPOINTS
  }
}
