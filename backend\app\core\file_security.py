"""文件上传安全验证模块
提供全面的文件安全检查和处理
"""

import os
import time
import hashlib
import mimetypes
import magic
import logging
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
from fastapi import UploadFile, HTTPException
from PIL import Image
import tempfile
import subprocess

from app.core.config import settings

logger = logging.getLogger(__name__)


class FileSecurityError(Exception):
    """文件安全错误"""
    pass


class FileValidator:
    """文件安全验证器"""
    
    # 允许的文件类型
    ALLOWED_VIDEO_EXTENSIONS = {
        '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v'
    }
    
    ALLOWED_IMAGE_EXTENSIONS = {
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'
    }
    
    ALLOWED_AUDIO_EXTENSIONS = {
        '.mp3', '.wav', '.aac', '.ogg', '.m4a', '.flac'
    }
    
    # 允许的MIME类型
    ALLOWED_VIDEO_MIMES = {
        'video/mp4', 'video/avi', 'video/quicktime', 'video/x-msvideo',
        'video/x-flv', 'video/webm', 'video/x-matroska'
    }
    
    ALLOWED_IMAGE_MIMES = {
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp',
        'image/webp', 'image/tiff'
    }
    
    ALLOWED_AUDIO_MIMES = {
        'audio/mpeg', 'audio/wav', 'audio/aac', 'audio/ogg',
        'audio/mp4', 'audio/flac'
    }
    
    # 危险文件扩展名
    DANGEROUS_EXTENSIONS = {
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
        '.jar', '.php', '.asp', '.aspx', '.jsp', '.py', '.pl', '.sh',
        '.ps1', '.msi', '.deb', '.rpm', '.dmg', '.app'
    }
    
    # 文件大小限制（字节）
    MAX_FILE_SIZES = {
        'video': 500 * 1024 * 1024,  # 500MB
        'image': 10 * 1024 * 1024,   # 10MB
        'audio': 50 * 1024 * 1024,   # 50MB
    }
    
    def __init__(self):
        self.magic_mime = None
        try:
            self.magic_mime = magic.Magic(mime=True)
        except Exception as e:
            logger.warning(f"python-magic初始化失败: {e}")
    
    def _get_file_type(self, extension: str) -> str:
        """根据扩展名获取文件类型"""
        if extension in self.ALLOWED_VIDEO_EXTENSIONS:
            return 'video'
        elif extension in self.ALLOWED_IMAGE_EXTENSIONS:
            return 'image'
        elif extension in self.ALLOWED_AUDIO_EXTENSIONS:
            return 'audio'
        else:
            return 'unknown'
    
    def _validate_extension(self, filename: str) -> Tuple[str, str]:
        """验证文件扩展名"""
        file_path = Path(filename)
        extension = file_path.suffix.lower()
        
        # 检查危险扩展名
        if extension in self.DANGEROUS_EXTENSIONS:
            raise FileSecurityError(f"危险的文件类型: {extension}")
        
        # 检查是否为允许的扩展名
        file_type = self._get_file_type(extension)
        if file_type == 'unknown':
            raise FileSecurityError(f"不支持的文件类型: {extension}")
        
        return extension, file_type
    
    def _validate_mime_type(self, file_content: bytes, expected_type: str) -> str:
        """验证MIME类型"""
        # 使用python-magic检测MIME类型
        if self.magic_mime:
            try:
                detected_mime = self.magic_mime.from_buffer(file_content)
            except Exception as e:
                logger.warning(f"MIME类型检测失败: {e}")
                detected_mime = None
        else:
            detected_mime = None
        
        # 验证MIME类型
        allowed_mimes = set()
        if expected_type == 'video':
            allowed_mimes = self.ALLOWED_VIDEO_MIMES
        elif expected_type == 'image':
            allowed_mimes = self.ALLOWED_IMAGE_MIMES
        elif expected_type == 'audio':
            allowed_mimes = self.ALLOWED_AUDIO_MIMES
        
        if detected_mime and detected_mime not in allowed_mimes:
            raise FileSecurityError(f"MIME类型不匹配: 检测到 {detected_mime}, 期望 {expected_type}")
        
        return detected_mime or f"{expected_type}/unknown"
    
    def _validate_file_size(self, file_size: int, file_type: str) -> None:
        """验证文件大小"""
        max_size = self.MAX_FILE_SIZES.get(file_type, 1024 * 1024)  # 默认1MB
        
        if file_size > max_size:
            raise FileSecurityError(
                f"文件过大: {file_size} bytes, 最大允许: {max_size} bytes"
            )
    
    def _validate_image_content(self, file_content: bytes) -> Dict[str, Any]:
        """验证图片内容"""
        try:
            with tempfile.NamedTemporaryFile() as temp_file:
                temp_file.write(file_content)
                temp_file.flush()
                
                # 使用PIL验证图片
                with Image.open(temp_file.name) as img:
                    # 检查图片尺寸
                    width, height = img.size
                    if width > 4096 or height > 4096:
                        raise FileSecurityError(f"图片尺寸过大: {width}x{height}")
                    
                    # 检查图片模式
                    if img.mode not in ['RGB', 'RGBA', 'L', 'P']:
                        raise FileSecurityError(f"不支持的图片模式: {img.mode}")
                    
                    return {
                        'width': width,
                        'height': height,
                        'mode': img.mode,
                        'format': img.format
                    }
        except Exception as e:
            if isinstance(e, FileSecurityError):
                raise
            raise FileSecurityError(f"图片验证失败: {str(e)}")
    
    def _validate_video_content(self, file_path: str) -> Dict[str, Any]:
        """验证视频内容"""
        try:
            # 使用ffprobe获取视频信息
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', file_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                raise FileSecurityError("视频文件损坏或格式不支持")
            
            import json
            info = json.loads(result.stdout)
            
            # 检查视频流
            video_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'video']
            if not video_streams:
                raise FileSecurityError("文件中没有视频流")
            
            video_stream = video_streams[0]
            
            # 检查视频参数
            width = int(video_stream.get('width', 0))
            height = int(video_stream.get('height', 0))
            duration = float(info.get('format', {}).get('duration', 0))
            
            if width > 3840 or height > 2160:  # 4K限制
                raise FileSecurityError(f"视频分辨率过高: {width}x{height}")
            
            if duration > 3600:  # 1小时限制
                raise FileSecurityError(f"视频时长过长: {duration}秒")
            
            return {
                'width': width,
                'height': height,
                'duration': duration,
                'codec': video_stream.get('codec_name'),
                'bitrate': info.get('format', {}).get('bit_rate')
            }
            
        except subprocess.TimeoutExpired:
            raise FileSecurityError("视频分析超时")
        except Exception as e:
            if isinstance(e, FileSecurityError):
                raise
            logger.warning(f"视频验证失败: {e}")
            return {}
    
    def _scan_for_malware(self, file_content: bytes) -> bool:
        """恶意软件扫描（简单实现）"""
        # 检查常见的恶意软件特征
        suspicious_patterns = [
            b'<script',
            b'javascript:',
            b'vbscript:',
            b'onload=',
            b'onerror=',
            b'<?php',
            b'<%',
            b'eval(',
            b'exec(',
            b'system(',
            b'shell_exec('
        ]
        
        content_lower = file_content.lower()
        for pattern in suspicious_patterns:
            if pattern in content_lower:
                logger.warning(f"检测到可疑内容: {pattern}")
                return False
        
        return True
    
    def _generate_safe_filename(self, original_filename: str) -> str:
        """生成安全的文件名"""
        # 移除路径分隔符和特殊字符
        safe_chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-_'
        filename = ''.join(c for c in original_filename if c in safe_chars)
        
        # 限制文件名长度
        if len(filename) > 100:
            name, ext = os.path.splitext(filename)
            filename = name[:95] + ext
        
        # 确保文件名不为空
        if not filename:
            filename = 'unnamed_file'
        
        return filename
    
    async def validate_upload_file(self, upload_file: UploadFile) -> Dict[str, Any]:
        """验证上传文件
        
        Args:
            upload_file: FastAPI上传文件对象
        
        Returns:
            Dict: 验证结果和文件信息
        
        Raises:
            FileSecurityError: 文件验证失败
        """
        try:
            # 读取文件内容
            file_content = await upload_file.read()
            await upload_file.seek(0)  # 重置文件指针
            
            # 基本验证
            if not file_content:
                raise FileSecurityError("文件为空")
            
            # 验证文件扩展名
            extension, file_type = self._validate_extension(upload_file.filename)
            
            # 验证文件大小
            file_size = len(file_content)
            self._validate_file_size(file_size, file_type)
            
            # 验证MIME类型
            detected_mime = self._validate_mime_type(file_content, file_type)
            
            # 恶意软件扫描
            if not self._scan_for_malware(file_content):
                raise FileSecurityError("文件包含可疑内容")
            
            # 生成安全文件名
            safe_filename = self._generate_safe_filename(upload_file.filename)
            
            # 计算文件哈希
            file_hash = hashlib.sha256(file_content).hexdigest()
            
            # 内容特定验证
            content_info = {}
            if file_type == 'image':
                content_info = self._validate_image_content(file_content)
            elif file_type == 'video':
                # 对于视频，需要先保存到临时文件
                with tempfile.NamedTemporaryFile(suffix=extension, delete=False) as temp_file:
                    temp_file.write(file_content)
                    temp_file.flush()
                    try:
                        content_info = self._validate_video_content(temp_file.name)
                    finally:
                        os.unlink(temp_file.name)
            
            return {
                'original_filename': upload_file.filename,
                'safe_filename': safe_filename,
                'file_type': file_type,
                'extension': extension,
                'mime_type': detected_mime,
                'file_size': file_size,
                'file_hash': file_hash,
                'content_info': content_info,
                'validation_passed': True
            }
            
        except FileSecurityError:
            raise
        except Exception as e:
            logger.error(f"文件验证过程中发生错误: {e}")
            raise FileSecurityError(f"文件验证失败: {str(e)}")


class SecureFileHandler:
    """安全文件处理器"""
    
    def __init__(self, upload_dir: str = None):
        self.upload_dir = Path(upload_dir or settings.UPLOAD_DIR)
        self.validator = FileValidator()
        
        # 确保上传目录存在
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        for subdir in ['videos', 'images', 'audio', 'temp']:
            (self.upload_dir / subdir).mkdir(exist_ok=True)
    
    def _get_upload_path(self, file_type: str, safe_filename: str) -> Path:
        """获取上传路径"""
        # 根据文件类型选择子目录
        subdir = self.upload_dir / file_type
        
        # 生成唯一文件名（避免冲突）
        timestamp = int(time.time())
        name, ext = os.path.splitext(safe_filename)
        unique_filename = f"{name}_{timestamp}{ext}"
        
        return subdir / unique_filename
    
    async def save_upload_file(self, upload_file: UploadFile) -> Dict[str, Any]:
        """安全保存上传文件
        
        Args:
            upload_file: FastAPI上传文件对象
        
        Returns:
            Dict: 保存结果信息
        
        Raises:
            FileSecurityError: 文件保存失败
        """
        try:
            # 验证文件
            validation_result = await self.validator.validate_upload_file(upload_file)
            
            # 获取保存路径
            file_path = self._get_upload_path(
                validation_result['file_type'],
                validation_result['safe_filename']
            )
            
            # 保存文件
            with open(file_path, 'wb') as f:
                content = await upload_file.read()
                f.write(content)
            
            # 设置文件权限（只读）
            os.chmod(file_path, 0o644)
            
            logger.info(f"文件安全保存: {file_path}")
            
            return {
                **validation_result,
                'file_path': str(file_path),
                'relative_path': str(file_path.relative_to(self.upload_dir)),
                'saved_at': time.time()
            }
            
        except Exception as e:
            logger.error(f"文件保存失败: {e}")
            raise FileSecurityError(f"文件保存失败: {str(e)}")
    
    def delete_file(self, file_path: str) -> bool:
        """安全删除文件"""
        try:
            path = Path(file_path)
            
            # 确保文件在上传目录内
            if not str(path.resolve()).startswith(str(self.upload_dir.resolve())):
                raise FileSecurityError("文件路径不安全")
            
            if path.exists():
                path.unlink()
                logger.info(f"文件已删除: {file_path}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"文件删除失败: {e}")
            return False


# 创建全局文件处理器实例
file_handler = SecureFileHandler()


# 便捷函数
async def validate_and_save_file(upload_file: UploadFile) -> Dict[str, Any]:
    """验证并保存上传文件"""
    return await file_handler.save_upload_file(upload_file)


def is_safe_filename(filename: str) -> bool:
    """检查文件名是否安全"""
    try:
        validator = FileValidator()
        validator._validate_extension(filename)
        return True
    except FileSecurityError:
        return False