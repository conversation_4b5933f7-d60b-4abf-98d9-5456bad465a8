"""
数据库模型测试
"""

from sqlalchemy.orm import Session

from app.models import AuditLog, Content, Project, User


class TestUserModel:
    """用户模型测试"""

    def test_create_user(self, db: Session):
        """测试创建用户"""
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password_123",
            full_name="Test User",
        )
        db.add(user)
        db.commit()
        db.refresh(user)

        assert user.id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.is_active is True
        assert user.is_superuser is False
        assert user.created_at is not None

    def test_user_repr(self, db: Session):
        """测试用户字符串表示"""
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password_123",
        )
        db.add(user)
        db.commit()
        db.refresh(user)

        repr_str = repr(user)
        assert "testuser" in repr_str
        assert str(user.id) in repr_str


class TestProjectModel:
    """项目模型测试"""

    def test_create_project(self, db: Session):
        """测试创建项目"""
        project = Project(
            name="Test Project",
            description="A test project",
            owner_id=1,
            status="active",
        )
        db.add(project)
        db.commit()
        db.refresh(project)

        assert project.id is not None
        assert project.name == "Test Project"
        assert project.description == "A test project"
        assert project.owner_id == 1
        assert project.status == "active"
        assert project.created_at is not None

    def test_project_repr(self, db: Session):
        """测试项目字符串表示"""
        project = Project(name="Test Project", description="A test project", owner_id=1)
        db.add(project)
        db.commit()
        db.refresh(project)

        repr_str = repr(project)
        assert "Test Project" in repr_str
        assert str(project.id) in repr_str


class TestContentModel:
    """内容模型测试"""

    def test_create_content(self, db: Session):
        """测试创建内容"""
        content = Content(
            title="Test Content",
            content_type="text",
            original_text="Original text content",
            creator_id=1,
            project_id=1,
            status="draft",
        )
        db.add(content)
        db.commit()
        db.refresh(content)

        assert content.id is not None
        assert content.title == "Test Content"
        assert content.content_type == "text"
        assert content.original_text == "Original text content"
        assert content.creator_id == 1
        assert content.project_id == 1
        assert content.status == "draft"
        assert content.created_at is not None

    def test_content_compliance_score(self, db: Session):
        """测试内容合规分数"""
        content = Content(
            title="Test Content",
            content_type="text",
            creator_id=1,
            compliance_score=0.85,
        )
        db.add(content)
        db.commit()
        db.refresh(content)

        assert content.compliance_score == 0.85

    def test_content_repr(self, db: Session):
        """测试内容字符串表示"""
        content = Content(title="Test Content", content_type="text", creator_id=1)
        db.add(content)
        db.commit()
        db.refresh(content)

        repr_str = repr(content)
        assert "Test Content" in repr_str
        assert "text" in repr_str
        assert str(content.id) in repr_str


class TestAuditLogModel:
    """审计日志模型测试"""

    def test_create_audit_log(self, db: Session):
        """测试创建审计日志"""
        audit_log = AuditLog(
            user_id=1,
            action="CREATE",
            resource_type="user",
            resource_id="123",
            description="Created a new user",
            ip_address="***********",
        )
        db.add(audit_log)
        db.commit()
        db.refresh(audit_log)

        assert audit_log.id is not None
        assert audit_log.user_id == 1
        assert audit_log.action == "CREATE"
        assert audit_log.resource_type == "user"
        assert audit_log.resource_id == "123"
        assert audit_log.description == "Created a new user"
        assert audit_log.ip_address == "***********"
        assert audit_log.created_at is not None

    def test_audit_log_repr(self, db: Session):
        """测试审计日志字符串表示"""
        audit_log = AuditLog(
            action="CREATE", resource_type="user", description="Test action"
        )
        db.add(audit_log)
        db.commit()
        db.refresh(audit_log)

        repr_str = repr(audit_log)
        assert "CREATE" in repr_str
        assert "user" in repr_str
        assert str(audit_log.id) in repr_str


class TestModelRelationships:
    """模型关系测试"""

    def test_user_project_relationship(self, db: Session):
        """测试用户和项目的关系"""
        # 创建用户
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password_123",
        )
        db.add(user)
        db.commit()
        db.refresh(user)

        # 创建项目
        project = Project(
            name="Test Project", description="A test project", owner_id=user.id
        )
        db.add(project)
        db.commit()
        db.refresh(project)

        assert project.owner_id == user.id

    def test_project_content_relationship(self, db: Session):
        """测试项目和内容的关系"""
        # 创建项目
        project = Project(name="Test Project", description="A test project", owner_id=1)
        db.add(project)
        db.commit()
        db.refresh(project)

        # 创建内容
        content = Content(
            title="Test Content",
            content_type="text",
            creator_id=1,
            project_id=project.id,
        )
        db.add(content)
        db.commit()
        db.refresh(content)

        assert content.project_id == project.id
