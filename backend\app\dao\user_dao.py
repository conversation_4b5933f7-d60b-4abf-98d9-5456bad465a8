#!/usr/bin/env python3
"""
数据访问层 (DAO) - 用户管理
提供用户相关的数据库操作
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import desc, func, or_
from sqlalchemy.orm import Session

from app.models.database_models import User, UserRole, UserStatus


class UserDAO:
    """用户数据访问对象"""

    def __init__(self, db: Session):
        self.db = db

    def create_user(self, user_data: Dict[str, Any]) -> User:
        """创建新用户"""
        user = User(**user_data)
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user

    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """根据用户ID获取用户"""
        return self.db.query(User).filter(User.user_id == user_id).first()

    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return self.db.query(User).filter(User.username == username).first()

    def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return self.db.query(User).filter(User.email == email).first()

    def get_user_by_username_or_email(self, identifier: str) -> Optional[User]:
        """根据用户名或邮箱获取用户"""
        return (
            self.db.query(User)
            .filter(or_(User.username == identifier, User.email == identifier))
            .first()
        )

    def update_user(self, user_id: str, update_data: Dict[str, Any]) -> bool:
        """更新用户信息"""
        result = self.db.query(User).filter(User.user_id == user_id).update(update_data)
        self.db.commit()
        return result > 0

    def update_user_login_time(self, user_id: str) -> bool:
        """更新用户登录时间"""
        return self.update_user(user_id, {"last_login_at": datetime.now()})

    def update_user_stats(
        self,
        user_id: str,
        video_count: int = None,
        total_views: int = None,
        total_likes: int = None,
    ) -> bool:
        """更新用户统计信息"""
        update_data = {}
        if video_count is not None:
            update_data["video_count"] = video_count
        if total_views is not None:
            update_data["total_views"] = total_views
        if total_likes is not None:
            update_data["total_likes"] = total_likes

        if update_data:
            return self.update_user(user_id, update_data)
        return True

    def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        result = self.db.query(User).filter(User.user_id == user_id).delete()
        self.db.commit()
        return result > 0

    def get_users_by_role(self, role: UserRole) -> List[User]:
        """根据角色获取用户列表"""
        return self.db.query(User).filter(User.role == role.value).all()

    def get_users_by_status(self, status: UserStatus) -> List[User]:
        """根据状态获取用户列表"""
        return self.db.query(User).filter(User.status == status.value).all()

    def get_active_users(self) -> List[User]:
        """获取活跃用户"""
        return self.get_users_by_status(UserStatus.ACTIVE)

    def get_users_paginated(self, skip: int = 0, limit: int = 10) -> List[User]:
        """分页获取用户列表"""
        return self.db.query(User).offset(skip).limit(limit).all()

    def count_users(self) -> int:
        """获取用户总数"""
        return self.db.query(User).count()

    def count_users_by_role(self) -> Dict[str, int]:
        """按角色统计用户数量"""
        result = self.db.query(User.role, func.count(User.id)).group_by(User.role).all()
        return dict(result)

    def count_users_by_status(self) -> Dict[str, int]:
        """按状态统计用户数量"""
        result = (
            self.db.query(User.status, func.count(User.id)).group_by(User.status).all()
        )
        return dict(result)

    def search_users(self, query: str, limit: int = 10) -> List[User]:
        """搜索用户"""
        return (
            self.db.query(User)
            .filter(
                or_(
                    User.username.ilike(f"%{query}%"),
                    User.display_name.ilike(f"%{query}%"),
                    User.email.ilike(f"%{query}%"),
                )
            )
            .limit(limit)
            .all()
        )

    def get_user_stats(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        total_users = self.count_users()
        role_stats = self.count_users_by_role()
        status_stats = self.count_users_by_status()

        # 获取最近注册用户
        recent_users = (
            self.db.query(User).order_by(desc(User.created_at)).limit(5).all()
        )

        return {
            "total_users": total_users,
            "role_distribution": role_stats,
            "status_distribution": status_stats,
            "recent_users": [
                {
                    "user_id": user.user_id,
                    "username": user.username,
                    "created_at": user.created_at.isoformat(),
                }
                for user in recent_users
            ],
        }
