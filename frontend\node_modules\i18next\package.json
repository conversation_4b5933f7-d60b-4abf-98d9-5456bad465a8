{"name": "i18next", "version": "25.3.2", "description": "i18next internationalization framework", "main": "./dist/cjs/i18next.js", "module": "./dist/esm/i18next.js", "types": "./index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./index.d.mts", "default": "./dist/esm/i18next.js"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/i18next.js"}}}, "keywords": ["i18next", "internationalization", "i18n", "translation", "localization", "l10n", "globalization", "gettext"], "homepage": "https://www.i18next.com", "bugs": "https://github.com/i18next/i18next/issues", "repository": {"type": "git", "url": "https://github.com/i18next/i18next.git"}, "funding": [{"type": "individual", "url": "https://locize.com"}, {"type": "individual", "url": "https://locize.com/i18next.html"}, {"type": "individual", "url": "https://www.i18next.com/how-to/faq#i18next-is-awesome.-how-can-i-support-the-project"}], "dependencies": {"@babel/runtime": "^7.27.6"}, "peerDependencies": {"typescript": "^5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-async-generator-functions": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/register": "^7.27.1", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@types/node": "^22.15.17", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitest/coverage-v8": "^3.1.3", "babelify": "^10.0.0", "coveralls": "^3.1.1", "cpy-cli": "^5.0.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^4.3.4", "eslint-plugin-import": "^2.31.0", "gh-release": "^7.0.2", "husky": "^9.1.7", "i18next-browser-languagedetector": "^8.1.0", "i18next-fs-backend": "^2.6.0", "i18next-http-backend": "^3.0.2", "i18next-localstorage-cache": "^1.1.1", "i18next-sprintf-postprocessor": "^0.2.2", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "rollup": "^4.40.2", "sinon": "^19.0.5", "typescript": "^5.8.3", "vitest": "^3.1.3"}, "scripts": {"lint": "eslint src typescript test \"./*.{ts,mts,mjs}\"", "format": "prettier \"{,**/}*.{ts,tsx,mts,js,mjs,json,md}\" --check", "format:fix": "prettier \"{,**/}*.{ts,tsx,mts,js,mjs,json,md}\" --write", "test": "vitest --run", "test:coverage": "vitest --coverage --run", "test:runtime": "vitest --project runtime", "test:compatibility": "vitest --project compatibility", "test:typescript": "vitest --workspace vitest.workspace.typescript.mts", "test:local": "vitest --workspace vitest.workspace.local.mts", "build": "rimraf dist && rollup -c && echo '{\"type\":\"module\"}' > dist/esm/package.json && cpy \"./dist/umd/*.js\" ./", "fix_dist_package": "node -e 'console.log(`{\"type\":\"module\",\"version\":\"${process.env.npm_package_version}\"}`)' > dist/esm/package.json", "preversion": "npm run test && npm run build && git push", "postversion": "npm run fix_dist_package && git push && git push --tags && npm run release", "prepare": "husky", "release": "gh-release"}, "author": "<PERSON> <<EMAIL>> (https://github.com/jamuhl)", "license": "MIT", "lint-staged": {"*": "prettier --write --ignore-unknown"}}