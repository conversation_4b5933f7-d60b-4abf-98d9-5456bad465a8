"""Initial migration

Revision ID: c373bbffc202
Revises:
Create Date: 2025-07-05 19:40:56.286756

"""

from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "c373bbffc202"
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # #  # # commands auto generated by Alembic - please adjust! #  # #
    # #  # # end Alembic commands #  # #


def downgrade() -> None:
    """Downgrade schema."""
    # #  # # commands auto generated by Alembic - please adjust! #  # #
    # #  # # end Alembic commands #  # #
