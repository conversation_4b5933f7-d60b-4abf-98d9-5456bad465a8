{"result": [{"scriptId": "866", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/tests/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49811, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49811, "count": 3}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 577, "endOffset": 946, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 981, "endOffset": 996, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 999, "endOffset": 1016, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1019, "endOffset": 1037, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 1175, "endOffset": 1190, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 1193, "endOffset": 1210, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1213, "endOffset": 1231, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3284, "endOffset": 3354, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3458, "endOffset": 4555, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4665, "endOffset": 6533, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7131, "endOffset": 7312, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7339, "endOffset": 8381, "count": 1}], "isBlockCoverage": false}, {"functionName": "getVoices", "ranges": [{"startOffset": 7400, "endOffset": 7745, "count": 0}], "isBlockCoverage": false}, {"functionName": "speak", "ranges": [{"startOffset": 7748, "endOffset": 8170, "count": 0}], "isBlockCoverage": false}, {"functionName": "cancel", "ranges": [{"startOffset": 8173, "endOffset": 8240, "count": 0}], "isBlockCoverage": false}, {"functionName": "pause", "ranges": [{"startOffset": 8243, "endOffset": 8280, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 8283, "endOffset": 8322, "count": 0}], "isBlockCoverage": false}, {"functionName": "addEventListener", "ranges": [{"startOffset": 8325, "endOffset": 8349, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEventListener", "ranges": [{"startOffset": 8352, "endOffset": 8379, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 8633, "endOffset": 9480, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 9703, "endOffset": 11003, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTracks", "ranges": [{"startOffset": 11429, "endOffset": 11618, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAudioTracks", "ranges": [{"startOffset": 11638, "endOffset": 11833, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVideoTracks", "ranges": [{"startOffset": 11853, "endOffset": 11861, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTest", "ranges": [{"startOffset": 13502, "endOffset": 13920, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14018, "endOffset": 14043, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupTest", "ranges": [{"startOffset": 14047, "endOffset": 14164, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14264, "endOffset": 14291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14400, "endOffset": 14432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14542, "endOffset": 14576, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14678, "endOffset": 14704, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14810, "endOffset": 14840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14940, "endOffset": 14964, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15001, "endOffset": 15073, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15108, "endOffset": 15177, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "959", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/tests/unit/utils/test-helpers.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27041, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27041, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 637, "endOffset": 9811, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 680, "endOffset": 763, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 820, "endOffset": 1704, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 883, "endOffset": 1035, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1085, "endOffset": 1248, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1299, "endOffset": 1544, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1594, "endOffset": 1698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1764, "endOffset": 3109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1816, "endOffset": 2375, "count": 1}, {"startOffset": 2222, "endOffset": 2229, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2423, "endOffset": 2750, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2797, "endOffset": 3103, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3171, "endOffset": 3784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3236, "endOffset": 3778, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3841, "endOffset": 5266, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3886, "endOffset": 3948, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3995, "endOffset": 4349, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4397, "endOffset": 4745, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4791, "endOffset": 5037, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5087, "endOffset": 5260, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5328, "endOffset": 6620, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5510, "endOffset": 5572, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5621, "endOffset": 5793, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5843, "endOffset": 6014, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6065, "endOffset": 6402, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6452, "endOffset": 6614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6670, "endOffset": 8782, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6724, "endOffset": 7022, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7071, "endOffset": 7347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7395, "endOffset": 7705, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7754, "endOffset": 8068, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8119, "endOffset": 8776, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8189, "endOffset": 8768, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8828, "endOffset": 9807, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8880, "endOffset": 9161, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9209, "endOffset": 9482, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9538, "endOffset": 9801, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "960", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/utils/test-helpers.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20014, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20014, "count": 1}, {"startOffset": 6388, "endOffset": 6469, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1454, "endOffset": 1480, "count": 7}], "isBlockCoverage": true}, {"functionName": "isTestEnvironment", "ranges": [{"startOffset": 1484, "endOffset": 1969, "count": 22}, {"startOffset": 1704, "endOffset": 1777, "count": 0}, {"startOffset": 1778, "endOffset": 1874, "count": 0}, {"startOffset": 1875, "endOffset": 1962, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2075, "endOffset": 2108, "count": 4}], "isBlockCoverage": true}, {"functionName": "setupTestEnvironment", "ranges": [{"startOffset": 2112, "endOffset": 2779, "count": 5}, {"startOffset": 2201, "endOffset": 2208, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2888, "endOffset": 2924, "count": 4}], "isBlockCoverage": true}, {"functionName": "cleanupTestEnvironment", "ranges": [{"startOffset": 2928, "endOffset": 3260, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3371, "endOffset": 3409, "count": 0}], "isBlockCoverage": false}, {"functionName": "mockAuthState", "ranges": [{"startOffset": 3413, "endOffset": 3835, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3937, "endOffset": 3966, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasTestPermission", "ranges": [{"startOffset": 3970, "endOffset": 4205, "count": 9}, {"startOffset": 4059, "endOffset": 4072, "count": 0}, {"startOffset": 4158, "endOffset": 4202, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4311, "endOffset": 4344, "count": 9}], "isBlockCoverage": true}, {"functionName": "mockApiResponse", "ranges": [{"startOffset": 4348, "endOffset": 4481, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4585, "endOffset": 4616, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldBypassRouteGuard", "ranges": [{"startOffset": 4620, "endOffset": 5424, "count": 7}, {"startOffset": 4686, "endOffset": 4699, "count": 0}, {"startOffset": 4797, "endOffset": 4806, "count": 0}, {"startOffset": 4906, "endOffset": 4919, "count": 6}, {"startOffset": 4920, "endOffset": 4925, "count": 3}, {"startOffset": 4958, "endOffset": 4965, "count": 6}, {"startOffset": 4966, "endOffset": 4971, "count": 3}, {"startOffset": 5061, "endOffset": 5122, "count": 0}, {"startOffset": 5124, "endOffset": 5423, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5184, "endOffset": 5229, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5535, "endOffset": 5573, "count": 7}], "isBlockCoverage": true}, {"functionName": "initTestEnvironment", "ranges": [{"startOffset": 5577, "endOffset": 6142, "count": 1}, {"startOffset": 5638, "endOffset": 5645, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5823, "endOffset": 5867, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6250, "endOffset": 6285, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6421, "endOffset": 6463, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "961", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/stores/auth.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27244, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27244, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 506, "endOffset": 7582, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7685, "endOffset": 7713, "count": 0}], "isBlockCoverage": false}]}]}