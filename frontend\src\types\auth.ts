/**
 * 认证相关类型定义 - 2025年最佳实践
 */

// 用户信息接口
export interface User {
  id: string
  username: string
  email?: string
  role?: string
  avatar?: string
  permissions?: string[]
  createdAt?: string
  lastLoginAt?: string
  lastLoginTime?: number
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
  userType?: 'user' | 'admin'
}

// 登录响应接口
export interface LoginResponse {
  success: boolean
  message: string
  data?: {
    user: User
    token: string
    refreshToken: string
    expiresAt: string
  }
}

// 认证状态接口
export interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  loginAttempts: number
  lastLoginAttempt: number
}

// 权限检查接口
export interface PermissionCheck {
  hasPermission: (permission: string) => boolean
  hasRole: (role: string) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  hasAllPermissions: (permissions: string[]) => boolean
}

// 用户类型
export type UserRole = 'user' | 'admin' | 'developer'
export type UserType = 'user' | 'admin'

// 认证错误类型
export interface AuthError {
  code: string
  message: string
  details?: any
}

// 导出所有类型
export type {
  User as default,
  LoginRequest,
  LoginResponse,
  AuthState,
  PermissionCheck,
  UserRole,
  UserType,
  AuthError
}
