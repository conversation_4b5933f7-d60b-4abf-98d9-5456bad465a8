#!/usr/bin/env python3
"""
简单的测试脚本
"""

import os
import sys
from pathlib import Path

print("测试开始...")
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")

# 尝试导入我们的服务
try:
    current_dir = Path(__file__).parent
    backend_dir = current_dir.parent
    sys.path.append(str(backend_dir))
    print(f"已添加路径: {backend_dir}")

    # 重新加载模块以确保最新修改生效
    import importlib

    import app.services.open_source_integration_service

    importlib.reload(app.services.open_source_integration_service)

    from app.services.open_source_integration_service import (
        OpenSourceIntegrationService,
    )

    service = OpenSourceIntegrationService()
    print("✅ 服务导入成功!")

    # 测试基本功能
    projects = service.get_all_projects()
    print(f"📦 注册项目数量: {len(projects)}")

    for project_name, project in list(projects.items())[:3]:  # 显示前3个项目
        print(f"  - {project.name}: {project.project_type.value}")

except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback

    traceback.print_exc()

print("测试完成!")
