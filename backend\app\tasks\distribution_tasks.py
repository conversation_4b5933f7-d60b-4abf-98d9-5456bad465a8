"""
分发任务
"""

import json
import time
from typing import Any, Dict, List

from celery import current_task
from sqlalchemy import func

from app.core.celery_app import celery_app
from app.core.database import SessionLocal
from app.models import Content, Distribution


@celery_app.task(bind=True)
def distribute_content_to_platform(
    self, distribution_id: int, platform: str, content_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    分发内容到指定平台
    """
    try:
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "message": "开始内容分发"},
        )

        # 获取数据库会话
        db = SessionLocal()
        try:
            distribution = (
                db.query(Distribution)
                .filter(Distribution.id == distribution_id)
                .first()
            )

            if not distribution:
                raise Exception(f"Distribution with id {distribution_id} not found")

            # 分发流程步骤
            steps = [
                "准备分发数据",
                "连接平台API",
                "上传媒体文件",
                "发布内容",
                "获取发布结果",
            ]

            platform_result = {}

            for i, step in enumerate(steps):
                current_task.update_state(
                    state="PROGRESS",
                    meta={
                        "current": (i + 1) * 20,
                        "total": 100,
                        "message": f"正在进行: {step}",
                    },
                )

                # 模拟处理时间
                time.sleep(1)

                # 模拟不同平台的处理逻辑
                if step == "发布内容":
                    if platform == "douyin":
                        platform_result = {
                            "platform_id": f"dy_{int(time.time())}",
                            "platform_url": (
                                f"https://douyin.com/video/{int(time.time())}"
                            ),
                            "video_id": (
                                f"douyin_{distribution_id}_{int(time.time())}"
                            ),
                        }
                    elif platform == "xiaohongshu":
                        platform_result = {
                            "platform_id": f"xhs_{int(time.time())}",
                            "platform_url": (
                                f"https://xiaohongshu.com/discovery/item/"
                                f"{int(time.time())}"
                            ),
                            "note_id": (f"xhs_{distribution_id}_{int(time.time())}"),
                        }
                    elif platform == "bilibili":
                        platform_result = {
                            "platform_id": f"bv_{int(time.time())}",
                            "platform_url": (
                                f"https://bilibili.com/video/" f"BV{int(time.time())}"
                            ),
                            "bvid": f"BV{distribution_id}{int(time.time())}",
                        }
                    else:
                        platform_result = {
                            "platform_id": f"{platform}_{int(time.time())}",
                            "platform_url": (
                                f"https://{platform}.com/post/" f"{int(time.time())}"
                            ),
                            "post_id": (
                                f"{platform}_{distribution_id}_" f"{int(time.time())}"
                            ),
                        }

            # 更新分发记录
            distribution.status = "completed"
            distribution.published_at = func.now()
            distribution.result_data = json.dumps(platform_result, ensure_ascii=False)

            db.commit()

            return {
                "distribution_id": distribution_id,
                "platform": platform,
                "status": "completed",
                "platform_result": platform_result,
                "message": f"内容已成功分发到{platform}",
            }

        finally:
            db.close()

    except Exception as e:
        # 更新失败状态
        try:
            db = SessionLocal()
            try:
                distribution = (
                    db.query(Distribution)
                    .filter(Distribution.id == distribution_id)
                    .first()
                )
                if distribution:
                    distribution.status = "failed"
                    distribution.error_message = str(e)
                    db.commit()
            finally:
                db.close()
        except Exception:
            pass

        current_task.update_state(state="FAILURE", meta={"error": str(e)})
        raise


@celery_app.task(bind=True)
def batch_distribute_content(
    self, content_id: int, platforms: List[str], user_id: int
) -> Dict[str, Any]:
    """
    批量分发内容到多个平台
    """
    try:
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "message": "开始批量分发"},
        )

        # 获取数据库会话
        db = SessionLocal()
        try:
            content = db.query(Content).filter(Content.id == content_id).first()

            if not content:
                raise Exception(f"Content with id {content_id} not found")

            distribution_results = []
            total_platforms = len(platforms)

            for i, platform in enumerate(platforms):
                current_task.update_state(
                    state="PROGRESS",
                    meta={
                        "current": int((i / total_platforms) * 100),
                        "total": 100,
                        "message": f"正在分发到{platform}",
                    },
                )

                # 创建分发记录
                distribution = Distribution(
                    content_id=content_id,
                    platform=platform,
                    creator_id=user_id,
                    title=content.title,
                    description=f"分发到{platform}的内容",
                    status="processing",
                )

                db.add(distribution)
                db.commit()
                db.refresh(distribution)

                # 调用单个平台分发任务
                result = distribute_content_to_platform.delay(
                    distribution.id,
                    platform,
                    {
                        "title": content.title,
                        "content": content.rewritten_text or content.original_text,
                        "media_files": json.loads(content.media_files or "[]"),
                    },
                )

                distribution_results.append(
                    {
                        "platform": platform,
                        "distribution_id": distribution.id,
                        "task_id": result.id,
                    }
                )

            return {
                "content_id": content_id,
                "platforms": platforms,
                "distributions": distribution_results,
                "message": f"内容已开始分发到{len(platforms)}个平台",
            }

        finally:
            db.close()

    except Exception as e:
        current_task.update_state(state="FAILURE", meta={"error": str(e)})
        raise


@celery_app.task(bind=True)
def schedule_content_distribution(
    self, distribution_id: int, scheduled_time: str
) -> Dict[str, Any]:
    """
    定时分发内容
    """
    try:
        # 这里应该实现定时分发逻辑
        # 可以使用Celery的ETA功能
        from datetime import datetime

        current_task.update_state(
            state="PROGRESS",
            meta={"current": 50, "total": 100, "message": "设置定时分发"},
        )

        # 获取数据库会话
        db = SessionLocal()
        try:
            distribution = (
                db.query(Distribution)
                .filter(Distribution.id == distribution_id)
                .first()
            )

            if not distribution:
                raise Exception(f"Distribution with id {distribution_id} not found")

            # 解析定时时间
            scheduled_dt = datetime.fromisoformat(scheduled_time)

            # 更新分发记录
            distribution.scheduled_time = scheduled_dt
            distribution.status = "scheduled"

            db.commit()

            return {
                "distribution_id": distribution_id,
                "scheduled_time": scheduled_time,
                "message": "定时分发已设置",
            }

        finally:
            db.close()

    except Exception as e:
        current_task.update_state(state="FAILURE", meta={"error": str(e)})
        raise
