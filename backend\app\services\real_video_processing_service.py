#!/usr/bin/env python3
"""
真实视频处理服务 - 基于FFmpeg
实现视频转码、剪辑、压缩等功能
"""

import asyncio
import logging
import os
import subprocess
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

try:
    import ffmpeg

    FFMPEG_AVAILABLE = True
except ImportError:
    FFMPEG_AVAILABLE = False

from enum import Enum

from pydantic import BaseModel


class VideoFormat(str, Enum):
    """支持的视频格式"""

    MP4 = "mp4"
    AVI = "avi"
    MOV = "mov"
    MKV = "mkv"
    WEBM = "webm"
    FLV = "flv"


class VideoResolution(str, Enum):
    """视频分辨率"""

    SD_480P = "854x480"
    HD_720P = "1280x720"
    FHD_1080P = "1920x1080"
    QHD_1440P = "2560x1440"
    UHD_4K = "3840x2160"


class VideoCodec(str, Enum):
    """视频编码器"""

    H264 = "libx264"
    H265 = "libx265"
    VP9 = "libvpx-vp9"
    AV1 = "libaom-av1"


class AudioCodec(str, Enum):
    """音频编码器"""

    AAC = "aac"
    MP3 = "libmp3lame"
    OPUS = "libopus"
    VORBIS = "libvorbis"


class VideoProcessingTask(BaseModel):
    """视频处理任务"""

    task_id: str
    input_file: str
    output_file: str
    operation: str
    parameters: Dict[str, Any]
    status: str = "pending"
    progress: float = 0.0
    created_at: datetime = datetime.now()
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


class RealVideoProcessingService:
    """真实视频处理服务"""

    def __init__(
        self, working_dir: str = "./video_processing", temp_dir: str = "./temp"
    ):
        """
        初始化视频处理服务

        Args:
            working_dir: 工作目录
            temp_dir: 临时文件目录
        """
        self.logger = logging.getLogger(__name__)

        # 设置目录
        self.working_dir = Path(working_dir)
        self.temp_dir = Path(temp_dir)
        self.working_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)

        # 任务管理
        self.tasks: Dict[str, VideoProcessingTask] = {}

        # 检查FFmpeg可用性
        self._check_ffmpeg()

    def _check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"], capture_output=True, text=True
            )
            if result.returncode == 0:
                self.logger.info("FFmpeg 可用")
                return True
            else:
                self.logger.error("FFmpeg 不可用")
                return False
        except FileNotFoundError:
            self.logger.error("FFmpeg 未安装或不在PATH中")
            return False

    async def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频信息

        Args:
            video_path: 视频文件路径

        Returns:
            视频信息字典
        """
        try:
            if not FFMPEG_AVAILABLE:
                raise RuntimeError("ffmpeg-python 不可用")

            probe = ffmpeg.probe(video_path)

            # 获取视频流信息
            video_stream = next(
                (
                    stream
                    for stream in probe["streams"]
                    if stream["codec_type"] == "video"
                ),
                None,
            )

            # 获取音频流信息
            audio_stream = next(
                (
                    stream
                    for stream in probe["streams"]
                    if stream["codec_type"] == "audio"
                ),
                None,
            )

            info = {
                "filename": os.path.basename(video_path),
                "format": probe["format"]["format_name"],
                "duration": float(probe["format"].get("duration", 0)),
                "size": int(probe["format"].get("size", 0)),
                "bit_rate": int(probe["format"].get("bit_rate", 0)),
            }

            if video_stream:
                info.update(
                    {
                        "width": int(video_stream.get("width", 0)),
                        "height": int(video_stream.get("height", 0)),
                        "video_codec": video_stream.get("codec_name"),
                        "fps": self._parse_fps(video_stream.get("r_frame_rate")),
                        "video_bitrate": int(video_stream.get("bit_rate", 0)),
                    }
                )

            if audio_stream:
                info.update(
                    {
                        "audio_codec": audio_stream.get("codec_name"),
                        "sample_rate": int(audio_stream.get("sample_rate", 0)),
                        "channels": int(audio_stream.get("channels", 0)),
                        "audio_bitrate": int(audio_stream.get("bit_rate", 0)),
                    }
                )

            return info

        except Exception as e:
            self.logger.error(f"获取视频信息失败: {e}")
            raise

    def _parse_fps(self, fps_string: str) -> float:
        """解析帧率字符串"""
        try:
            if "/" in fps_string:
                num, den = fps_string.split("/")
                return float(num) / float(den)
            return float(fps_string)
        except (ValueError, ZeroDivisionError):
            return 0.0

    async def convert_format(
        self,
        input_path: str,
        output_path: str,
        target_format: VideoFormat,
        video_codec: VideoCodec = VideoCodec.H264,
        audio_codec: AudioCodec = AudioCodec.AAC,
        quality: str = "medium",
    ) -> str:
        """
        转换视频格式

        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            target_format: 目标格式
            video_codec: 视频编码器
            audio_codec: 音频编码器
            quality: 质量等级 (high/medium/low)

        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())

        # 创建任务
        task = VideoProcessingTask(
            task_id=task_id,
            input_file=input_path,
            output_file=output_path,
            operation="format_conversion",
            parameters={
                "target_format": target_format.value,
                "video_codec": video_codec.value,
                "audio_codec": audio_codec.value,
                "quality": quality,
            },
        )

        self.tasks[task_id] = task

        # 异步执行转换
        asyncio.create_task(self._execute_format_conversion(task))

        return task_id

    async def _execute_format_conversion(self, task: VideoProcessingTask):
        """执行格式转换"""
        try:
            task.status = "running"
            task.started_at = datetime.now()

            input_path = task.input_file
            output_path = task.output_file
            params = task.parameters

            # 构建FFmpeg命令
            stream = ffmpeg.input(input_path)

            # 设置编码参数
            codec_args = {
                "vcodec": params["video_codec"],
                "acodec": params["audio_codec"],
            }

            # 根据质量设置参数
            if params["quality"] == "high":
                codec_args.update({"crf": 18, "preset": "slow"})
            elif params["quality"] == "medium":
                codec_args.update({"crf": 23, "preset": "medium"})
            else:  # low
                codec_args.update({"crf": 28, "preset": "fast"})

            # 输出流
            stream = ffmpeg.output(stream, output_path, **codec_args)

            # 执行转换
            await self._run_ffmpeg_async(stream, task)

            task.status = "completed"
            task.completed_at = datetime.now()
            task.progress = 1.0

        except Exception as e:
            task.status = "failed"
            task.error_message = str(e)
            self.logger.error(f"格式转换失败: {e}")

    async def _run_ffmpeg_async(self, stream, task: VideoProcessingTask):
        """异步运行FFmpeg命令"""
        try:
            # 获取总帧数用于计算进度
            input_info = await self.get_video_info(task.input_file)
            total_frames = input_info.get("fps", 25) * input_info.get("duration", 1)

            # 运行FFmpeg
            process = await asyncio.create_subprocess_exec(
                *ffmpeg.compile(stream),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            # 监控进度
            while True:
                try:
                    line = await asyncio.wait_for(
                        process.stderr.readline(), timeout=1.0
                    )
                    if not line:
                        break

                    # 解析进度信息
                    line_str = line.decode().strip()
                    if "frame=" in line_str:
                        progress = self._parse_progress(line_str, total_frames)
                        task.progress = progress

                except asyncio.TimeoutError:
                    if process.returncode is not None:
                        break

            await process.wait()

            if process.returncode != 0:
                stderr = await process.stderr.read()
                raise RuntimeError(f"FFmpeg执行失败: {stderr.decode()}")

        except Exception as e:
            raise RuntimeError(f"FFmpeg异步执行失败: {e}")

    def _parse_progress(self, line: str, total_frames: float) -> float:
        """解析FFmpeg输出中的进度信息"""
        try:
            if "frame=" in line:
                # 提取当前帧数
                parts = line.split()
                for part in parts:
                    if part.startswith("frame="):
                        current_frame = float(part.split("=")[1])
                        return min(current_frame / total_frames, 1.0)
            return 0.0
        except (ValueError, IndexError, ZeroDivisionError):
            return 0.0

    async def compress_video(
        self,
        input_path: str,
        output_path: str,
        target_size_mb: Optional[float] = None,
        quality_factor: float = 0.8,
    ) -> str:
        """
        压缩视频

        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            target_size_mb: 目标文件大小(MB)
            quality_factor: 质量因子 (0.1-1.0)

        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())

        task = VideoProcessingTask(
            task_id=task_id,
            input_file=input_path,
            output_file=output_path,
            operation="compression",
            parameters={
                "target_size_mb": target_size_mb,
                "quality_factor": quality_factor,
            },
        )

        self.tasks[task_id] = task

        # 异步执行压缩
        asyncio.create_task(self._execute_compression(task))

        return task_id

    async def _execute_compression(self, task: VideoProcessingTask):
        """执行视频压缩"""
        try:
            task.status = "running"
            task.started_at = datetime.now()

            input_path = task.input_file
            output_path = task.output_file
            params = task.parameters

            # 获取输入视频信息
            info = await self.get_video_info(input_path)

            # 计算压缩参数
            if params.get("target_size_mb"):
                # 根据目标大小计算比特率
                target_bitrate = self._calculate_target_bitrate(
                    info["duration"], params["target_size_mb"]
                )
            else:
                # 根据质量因子压缩
                original_bitrate = info.get("bit_rate", 0)
                target_bitrate = int(original_bitrate * params["quality_factor"])

            # 构建压缩命令
            stream = ffmpeg.input(input_path)
            stream = ffmpeg.output(
                stream,
                output_path,
                vcodec="libx264",
                acodec="aac",
                video_bitrate=target_bitrate,
                audio_bitrate="128k",
                preset="medium",
            )

            # 执行压缩
            await self._run_ffmpeg_async(stream, task)

            task.status = "completed"
            task.completed_at = datetime.now()
            task.progress = 1.0

        except Exception as e:
            task.status = "failed"
            task.error_message = str(e)
            self.logger.error(f"视频压缩失败: {e}")

    def _calculate_target_bitrate(self, duration: float, target_size_mb: float) -> int:
        """计算目标比特率"""
        # 计算总比特数 (减去音频大概占用)
        target_bits = target_size_mb * 8 * 1024 * 1024 * 0.9  # 90%给视频
        return int(target_bits / duration)

    async def extract_audio(self, input_path: str, output_path: str) -> str:
        """
        提取音频

        Args:
            input_path: 输入视频文件路径
            output_path: 输出音频文件路径

        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())

        task = VideoProcessingTask(
            task_id=task_id,
            input_file=input_path,
            output_file=output_path,
            operation="audio_extraction",
            parameters={},
        )

        self.tasks[task_id] = task

        # 异步执行音频提取
        asyncio.create_task(self._execute_audio_extraction(task))

        return task_id

    async def _execute_audio_extraction(self, task: VideoProcessingTask):
        """执行音频提取"""
        try:
            task.status = "running"
            task.started_at = datetime.now()

            # 构建FFmpeg命令进行音频提取
            stream = ffmpeg.input(task.input_file)
            stream = ffmpeg.output(
                stream,
                task.output_file,
                acodec="libmp3lame",
                audio_bitrate="192k",
                vn=None,  # 禁用视频流
            )

            await self._run_ffmpeg_async(stream, task)

            task.status = "completed"
            task.completed_at = datetime.now()
            task.progress = 1.0

        except Exception as e:
            task.status = "failed"
            task.error_message = str(e)
            self.logger.error(f"音频提取失败: {e}")

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id not in self.tasks:
            return None

        task = self.tasks[task_id]
        return {
            "task_id": task.task_id,
            "operation": task.operation,
            "status": task.status,
            "progress": task.progress,
            "created_at": task.created_at.isoformat(),
            "started_at": (task.started_at.isoformat() if task.started_at else None),
            "completed_at": (
                task.completed_at.isoformat() if task.completed_at else None
            ),
            "error_message": task.error_message,
        }

    def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任务"""
        return [self.get_task_status(task_id) for task_id in self.tasks.keys()]
