"""
用户认证和授权模块
"""

from datetime import datetime, timedelta
from typing import Any, Dict, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from jose import JWTError, jwt
from passlib.context import CryptContext

from app.core.config import settings

# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """认证服务类"""

    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)

    def get_password_hash(self, password: str) -> str:
        """生成密码哈希"""
        return pwd_context.hash(password)

    def create_access_token(
        self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        创建访问令牌

        Args:
            data: 要编码的数据
            expires_delta: 过期时间差

        Returns:
            str: JWT令牌
        """
        to_encode = data.copy()

        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=self.access_token_expire_minutes
            )

        to_encode.update({"exp": expire})

        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)

        return encoded_jwt

    def verify_token(self, token: str) -> Dict[str, Any]:
        """
        验证JWT令牌

        Args:
            token: JWT令牌

        Returns:
            Dict[str, Any]: 解码的数据

        Raises:
            HTTPException: 令牌无效时抛出
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload

        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌无效",
                headers={"WWW-Authenticate": "Bearer"},
            )

    def create_user_token(
        self, user_id: int, username: str, email: str, role: str = "user"
    ) -> str:
        """
        为用户创建令牌

        Args:
            user_id: 用户ID
            username: 用户名
            email: 邮箱
            role: 角色

        Returns:
            str: JWT令牌
        """
        token_data = {
            "sub": str(user_id),
            "username": username,
            "email": email,
            "role": role,
            "iat": datetime.utcnow(),
            "type": "access",
        }

        return self.create_access_token(token_data)

    def extract_user_from_token(self, token: str) -> Dict[str, Any]:
        """
        从令牌中提取用户信息

        Args:
            token: JWT令牌

        Returns:
            Dict[str, Any]: 用户信息
        """
        payload = self.verify_token(token)

        user_info = {
            "user_id": int(payload.get("sub")),
            "username": payload.get("username"),
            "email": payload.get("email"),
            "role": payload.get("role", "user"),
        }

        return user_info


# 全局认证服务实例
auth_service = AuthService()


# 便捷函数
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return auth_service.verify_password(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return auth_service.get_password_hash(password)


def create_access_token(
    data: Dict[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """创建访问令牌"""
    return auth_service.create_access_token(data, expires_delta)


def verify_token(token: str) -> Dict[str, Any]:
    """验证令牌"""
    return auth_service.verify_token(token)


# 认证依赖函数
def get_current_user_from_token(token: str) -> Dict[str, Any]:
    """从令牌获取当前用户"""
    return auth_service.extract_user_from_token(token)


# FastAPI认证依赖
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> Dict[str, Any]:
    """
    获取当前用户 - FastAPI依赖函数

    Args:
        credentials: HTTP Bearer令牌
        db: 数据库会话

    Returns:
        Dict[str, Any]: 当前用户信息

    Raises:
        HTTPException: 认证失败时抛出异常
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭证",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # 验证令牌并获取用户信息
        user_info = get_current_user_from_token(credentials.credentials)
        return user_info
    except Exception:
        raise credentials_exception


# 便捷的用户权限检查函数
async def get_current_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """获取当前活跃用户"""
    # 这里可以添加额外的用户状态检查
    return current_user


async def get_current_admin_user(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """获取当前管理员用户"""
    if current_user.get("role") not in ["admin", "superuser"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：需要管理员权限",
        )
    return current_user
