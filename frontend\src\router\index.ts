import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { setupRouterGuards } from './guards'

// 🚀 路由级别懒加载 - 2025年最佳实践
// 使用动态导入和魔法注释进行代码分割优化

// 首页 - 关键路径，使用预加载
const Home = () => import(
  /* webpackChunkName: "home" */
  /* webpackPreload: true */
  '../pages/Home.vue'
)

// 视频创作 - 高频使用，使用预取
const VideoCreation = () => import(
  /* webpackChunkName: "video-creation" */
  /* webpackPrefetch: true */
  '../pages/VideoCreation.vue'
)

// 计算引擎测试 - 按需加载
const ComputeTest = () => import(
  /* webpackChunkName: "compute-test" */
  '../pages/ComputeTest.vue'
)

// 个人中心 - 按需加载
const Profile = () => import(
  /* webpackChunkName: "profile" */
  '../pages/Profile.vue'
)

// 登录页面 - 关键路径，使用预加载
const Login = () => import(
  /* webpackChunkName: "auth" */
  /* webpackPreload: true */
  '../pages/Login.vue'
)

// 简单登录页面 - 测试用
const SimpleLogin = () => import(
  /* webpackChunkName: "simple-login" */
  '../pages/SimpleLogin.vue'
)

/**
 * 路由元信息接口
 */
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    requiresAuth?: boolean
    permissions?: string[]
    roles?: string[]
    layout?: string
    keepAlive?: boolean
  }
}

/**
 * 路由配置
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '二创短视频分发系统',
      requiresAuth: false,
    },
  },
  {
    path: '/video-creation',
    name: 'VideoCreation',
    component: VideoCreation,
    meta: {
      title: '视频创作',
      requiresAuth: true,
      permissions: ['video:create'],
      keepAlive: true,
    },
  },
  {
    path: '/compute-test',
    name: 'ComputeTest',
    component: ComputeTest,
    meta: {
      title: '计算引擎测试',
      requiresAuth: true,
      permissions: ['system:test'],
      roles: ['admin', 'developer'],
    },
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '用户登录',
      requiresAuth: false,
      layout: 'auth',
    },
  },
  {
    path: '/simple-login',
    name: 'SimpleLogin',
    component: SimpleLogin,
    meta: {
      title: '简单登录测试',
      requiresAuth: false,
      layout: 'auth',
    },
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人中心',
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('../pages/Admin.vue'),
    meta: {
      title: '系统管理',
      requiresAuth: true,
      roles: ['admin'],
      permissions: ['system:manage'],
    },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: () => import('../pages/admin/AdminDashboard.vue'),
        meta: {
          title: '管理员仪表板',
          requiresAuth: true,
          roles: ['admin'],
        },
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('../pages/admin/UserManagement.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
          roles: ['admin'],
          permissions: ['user:manage'],
        },
      },
      {
        path: 'videos',
        name: 'AdminVideos',
        component: () => import('../pages/admin/VideoManagement.vue'),
        meta: {
          title: '视频管理',
          requiresAuth: true,
          roles: ['admin'],
          permissions: ['video:manage'],
        },
      },
    ],
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('../pages/TestPage.vue'),
    meta: {
      title: '测试页面',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/',
  },
]

/**
 * 创建路由实例
 */
const router = createRouter({
  history: createWebHistory(),
  routes,
  // 滚动行为配置
  scrollBehavior(to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }
    return { top: 0, behavior: 'smooth' }
  }
})

// 临时禁用路由守卫进行调试
// setupRouterGuards(router)

// 简化的路由守卫用于调试
router.beforeEach((to, from, next) => {
  console.log('路由导航:', from.path, '->', to.path)

  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 二创短视频分发系统`
  } else {
    document.title = '二创短视频分发系统'
  }

  // 在开发环境中跳过认证检查
  if (import.meta.env.DEV) {
    console.log('开发环境：跳过认证检查')
    next()
    return
  }

  // 检查是否需要认证
  if (to.meta?.requiresAuth) {
    console.log('需要认证的路由，开发环境跳过检查')
    // 在开发环境中直接允许访问
    next()
  } else {
    next()
  }
})

export default router