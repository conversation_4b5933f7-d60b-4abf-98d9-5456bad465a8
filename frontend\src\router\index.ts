import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { setupRouterGuards } from './guards'

// 导入页面组件
import Home from '../pages/Home.vue'
import VideoCreation from '../pages/VideoCreation.vue'
import ComputeTest from '../pages/ComputeTest.vue'

/**
 * 路由元信息接口
 */
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    requiresAuth?: boolean
    permissions?: string[]
    roles?: string[]
    layout?: string
    keepAlive?: boolean
  }
}

/**
 * 路由配置
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '二创短视频分发系统',
      requiresAuth: false,
    },
  },
  {
    path: '/video-creation',
    name: 'VideoCreation',
    component: VideoCreation,
    meta: {
      title: '视频创作',
      requiresAuth: true,
      permissions: ['video:create'],
      keepAlive: true,
    },
  },
  {
    path: '/compute-test',
    name: 'ComputeTest',
    component: ComputeTest,
    meta: {
      title: '计算引擎测试',
      requiresAuth: true,
      permissions: ['system:test'],
      roles: ['admin', 'developer'],
    },
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../pages/Login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      layout: 'auth',
    },
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../pages/Profile.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('../pages/Admin.vue'),
    meta: {
      title: '系统管理',
      requiresAuth: true,
      roles: ['admin'],
      permissions: ['system:manage'],
    },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: () => import('../pages/admin/AdminDashboard.vue'),
        meta: {
          title: '管理员仪表板',
          requiresAuth: true,
          roles: ['admin'],
        },
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('../pages/admin/UserManagement.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
          roles: ['admin'],
          permissions: ['user:manage'],
        },
      },
      {
        path: 'videos',
        name: 'AdminVideos',
        component: () => import('../pages/admin/VideoManagement.vue'),
        meta: {
          title: '视频管理',
          requiresAuth: true,
          roles: ['admin'],
          permissions: ['video:manage'],
        },
      },
    ],
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('../pages/TestPage.vue'),
    meta: {
      title: '测试页面',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/',
  },
]

/**
 * 创建路由实例
 */
const router = createRouter({
  history: createWebHistory(),
  routes,
  // 滚动行为配置
  scrollBehavior(to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }
    return { top: 0, behavior: 'smooth' }
  }
})

// 设置路由守卫
setupRouterGuards(router)

export default router