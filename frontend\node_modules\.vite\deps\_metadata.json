{"hash": "fae9a5de", "configHash": "9f91665e", "lockfileHash": "ae9a44bd", "browserHash": "f4d06542", "optimized": {"@ffmpeg/ffmpeg": {"src": "../../@ffmpeg/ffmpeg/dist/esm/index.js", "file": "@ffmpeg_ffmpeg.js", "fileHash": "bed66406", "needsInterop": false}, "@ffmpeg/util": {"src": "../../@ffmpeg/util/dist/esm/index.js", "file": "@ffmpeg_util.js", "fileHash": "31fad646", "needsInterop": false}, "long": {"src": "../../long/src/long.js", "file": "long.js", "fileHash": "c60222e0", "needsInterop": true}, "seedrandom": {"src": "../../seedrandom/index.js", "file": "seedrandom.js", "fileHash": "965e68e6", "needsInterop": true}, "@sentry/vue": {"src": "../../@sentry/vue/build/esm/index.js", "file": "@sentry_vue.js", "fileHash": "7027446d", "needsInterop": false}, "@tensorflow-models/coco-ssd": {"src": "../../@tensorflow-models/coco-ssd/dist/coco-ssd.es2017.esm.min.js", "file": "@tensorflow-models_coco-ssd.js", "fileHash": "7412f33e", "needsInterop": true}, "@tensorflow-models/mobilenet": {"src": "../../@tensorflow-models/mobilenet/dist/mobilenet.esm.js", "file": "@tensorflow-models_mobilenet.js", "fileHash": "bc403d92", "needsInterop": false}, "lucide-vue-next": {"src": "../../lucide-vue-next/dist/esm/lucide-vue-next.js", "file": "lucide-vue-next.js", "fileHash": "2e502c1e", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "1ef2c6bb", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "56a33cb2", "needsInterop": false}, "vue-lazyload": {"src": "../../vue-lazyload/vue-lazyload.esm.js", "file": "vue-lazyload.js", "fileHash": "b8152c67", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "d347af0b", "needsInterop": false}}, "chunks": {"chunk-YFT6OQ5R": {"file": "chunk-YFT6OQ5R.js"}, "chunk-D5MDHDZB": {"file": "chunk-D5MDHDZB.js"}, "chunk-4OY4FY3Y": {"file": "chunk-4OY4FY3Y.js"}, "chunk-WFQWBLSG": {"file": "chunk-WFQWBLSG.js"}, "chunk-FIAHBV72": {"file": "chunk-FIAHBV72.js"}, "chunk-OCBYBPSH": {"file": "chunk-OCBYBPSH.js"}}}