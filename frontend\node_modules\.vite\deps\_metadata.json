{"hash": "2e4861bb", "configHash": "9f91665e", "lockfileHash": "f84a17a7", "browserHash": "50cf79d0", "optimized": {"@ffmpeg/ffmpeg": {"src": "../../@ffmpeg/ffmpeg/dist/esm/index.js", "file": "@ffmpeg_ffmpeg.js", "fileHash": "2eeb89a6", "needsInterop": false}, "@ffmpeg/util": {"src": "../../@ffmpeg/util/dist/esm/index.js", "file": "@ffmpeg_util.js", "fileHash": "a3d6f0db", "needsInterop": false}, "long": {"src": "../../long/src/long.js", "file": "long.js", "fileHash": "8186c27a", "needsInterop": true}, "seedrandom": {"src": "../../seedrandom/index.js", "file": "seedrandom.js", "fileHash": "941183b1", "needsInterop": true}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "94bd1ab9", "needsInterop": false}, "vue-lazyload": {"src": "../../vue-lazyload/vue-lazyload.esm.js", "file": "vue-lazyload.js", "fileHash": "3fe907e5", "needsInterop": false}, "@sentry/vue": {"src": "../../@sentry/vue/build/esm/index.js", "file": "@sentry_vue.js", "fileHash": "e2814ee9", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "801a3484", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "d864ff24", "needsInterop": false}, "lucide-vue-next": {"src": "../../lucide-vue-next/dist/esm/lucide-vue-next.js", "file": "lucide-vue-next.js", "fileHash": "00c0955f", "needsInterop": false}, "@tensorflow-models/mobilenet": {"src": "../../@tensorflow-models/mobilenet/dist/mobilenet.esm.js", "file": "@tensorflow-models_mobilenet.js", "fileHash": "252f8673", "needsInterop": false}, "@tensorflow-models/coco-ssd": {"src": "../../@tensorflow-models/coco-ssd/dist/coco-ssd.es2017.esm.min.js", "file": "@tensorflow-models_coco-ssd.js", "fileHash": "de763f65", "needsInterop": true}}, "chunks": {"chunk-D5MDHDZB": {"file": "chunk-D5MDHDZB.js"}, "chunk-4OY4FY3Y": {"file": "chunk-4OY4FY3Y.js"}, "chunk-WFQWBLSG": {"file": "chunk-WFQWBLSG.js"}, "chunk-YFT6OQ5R": {"file": "chunk-YFT6OQ5R.js"}, "chunk-FIAHBV72": {"file": "chunk-FIAHBV72.js"}, "chunk-OCBYBPSH": {"file": "chunk-OCBYBPSH.js"}}}