{"hash": "4570b5fb", "configHash": "9f91665e", "lockfileHash": "c85f1465", "browserHash": "cc50385a", "optimized": {"@ffmpeg/ffmpeg": {"src": "../../@ffmpeg/ffmpeg/dist/esm/index.js", "file": "@ffmpeg_ffmpeg.js", "fileHash": "5543c56a", "needsInterop": false}, "@ffmpeg/util": {"src": "../../@ffmpeg/util/dist/esm/index.js", "file": "@ffmpeg_util.js", "fileHash": "c2ecc091", "needsInterop": false}, "long": {"src": "../../long/src/long.js", "file": "long.js", "fileHash": "565b1a0e", "needsInterop": true}, "seedrandom": {"src": "../../seedrandom/index.js", "file": "seedrandom.js", "fileHash": "a029deaa", "needsInterop": true}, "@sentry/vue": {"src": "../../@sentry/vue/build/esm/index.js", "file": "@sentry_vue.js", "fileHash": "20ce4b47", "needsInterop": false}, "@tensorflow-models/coco-ssd": {"src": "../../@tensorflow-models/coco-ssd/dist/coco-ssd.es2017.esm.min.js", "file": "@tensorflow-models_coco-ssd.js", "fileHash": "e334b96f", "needsInterop": true}, "@tensorflow-models/mobilenet": {"src": "../../@tensorflow-models/mobilenet/dist/mobilenet.esm.js", "file": "@tensorflow-models_mobilenet.js", "fileHash": "53caba4b", "needsInterop": false}, "lucide-vue-next": {"src": "../../lucide-vue-next/dist/esm/lucide-vue-next.js", "file": "lucide-vue-next.js", "fileHash": "5d9cde0d", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "45995685", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "0c726c79", "needsInterop": false}, "vue-lazyload": {"src": "../../vue-lazyload/vue-lazyload.esm.js", "file": "vue-lazyload.js", "fileHash": "efb590ba", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "3b5e7ec4", "needsInterop": false}}, "chunks": {"chunk-YFT6OQ5R": {"file": "chunk-YFT6OQ5R.js"}, "chunk-D5MDHDZB": {"file": "chunk-D5MDHDZB.js"}, "chunk-4OY4FY3Y": {"file": "chunk-4OY4FY3Y.js"}, "chunk-WFQWBLSG": {"file": "chunk-WFQWBLSG.js"}, "chunk-FIAHBV72": {"file": "chunk-FIAHBV72.js"}, "chunk-OCBYBPSH": {"file": "chunk-OCBYBPSH.js"}}}