<!--
  性能指标卡片组件 - 2025年最佳实践
-->

<template>
  <div class="metric-card" :class="statusClass">
    <div class="metric-header">
      <div class="metric-info">
        <h3 class="metric-title">{{ title }}</h3>
        <p class="metric-description">{{ description }}</p>
      </div>
      <div class="metric-status" :class="statusClass">
        <component :is="statusIcon" class="status-icon" />
      </div>
    </div>
    
    <div class="metric-content">
      <div class="metric-value">
        <span class="value">{{ displayValue }}</span>
        <span class="unit" v-if="unit">{{ unit }}</span>
      </div>
      
      <div class="metric-trend" v-if="trend !== null">
        <component :is="trendIcon" class="trend-icon" :class="trendClass" />
        <span class="trend-value" :class="trendClass">
          {{ Math.abs(trend).toFixed(1) }}%
        </span>
      </div>
    </div>
    
    <div class="metric-footer">
      <div class="threshold-bar">
        <div class="threshold-fill" :style="{ width: fillPercentage + '%' }"></div>
        <div class="threshold-marker" :style="{ left: thresholdPercentage + '%' }"></div>
      </div>
      <div class="threshold-info">
        <span class="current-label">当前: {{ displayValue }}{{ unit }}</span>
        <span class="threshold-label">阈值: {{ threshold }}{{ unit }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CheckCircleIcon, AlertTriangleIcon, XCircleIcon, TrendingUpIcon, TrendingDownIcon } from '@/components/icons'

interface Props {
  title: string
  value: number | string
  trend: number | null
  threshold: number
  unit?: string
  description?: string
}

const props = withDefaults(defineProps<Props>(), {
  unit: '',
  description: ''
})

// 计算属性
const numericValue = computed(() => {
  return typeof props.value === 'string' ? parseFloat(props.value) || 0 : props.value
})

const displayValue = computed(() => {
  if (typeof props.value === 'string') return props.value
  return props.value.toLocaleString()
})

const statusClass = computed(() => {
  const value = numericValue.value
  const threshold = props.threshold
  
  if (props.unit === '' && props.title.includes('Layout Shift')) {
    // CLS 特殊处理
    if (value <= 0.1) return 'good'
    if (value <= 0.25) return 'needs-improvement'
    return 'poor'
  }
  
  if (value <= threshold * 0.8) return 'good'
  if (value <= threshold) return 'needs-improvement'
  return 'poor'
})

const statusIcon = computed(() => {
  switch (statusClass.value) {
    case 'good': return CheckCircleIcon
    case 'needs-improvement': return AlertTriangleIcon
    case 'poor': return XCircleIcon
    default: return CheckCircleIcon
  }
})

const trendClass = computed(() => {
  if (props.trend === null) return ''
  return props.trend > 0 ? 'trend-up' : 'trend-down'
})

const trendIcon = computed(() => {
  if (props.trend === null) return null
  return props.trend > 0 ? TrendingUpIcon : TrendingDownIcon
})

const fillPercentage = computed(() => {
  const value = numericValue.value
  const threshold = props.threshold
  
  if (props.unit === '' && props.title.includes('Layout Shift')) {
    // CLS 使用不同的计算方式
    return Math.min((value / 0.25) * 100, 100)
  }
  
  return Math.min((value / threshold) * 100, 100)
})

const thresholdPercentage = computed(() => {
  if (props.unit === '' && props.title.includes('Layout Shift')) {
    return (0.1 / 0.25) * 100 // CLS 阈值位置
  }
  return 100 // 其他指标阈值在100%位置
})
</script>

<style scoped>
.metric-card {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--status-color);
  transition: background 0.3s ease;
}

.metric-card.good {
  --status-color: #10b981;
}

.metric-card.needs-improvement {
  --status-color: #f59e0b;
}

.metric-card.poor {
  --status-color: #ef4444;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.metric-info {
  flex: 1;
}

.metric-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.metric-description {
  font-size: 14px;
  color: var(--color-text-soft);
  margin: 0;
  line-height: 1.4;
}

.metric-status {
  flex-shrink: 0;
  margin-left: 12px;
}

.status-icon {
  width: 24px;
  height: 24px;
  color: var(--status-color);
}

.metric-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
}

.metric-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.value {
  font-size: 32px;
  font-weight: 700;
  color: var(--color-text);
  line-height: 1;
}

.unit {
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-soft);
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
}

.trend-icon {
  width: 16px;
  height: 16px;
}

.trend-up {
  color: #ef4444;
}

.trend-down {
  color: #10b981;
}

.metric-footer {
  margin-top: auto;
}

.threshold-bar {
  position: relative;
  height: 6px;
  background: var(--color-background-soft);
  border-radius: 3px;
  margin-bottom: 8px;
  overflow: hidden;
}

.threshold-fill {
  height: 100%;
  background: var(--status-color);
  border-radius: 3px;
  transition: width 0.5s ease;
}

.threshold-marker {
  position: absolute;
  top: -2px;
  width: 2px;
  height: 10px;
  background: var(--color-text);
  border-radius: 1px;
  transform: translateX(-50%);
}

.threshold-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--color-text-soft);
}

.current-label {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metric-card {
    padding: 20px;
  }
  
  .metric-title {
    font-size: 14px;
  }
  
  .metric-description {
    font-size: 12px;
  }
  
  .value {
    font-size: 28px;
  }
  
  .unit {
    font-size: 14px;
  }
  
  .status-icon {
    width: 20px;
    height: 20px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .metric-card {
    background: var(--color-background-dark);
    border-color: var(--color-border-dark);
  }
  
  .threshold-bar {
    background: var(--color-background-soft-dark);
  }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  .metric-card {
    border-width: 2px;
  }
  
  .metric-card::before {
    height: 6px;
  }
  
  .threshold-fill {
    opacity: 0.8;
  }
}

/* 减少动画支持 */
@media (prefers-reduced-motion: reduce) {
  .metric-card,
  .threshold-fill {
    transition: none;
  }
  
  .metric-card:hover {
    transform: none;
  }
}
</style>
