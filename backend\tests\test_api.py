"""API相关测试
测试API端点、认证、权限等功能
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi import FastAP<PERSON>, status
from fastapi.testclient import TestClient
from fastapi.security import HTTPBearer

# 假设这些是实际的API模块（需要根据实际项目结构调整）
# from app.api.v1.auth import router as auth_router
# from app.api.v1.users import router as users_router
# from app.api.v1.videos import router as videos_router
# from app.core.security import verify_token, create_access_token


class TestAuthAPI:
    """认证API测试"""
    
    @pytest.fixture
    def app(self):
        """创建测试应用"""
        app = FastAPI()
        # app.include_router(auth_router, prefix="/api/v1/auth")
        return app
    
    @pytest.fixture
    def client(self, app):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_register_success(self, client):
        """测试成功注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
        
        with patch('app.api.v1.auth.create_user') as mock_create_user:
            mock_create_user.return_value = {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>"
            }
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["username"] == "testuser"
            assert data["email"] == "<EMAIL>"
            assert "password" not in data  # 密码不应该返回
    
    def test_register_duplicate_email(self, client):
        """测试重复邮箱注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
        
        with patch('app.api.v1.auth.create_user') as mock_create_user:
            mock_create_user.side_effect = ValueError("邮箱已存在")
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            data = response.json()
            assert "邮箱已存在" in data["detail"]
    
    def test_register_password_mismatch(self, client):
        """测试密码不匹配注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "different_password"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_login_success(self, client):
        """测试成功登录"""
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        with patch('app.api.v1.auth.authenticate_user') as mock_auth:
            mock_auth.return_value = {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>"
            }
            
            with patch('app.api.v1.auth.create_access_token') as mock_token:
                mock_token.return_value = "fake_jwt_token"
                
                response = client.post("/api/v1/auth/login", json=login_data)
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert "access_token" in data
                assert data["token_type"] == "bearer"
                assert "user" in data
    
    def test_login_invalid_credentials(self, client):
        """测试无效凭据登录"""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrong_password"
        }
        
        with patch('app.api.v1.auth.authenticate_user') as mock_auth:
            mock_auth.return_value = None
            
            response = client.post("/api/v1/auth/login", json=login_data)
            
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            data = response.json()
            assert "用户名或密码错误" in data["detail"]
    
    def test_refresh_token_success(self, client):
        """测试成功刷新令牌"""
        headers = {"Authorization": "Bearer valid_refresh_token"}
        
        with patch('app.api.v1.auth.verify_refresh_token') as mock_verify:
            mock_verify.return_value = {"user_id": 1}
            
            with patch('app.api.v1.auth.create_access_token') as mock_token:
                mock_token.return_value = "new_access_token"
                
                response = client.post("/api/v1/auth/refresh", headers=headers)
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["access_token"] == "new_access_token"
    
    def test_logout_success(self, client):
        """测试成功登出"""
        headers = {"Authorization": "Bearer valid_token"}
        
        with patch('app.api.v1.auth.revoke_token') as mock_revoke:
            mock_revoke.return_value = True
            
            response = client.post("/api/v1/auth/logout", headers=headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["message"] == "登出成功"


class TestUsersAPI:
    """用户API测试"""
    
    @pytest.fixture
    def app(self):
        """创建测试应用"""
        app = FastAPI()
        # app.include_router(users_router, prefix="/api/v1/users")
        return app
    
    @pytest.fixture
    def client(self, app):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        """认证头"""
        return {"Authorization": "Bearer valid_token"}
    
    def test_get_current_user(self, client, auth_headers):
        """测试获取当前用户信息"""
        with patch('app.api.v1.users.get_current_user') as mock_get_user:
            mock_get_user.return_value = {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>",
                "created_at": "2023-01-01T00:00:00"
            }
            
            response = client.get("/api/v1/users/me", headers=auth_headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["username"] == "testuser"
    
    def test_update_user_profile(self, client, auth_headers):
        """测试更新用户资料"""
        update_data = {
            "username": "updated_user",
            "bio": "Updated bio"
        }
        
        with patch('app.api.v1.users.update_user_profile') as mock_update:
            mock_update.return_value = {
                "id": 1,
                "username": "updated_user",
                "bio": "Updated bio"
            }
            
            response = client.put(
                "/api/v1/users/me", 
                json=update_data, 
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["username"] == "updated_user"
            assert data["bio"] == "Updated bio"
    
    def test_change_password(self, client, auth_headers):
        """测试修改密码"""
        password_data = {
            "current_password": "old_password",
            "new_password": "new_password123",
            "confirm_password": "new_password123"
        }
        
        with patch('app.api.v1.users.change_password') as mock_change:
            mock_change.return_value = True
            
            response = client.post(
                "/api/v1/users/me/password", 
                json=password_data, 
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["message"] == "密码修改成功"
    
    def test_change_password_wrong_current(self, client, auth_headers):
        """测试修改密码（当前密码错误）"""
        password_data = {
            "current_password": "wrong_password",
            "new_password": "new_password123",
            "confirm_password": "new_password123"
        }
        
        with patch('app.api.v1.users.change_password') as mock_change:
            mock_change.side_effect = ValueError("当前密码错误")
            
            response = client.post(
                "/api/v1/users/me/password", 
                json=password_data, 
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_get_user_list(self, client, auth_headers):
        """测试获取用户列表"""
        with patch('app.api.v1.users.get_user_list') as mock_get_list:
            mock_get_list.return_value = {
                "items": [
                    {"id": 1, "username": "user1"},
                    {"id": 2, "username": "user2"}
                ],
                "meta": {
                    "page": 1,
                    "size": 10,
                    "total": 2,
                    "pages": 1
                }
            }
            
            response = client.get(
                "/api/v1/users?page=1&size=10", 
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert len(data["items"]) == 2
            assert data["meta"]["total"] == 2
    
    def test_get_user_by_id(self, client, auth_headers):
        """测试根据ID获取用户"""
        with patch('app.api.v1.users.get_user_by_id') as mock_get_user:
            mock_get_user.return_value = {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>"
            }
            
            response = client.get("/api/v1/users/1", headers=auth_headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == 1
            assert data["username"] == "testuser"
    
    def test_get_user_not_found(self, client, auth_headers):
        """测试获取不存在的用户"""
        with patch('app.api.v1.users.get_user_by_id') as mock_get_user:
            mock_get_user.return_value = None
            
            response = client.get("/api/v1/users/999", headers=auth_headers)
            
            assert response.status_code == status.HTTP_404_NOT_FOUND


class TestVideosAPI:
    """视频API测试"""
    
    @pytest.fixture
    def app(self):
        """创建测试应用"""
        app = FastAPI()
        # app.include_router(videos_router, prefix="/api/v1/videos")
        return app
    
    @pytest.fixture
    def client(self, app):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        """认证头"""
        return {"Authorization": "Bearer valid_token"}
    
    def test_upload_video_success(self, client, auth_headers, temp_file):
        """测试成功上传视频"""
        # 创建模拟视频文件
        with open(temp_file, "wb") as f:
            f.write(b"fake_video_content")
        
        with patch('app.api.v1.videos.process_video_upload') as mock_upload:
            mock_upload.return_value = {
                "id": 1,
                "title": "Test Video",
                "filename": "test.mp4",
                "status": "processing"
            }
            
            with open(temp_file, "rb") as f:
                files = {"file": ("test.mp4", f, "video/mp4")}
                data = {"title": "Test Video", "description": "Test description"}
                
                response = client.post(
                    "/api/v1/videos/upload",
                    files=files,
                    data=data,
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_201_CREATED
            result = response.json()
            assert result["title"] == "Test Video"
            assert result["status"] == "processing"
    
    def test_upload_video_invalid_format(self, client, auth_headers, temp_file):
        """测试上传无效格式视频"""
        with open(temp_file, "wb") as f:
            f.write(b"not_a_video")
        
        with open(temp_file, "rb") as f:
            files = {"file": ("test.txt", f, "text/plain")}
            data = {"title": "Test Video"}
            
            response = client.post(
                "/api/v1/videos/upload",
                files=files,
                data=data,
                headers=auth_headers
            )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_upload_video_failure(self, client, auth_headers, temp_file):
        """测试上传视频失败（例如文件过大）"""
        with open(temp_file, "wb") as f:
            f.write(b'x' * (100 * 1024 * 1024))  # 100MB file
        
        with patch('app.api.v1.videos.process_video_upload') as mock_upload:
            mock_upload.side_effect = ValueError("File too large")
            
            with open(temp_file, "rb") as f:
                files = {"file": ("large.mp4", f, "video/mp4")}
                data = {"title": "Large Video"}
                
                response = client.post(
                    "/api/v1/videos/upload",
                    files=files,
                    data=data,
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            result = response.json()
            assert "File too large" in result["detail"]

    def test_get_video_list(self, client):
        """测试获取视频列表"""
        with patch('app.api.v1.videos.get_video_list') as mock_get_list:
            mock_get_list.return_value = {
                "items": [
                    {
                        "id": 1,
                        "title": "Video 1",
                        "thumbnail": "thumb1.jpg",
                        "duration": 120
                    },
                    {
                        "id": 2,
                        "title": "Video 2",
                        "thumbnail": "thumb2.jpg",
                        "duration": 180
                    }
                ],
                "meta": {
                    "page": 1,
                    "size": 10,
                    "total": 2,
                    "pages": 1
                }
            }
            
            response = client.get("/api/v1/videos?page=1&size=10")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert len(data["items"]) == 2
            assert data["items"][0]["title"] == "Video 1"
    
    def test_get_video_by_id(self, client):
        """测试根据ID获取视频"""
        with patch('app.api.v1.videos.get_video_by_id') as mock_get_video:
            mock_get_video.return_value = {
                "id": 1,
                "title": "Test Video",
                "description": "Test description",
                "url": "https://example.com/video1.mp4",
                "thumbnail": "thumb1.jpg",
                "duration": 120,
                "views": 100,
                "likes": 10,
                "created_at": "2023-01-01T00:00:00"
            }
            
            response = client.get("/api/v1/videos/1")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == 1
            assert data["title"] == "Test Video"
            assert data["views"] == 100
    
    def test_update_video(self, client, auth_headers):
        """测试更新视频信息"""
        update_data = {
            "title": "Updated Video Title",
            "description": "Updated description"
        }
        
        with patch('app.api.v1.videos.update_video') as mock_update:
            mock_update.return_value = {
                "id": 1,
                "title": "Updated Video Title",
                "description": "Updated description"
            }
            
            response = client.put(
                "/api/v1/videos/1",
                json=update_data,
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["title"] == "Updated Video Title"
    
    def test_delete_video(self, client, auth_headers):
        """测试删除视频"""
        with patch('app.api.v1.videos.delete_video') as mock_delete:
            mock_delete.return_value = True
            
            response = client.delete("/api/v1/videos/1", headers=auth_headers)
            
            assert response.status_code == status.HTTP_204_NO_CONTENT
    
    def test_like_video(self, client, auth_headers):
        """测试点赞视频"""
        with patch('app.api.v1.videos.like_video') as mock_like:
            mock_like.return_value = {"likes": 11, "liked": True}
            
            response = client.post("/api/v1/videos/1/like", headers=auth_headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["likes"] == 11
            assert data["liked"] is True
    
    def test_search_videos(self, client):
        """测试搜索视频"""
        with patch('app.api.v1.videos.search_videos') as mock_search:
            mock_search.return_value = {
                "items": [
                    {
                        "id": 1,
                        "title": "Matching Video",
                        "description": "Contains search term"
                    }
                ],
                "meta": {
                    "page": 1,
                    "size": 10,
                    "total": 1,
                    "pages": 1
                }
            }
            
            response = client.get("/api/v1/videos/search?q=test&page=1&size=10")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert len(data["items"]) == 1
            assert "Matching" in data["items"][0]["title"]


class TestAPIMiddleware:
    """API中间件测试"""
    
    def test_rate_limiting_middleware(self, client):
        """测试限流中间件"""
        # 模拟大量请求
        responses = []
        for i in range(10):
            response = client.get("/api/v1/videos")
            responses.append(response.status_code)
        
        # 检查是否有请求被限流
        assert any(code == 429 for code in responses)
    
    def test_cors_middleware(self, client):
        """测试CORS中间件"""
        response = client.options(
            "/api/v1/videos",
            headers={
                "Origin": "https://example.com",
                "Access-Control-Request-Method": "GET"
            }
        )
        
        assert "Access-Control-Allow-Origin" in response.headers
    
    def test_error_handling_middleware(self, client):
        """测试错误处理中间件"""
        with patch('app.api.v1.videos.get_video_list') as mock_get_list:
            mock_get_list.side_effect = Exception("Database error")
            
            response = client.get("/api/v1/videos")
            
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            data = response.json()
            assert "error_id" in data
            assert data["error"] == "InternalServerError"


class TestAPIValidation:
    """API验证测试"""
    
    def test_request_validation(self, client):
        """测试请求验证"""
        # 测试缺少必需字段
        invalid_data = {"email": "<EMAIL>"}  # 缺少密码
        
        response = client.post("/api/v1/auth/login", json=invalid_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data
    
    def test_response_validation(self, client):
        """测试响应验证"""
        with patch('app.api.v1.users.get_current_user') as mock_get_user:
            # 模拟返回格式错误的数据
            mock_get_user.return_value = {"invalid_field": "value"}
            
            headers = {"Authorization": "Bearer valid_token"}
            response = client.get("/api/v1/users/me", headers=headers)
            
            # 应该返回500错误，因为响应格式不正确
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    
    def test_input_sanitization(self, client):
        """测试输入清理"""
        malicious_data = {
            "username": "<script>alert('xss')</script>",
            "bio": "Normal bio with <script>alert('xss')</script>"
        }
        
        headers = {"Authorization": "Bearer valid_token"}
        
        with patch('app.api.v1.users.update_user_profile') as mock_update:
            mock_update.return_value = {
                "username": "alert('xss')",  # 脚本标签应该被移除
                "bio": "Normal bio with alert('xss')"
            }
            
            response = client.put(
                "/api/v1/users/me",
                json=malicious_data,
                headers=headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "<script>" not in data["username"]
            assert "<script>" not in data["bio"]


@pytest.mark.integration
class TestAPIIntegration:
    """API集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_user_workflow(self, client):
        """测试完整用户工作流"""
        # 1. 注册用户
        # 2. 登录获取令牌
        # 3. 使用令牌访问受保护资源
        # 4. 更新用户信息
        # 5. 登出
        pass
    
    @pytest.mark.asyncio
    async def test_video_upload_workflow(self, client):
        """测试视频上传工作流"""
        # 1. 用户登录
        # 2. 上传视频
        # 3. 检查处理状态
        # 4. 获取视频信息
        # 5. 更新视频信息
        pass
    
    @pytest.mark.asyncio
    async def test_api_performance(self, client):
        """测试API性能"""
        # 测试并发请求处理能力
        # 测试响应时间
        # 测试内存使用
        pass


class TestAuthAPI:
    """认证API测试"""
    
    @pytest.fixture
    def app(self):
        """创建测试应用"""
        app = FastAPI()
        # app.include_router(auth_router, prefix="/api/v1/auth")
        return app
    
    @pytest.fixture
    def client(self, app):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_register_success(self, client):
        """测试成功注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
        
        with patch('app.api.v1.auth.create_user') as mock_create_user:
            mock_create_user.return_value = {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>"
            }
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["username"] == "testuser"
            assert data["email"] == "<EMAIL>"
            assert "password" not in data  # 密码不应该返回
    
    def test_register_duplicate_email(self, client):
        """测试重复邮箱注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
        
        with patch('app.api.v1.auth.create_user') as mock_create_user:
            mock_create_user.side_effect = ValueError("邮箱已存在")
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            data = response.json()
            assert "邮箱已存在" in data["detail"]
    
    def test_register_password_mismatch(self, client):
        """测试密码不匹配注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "different_password"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_login_success(self, client):
        """测试成功登录"""
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        with patch('app.api.v1.auth.authenticate_user') as mock_auth:
            mock_auth.return_value = {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>"
            }
            
            with patch('app.api.v1.auth.create_access_token') as mock_token:
                mock_token.return_value = "fake_jwt_token"
                
                response = client.post("/api/v1/auth/login", json=login_data)
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert "access_token" in data
                assert data["token_type"] == "bearer"
                assert "user" in data
    
    def test_login_invalid_credentials(self, client):
        """测试无效凭据登录"""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrong_password"
        }
        
        with patch('app.api.v1.auth.authenticate_user') as mock_auth:
            mock_auth.return_value = None
            
            response = client.post("/api/v1/auth/login", json=login_data)
            
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            data = response.json()
            assert "用户名或密码错误" in data["detail"]
    
    def test_refresh_token_success(self, client):
        """测试成功刷新令牌"""
        headers = {"Authorization": "Bearer valid_refresh_token"}
        
        with patch('app.api.v1.auth.verify_refresh_token') as mock_verify:
            mock_verify.return_value = {"user_id": 1}
            
            with patch('app.api.v1.auth.create_access_token') as mock_token:
                mock_token.return_value = "new_access_token"
                
                response = client.post("/api/v1/auth/refresh", headers=headers)
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["access_token"] == "new_access_token"
    
    def test_logout_success(self, client):
        """测试成功登出"""
        headers = {"Authorization": "Bearer valid_token"}
        
        with patch('app.api.v1.auth.revoke_token') as mock_revoke:
            mock_revoke.return_value = True
            
            response = client.post("/api/v1/auth/logout", headers=headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["message"] == "登出成功"


class TestAuthAPI:
    """认证API测试"""
    
    @pytest.fixture
    def app(self):
        """创建测试应用"""
        app = FastAPI()
        # app.include_router(auth_router, prefix="/api/v1/auth")
        return app
    
    @pytest.fixture
    def client(self, app):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_register_success(self, client):
        """测试成功注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
        
        with patch('app.api.v1.auth.create_user') as mock_create_user:
            mock_create_user.return_value = {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>"
            }
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["username"] == "testuser"
            assert data["email"] == "<EMAIL>"
            assert "password" not in data  # 密码不应该返回
    
    def test_register_duplicate_email(self, client):
        """测试重复邮箱注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
        
        with patch('app.api.v1.auth.create_user') as mock_create_user:
            mock_create_user.side_effect = ValueError("邮箱已存在")
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            data = response.json()
            assert "邮箱已存在" in data["detail"]
    
    def test_register_password_mismatch(self, client):
        """测试密码不匹配注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "different_password"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_login_success(self, client):
        """测试成功登录"""
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        with patch('app.api.v1.auth.authenticate_user') as mock_auth:
            mock_auth.return_value = {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>"
            }
            
            with patch('app.api.v1.auth.create_access_token') as mock_token:
                mock_token.return_value = "fake_jwt_token"
                
                response = client.post("/api/v1/auth/login", json=login_data)
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert "access_token" in data
                assert data["token_type"] == "bearer"
                assert "user" in data
    
    def test_login_invalid_credentials(self, client):
        """测试无效凭据登录"""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrong_password"
        }
        
        with patch('app.api.v1.auth.authenticate_user') as mock_auth:
            mock_auth.return_value = None
            
            response = client.post("/api/v1/auth/login", json=login_data)
            
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            data = response.json()
            assert "用户名或密码错误" in data["detail"]
    
    def test_refresh_token_success(self, client):
        """测试成功刷新令牌"""
        headers = {"Authorization": "Bearer valid_refresh_token"}
        
        with patch('app.api.v1.auth.verify_refresh_token') as mock_verify:
            mock_verify.return_value = {"user_id": 1}
            
            with patch('app.api.v1.auth.create_access_token') as mock_token:
                mock_token.return_value = "new_access_token"
                
                response = client.post("/api/v1/auth/refresh", headers=headers)
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["access_token"] == "new_access_token"
    
    def test_logout_success(self, client):
        """测试成功登出"""
        headers = {"Authorization": "Bearer valid_token"}
        
        with patch('app.api.v1.auth.revoke_token') as mock_revoke:
            mock_revoke.return_value = True
            
            response = client.post("/api/v1/auth/logout", headers=headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["message"] == "登出成功"


class TestAuthAPI:
    """认证API测试"""
    
    @pytest.fixture
    def app(self):
        """创建测试应用"""
        app = FastAPI()
        # app.include_router(auth_router, prefix="/api/v1/auth")
        return app
    
    @pytest.fixture
    def client(self, app):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_register_success(self, client):
        """测试成功注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
        
        with patch('app.api.v1.auth.create_user') as mock_create_user:
            mock_create_user.return_value = {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>"
            }
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["username"] == "testuser"
            assert data["email"] == "<EMAIL>"
            assert "password" not in data  # 密码不应该返回
    
    def test_register_duplicate_email(self, client):
        """测试重复邮箱注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
        
        with patch('app.api.v1.auth.create_user') as mock_create_user:
            mock_create_user.side_effect = ValueError("邮箱已存在")
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            data = response.json()
            assert "邮箱已存在" in data["detail"]
    
    def test_register_password_mismatch(self, client):
        """测试密码不匹配注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "different_password"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_login_success(self, client):
        """测试成功登录"""
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        with patch('app.api.v1.auth.authenticate_user') as mock_auth:
            mock_auth.return_value = {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>"
            }
            
            with patch('app.api.v1.auth.create_access_token') as mock_token:
                mock_token.return_value = "fake_jwt_token"
                
                response = client.post("/api/v1/auth/login", json=login_data)
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert "access_token" in data
                assert data["token_type"] == "bearer"
                assert "user" in data
    
    def test_login_invalid_credentials(self, client):
        """测试无效凭据登录"""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrong_password"
        }
        
        with patch('app.api.v1.auth.authenticate_user') as mock_auth:
            mock_auth.return_value = None
            
            response = client.post("/api/v1/auth/login", json=login_data)
            
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            data = response.json()
            assert "用户名或密码错误" in data["detail"]
    
    def test_refresh_token_success(self, client):
        """测试成功刷新令牌"""
        headers = {"Authorization": "Bearer valid_refresh_token"}
        
        with patch('app.api.v1.auth.verify_refresh_token') as mock_verify:
            mock_verify.return_value = {"user_id": 1}
            
            with patch('app.api.v1.auth.create_access_token') as mock_token:
                mock_token.return_value = "new_access_token"
                
                response = client.post("/api/v1/auth/refresh", headers=headers)
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["access_token"] == "new_access_token"
    
    def test_logout_success(self, client):
        """测试成功登出"""
        headers = {"Authorization": "Bearer valid_token"}
        
        with patch('app.api.v1.auth.revoke_token') as mock_revoke:
            mock_revoke.return_value = True
            
            response = client.post("/api/v1/auth/logout", headers=headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["message"] == "登出成功"


class TestAuthAPI:
    """认证API测试"""
    
    @pytest.fixture
    def app(self):
        """创建测试应用"""
        app = FastAPI()
        # app.include_router(auth_router, prefix="/api/v1/auth")
        return app
    
    @pytest.fixture
    def client(self, app):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_login_account_locked(self, client):
        """测试登录账户被锁定"""
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        with patch('app.api.v1.auth.authenticate_user') as mock_auth:
            mock_auth.side_effect = ValueError("Account locked")
            
            response = client.post("/api/v1/auth/login", json=login_data)
            
            assert response.status_code == status.HTTP_403_FORBIDDEN
            data = response.json()
            assert "Account locked" in data["detail"]


class TestAPIMiddleware:
    """API中间件测试"""
    
    def test_rate_limiting_middleware(self, client):
        """测试限流中间件"""
        # 模拟大量请求
        responses = []
        for i in range(10):
            response = client.get("/api/v1/videos")
            responses.append(response.status_code)
        
        # 检查是否有请求被限流
        assert any(code == 429 for code in responses)
    
    def test_cors_middleware(self, client):
        """测试CORS中间件"""
        response = client.options(
            "/api/v1/videos",
            headers={
                "Origin": "https://example.com",
                "Access-Control-Request-Method": "GET"
            }
        )
        
        assert "Access-Control-Allow-Origin" in response.headers
    
    def test_error_handling_middleware(self, client):
        """测试错误处理中间件"""
        with patch('app.api.v1.videos.get_video_list') as mock_get_list:
            mock_get_list.side_effect = Exception("Database error")
            
            response = client.get("/api/v1/videos")
            
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            data = response.json()
            assert "error_id" in data
            assert data["error"] == "InternalServerError"


class TestAPIValidation:
    """API验证测试"""
    
    def test_request_validation(self, client):
        """测试请求验证"""
        # 测试缺少必需字段
        invalid_data = {"email": "<EMAIL>"}  # 缺少密码
        
        response = client.post("/api/v1/auth/login", json=invalid_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data
    
    def test_response_validation(self, client):
        """测试响应验证"""
        with patch('app.api.v1.users.get_current_user') as mock_get_user:
            # 模拟返回格式错误的数据
            mock_get_user.return_value = {"invalid_field": "value"}
            
            headers = {"Authorization": "Bearer valid_token"}
            response = client.get("/api/v1/users/me", headers=headers)
            
            # 应该返回500错误，因为响应格式不正确
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    
    def test_input_sanitization(self, client):
        """测试输入清理"""
        malicious_data = {
            "username": "<script>alert('xss')</script>",
            "bio": "Normal bio with <script>alert('xss')</script>"
        }
        
        headers = {"Authorization": "Bearer valid_token"}
        
        with patch('app.api.v1.users.update_user_profile') as mock_update:
            mock_update.return_value = {
                "username": "alert('xss')",  # 脚本标签应该被移除
                "bio": "Normal bio with alert('xss')"
            }
            
            response = client.put(
                "/api/v1/users/me",
                json=malicious_data,
                headers=headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "<script>" not in data["username"]
            assert "<script>" not in data["bio"]


@pytest.mark.integration
class TestAPIIntegration:
    """API集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_user_workflow(self, client):
        """测试完整用户工作流"""
        # 1. 注册用户
        # 2. 登录获取令牌
        # 3. 使用令牌访问受保护资源
        # 4. 更新用户信息
        # 5. 登出
        pass
    
    @pytest.mark.asyncio
    async def test_video_upload_workflow(self, client):
        """测试视频上传工作流"""
        # 1. 用户登录
        # 2. 上传视频
        # 3. 检查处理状态
        # 4. 获取视频信息
        # 5. 更新视频信息
        pass
    
    @pytest.mark.asyncio
    async def test_api_performance(self, client):
        """测试API性能"""
        # 测试并发请求处理能力
        # 测试响应时间
        # 测试内存使用
        pass