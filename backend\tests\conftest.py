"""pytest配置文件
提供测试夹具和配置
"""

import os
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# 设置测试环境变量（必须在导入app模块之前设置）
os.environ.update({
    "ENVIRONMENT": "test",
    "DEBUG": "true",
    "DATABASE_URL": "sqlite:///test.db",
    "DB_HOST": "localhost",
    "DB_PORT": "3306",
    "DB_USER": "test_user",
    "DB_PASSWORD": "test_password",
    "DB_NAME": "test_db",
    "REDIS_URL": "redis://localhost:6379/1",
    "SECRET_KEY": "test-secret-key-for-testing-only-must-be-at-least-32-characters-long",
    "ALGORITHM": "HS256",
    "ACCESS_TOKEN_EXPIRE_MINUTES": "30",
    "REFRESH_TOKEN_EXPIRE_DAYS": "7",
    "CORS_ORIGINS": "http://localhost:3000,http://127.0.0.1:3000",
    "MAX_UPLOAD_SIZE": "104857600",  # 100MB
    "UPLOAD_DIR": "./uploads",
    "LOG_LEVEL": "INFO"
})


@pytest.fixture
def temp_file():
    """创建临时文件"""
    fd, path = tempfile.mkstemp()
    os.close(fd)
    yield path
    try:
        os.unlink(path)
    except OSError:
        pass


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def mock_redis():
    """模拟Redis客户端"""
    redis_mock = Mock()
    redis_mock.get = AsyncMock(return_value=None)
    redis_mock.set = AsyncMock(return_value=True)
    redis_mock.delete = AsyncMock(return_value=1)
    redis_mock.exists = AsyncMock(return_value=False)
    redis_mock.expire = AsyncMock(return_value=True)
    redis_mock.incr = AsyncMock(return_value=1)
    redis_mock.keys = AsyncMock(return_value=[])
    return redis_mock


@pytest.fixture
def mock_db_connection():
    """模拟数据库连接"""
    conn_mock = Mock()
    cursor_mock = Mock()
    
    # 设置cursor的上下文管理器
    cursor_mock.__enter__ = Mock(return_value=cursor_mock)
    cursor_mock.__exit__ = Mock(return_value=None)
    
    # 设置基本方法
    cursor_mock.execute = Mock()
    cursor_mock.fetchall = Mock(return_value=[])
    cursor_mock.fetchone = Mock(return_value=None)
    cursor_mock.rowcount = 0
    
    # 设置连接方法
    conn_mock.cursor = Mock(return_value=cursor_mock)
    conn_mock.commit = Mock()
    conn_mock.rollback = Mock()
    conn_mock.close = Mock()
    conn_mock.ping = Mock()
    conn_mock.begin = Mock()
    
    return conn_mock


@pytest.fixture
def sample_user_data():
    """示例用户数据"""
    return {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "password_hash": "hashed_password",
        "is_active": True,
        "created_at": "2023-01-01T00:00:00"
    }


@pytest.fixture
def sample_video_data():
    """示例视频数据"""
    return {
        "id": 1,
        "title": "Test Video",
        "description": "Test video description",
        "filename": "test_video.mp4",
        "url": "https://example.com/videos/test_video.mp4",
        "thumbnail": "https://example.com/thumbnails/test_video.jpg",
        "duration": 120,
        "size": 1024000,
        "status": "active",
        "user_id": 1,
        "views": 100,
        "likes": 10,
        "created_at": "2023-01-01T00:00:00"
    }


# 测试标记
pytest_plugins = []

# 配置pytest-asyncio
pytest.mark.asyncio_mode = "auto"