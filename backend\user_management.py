"""
统一用户管理系统
支持用户和管理员认证
"""

from typing import Dict, List, Optional, Union
from datetime import datetime, timedelta
import hashlib
import secrets
import json
from pathlib import Path


class User:
    """用户模型"""
    def __init__(self, user_id: int, username: str, email: str, 
                 password_hash: str, role: str = "user", 
                 display_name: str = "", is_active: bool = True,
                 created_at: datetime = None, last_login: datetime = None):
        self.user_id = user_id
        self.username = username
        self.email = email
        self.password_hash = password_hash
        self.role = role  # "user" or "admin"
        self.display_name = display_name or username
        self.is_active = is_active
        self.created_at = created_at or datetime.now()
        self.last_login = last_login

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email,
            "role": self.role,
            "display_name": self.display_name,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'User':
        """从字典创建用户"""
        return cls(
            user_id=data["user_id"],
            username=data["username"],
            email=data["email"],
            password_hash=data["password_hash"],
            role=data.get("role", "user"),
            display_name=data.get("display_name", ""),
            is_active=data.get("is_active", True),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
            last_login=datetime.fromisoformat(data["last_login"]) if data.get("last_login") else None
        )


class UserManager:
    """用户管理器"""
    
    def __init__(self, data_file: str = "users.json"):
        self.data_file = Path(data_file)
        self.users: Dict[str, User] = {}
        self.sessions: Dict[str, Dict] = {}  # token -> user_info
        self.load_users()
        self.init_default_users()

    def load_users(self):
        """加载用户数据"""
        if self.data_file.exists():
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for username, user_data in data.items():
                        self.users[username] = User.from_dict(user_data)
            except Exception as e:
                print(f"加载用户数据失败: {e}")

    def save_users(self):
        """保存用户数据"""
        try:
            data = {}
            for username, user in self.users.items():
                user_dict = user.to_dict()
                user_dict["password_hash"] = user.password_hash  # 保存密码哈希
                data[username] = user_dict
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存用户数据失败: {e}")

    def init_default_users(self):
        """初始化默认用户"""
        # 创建默认管理员
        if "admin" not in self.users:
            admin_user = User(
                user_id=1,
                username="admin",
                email="<EMAIL>",
                password_hash=self.hash_password("admin123"),
                role="admin",
                display_name="系统管理员"
            )
            self.users["admin"] = admin_user

        # 创建默认测试用户
        if "testuser" not in self.users:
            test_user = User(
                user_id=2,
                username="testuser",
                email="<EMAIL>",
                password_hash=self.hash_password("password123"),
                role="user",
                display_name="测试用户"
            )
            self.users["testuser"] = test_user

        # 保存默认用户
        self.save_users()

    def hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', 
                                          password.encode('utf-8'), 
                                          salt.encode('utf-8'), 
                                          100000)
        return f"{salt}:{password_hash.hex()}"

    def verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, stored_hash = password_hash.split(':')
            password_hash_check = hashlib.pbkdf2_hmac('sha256',
                                                    password.encode('utf-8'),
                                                    salt.encode('utf-8'),
                                                    100000)
            return stored_hash == password_hash_check.hex()
        except Exception:
            return False

    def authenticate(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        # 支持用户名或邮箱登录
        user = None
        if username in self.users:
            user = self.users[username]
        else:
            # 通过邮箱查找用户
            for u in self.users.values():
                if u.email == username:
                    user = u
                    break

        if user and user.is_active and self.verify_password(password, user.password_hash):
            # 更新最后登录时间
            user.last_login = datetime.now()
            self.save_users()
            return user
        
        return None

    def create_session(self, user: User) -> str:
        """创建会话"""
        token = secrets.token_urlsafe(32)
        self.sessions[token] = {
            "user_id": user.user_id,
            "username": user.username,
            "email": user.email,
            "role": user.role,
            "display_name": user.display_name,
            "created_at": datetime.now().isoformat(),
            "expires_at": (datetime.now() + timedelta(hours=24)).isoformat()
        }
        return token

    def get_user_by_token(self, token: str) -> Optional[Dict]:
        """通过token获取用户信息"""
        if token in self.sessions:
            session = self.sessions[token]
            expires_at = datetime.fromisoformat(session["expires_at"])
            if datetime.now() < expires_at:
                return session
            else:
                # 会话过期，删除
                del self.sessions[token]
        return None

    def logout(self, token: str) -> bool:
        """用户登出"""
        if token in self.sessions:
            del self.sessions[token]
            return True
        return False

    def create_user(self, username: str, email: str, password: str, 
                   role: str = "user", display_name: str = "") -> Optional[User]:
        """创建新用户"""
        if username in self.users:
            return None  # 用户已存在

        # 检查邮箱是否已存在
        for user in self.users.values():
            if user.email == email:
                return None  # 邮箱已存在

        # 创建新用户
        user_id = max([u.user_id for u in self.users.values()], default=0) + 1
        new_user = User(
            user_id=user_id,
            username=username,
            email=email,
            password_hash=self.hash_password(password),
            role=role,
            display_name=display_name or username
        )
        
        self.users[username] = new_user
        self.save_users()
        return new_user

    def get_all_users(self) -> List[Dict]:
        """获取所有用户（不包含密码哈希）"""
        return [user.to_dict() for user in self.users.values()]

    def get_user_by_username(self, username: str) -> Optional[User]:
        """通过用户名获取用户"""
        return self.users.get(username)

    def update_user(self, username: str, **kwargs) -> bool:
        """更新用户信息"""
        if username not in self.users:
            return False

        user = self.users[username]
        for key, value in kwargs.items():
            if hasattr(user, key) and key != 'password_hash':
                setattr(user, key, value)
            elif key == 'password' and value:
                user.password_hash = self.hash_password(value)

        self.save_users()
        return True

    def delete_user(self, username: str) -> bool:
        """删除用户"""
        if username in self.users and username != "admin":  # 不能删除管理员
            del self.users[username]
            self.save_users()
            return True
        return False


# 全局用户管理器实例
user_manager = UserManager()
