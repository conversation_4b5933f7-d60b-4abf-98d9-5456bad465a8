"""
大模型API KEY配置管理 - 后端管理员配置前端大模型API KEY和TOKEN监控
"""

from fastapi import APIRouter
from fastapi.responses import HTMLResponse

router = APIRouter(tags=["大模型API配置"])


def get_ai_model_config_content() -> str:
    """获取大模型API配置页面内容"""
    return """
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold gradient-text">大模型API配置管理</h2>
        <div class="flex space-x-4">
            <button class="modern-btn" onclick="refreshTokenUsage()">
                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                刷新TOKEN用量
            </button>
            <button class="modern-btn" onclick="addNewAPIKey()">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                添加API KEY
            </button>
        </div>
    </div>

    <!-- TOKEN使用量概览 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="modern-card p-6 animate-slideInUp delay-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 mb-1">本月TOKEN消耗</p>
                    <p class="text-2xl font-bold gradient-text">2,456,789</p>
                </div>
                <div class="p-3 bg-gradient-to-r from-blue-500 to-purple-600
                           rounded-lg">
                    <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                </div>
            </div>
            <div class="mt-2 text-sm text-blue-600">剩余额度: 7,543,211</div>
        </div>

        <div class="modern-card p-6 animate-slideInUp delay-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 mb-1">今日费用</p>
                    <p class="text-2xl font-bold gradient-text">￥156.78</p>
                </div>
                <div class="p-3 bg-gradient-to-r from-green-500 to-teal-600
                           rounded-lg">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-white"></i>
                </div>
            </div>
            <div class="mt-2 text-sm text-green-600">+12% 较昨日</div>
        </div>

        <div class="modern-card p-6 animate-slideInUp delay-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 mb-1">API调用次数</p>
                    <p class="text-2xl font-bold gradient-text">8,934</p>
                </div>
                <div class="p-3 bg-gradient-to-r from-purple-500 to-pink-600
                           rounded-lg">
                    <i data-lucide="activity" class="w-6 h-6 text-white"></i>
                </div>
            </div>
            <div class="mt-2 text-sm text-purple-600">成功率: 99.2%</div>
        </div>

        <div class="modern-card p-6 animate-slideInUp delay-400">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 mb-1">平均响应时间</p>
                    <p class="text-2xl font-bold gradient-text">1.2s</p>
                </div>
                <div class="p-3 bg-gradient-to-r from-orange-500 to-red-600
                           rounded-lg">
                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                </div>
            </div>
            <div class="mt-2 text-sm text-orange-600">性能优秀</div>
        </div>
    </div>

    <!-- API KEY配置列表 -->
    <div class="modern-card p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold gradient-text">API KEY配置</h3>
            <div class="flex space-x-2">
                <button class="text-sm modern-btn" onclick="exportConfig()">
                    <i data-lucide="download" class="w-4 h-4 mr-1"></i>
                    导出配置
                </button>
                <button class="text-sm modern-btn" onclick="importConfig()">
                    <i data-lucide="upload" class="w-4 h-4 mr-1"></i>
                    导入配置
                </button>
            </div>
        </div>

        <div class="space-y-4">
            <!-- OpenAI GPT配置 -->
            <div class="api-key-card p-6 bg-gradient-to-r from-green-50 to-emerald-50
                        border border-green-200 rounded-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500
                                    to-emerald-600 rounded-lg flex items-center
                                    justify-center">
                            <i data-lucide="brain" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">OpenAI GPT-4</h4>
                            <p class="text-sm text-gray-600">用于文本生成和对话</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="modern-badge success">已配置</div>
                        <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            API KEY
                        </label>
                        <div class="relative">
                            <input type="password" class="modern-input pr-10"
                                   value="sk-proj-*********************ABCD"
                                   id="openai-api-key">
                            <button class="absolute right-2 top-1/2 transform -translate-y-1/2"
                                    onclick="togglePasswordVisibility('openai-api-key')">
                                <i data-lucide="eye" class="w-4 h-4 text-gray-500"></i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            模型版本
                        </label>
                        <select class="modern-input">
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-4-turbo">GPT-4 Turbo</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            最大TOKEN
                        </label>
                        <input type="number" class="modern-input" value="4000"
                               placeholder="最大TOKEN数量">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">今日调用</div>
                        <div class="text-lg font-bold text-green-600">2,345</div>
                    </div>
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">TOKEN消耗</div>
                        <div class="text-lg font-bold text-blue-600">456,789</div>
                    </div>
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">费用</div>
                        <div class="text-lg font-bold text-purple-600">￥89.12</div>
                    </div>
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">成功率</div>
                        <div class="text-lg font-bold text-orange-600">99.8%</div>
                    </div>
                </div>

                <div class="flex justify-end space-x-2">
                    <button class="modern-btn-secondary" onclick="testAPIKey('openai')">
                        <i data-lucide="test-tube" class="w-4 h-4 mr-2"></i>
                        测试连接
                    </button>
                    <button class="modern-btn" onclick="saveAPIConfig('openai')">
                        <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                        保存配置
                    </button>
                </div>
            </div>

            <!-- Claude配置 -->
            <div class="api-key-card p-6 bg-gradient-to-r from-purple-50 to-violet-50
                        border border-purple-200 rounded-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500
                                    to-violet-600 rounded-lg flex items-center
                                    justify-center">
                            <i data-lucide="bot" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">Anthropic Claude</h4>
                            <p class="text-sm text-gray-600">高质量对话和分析</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="modern-badge warning">待配置</div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            API KEY
                        </label>
                        <div class="relative">
                            <input type="password" class="modern-input pr-10"
                                   placeholder="请输入Claude API KEY"
                                   id="claude-api-key">
                            <button class="absolute right-2 top-1/2 transform -translate-y-1/2"
                                    onclick="togglePasswordVisibility('claude-api-key')">
                                <i data-lucide="eye" class="w-4 h-4 text-gray-500"></i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            模型版本
                        </label>
                        <select class="modern-input">
                            <option value="claude-3-opus">Claude-3 Opus</option>
                            <option value="claude-3-sonnet">Claude-3 Sonnet</option>
                            <option value="claude-3-haiku">Claude-3 Haiku</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            最大TOKEN
                        </label>
                        <input type="number" class="modern-input" value="8000"
                               placeholder="最大TOKEN数量">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">今日调用</div>
                        <div class="text-lg font-bold text-gray-400">-</div>
                    </div>
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">TOKEN消耗</div>
                        <div class="text-lg font-bold text-gray-400">-</div>
                    </div>
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">费用</div>
                        <div class="text-lg font-bold text-gray-400">-</div>
                    </div>
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">成功率</div>
                        <div class="text-lg font-bold text-gray-400">-</div>
                    </div>
                </div>

                <div class="flex justify-end space-x-2">
                    <button class="modern-btn-secondary" onclick="testAPIKey('claude')" disabled>
                        <i data-lucide="test-tube" class="w-4 h-4 mr-2"></i>
                        测试连接
                    </button>
                    <button class="modern-btn" onclick="saveAPIConfig('claude')">
                        <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                        保存配置
                    </button>
                </div>
            </div>

            <!-- 通义千问配置 -->
            <div class="api-key-card p-6 bg-gradient-to-r from-blue-50 to-indigo-50
                        border border-blue-200 rounded-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500
                                    to-indigo-600 rounded-lg flex items-center
                                    justify-center">
                            <i data-lucide="message-circle" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">阿里通义千问</h4>
                            <p class="text-sm text-gray-600">国产大模型支持</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="modern-badge success">已配置</div>
                        <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            API KEY
                        </label>
                        <div class="relative">
                            <input type="password" class="modern-input pr-10"
                                   value="sk-******************xyz"
                                   id="qwen-api-key">
                            <button class="absolute right-2 top-1/2 transform -translate-y-1/2"
                                    onclick="togglePasswordVisibility('qwen-api-key')">
                                <i data-lucide="eye" class="w-4 h-4 text-gray-500"></i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            模型版本
                        </label>
                        <select class="modern-input">
                            <option value="qwen-turbo">通义千问-Turbo</option>
                            <option value="qwen-plus">通义千问-Plus</option>
                            <option value="qwen-max">通义千问-Max</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            最大TOKEN
                        </label>
                        <input type="number" class="modern-input" value="6000"
                               placeholder="最大TOKEN数量">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">今日调用</div>
                        <div class="text-lg font-bold text-blue-600">1,567</div>
                    </div>
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">TOKEN消耗</div>
                        <div class="text-lg font-bold text-blue-600">234,567</div>
                    </div>
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">费用</div>
                        <div class="text-lg font-bold text-blue-600">￥45.67</div>
                    </div>
                    <div class="text-center p-3 bg-white/50 rounded-lg">
                        <div class="text-sm text-gray-600">成功率</div>
                        <div class="text-lg font-bold text-blue-600">98.9%</div>
                    </div>
                </div>

                <div class="flex justify-end space-x-2">
                    <button class="modern-btn-secondary" onclick="testAPIKey('qwen')">
                        <i data-lucide="test-tube" class="w-4 h-4 mr-2"></i>
                        测试连接
                    </button>
                    <button class="modern-btn" onclick="saveAPIConfig('qwen')">
                        <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                        保存配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- TOKEN使用量详细统计 -->
    <div class="modern-card p-6 mb-8">
        <h3 class="text-lg font-semibold gradient-text mb-4">TOKEN使用量统计</h3>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 使用量趋势图 -->
            <div class="bg-white/30 rounded-lg p-4">
                <h4 class="font-medium text-gray-800 mb-3">本月TOKEN使用趋势</h4>
                <div class="h-64 flex items-end justify-between space-x-1">
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-blue-500 rounded-t" style="height: 60%"></div>
                        <span class="text-xs text-gray-600 mt-2">1日</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-blue-500 rounded-t" style="height: 75%"></div>
                        <span class="text-xs text-gray-600 mt-2">2日</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-blue-500 rounded-t" style="height: 45%"></div>
                        <span class="text-xs text-gray-600 mt-2">3日</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-blue-500 rounded-t" style="height: 90%"></div>
                        <span class="text-xs text-gray-600 mt-2">4日</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-blue-500 rounded-t" style="height: 80%"></div>
                        <span class="text-xs text-gray-600 mt-2">5日</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-blue-500 rounded-t" style="height: 95%"></div>
                        <span class="text-xs text-gray-600 mt-2">6日</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-blue-500 rounded-t" style="height: 100%"></div>
                        <span class="text-xs text-gray-600 mt-2">7日</span>
                    </div>
                </div>
            </div>

            <!-- 费用分布 -->
            <div class="bg-white/30 rounded-lg p-4">
                <h4 class="font-medium text-gray-800 mb-3">费用分布</h4>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">OpenAI GPT-4</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium">￥89.12 (57%)</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">通义千问</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium">￥45.67 (29%)</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">Claude</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium">￥21.99 (14%)</div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-700">总计</span>
                        <span class="text-lg font-bold gradient-text">￥156.78</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局配置 -->
    <div class="modern-card p-6">
        <h3 class="text-lg font-semibold gradient-text mb-4">全局配置</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    TOKEN使用量预警阈值
                </label>
                <div class="flex space-x-2">
                    <input type="number" class="modern-input flex-1" value="80"
                           placeholder="百分比">
                    <span class="flex items-center text-sm text-gray-600">%</span>
                </div>
                <p class="text-xs text-gray-500 mt-1">
                    当TOKEN使用量超过此百分比时发送预警
                </p>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    费用预算上限（月）
                </label>
                <div class="flex space-x-2">
                    <span class="flex items-center text-sm text-gray-600">￥</span>
                    <input type="number" class="modern-input flex-1" value="500"
                           placeholder="金额">
                </div>
                <p class="text-xs text-gray-500 mt-1">
                    月度费用预算上限，超出时暂停服务
                </p>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    自动切换备用API
                </label>
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="auto-switch" class="modern-checkbox" checked>
                    <label for="auto-switch" class="text-sm text-gray-700">
                        主API失败时自动切换到备用API
                    </label>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    使用量统计周期
                </label>
                <select class="modern-input">
                    <option value="realtime">实时</option>
                    <option value="hourly">每小时</option>
                    <option value="daily">每日</option>
                </select>
            </div>
        </div>

        <div class="flex justify-end mt-6 space-x-2">
            <button class="modern-btn-secondary" onclick="resetToDefaults()">
                <i data-lucide="rotate-ccw" class="w-4 h-4 mr-2"></i>
                恢复默认
            </button>
            <button class="modern-btn" onclick="saveGlobalConfig()">
                <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                保存全局配置
            </button>
        </div>
    </div>

    <script>
    // 切换密码可见性
    function togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        const icon = input.nextElementSibling.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.setAttribute('data-lucide', 'eye-off');
        } else {
            input.type = 'password';
            icon.setAttribute('data-lucide', 'eye');
        }

        // 重新初始化Lucide图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // 测试API KEY连接
    function testAPIKey(provider) {
        const button = event.target;
        const originalText = button.innerHTML;

        button.innerHTML = '<i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>测试中...';
        button.disabled = true;

        // 模拟API测试
        setTimeout(() => {
            button.innerHTML = '<i data-lucide="check" class="w-4 h-4 mr-2"></i>连接成功';
            button.className = 'modern-btn-secondary bg-green-100 text-green-700';

            setTimeout(() => {
                button.innerHTML = originalText;
                button.className = 'modern-btn-secondary';
                button.disabled = false;
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }, 2000);
        }, 2000);

        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // 保存API配置
    function saveAPIConfig(provider) {
        const button = event.target;
        const originalText = button.innerHTML;

        button.innerHTML = '<i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>保存中...';
        button.disabled = true;

        // 模拟保存过程
        setTimeout(() => {
            button.innerHTML = '<i data-lucide="check" class="w-4 h-4 mr-2"></i>已保存';
            button.className = 'modern-btn bg-green-500 hover:bg-green-600';

            // 显示成功消息
            showNotification(`${provider} API配置已成功保存`, 'success');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.className = 'modern-btn';
                button.disabled = false;
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }, 2000);
        }, 1500);

        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // 刷新TOKEN使用量
    function refreshTokenUsage() {
        const button = event.target;
        const originalText = button.innerHTML;

        button.innerHTML = '<i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>刷新中...';
        button.disabled = true;

        // 模拟刷新过程
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
            showNotification('TOKEN使用量已刷新', 'success');
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }, 2000);

        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // 添加新API KEY
    function addNewAPIKey() {
        showNotification('添加新API KEY功能开发中...', 'info');
    }

    // 导出配置
    function exportConfig() {
        const config = {
            apiKeys: {
                openai: { model: 'gpt-4', maxTokens: 4000 },
                claude: { model: 'claude-3-opus', maxTokens: 8000 },
                qwen: { model: 'qwen-turbo', maxTokens: 6000 }
            },
            globalSettings: {
                warningThreshold: 80,
                budgetLimit: 500,
                autoSwitch: true,
                statsInterval: 'realtime'
            }
        };

        const dataStr = JSON.stringify(config, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

        const exportFileDefaultName = 'ai_model_config.json';

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();

        showNotification('配置已导出', 'success');
    }

    // 导入配置
    function importConfig() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const config = JSON.parse(e.target.result);
                        console.log('导入配置:', config);
                        showNotification('配置导入成功', 'success');
                    } catch (error) {
                        showNotification('配置文件格式错误', 'error');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    // 保存全局配置
    function saveGlobalConfig() {
        const button = event.target;
        const originalText = button.innerHTML;

        button.innerHTML = '<i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>保存中...';
        button.disabled = true;

        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
            showNotification('全局配置已保存', 'success');
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }, 1500);

        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // 恢复默认设置
    function resetToDefaults() {
        if (confirm('确定要恢复到默认设置吗？此操作不可撤销。')) {
            // 重置表单值
            document.querySelector('input[value="80"]').value = '80';
            document.querySelector('input[value="500"]').value = '500';
            document.getElementById('auto-switch').checked = true;

            showNotification('已恢复默认设置', 'success');
        }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 animate-slideInRight`;

        const colors = {
            success: 'bg-green-500 text-white',
            error: 'bg-red-500 text-white',
            warning: 'bg-yellow-500 text-white',
            info: 'bg-blue-500 text-white'
        };

        notification.className += ` ${colors[type]}`;
        notification.innerHTML = `
            <div class="flex items-center space-x-2">
                <i data-lucide="${type === 'success' ? 'check' : type === 'error' ? 'x' : 'info'}"
                   class="w-5 h-5"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
    </script>
    """


@router.get("/", response_class=HTMLResponse)
async def ai_model_config_page():
    """大模型API配置管理页面"""
    return f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>大模型API配置管理 - 超现代化界面</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

            * {{
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            }}

            body {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }}

            .modern-card {{
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }}

            .modern-btn {{
                @apply px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600
                       text-white rounded-lg hover:from-blue-600 hover:to-purple-700
                       transition-all duration-300 transform hover:scale-105
                       shadow-lg flex items-center;
            }}

            .modern-btn-secondary {{
                @apply px-4 py-2 bg-white/20 text-gray-700 rounded-lg
                       hover:bg-white/30 transition-all duration-300
                       border border-white/30 flex items-center;
            }}

            .modern-input {{
                @apply w-full px-4 py-2 bg-white/20 border border-white/30
                       rounded-lg backdrop-filter backdrop-blur-sm
                       focus:outline-none focus:ring-2 focus:ring-blue-500/50
                       transition-all duration-300;
            }}

            .modern-checkbox {{
                @apply w-4 h-4 text-blue-600 bg-white/20 border-white/30
                       rounded focus:ring-blue-500 focus:ring-2;
            }}

            .modern-badge {{
                @apply px-2 py-1 rounded-full text-xs font-medium;
            }}

            .modern-badge.success {{
                @apply bg-green-100 text-green-800;
            }}

            .modern-badge.warning {{
                @apply bg-yellow-100 text-yellow-800;
            }}

            .modern-badge.error {{
                @apply bg-red-100 text-red-800;
            }}

            .gradient-text {{
                background: linear-gradient(135deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }}

            .api-key-card {{
                transition: all 0.3s ease;
            }}

            .api-key-card:hover {{
                transform: translateY(-2px);
                box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
            }}

            @keyframes slideInUp {{
                from {{ transform: translateY(20px); opacity: 0; }}
                to {{ transform: translateY(0); opacity: 1; }}
            }}

            @keyframes slideInRight {{
                from {{ transform: translateX(100px); opacity: 0; }}
                to {{ transform: translateX(0); opacity: 1; }}
            }}

            .animate-slideInUp {{
                animation: slideInUp 0.6s ease-out forwards;
            }}

            .animate-slideInRight {{
                animation: slideInRight 0.5s ease-out forwards;
            }}
        </style>
    </head>
    <body>
        <div class="container mx-auto px-6 py-8">
            {get_ai_model_config_content()}
        </div>

        <script>
            // 初始化Lucide图标
            lucide.createIcons();
        </script>
    </body>
    </html>
    """
