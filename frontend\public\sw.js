/**
 * Service Worker - 2025年最佳实践
 * 提供离线支持、缓存管理和后台同步
 */

const CACHE_NAME = 'video-system-v1.0.0'
const STATIC_CACHE = 'static-v1.0.0'
const DYNAMIC_CACHE = 'dynamic-v1.0.0'
const API_CACHE = 'api-v1.0.0'

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/shared-ui-design.css',
  '/shared-login-styles.css',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
]

// 需要缓存的API路径
const API_ROUTES = [
  '/api/auth/profile',
  '/api/system/status'
]

// 离线页面
const OFFLINE_PAGE = '/offline.html'

// 安装事件
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker 安装中...')
  
  event.waitUntil(
    Promise.all([
      // 缓存静态资源
      caches.open(STATIC_CACHE).then((cache) => {
        return cache.addAll(STATIC_ASSETS)
      }),
      // 预缓存离线页面
      caches.open(DYNAMIC_CACHE).then((cache) => {
        return cache.add(OFFLINE_PAGE)
      })
    ]).then(() => {
      console.log('✅ Service Worker 安装完成')
      // 立即激活新的Service Worker
      return self.skipWaiting()
    })
  )
})

// 激活事件
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker 激活中...')
  
  event.waitUntil(
    Promise.all([
      // 清理旧缓存
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== API_CACHE) {
              console.log('🗑️ 删除旧缓存:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      }),
      // 立即控制所有客户端
      self.clients.claim()
    ]).then(() => {
      console.log('✅ Service Worker 激活完成')
    })
  )
})

// 网络请求拦截
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // 只处理同源请求
  if (url.origin !== location.origin) {
    return
  }
  
  // API请求策略
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request))
    return
  }
  
  // 静态资源策略
  if (isStaticAsset(request)) {
    event.respondWith(handleStaticRequest(request))
    return
  }
  
  // 页面请求策略
  if (request.mode === 'navigate') {
    event.respondWith(handlePageRequest(request))
    return
  }
  
  // 其他请求使用网络优先策略
  event.respondWith(handleOtherRequest(request))
})

// API请求处理 - 网络优先，缓存备用
async function handleApiRequest(request) {
  const cacheName = API_CACHE
  
  try {
    // 尝试网络请求
    const networkResponse = await fetch(request)
    
    // 只缓存成功的GET请求
    if (request.method === 'GET' && networkResponse.ok) {
      const cache = await caches.open(cacheName)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    console.log('📡 网络请求失败，尝试缓存:', request.url)
    
    // 网络失败时使用缓存
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // 返回离线响应
    return new Response(
      JSON.stringify({ 
        error: 'Network unavailable', 
        message: '网络不可用，请检查网络连接' 
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

// 静态资源处理 - 缓存优先
async function handleStaticRequest(request) {
  const cachedResponse = await caches.match(request)
  
  if (cachedResponse) {
    return cachedResponse
  }
  
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    console.log('📦 静态资源加载失败:', request.url)
    throw error
  }
}

// 页面请求处理 - 网络优先，离线页面备用
async function handlePageRequest(request) {
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    console.log('📄 页面加载失败，显示离线页面:', request.url)
    
    // 尝试从缓存获取页面
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // 返回离线页面
    return caches.match(OFFLINE_PAGE)
  }
}

// 其他请求处理 - 网络优先
async function handleOtherRequest(request) {
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    throw error
  }
}

// 判断是否为静态资源
function isStaticAsset(request) {
  const url = new URL(request.url)
  const pathname = url.pathname
  
  return pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/) ||
         STATIC_ASSETS.includes(pathname)
}

// 后台同步
self.addEventListener('sync', (event) => {
  console.log('🔄 后台同步事件:', event.tag)
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync())
  }
})

// 执行后台同步
async function doBackgroundSync() {
  try {
    // 这里可以执行需要后台同步的任务
    // 例如：上传离线时创建的内容、同步用户数据等
    console.log('🔄 执行后台同步任务')
    
    // 示例：同步离线数据
    const offlineData = await getOfflineData()
    if (offlineData.length > 0) {
      await syncOfflineData(offlineData)
    }
  } catch (error) {
    console.error('❌ 后台同步失败:', error)
  }
}

// 获取离线数据（示例）
async function getOfflineData() {
  // 这里应该从IndexedDB或其他存储中获取离线数据
  return []
}

// 同步离线数据（示例）
async function syncOfflineData(data) {
  // 这里应该将离线数据同步到服务器
  console.log('📤 同步离线数据:', data.length, '条记录')
}

// 推送通知
self.addEventListener('push', (event) => {
  console.log('📬 收到推送通知')
  
  const options = {
    body: '您有新的消息',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: '查看详情',
        icon: '/icons/checkmark.png'
      },
      {
        action: 'close',
        title: '关闭',
        icon: '/icons/xmark.png'
      }
    ]
  }
  
  if (event.data) {
    const payload = event.data.json()
    options.body = payload.body || options.body
    options.data = { ...options.data, ...payload.data }
  }
  
  event.waitUntil(
    self.registration.showNotification('二创短视频分发系统', options)
  )
})

// 通知点击处理
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 通知被点击:', event.action)
  
  event.notification.close()
  
  if (event.action === 'explore') {
    // 打开应用
    event.waitUntil(
      clients.openWindow('/')
    )
  } else if (event.action === 'close') {
    // 关闭通知
    console.log('🔕 通知已关闭')
  }
})

// 错误处理
self.addEventListener('error', (event) => {
  console.error('❌ Service Worker 错误:', event.error)
})

// 未处理的Promise rejection
self.addEventListener('unhandledrejection', (event) => {
  console.error('❌ Service Worker 未处理的Promise rejection:', event.reason)
  event.preventDefault()
})

console.log('🎉 Service Worker 加载完成 - 版本:', CACHE_NAME)
