<template>
  <header class="h-16 px-4 flex items-center justify-between border-b border-border">
    <!-- 左侧：菜单按钮和标题 -->
    <div class="flex items-center space-x-4">
      <button
        @click="$emit('toggle-sidebar')"
        class="p-2 rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
        aria-label="切换侧边栏"
      >
        <svg
          class="w-5 h-5"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <path d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
      <h1 class="text-lg font-semibold text-foreground hidden md:block">AI视频内容创作系统</h1>
    </div>

    <!-- 右侧：工具栏 -->
    <div class="flex items-center space-x-2">
      <!-- 主题切换 -->
      <button
        @click="$emit('toggle-theme')"
        class="p-2 rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
        aria-label="切换主题"
      >
        <svg
          class="w-5 h-5"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <path
            v-if="isDarkTheme"
            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
          />
          <path
            v-else
            d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
          />
        </svg>
      </button>

      <!-- 状态栏切换 -->
      <button
        @click="$emit('toggle-status-bar')"
        class="p-2 rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors hidden lg:block"
        aria-label="切换状态栏"
      >
        <svg
          class="w-5 h-5"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <path d="M4 6h16M4 12h16M4 18h7" />
        </svg>
      </button>

      <!-- 用户菜单 -->
      <button
        class="p-2 rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
        aria-label="用户菜单"
      >
        <svg
          class="w-5 h-5"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2" />
          <circle cx="12" cy="7" r="4" />
        </svg>
      </button>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Props
interface Props {
  sidebarCollapsed: boolean
}
defineProps<Props>()

// Emits
defineEmits<{
  'toggle-sidebar': []
  'toggle-theme': []
  'toggle-status-bar': []
}>()

// 主题状态
const isDarkTheme = ref(false)

// 初始化主题状态
onMounted(() => {
  isDarkTheme.value = document.documentElement.classList.contains('dark')
  // eslint-disable-next-line no-undef
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        isDarkTheme.value = document.documentElement.classList.contains('dark')
      }
    })
  })
  observer.observe(document.documentElement, { attributes: true })
})
</script>

<style scoped>
.bg-card {
  background-color: hsl(var(--card));
}

.border-border {
  border-color: hsl(var(--border));
}

.text-foreground {
  color: hsl(var(--foreground));
}

.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.bg-background {
  background-color: hsl(var(--background));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.bg-popover {
  background-color: hsl(var(--popover));
}

.bg-destructive {
  background-color: hsl(var(--destructive));
}

.text-destructive {
  color: hsl(var(--destructive));
}

.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}

.hover\:bg-destructive:hover {
  background-color: hsl(var(--destructive));
}

.hover\:text-destructive-foreground:hover {
  color: hsl(var(--destructive-foreground));
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.focus\:ring-primary:focus {
  --tw-ring-color: hsl(var(--primary));
}
</style>