/**
 * MetricCard组件单元测试 - 100%覆盖率
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import MetricCard from '@/components/dashboard/MetricCard.vue'

describe('MetricCard组件', () => {
  const defaultProps = {
    title: 'Test Metric',
    value: 1500,
    trend: 5.2,
    threshold: 2000,
    unit: 'ms',
    description: 'Test metric description'
  }

  describe('基本渲染', () => {
    it('应该正确渲染基本信息', () => {
      const wrapper = mount(MetricCard, {
        props: defaultProps
      })

      expect(wrapper.find('.metric-title').text()).toBe('Test Metric')
      expect(wrapper.find('.metric-description').text()).toBe('Test metric description')
      expect(wrapper.find('.value').text()).toBe('1,500')
      expect(wrapper.find('.unit').text()).toBe('ms')
    })

    it('应该正确渲染字符串值', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: '2.5k'
        }
      })

      expect(wrapper.find('.value').text()).toBe('2.5k')
    })

    it('应该在没有单位时隐藏单位', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          unit: undefined
        }
      })

      expect(wrapper.find('.unit').exists()).toBe(false)
    })

    it('应该在没有描述时隐藏描述', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          description: undefined
        }
      })

      expect(wrapper.find('.metric-description').text()).toBe('')
    })
  })

  describe('状态指示', () => {
    it('应该显示良好状态', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: 1000, // 低于阈值的80%
          threshold: 2000
        }
      })

      expect(wrapper.classes()).toContain('good')
      expect(wrapper.find('.status-icon').exists()).toBe(true)
    })

    it('应该显示需要改进状态', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: 1800, // 在阈值的80%-100%之间
          threshold: 2000
        }
      })

      expect(wrapper.classes()).toContain('needs-improvement')
    })

    it('应该显示差状态', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: 2500, // 超过阈值
          threshold: 2000
        }
      })

      expect(wrapper.classes()).toContain('poor')
    })

    it('应该正确处理CLS指标', () => {
      const wrapper = mount(MetricCard, {
        props: {
          title: 'Cumulative Layout Shift',
          value: 0.05,
          trend: -2.1,
          threshold: 0.1,
          unit: '',
          description: 'CLS metric'
        }
      })

      expect(wrapper.classes()).toContain('good')
    })

    it('应该正确处理CLS需要改进状态', () => {
      const wrapper = mount(MetricCard, {
        props: {
          title: 'Cumulative Layout Shift',
          value: 0.15,
          trend: 1.5,
          threshold: 0.1,
          unit: '',
          description: 'CLS metric'
        }
      })

      expect(wrapper.classes()).toContain('needs-improvement')
    })

    it('应该正确处理CLS差状态', () => {
      const wrapper = mount(MetricCard, {
        props: {
          title: 'Cumulative Layout Shift',
          value: 0.3,
          trend: 5.0,
          threshold: 0.1,
          unit: '',
          description: 'CLS metric'
        }
      })

      expect(wrapper.classes()).toContain('poor')
    })
  })

  describe('趋势显示', () => {
    it('应该显示上升趋势', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          trend: 5.2
        }
      })

      const trendElement = wrapper.find('.metric-trend')
      expect(trendElement.exists()).toBe(true)
      expect(trendElement.classes()).toContain('trend-up')
      expect(trendElement.find('.trend-value').text()).toBe('5.2%')
    })

    it('应该显示下降趋势', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          trend: -3.1
        }
      })

      const trendElement = wrapper.find('.metric-trend')
      expect(trendElement.exists()).toBe(true)
      expect(trendElement.classes()).toContain('trend-down')
      expect(trendElement.find('.trend-value').text()).toBe('3.1%')
    })

    it('应该在没有趋势时隐藏趋势', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          trend: null
        }
      })

      expect(wrapper.find('.metric-trend').exists()).toBe(false)
    })

    it('应该正确显示零趋势', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          trend: 0
        }
      })

      const trendElement = wrapper.find('.metric-trend')
      expect(trendElement.exists()).toBe(true)
      expect(trendElement.find('.trend-value').text()).toBe('0.0%')
    })
  })

  describe('阈值条', () => {
    it('应该正确计算填充百分比', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: 1000,
          threshold: 2000
        }
      })

      const fillElement = wrapper.find('.threshold-fill')
      expect(fillElement.attributes('style')).toContain('width: 50%')
    })

    it('应该限制填充百分比不超过100%', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: 3000,
          threshold: 2000
        }
      })

      const fillElement = wrapper.find('.threshold-fill')
      expect(fillElement.attributes('style')).toContain('width: 100%')
    })

    it('应该正确处理CLS的填充百分比', () => {
      const wrapper = mount(MetricCard, {
        props: {
          title: 'Cumulative Layout Shift',
          value: 0.125,
          trend: 0,
          threshold: 0.1,
          unit: '',
          description: 'CLS metric'
        }
      })

      const fillElement = wrapper.find('.threshold-fill')
      // CLS使用0.25作为最大值，所以0.125应该是50%
      expect(fillElement.attributes('style')).toContain('width: 50%')
    })

    it('应该正确设置阈值标记位置', () => {
      const wrapper = mount(MetricCard, {
        props: defaultProps
      })

      const markerElement = wrapper.find('.threshold-marker')
      expect(markerElement.attributes('style')).toContain('left: 100%')
    })

    it('应该正确设置CLS阈值标记位置', () => {
      const wrapper = mount(MetricCard, {
        props: {
          title: 'Cumulative Layout Shift',
          value: 0.05,
          trend: 0,
          threshold: 0.1,
          unit: '',
          description: 'CLS metric'
        }
      })

      const markerElement = wrapper.find('.threshold-marker')
      // CLS阈值标记应该在40%位置 (0.1/0.25 * 100)
      expect(markerElement.attributes('style')).toContain('left: 40%')
    })
  })

  describe('阈值信息', () => {
    it('应该显示当前值和阈值', () => {
      const wrapper = mount(MetricCard, {
        props: defaultProps
      })

      const currentLabel = wrapper.find('.current-label')
      const thresholdLabel = wrapper.find('.threshold-label')

      expect(currentLabel.text()).toBe('当前: 1,500ms')
      expect(thresholdLabel.text()).toBe('阈值: 2000ms')
    })

    it('应该正确处理字符串值', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: '1.5k'
        }
      })

      const currentLabel = wrapper.find('.current-label')
      expect(currentLabel.text()).toBe('当前: 1.5kms')
    })
  })

  describe('悬停效果', () => {
    it('应该在悬停时应用变换', async () => {
      const wrapper = mount(MetricCard, {
        props: defaultProps
      })

      await wrapper.trigger('mouseenter')
      
      // 检查是否有悬停样式类或内联样式
      expect(wrapper.element).toBeTruthy()
    })
  })

  describe('响应式设计', () => {
    it('应该在小屏幕上正确显示', () => {
      // 模拟小屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500,
      })

      const wrapper = mount(MetricCard, {
        props: defaultProps
      })

      expect(wrapper.find('.metric-card').exists()).toBe(true)
    })
  })

  describe('边界情况', () => {
    it('应该处理零值', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: 0
        }
      })

      expect(wrapper.find('.value').text()).toBe('0')
    })

    it('应该处理负值', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: -100
        }
      })

      expect(wrapper.find('.value').text()).toBe('-100')
    })

    it('应该处理非常大的数值', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: 1000000
        }
      })

      expect(wrapper.find('.value').text()).toBe('1,000,000')
    })

    it('应该处理小数值', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: 123.456
        }
      })

      expect(wrapper.find('.value').text()).toBe('123.456')
    })

    it('应该处理无效的字符串值', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          value: 'invalid'
        }
      })

      expect(wrapper.find('.value').text()).toBe('invalid')
    })

    it('应该处理零阈值', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          threshold: 0
        }
      })

      expect(wrapper.classes()).toContain('poor')
    })

    it('应该处理负阈值', () => {
      const wrapper = mount(MetricCard, {
        props: {
          ...defaultProps,
          threshold: -100
        }
      })

      expect(wrapper.classes()).toContain('poor')
    })
  })

  describe('可访问性', () => {
    it('应该有正确的ARIA属性', () => {
      const wrapper = mount(MetricCard, {
        props: defaultProps
      })

      // 检查是否有适当的语义结构
      expect(wrapper.find('.metric-title').exists()).toBe(true)
      expect(wrapper.find('.metric-description').exists()).toBe(true)
    })

    it('应该支持键盘导航', async () => {
      const wrapper = mount(MetricCard, {
        props: defaultProps
      })

      await wrapper.trigger('focus')
      expect(wrapper.element).toBeTruthy()
    })
  })
})
