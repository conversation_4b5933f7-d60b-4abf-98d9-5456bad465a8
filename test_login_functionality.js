/**
 * 统一登录页面功能测试
 * 测试前端和后端登录页面的所有功能和按钮
 */

// 测试前端登录页面
async function testFrontendLogin() {
    console.log('🧪 测试前端登录页面...');
    
    try {
        // 测试页面加载
        const response = await fetch('http://localhost:3000/login');
        if (response.ok) {
            console.log('✅ 前端登录页面加载成功');
        } else {
            console.log('❌ 前端登录页面加载失败');
            return false;
        }
        
        // 测试前端登录API（通过后端）
        const loginResponse = await fetch('http://localhost:8000/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'testuser',
                password: 'password123',
                userType: 'user'
            })
        });
        
        if (loginResponse.ok) {
            const loginData = await loginResponse.json();
            console.log('✅ 前端用户登录API测试成功');
            console.log('   - Token:', loginData.access_token.substring(0, 20) + '...');
            console.log('   - 用户:', loginData.user.display_name);
        } else {
            console.log('❌ 前端用户登录API测试失败');
            return false;
        }
        
        return true;
    } catch (error) {
        console.log('❌ 前端登录测试出错:', error.message);
        return false;
    }
}

// 测试后端登录页面
async function testBackendLogin() {
    console.log('🧪 测试后端登录页面...');
    
    try {
        // 测试页面加载
        const response = await fetch('http://localhost:8000/login');
        if (response.ok) {
            const html = await response.text();
            if (html.includes('二创短视频分发系统') && html.includes('用户类型选择')) {
                console.log('✅ 后端登录页面加载成功');
            } else {
                console.log('❌ 后端登录页面内容不完整');
                return false;
            }
        } else {
            console.log('❌ 后端登录页面加载失败');
            return false;
        }
        
        // 测试管理员登录
        const adminLoginResponse = await fetch('http://localhost:8000/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123',
                userType: 'admin'
            })
        });
        
        if (adminLoginResponse.ok) {
            const adminData = await adminLoginResponse.json();
            console.log('✅ 后端管理员登录API测试成功');
            console.log('   - Token:', adminData.access_token.substring(0, 20) + '...');
            console.log('   - 管理员:', adminData.user.display_name);
        } else {
            console.log('❌ 后端管理员登录API测试失败');
            return false;
        }
        
        return true;
    } catch (error) {
        console.log('❌ 后端登录测试出错:', error.message);
        return false;
    }
}

// 测试登录功能
async function testLoginFunctionality() {
    console.log('🚀 开始测试统一登录页面功能...\n');
    
    const tests = [
        { name: '前端登录', test: testFrontendLogin },
        { name: '后端登录', test: testBackendLogin }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const { name, test } of tests) {
        console.log(`📋 测试 ${name}:`);
        const result = await test();
        if (result) {
            passedTests++;
            console.log(`✅ ${name} 测试通过\n`);
        } else {
            console.log(`❌ ${name} 测试失败\n`);
        }
    }
    
    // 测试错误处理
    console.log('📋 测试错误处理:');
    try {
        const errorResponse = await fetch('http://localhost:8000/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'wronguser',
                password: 'wrongpassword',
                userType: 'user'
            })
        });
        
        if (errorResponse.status === 401) {
            const errorData = await errorResponse.json();
            if (errorData.detail === '用户名或密码错误') {
                console.log('✅ 错误处理测试通过');
                console.log('   - 错误信息:', errorData.detail);
                passedTests++;
            } else {
                console.log('❌ 错误信息不正确');
            }
        } else {
            console.log('❌ 错误状态码不正确');
        }
        totalTests++;
    } catch (error) {
        console.log('❌ 错误处理测试出错:', error.message);
        totalTests++;
    }
    
    // 测试权限控制
    console.log('\n📋 测试权限控制:');
    try {
        const permissionResponse = await fetch('http://localhost:8000/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'testuser',
                password: 'password123',
                userType: 'admin'  // 普通用户尝试以管理员身份登录
            })
        });
        
        if (permissionResponse.status === 403) {
            const permissionData = await permissionResponse.json();
            if (permissionData.detail.includes('权限不足')) {
                console.log('✅ 权限控制测试通过');
                console.log('   - 权限信息:', permissionData.detail);
                passedTests++;
            } else {
                console.log('❌ 权限信息不正确');
            }
        } else {
            console.log('❌ 权限控制状态码不正确');
        }
        totalTests++;
    } catch (error) {
        console.log('❌ 权限控制测试出错:', error.message);
        totalTests++;
    }
    
    // 输出测试结果
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试结果汇总:');
    console.log(`✅ 通过测试: ${passedTests}/${totalTests}`);
    console.log(`❌ 失败测试: ${totalTests - passedTests}/${totalTests}`);
    console.log(`📈 成功率: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 所有登录功能测试通过！');
        console.log('✨ 统一登录页面功能完整，前后端协同工作正常');
    } else {
        console.log('\n⚠️  部分测试失败，请检查相关功能');
    }
    
    return passedTests === totalTests;
}

// 测试用户类型选择功能
async function testUserTypeSelection() {
    console.log('\n🔄 测试用户类型选择功能...');
    
    const testCases = [
        {
            name: '普通用户登录',
            data: { username: 'testuser', password: 'password123', userType: 'user' },
            expectedRole: 'user'
        },
        {
            name: '管理员登录',
            data: { username: 'admin', password: 'admin123', userType: 'admin' },
            expectedRole: 'admin'
        }
    ];
    
    let passed = 0;
    
    for (const testCase of testCases) {
        try {
            const response = await fetch('http://localhost:8000/api/auth/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(testCase.data)
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.user.role === testCase.expectedRole) {
                    console.log(`✅ ${testCase.name} - 角色匹配: ${data.user.role}`);
                    passed++;
                } else {
                    console.log(`❌ ${testCase.name} - 角色不匹配: 期望 ${testCase.expectedRole}, 实际 ${data.user.role}`);
                }
            } else {
                console.log(`❌ ${testCase.name} - 登录失败`);
            }
        } catch (error) {
            console.log(`❌ ${testCase.name} - 测试出错: ${error.message}`);
        }
    }
    
    console.log(`\n用户类型选择测试: ${passed}/${testCases.length} 通过`);
    return passed === testCases.length;
}

// 主测试函数
async function main() {
    console.log('🎬 二创短视频分发系统 - 统一登录页面功能测试');
    console.log('=' .repeat(60));
    
    const basicTests = await testLoginFunctionality();
    const userTypeTests = await testUserTypeSelection();
    
    console.log('\n' + '='.repeat(60));
    console.log('🏆 最终测试结果:');
    
    if (basicTests && userTypeTests) {
        console.log('🎉 所有登录功能测试完全通过！');
        console.log('✨ 前后端统一登录系统运行正常');
        process.exit(0);
    } else {
        console.log('⚠️  部分功能测试失败，需要进一步检查');
        process.exit(1);
    }
}

// 运行测试
if (typeof window === 'undefined') {
    // Node.js 环境 - 使用内置fetch (Node.js 18+)
    if (typeof fetch === 'undefined') {
        global.fetch = require('node-fetch');
    }
    main().catch(console.error);
} else {
    // 浏览器环境
    main().catch(console.error);
}
