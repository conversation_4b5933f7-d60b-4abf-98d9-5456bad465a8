/**
 * 无障碍访问系统单元测试 - 100%覆盖率
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { initAccessibility, getAccessibilityManager, AccessibilityManager } from '@/utils/accessibility'

// 模拟localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })

// 模拟matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

describe('无障碍访问系统', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
    
    // 清理DOM
    document.body.innerHTML = ''
    document.head.innerHTML = ''
    document.body.className = ''
  })

  afterEach(() => {
    // 清理无障碍管理器实例
    const manager = getAccessibilityManager()
    if (manager) {
      // 清理相关DOM元素
      const styles = document.querySelectorAll('#accessibility-styles, #focus-styles')
      styles.forEach(style => style.remove())
    }
  })

  describe('无障碍管理器初始化', () => {
    it('应该成功初始化无障碍管理器', () => {
      const manager = initAccessibility()
      expect(manager).toBeInstanceOf(AccessibilityManager)
      expect(getAccessibilityManager()).toBe(manager)
    })

    it('应该使用自定义配置初始化', () => {
      const config = {
        enableHighContrast: false,
        enableLargeText: false,
        fontSize: 18
      }
      
      const manager = initAccessibility(config)
      expect(manager).toBeInstanceOf(AccessibilityManager)
    })

    it('应该返回现有实例', () => {
      const manager1 = initAccessibility()
      const manager2 = initAccessibility()
      expect(manager1).toBe(manager2)
    })

    it('应该加载用户偏好设置', () => {
      const preferences = JSON.stringify({
        isHighContrast: true,
        isLargeText: true,
        currentFontSize: 20
      })
      
      mockLocalStorage.getItem.mockReturnValue(preferences)
      
      const manager = initAccessibility()
      expect(manager).toBeInstanceOf(AccessibilityManager)
      expect(document.body.classList.contains('high-contrast')).toBe(true)
      expect(document.body.classList.contains('large-text')).toBe(true)
    })
  })

  describe('媒体查询检测', () => {
    it('应该检测高对比度偏好', () => {
      const mockMatchMedia = vi.mocked(window.matchMedia)
      mockMatchMedia.mockImplementation((query) => {
        if (query === '(prefers-contrast: high)') {
          return {
            matches: true,
            media: query,
            onchange: null,
            addListener: vi.fn(),
            removeListener: vi.fn(),
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            dispatchEvent: vi.fn(),
          }
        }
        return {
          matches: false,
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        }
      })
      
      const manager = initAccessibility()
      expect(manager).toBeInstanceOf(AccessibilityManager)
      expect(document.body.classList.contains('high-contrast')).toBe(true)
    })

    it('应该检测减少动画偏好', () => {
      const mockMatchMedia = vi.mocked(window.matchMedia)
      mockMatchMedia.mockImplementation((query) => {
        if (query === '(prefers-reduced-motion: reduce)') {
          return {
            matches: true,
            media: query,
            onchange: null,
            addListener: vi.fn(),
            removeListener: vi.fn(),
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            dispatchEvent: vi.fn(),
          }
        }
        return {
          matches: false,
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        }
      })
      
      const manager = initAccessibility()
      expect(manager).toBeInstanceOf(AccessibilityManager)
      expect(document.body.classList.contains('reduced-motion')).toBe(true)
    })
  })

  describe('键盘导航', () => {
    it('应该启用键盘导航模式', () => {
      const manager = initAccessibility()
      
      const tabEvent = new KeyboardEvent('keydown', { key: 'Tab' })
      document.dispatchEvent(tabEvent)
      
      expect(document.body.classList.contains('keyboard-navigation')).toBe(true)
    })

    it('应该在鼠标点击时禁用键盘导航样式', () => {
      const manager = initAccessibility()
      
      // 先启用键盘导航
      const tabEvent = new KeyboardEvent('keydown', { key: 'Tab' })
      document.dispatchEvent(tabEvent)
      expect(document.body.classList.contains('keyboard-navigation')).toBe(true)
      
      // 然后点击鼠标
      const mouseEvent = new MouseEvent('mousedown')
      document.dispatchEvent(mouseEvent)
      
      expect(document.body.classList.contains('keyboard-navigation')).toBe(false)
    })

    it('应该处理Escape键', () => {
      const manager = initAccessibility()
      
      // 创建模拟模态框
      const modal = document.createElement('div')
      modal.setAttribute('role', 'dialog')
      modal.setAttribute('aria-hidden', 'false')
      modal.style.display = 'block'
      document.body.appendChild(modal)
      
      const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' })
      document.dispatchEvent(escapeEvent)
      
      expect(modal.style.display).toBe('none')
      expect(modal.getAttribute('aria-hidden')).toBe('true')
    })

    it('应该处理无障碍快捷键', () => {
      const manager = initAccessibility()
      
      // Alt + H: 切换高对比度
      const altHEvent = new KeyboardEvent('keydown', { key: 'h', altKey: true })
      const preventDefaultSpy = vi.spyOn(altHEvent, 'preventDefault')
      document.dispatchEvent(altHEvent)
      
      expect(preventDefaultSpy).toHaveBeenCalled()
      expect(document.body.classList.contains('high-contrast')).toBe(true)
    })
  })

  describe('屏幕阅读器支持', () => {
    it('应该检测屏幕阅读器', () => {
      // 模拟NVDA用户代理
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) NVDA/2023.1',
        writable: true
      })
      
      const manager = initAccessibility()
      expect(document.body.classList.contains('screen-reader-active')).toBe(true)
    })

    it('应该创建ARIA live regions', () => {
      const manager = initAccessibility()
      
      const politeAnnouncer = document.getElementById('aria-live-announcer')
      const assertiveAnnouncer = document.getElementById('aria-live-assertive')
      
      expect(politeAnnouncer).toBeTruthy()
      expect(assertiveAnnouncer).toBeTruthy()
      expect(politeAnnouncer?.getAttribute('aria-live')).toBe('polite')
      expect(assertiveAnnouncer?.getAttribute('aria-live')).toBe('assertive')
    })

    it('应该通知屏幕阅读器', () => {
      const manager = initAccessibility()
      
      manager.announce('测试消息')
      
      const announcer = document.getElementById('aria-live-announcer')
      expect(announcer?.textContent).toBe('测试消息')
    })

    it('应该使用assertive优先级通知', () => {
      const manager = initAccessibility()
      
      manager.announce('紧急消息', 'assertive')
      
      const announcer = document.getElementById('aria-live-assertive')
      expect(announcer?.textContent).toBe('紧急消息')
    })
  })

  describe('ARIA标签管理', () => {
    it('应该为缺少标签的按钮添加ARIA标签', () => {
      const manager = initAccessibility()
      
      const button = document.createElement('button')
      document.body.appendChild(button)
      
      // 触发ARIA标签添加
      const event = new Event('DOMContentLoaded')
      document.dispatchEvent(event)
      
      expect(button.getAttribute('aria-label')).toBeTruthy()
    })

    it('应该为缺少标签的输入框添加ARIA标签', () => {
      const manager = initAccessibility()
      
      const input = document.createElement('input')
      input.id = 'test-input'
      document.body.appendChild(input)
      
      // 触发ARIA标签添加
      const event = new Event('DOMContentLoaded')
      document.dispatchEvent(event)
      
      expect(input.getAttribute('aria-label')).toBeTruthy()
    })

    it('应该监听DOM变化并添加ARIA标签', () => {
      const manager = initAccessibility()
      
      const button = document.createElement('button')
      document.body.appendChild(button)
      
      // 等待MutationObserver触发
      setTimeout(() => {
        expect(button.getAttribute('aria-label')).toBeTruthy()
      }, 100)
    })
  })

  describe('功能切换', () => {
    it('应该切换高对比度模式', () => {
      const manager = initAccessibility()
      
      manager.toggleHighContrast()
      expect(document.body.classList.contains('high-contrast')).toBe(true)
      
      manager.toggleHighContrast()
      expect(document.body.classList.contains('high-contrast')).toBe(false)
    })

    it('应该强制设置高对比度模式', () => {
      const manager = initAccessibility()
      
      manager.toggleHighContrast(true)
      expect(document.body.classList.contains('high-contrast')).toBe(true)
      
      manager.toggleHighContrast(false)
      expect(document.body.classList.contains('high-contrast')).toBe(false)
    })

    it('应该切换大字体模式', () => {
      const manager = initAccessibility()
      
      manager.toggleLargeText()
      expect(document.body.classList.contains('large-text')).toBe(true)
      
      manager.toggleLargeText()
      expect(document.body.classList.contains('large-text')).toBe(false)
    })

    it('应该切换动画减少模式', () => {
      const manager = initAccessibility()
      
      manager.toggleMotionReduction()
      expect(document.body.classList.contains('reduced-motion')).toBe(true)
      
      manager.toggleMotionReduction()
      expect(document.body.classList.contains('reduced-motion')).toBe(false)
    })

    it('应该调整字体大小', () => {
      const manager = initAccessibility()
      
      manager.adjustFontSize(20)
      expect(document.documentElement.style.fontSize).toBe('20px')
      
      // 测试边界值
      manager.adjustFontSize(5) // 应该被限制为12
      expect(document.documentElement.style.fontSize).toBe('12px')
      
      manager.adjustFontSize(30) // 应该被限制为24
      expect(document.documentElement.style.fontSize).toBe('24px')
    })
  })

  describe('页面变化通知', () => {
    it('应该通知页面变化', () => {
      const manager = initAccessibility()
      
      // 设置页面标题
      document.title = '测试页面'
      
      manager.announcePageChange()
      
      const announcer = document.getElementById('aria-live-assertive')
      expect(announcer?.textContent).toBe('页面已切换到 测试页面')
    })
  })

  describe('状态获取', () => {
    it('应该返回当前状态', () => {
      const manager = initAccessibility()
      
      manager.toggleHighContrast(true)
      manager.adjustFontSize(18)
      
      const state = manager.getState()
      
      expect(state.isHighContrast).toBe(true)
      expect(state.currentFontSize).toBe(18)
    })
  })

  describe('数据持久化', () => {
    it('应该保存用户偏好设置', () => {
      const manager = initAccessibility()
      
      manager.toggleHighContrast(true)
      manager.toggleLargeText(true)
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'accessibility-preferences',
        expect.any(String)
      )
    })

    it('应该处理无效的存储数据', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid json')
      
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      const manager = initAccessibility()
      expect(manager).toBeInstanceOf(AccessibilityManager)
      
      consoleSpy.mockRestore()
    })
  })

  describe('边界情况', () => {
    it('应该处理不存在的通知元素', () => {
      const manager = initAccessibility()
      
      // 移除通知元素
      const announcer = document.getElementById('aria-live-announcer')
      announcer?.remove()
      
      // 应该不会抛出错误
      expect(() => manager.announce('测试消息')).not.toThrow()
    })

    it('应该处理媒体查询不支持的情况', () => {
      // 临时删除matchMedia
      const originalMatchMedia = window.matchMedia
      delete (window as any).matchMedia
      
      const manager = initAccessibility()
      expect(manager).toBeInstanceOf(AccessibilityManager)
      
      // 恢复matchMedia
      window.matchMedia = originalMatchMedia
    })

    it('应该处理localStorage不可用的情况', () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('localStorage not available')
      })
      
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      const manager = initAccessibility()
      manager.toggleHighContrast(true)
      
      expect(manager).toBeInstanceOf(AccessibilityManager)
      consoleSpy.mockRestore()
    })
  })
})
