import ipaddress
import time
import logging
from typing import Dict, Optional

from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

# 这是一个简化的示例，实际项目中应从数据库或配置文件中动态加载
# 比如从 app.models.SystemSettings 中获取
IP_WHITELIST = ["127.0.0.1"]


class IPWhitelistMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 仅对/admin路径生效
        if request.url.path.startswith("/admin"):
            client_ip = request.client.host

            # 检查白名单是否为空，如果为空则允许所有IP
            if IP_WHITELIST:
                is_allowed = False
                for allowed_ip in IP_WHITELIST:
                    try:
                        if ipaddress.ip_address(client_ip) in ipaddress.ip_network(
                            allowed_ip
                        ):
                            is_allowed = True
                            break
                    except ValueError:
                        # 处理无效的IP地址或网络地址
                        continue

                if not is_allowed:
                    return JSONResponse(
                        status_code=403,
                        content={"detail": f"IP address {client_ip} is not allowed."},
                    )

        response = await call_next(request)
        return response


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志记录中间件"""

    def __init__(self, app):
        super().__init__(app)
        self.logger = logging.getLogger("middleware.logging")

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # 记录请求信息
        self.logger.info(
            f"请求开始: {request.method} {request.url.path}",
            extra={
                "method": request.method,
                "path": request.url.path,
                "client_ip": request.client.host,
                "user_agent": request.headers.get("user-agent", "")
            }
        )
        
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 记录响应信息
        self.logger.info(
            f"请求完成: {request.method} {request.url.path} - {response.status_code}",
            extra={
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "process_time": process_time
            }
        )
        
        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全中间件"""

    async def dispatch(self, request: Request, call_next):
        # 添加安全头
        response = await call_next(request)
        
        # 安全响应头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """速率限制中间件"""

    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests: Dict[str, list] = {}
        self.logger = logging.getLogger("middleware.ratelimit")

    async def dispatch(self, request: Request, call_next):
        client_ip = request.client.host
        current_time = time.time()
        
        # 清理过期的请求记录
        if client_ip in self.requests:
            self.requests[client_ip] = [
                req_time for req_time in self.requests[client_ip]
                if current_time - req_time < 60  # 保留最近1分钟的记录
            ]
        else:
            self.requests[client_ip] = []
        
        # 检查是否超过速率限制
        if len(self.requests[client_ip]) >= self.requests_per_minute:
            self.logger.warning(
                f"速率限制触发: IP {client_ip} 超过 {self.requests_per_minute} 请求/分钟"
            )
            return JSONResponse(
                status_code=429,
                content={
                    "error": "请求过于频繁",
                    "code": "RATE_LIMIT_EXCEEDED",
                    "retry_after": 60
                },
                headers={"Retry-After": "60"}
            )
        
        # 记录当前请求
        self.requests[client_ip].append(current_time)
        
        response = await call_next(request)
        
        # 添加速率限制信息到响应头
        remaining = max(0, self.requests_per_minute - len(self.requests[client_ip]))
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(current_time + 60))
        
        return response
