"""
标准化日志模块
提供统一的日志格式和记录机制
"""

import json
import logging
import logging.handlers
import os
import sys
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, Optional


class LogLevel(Enum):
    """日志级别枚举"""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class CustomFormatter(logging.Formatter):
    """自定义日志格式化器"""

    def __init__(self):
        super().__init__()

    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 基础信息
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # 添加额外信息
        if hasattr(record, "extra_data"):
            log_data.update(record.extra_data)

        # 异常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_data, ensure_ascii=False, indent=None)


class ServiceLogger:
    """服务日志记录器"""

    def __init__(
        self,
        name: str,
        log_level: str = "INFO",
        log_file: Optional[str] = None,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5,
    ):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))

        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers(log_file, max_file_size, backup_count)

    def _setup_handlers(
        self, log_file: Optional[str], max_file_size: int, backup_count: int
    ):
        """设置日志处理器"""
        formatter = CustomFormatter()

        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # 文件处理器
        if log_file:
            # 确保日志目录存在
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)

            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding="utf-8",
            )
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)

    def debug(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """记录调试信息"""
        self._log(logging.DEBUG, message, extra_data)

    def info(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """记录信息"""
        self._log(logging.INFO, message, extra_data)

    def warning(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """记录警告"""
        self._log(logging.WARNING, message, extra_data)

    def error(
        self,
        message: str,
        extra_data: Optional[Dict[str, Any]] = None,
        exc_info: bool = True,
    ):
        """记录错误"""
        self._log(logging.ERROR, message, extra_data, exc_info)

    def critical(
        self,
        message: str,
        extra_data: Optional[Dict[str, Any]] = None,
        exc_info: bool = True,
    ):
        """记录严重错误"""
        self._log(logging.CRITICAL, message, extra_data, exc_info)

    def _log(
        self,
        level: int,
        message: str,
        extra_data: Optional[Dict[str, Any]] = None,
        exc_info: bool = False,
    ):
        """内部日志记录方法"""
        if extra_data:
            # 创建LogRecord并添加额外数据
            record = self.logger.makeRecord(
                self.logger.name, level, "", 0, message, (), None
            )
            record.extra_data = extra_data
            self.logger.handle(record)
        else:
            self.logger.log(level, message, exc_info=exc_info)


# 全局日志管理器
class LogManager:
    """日志管理器"""

    _loggers: Dict[str, ServiceLogger] = {}

    @classmethod
    def get_logger(
        cls, name: str, log_level: str = "INFO", log_file: Optional[str] = None
    ) -> ServiceLogger:
        """获取日志记录器"""
        if name not in cls._loggers:
            cls._loggers[name] = ServiceLogger(
                name=name, log_level=log_level, log_file=log_file
            )
        return cls._loggers[name]

    @classmethod
    def setup_service_logging(
        cls, service_name: str, log_level: str = "INFO", log_dir: str = "logs"
    ) -> ServiceLogger:
        """为服务设置日志"""
        log_file = os.path.join(log_dir, f"{service_name}.log")
        return cls.get_logger(name=service_name, log_level=log_level, log_file=log_file)


# 预配置的常用日志记录器
def get_app_logger() -> ServiceLogger:
    """获取应用主日志记录器"""
    return LogManager.setup_service_logging("app", "INFO")


def get_api_logger() -> ServiceLogger:
    """获取API日志记录器"""
    return LogManager.setup_service_logging("api", "INFO")


def get_service_logger(service_name: str) -> ServiceLogger:
    """获取服务日志记录器"""
    return LogManager.setup_service_logging(f"service.{service_name}", "INFO")


def get_task_logger() -> ServiceLogger:
    """获取任务日志记录器"""
    return LogManager.setup_service_logging("task", "INFO")


def get_error_logger() -> ServiceLogger:
    """获取错误日志记录器"""
    return LogManager.setup_service_logging("error", "ERROR")


def setup_logging(log_level: str = "INFO", log_dir: str = "logs"):
    """设置应用程序日志配置"""
    # 确保日志目录存在
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # 设置根日志记录器
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(os.path.join(log_dir, "app.log"), encoding="utf-8")
        ]
    )
    
    # 初始化主要的日志记录器
    get_app_logger()
    get_api_logger()
    get_error_logger()


# 装饰器用于记录函数调用
def log_function_call(logger: ServiceLogger):
    """函数调用日志装饰器"""

    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = datetime.now()
            logger.info(
                f"函数调用开始: {func.__name__}",
                extra_data={
                    "function": func.__name__,
                    "args_count": len(args),
                    "kwargs_keys": list(kwargs.keys()),
                    "start_time": start_time.isoformat(),
                },
            )

            try:
                result = func(*args, **kwargs)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                logger.info(
                    f"函数调用成功: {func.__name__}",
                    extra_data={
                        "function": func.__name__,
                        "duration": duration,
                        "end_time": end_time.isoformat(),
                    },
                )
                return result

            except Exception as e:
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                logger.error(
                    f"函数调用失败: {func.__name__}",
                    extra_data={
                        "function": func.__name__,
                        "duration": duration,
                        "error": str(e),
                        "error_type": type(e).__name__,
                        "end_time": end_time.isoformat(),
                    },
                )
                raise

        return wrapper

    return decorator


# 性能监控装饰器
def monitor_performance(logger: ServiceLogger, threshold_seconds: float = 1.0):
    """性能监控装饰器"""

    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = datetime.now()
            result = func(*args, **kwargs)
            end_time = datetime.now()

            duration = (end_time - start_time).total_seconds()

            if duration > threshold_seconds:
                logger.warning(
                    f"性能警告: {func.__name__} 执行时间过长",
                    extra_data={
                        "function": func.__name__,
                        "duration": duration,
                        "threshold": threshold_seconds,
                        "performance_issue": True,
                    },
                )

            return result

        return wrapper

    return decorator
