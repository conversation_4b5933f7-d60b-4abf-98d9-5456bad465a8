/**
 * 移动端响应式布局测试 - 2025年最佳实践
 * 测试不同设备尺寸下的布局表现
 */

import { test, expect, devices } from '@playwright/test';

// 测试配置
const BASE_URL = 'http://localhost:3000';

// 设备配置 - 2025年主流设备
const DEVICE_CONFIGS = [
  {
    name: 'iPhone 15 Pro',
    viewport: { width: 393, height: 852 },
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    deviceScaleFactor: 3,
    isMobile: true,
    hasTouch: true
  },
  {
    name: 'Samsung Galaxy S24',
    viewport: { width: 384, height: 854 },
    userAgent: 'Mozilla/5.0 (Linux; Android 14; SM-S921B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
    deviceScaleFactor: 3,
    isMobile: true,
    hasTouch: true
  },
  {
    name: 'iPad Pro 12.9',
    viewport: { width: 1024, height: 1366 },
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    deviceScaleFactor: 2,
    isMobile: false,
    hasTouch: true
  },
  {
    name: 'Desktop 1920x1080',
    viewport: { width: 1920, height: 1080 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    deviceScaleFactor: 1,
    isMobile: false,
    hasTouch: false
  },
  {
    name: 'Desktop 1366x768',
    viewport: { width: 1366, height: 768 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    deviceScaleFactor: 1,
    isMobile: false,
    hasTouch: false
  }
];

// 测试页面
const TEST_PAGES = [
  { path: '/', name: '首页' },
  { path: '/login', name: '登录页面' }
];

// 为每个设备创建测试
for (const device of DEVICE_CONFIGS) {
  test.describe(`${device.name} 响应式测试`, () => {
    test.use({
      viewport: device.viewport,
      userAgent: device.userAgent,
      deviceScaleFactor: device.deviceScaleFactor,
      isMobile: device.isMobile,
      hasTouch: device.hasTouch
    });

    test.beforeEach(async ({ page }) => {
      test.setTimeout(30000);
      
      // 设置测试环境
      await page.addInitScript(() => {
        window.localStorage.setItem('test_mode', 'true');
        window.localStorage.setItem('auth_token', 'test_token_123');
      });
    });

    for (const testPage of TEST_PAGES) {
      test(`${testPage.name} - 响应式布局测试`, async ({ page }) => {
        await page.goto(`${BASE_URL}${testPage.path}`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);

        // 基本页面加载检查
        await expect(page).toHaveTitle(/二创短视频分发系统/);

        if (testPage.path === '/login') {
          // 登录页面响应式检查
          await testLoginPageResponsive(page, device);
        } else {
          // 统一布局响应式检查
          await testUnifiedLayoutResponsive(page, device);
        }

        // 截图对比（可选）
        await page.screenshot({
          path: `test-results/screenshots/${device.name.replace(/\s+/g, '-')}-${testPage.name}.png`,
          fullPage: true
        });
      });
    }

    // 测试触摸交互（仅移动设备）
    if (device.hasTouch && device.isMobile) {
      test('触摸交互测试', async ({ page }) => {
        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');
        
        // 测试侧边栏切换（如果有汉堡菜单）
        const hamburgerMenu = page.locator('.mobile-menu-toggle');
        if (await hamburgerMenu.isVisible()) {
          await hamburgerMenu.tap();
          await page.waitForTimeout(500);
          
          // 检查侧边栏是否显示
          const sidebar = page.locator('.app-sidebar');
          await expect(sidebar).toBeVisible();
        }
      });
    }

    // 测试横竖屏切换（仅移动设备）
    if (device.isMobile) {
      test('横竖屏切换测试', async ({ page }) => {
        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');
        
        // 竖屏模式
        await page.setViewportSize({ 
          width: device.viewport.width, 
          height: device.viewport.height 
        });
        await page.waitForTimeout(1000);
        
        // 检查竖屏布局
        await checkPortraitLayout(page);
        
        // 横屏模式
        await page.setViewportSize({ 
          width: device.viewport.height, 
          height: device.viewport.width 
        });
        await page.waitForTimeout(1000);
        
        // 检查横屏布局
        await checkLandscapeLayout(page);
      });
    }
  });
}

// 辅助函数：测试登录页面响应式
async function testLoginPageResponsive(page, device) {
  // 检查登录容器
  const loginContainer = page.locator('.login-container');
  await expect(loginContainer).toBeVisible();
  
  // 检查表单元素
  const loginForm = page.locator('.login-form');
  await expect(loginForm).toBeVisible();
  
  // 移动端特殊检查
  if (device.isMobile) {
    // 检查表单是否适配移动端
    const formWidth = await loginForm.boundingBox();
    expect(formWidth.width).toBeLessThanOrEqual(device.viewport.width * 0.95);
    
    // 检查输入框是否足够大（移动端友好）
    const inputs = page.locator('input[type="text"], input[type="password"]');
    const inputCount = await inputs.count();
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      const inputBox = await input.boundingBox();
      expect(inputBox.height).toBeGreaterThanOrEqual(44); // iOS推荐最小触摸目标
    }
  }
}

// 辅助函数：测试统一布局响应式
async function testUnifiedLayoutResponsive(page, device) {
  const header = page.locator('.app-header');
  const sidebar = page.locator('.app-sidebar');
  const main = page.locator('.app-main');
  
  if (device.isMobile) {
    // 移动端布局检查
    await expect(header).toBeVisible();
    
    // 移动端侧边栏可能隐藏或折叠
    const sidebarVisible = await sidebar.isVisible();
    if (sidebarVisible) {
      // 如果侧边栏可见，检查是否为移动端样式
      const sidebarBox = await sidebar.boundingBox();
      // 移动端侧边栏通常是全宽或覆盖层
      expect(sidebarBox.width).toBeGreaterThanOrEqual(device.viewport.width * 0.7);
    }
    
    // 检查导航是否适配移动端
    const navItems = page.locator('.app-header__nav-item');
    const navCount = await navItems.count();
    
    if (navCount > 0) {
      // 检查导航项是否在移动端隐藏或折叠
      const firstNavItem = navItems.first();
      const navItemVisible = await firstNavItem.isVisible();
      
      if (navItemVisible) {
        // 如果导航可见，检查是否适合移动端
        const navBox = await firstNavItem.boundingBox();
        expect(navBox.height).toBeGreaterThanOrEqual(44); // 最小触摸目标
      }
    }
    
  } else {
    // 桌面端布局检查
    await expect(header).toBeVisible();
    await expect(sidebar).toBeVisible();
    await expect(main).toBeVisible();
    
    // 检查桌面端布局比例
    const headerBox = await header.boundingBox();
    const sidebarBox = await sidebar.boundingBox();
    const mainBox = await main.boundingBox();
    
    // 头部应该占满宽度
    expect(headerBox.width).toBeGreaterThanOrEqual(device.viewport.width * 0.95);
    
    // 侧边栏应该有合理的宽度
    expect(sidebarBox.width).toBeGreaterThanOrEqual(200);
    expect(sidebarBox.width).toBeLessThanOrEqual(400);
    
    // 主内容区应该占据剩余空间
    expect(mainBox.width).toBeGreaterThanOrEqual(device.viewport.width - sidebarBox.width - 50);
  }
}

// 辅助函数：检查竖屏布局
async function checkPortraitLayout(page) {
  const header = page.locator('.app-header');
  await expect(header).toBeVisible();
  
  // 竖屏模式下，导航可能折叠
  const navItems = page.locator('.app-header__nav-item');
  const navVisible = await navItems.first().isVisible();
  
  // 记录竖屏状态
  console.log('竖屏模式 - 导航可见:', navVisible);
}

// 辅助函数：检查横屏布局
async function checkLandscapeLayout(page) {
  const header = page.locator('.app-header');
  await expect(header).toBeVisible();
  
  // 横屏模式下，可能有更多空间显示导航
  const navItems = page.locator('.app-header__nav-item');
  const navVisible = await navItems.first().isVisible();
  
  // 记录横屏状态
  console.log('横屏模式 - 导航可见:', navVisible);
}
