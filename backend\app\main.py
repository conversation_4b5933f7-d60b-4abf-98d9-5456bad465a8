"""FastAPI应用主入口
🔒 证据链: 应用程序启动和路由配置
基于2024年最新技术文档
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

from app.api import (
    admin_ultra_modern,
    ai,
    auth,
    content,
    content_analysis,
    content_creation,
    distribution,
    health,
    monitoring,
    open_source,
    projects_enhanced,
    redis_cache,
    services,
    system,
    task_queue,
    tasks,
    video_processing,
)
from app.core.config import settings
from app.core.database import engine
from app.core.exceptions import CustomHTTPException
from app.core.logging import setup_logging
from app.core.middleware import (
    LoggingMiddleware,
    SecurityMiddleware,
    RateLimitMiddleware,
)
from app.models.database_models import Base
import sentry_sdk

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """应用生命周期管理"""
    logger.info("🚀 启动应用程序...")
    
    # 创建数据库表
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("✅ 数据库表创建成功")
    except Exception as e:
        logger.error(f"❌ 数据库表创建失败: {e}")
    
    yield
    
    logger.info("🛑 关闭应用程序...")


# 创建FastAPI应用实例
app = FastAPI(
    title="二创短视频分发系统",
    description="基于AI的短视频内容创作和多平台分发系统",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan,
)

if settings.SENTRY_DSN:
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        traces_sample_rate=1.0,
        environment="production" if not settings.DEBUG else "development"
    )
    logger.info("Sentry监控已初始化")
else:
    logger.warning("SENTRY_DSN未配置，监控未启用")
# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "*",  # 开发环境允许所有来源
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加自定义中间件
app.add_middleware(LoggingMiddleware)
app.add_middleware(SecurityMiddleware)
app.add_middleware(RateLimitMiddleware)

# 静态文件服务
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except Exception as e:
    logger.warning(f"静态文件目录不存在: {e}")

# 全局异常处理器
@app.exception_handler(CustomHTTPException)
async def custom_exception_handler(request: Request, exc: CustomHTTPException):
    """自定义异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "code": exc.error_code,
            "timestamp": exc.timestamp.isoformat(),
            "path": str(request.url),
        },
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "code": "INTERNAL_SERVER_ERROR",
            "path": str(request.url),
        },
    )

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "二创短视频分发系统 API",
        "version": "2.0.0",
        "status": "running",
        "docs": "/docs",
        "redoc": "/redoc",
    }

# 注册路由
app.include_router(health.router, prefix="/api/v1", tags=["健康检查"])
app.include_router(auth.router, prefix="/api/v1", tags=["认证"])
app.include_router(projects_enhanced.router, prefix="/api/v1", tags=["项目管理"])
app.include_router(content.router, prefix="/api/v1", tags=["内容管理"])
app.include_router(content_creation.router, prefix="/api/v1", tags=["内容创作"])
app.include_router(content_analysis.router, prefix="/api/v1", tags=["内容分析"])
app.include_router(video_processing.router, prefix="/api/v1", tags=["视频处理"])
app.include_router(distribution.router, prefix="/api/v1", tags=["内容分发"])
app.include_router(ai.router, prefix="/api/v1", tags=["AI服务"])
app.include_router(tasks.router, prefix="/api/v1", tags=["任务管理"])
app.include_router(task_queue.router, prefix="/api/v1", tags=["任务队列"])
app.include_router(services.router, prefix="/api/v1", tags=["服务管理"])
app.include_router(system.router, prefix="/api/v1", tags=["系统管理"])
app.include_router(monitoring.router, prefix="/api/v1", tags=["监控"])
app.include_router(redis_cache.router, prefix="/api/v1", tags=["缓存管理"])
app.include_router(open_source.router, prefix="/api/v1", tags=["开源"])
app.include_router(admin_ultra_modern.router, prefix="/api/v1/admin", tags=["管理后台"])

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )