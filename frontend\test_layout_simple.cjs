/**
 * 简单的布局测试脚本
 * 直接检查页面HTML内容
 */

const { execSync } = require('child_process');

function testPageLayout(path, pageName) {
  console.log(`\n📋 测试页面: ${pageName} (${path})`);
  
  try {
    // 使用curl获取页面内容
    const html = execSync(`curl -s http://localhost:3000${path}`, { encoding: 'utf8' });
    
    // 检查是否包含统一布局元素
    const hasAppHeader = html.includes('app-header');
    const hasAppSidebar = html.includes('app-sidebar');
    const hasAppMain = html.includes('app-main');
    
    // 检查是否包含登录页面元素
    const hasLoginContainer = html.includes('login-container');
    const hasUserTypeSelector = html.includes('user-type-selector');
    
    console.log(`   HTML长度: ${html.length} 字符`);
    console.log(`   包含app-header: ${hasAppHeader ? '✅' : '❌'}`);
    console.log(`   包含app-sidebar: ${hasAppSidebar ? '✅' : '❌'}`);
    console.log(`   包含app-main: ${hasAppMain ? '✅' : '❌'}`);
    console.log(`   包含login-container: ${hasLoginContainer ? '✅' : '❌'}`);
    console.log(`   包含user-type-selector: ${hasUserTypeSelector ? '✅' : '❌'}`);
    
    // 显示HTML的前200个字符
    console.log(`   HTML预览: ${html.substring(0, 200)}...`);
    
    if (path === '/login') {
      return hasLoginContainer && hasUserTypeSelector && !hasAppHeader;
    } else {
      return hasAppHeader && hasAppSidebar && hasAppMain;
    }
    
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
    return false;
  }
}

async function main() {
  console.log('🎬 简单布局测试');
  console.log('=' .repeat(50));
  
  const testPages = [
    { path: '/', name: '首页' },
    { path: '/video-creation', name: '视频创作' },
    { path: '/compute-test', name: '计算引擎测试' },
    { path: '/profile', name: '个人中心' },
    { path: '/login', name: '登录页面' }
  ];
  
  let passedTests = 0;
  
  for (const page of testPages) {
    const result = testPageLayout(page.path, page.name);
    if (result) {
      console.log(`✅ ${page.name} - 测试通过`);
      passedTests++;
    } else {
      console.log(`❌ ${page.name} - 测试失败`);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`📊 测试结果: ${passedTests}/${testPages.length} 通过`);
  
  return passedTests === testPages.length;
}

main().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('测试运行出错:', error);
  process.exit(1);
});
