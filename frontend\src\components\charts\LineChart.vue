<!--
  折线图组件 - 2025年最佳实践
  基于Chart.js的高性能图表组件
-->

<template>
  <div class="line-chart-container" :style="{ height: height + 'px' }">
    <canvas
      ref="chartCanvas"
      :width="width"
      :height="height"
      role="img"
      :aria-label="ariaLabel"
    ></canvas>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="chart-loading">
      <div class="loading-spinner"></div>
      <span>加载中...</span>
    </div>
    
    <!-- 无数据状态 -->
    <div v-if="!loading && isEmpty" class="chart-empty">
      <div class="empty-icon">📊</div>
      <span>暂无数据</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import {
  Chart,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'

// 注册Chart.js组件
Chart.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    borderColor?: string
    backgroundColor?: string
    fill?: boolean
    tension?: number
  }>
}

interface Props {
  data: ChartData
  options?: any
  width?: number
  height?: number
  loading?: boolean
  ariaLabel?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: 800,
  height: 400,
  loading: false,
  ariaLabel: '性能数据折线图'
})

const emit = defineEmits<{
  chartClick: [event: any, elements: any[]]
  chartHover: [event: any, elements: any[]]
}>()

// 响应式引用
const chartCanvas = ref<HTMLCanvasElement>()
let chartInstance: Chart | null = null

// 计算属性
const isEmpty = computed(() => {
  return !props.data?.datasets?.length || 
         props.data.datasets.every(dataset => !dataset.data?.length)
})

// 默认配置
const defaultOptions = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index' as const,
  },
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        usePointStyle: true,
        padding: 20,
        font: {
          size: 12
        }
      }
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      borderColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: true,
      callbacks: {
        title: (context: any) => {
          return `时间: ${context[0].label}`
        },
        label: (context: any) => {
          const label = context.dataset.label || ''
          const value = context.parsed.y
          return `${label}: ${value.toLocaleString()}ms`
        }
      }
    }
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true,
        text: '时间',
        font: {
          size: 14,
          weight: 'bold'
        }
      },
      grid: {
        color: 'rgba(0, 0, 0, 0.1)',
        drawBorder: false
      },
      ticks: {
        maxTicksLimit: 10,
        font: {
          size: 11
        }
      }
    },
    y: {
      display: true,
      title: {
        display: true,
        text: '响应时间 (ms)',
        font: {
          size: 14,
          weight: 'bold'
        }
      },
      grid: {
        color: 'rgba(0, 0, 0, 0.1)',
        drawBorder: false
      },
      ticks: {
        callback: function(value: any) {
          return value.toLocaleString() + 'ms'
        },
        font: {
          size: 11
        }
      },
      beginAtZero: true
    }
  },
  elements: {
    line: {
      tension: 0.4,
      borderWidth: 2
    },
    point: {
      radius: 4,
      hoverRadius: 6,
      borderWidth: 2,
      hoverBorderWidth: 3
    }
  },
  animation: {
    duration: 750,
    easing: 'easeInOutQuart'
  }
}

// 创建图表
const createChart = async () => {
  if (!chartCanvas.value || isEmpty.value) return

  await nextTick()

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  // 合并配置
  const mergedOptions = {
    ...defaultOptions,
    ...props.options,
    onClick: (event: any, elements: any[]) => {
      emit('chartClick', event, elements)
      props.options?.onClick?.(event, elements)
    },
    onHover: (event: any, elements: any[]) => {
      emit('chartHover', event, elements)
      props.options?.onHover?.(event, elements)
    }
  }

  // 处理数据集默认样式
  const processedData = {
    ...props.data,
    datasets: props.data.datasets.map((dataset, index) => ({
      ...dataset,
      borderColor: dataset.borderColor || getDefaultColor(index),
      backgroundColor: dataset.backgroundColor || getDefaultColor(index, 0.1),
      fill: dataset.fill !== undefined ? dataset.fill : false,
      tension: dataset.tension !== undefined ? dataset.tension : 0.4
    }))
  }

  chartInstance = new Chart(ctx, {
    type: 'line',
    data: processedData,
    options: mergedOptions
  })
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  if (isEmpty.value) {
    chartInstance.destroy()
    chartInstance = null
    return
  }

  // 更新数据
  chartInstance.data = {
    ...props.data,
    datasets: props.data.datasets.map((dataset, index) => ({
      ...dataset,
      borderColor: dataset.borderColor || getDefaultColor(index),
      backgroundColor: dataset.backgroundColor || getDefaultColor(index, 0.1),
      fill: dataset.fill !== undefined ? dataset.fill : false,
      tension: dataset.tension !== undefined ? dataset.tension : 0.4
    }))
  }

  // 更新配置
  if (props.options) {
    chartInstance.options = {
      ...defaultOptions,
      ...props.options
    }
  }

  chartInstance.update('active')
}

// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.destroy()
    chartInstance = null
  }
}

// 获取默认颜色
const getDefaultColor = (index: number, alpha = 1) => {
  const colors = [
    `rgba(255, 107, 107, ${alpha})`, // 红色
    `rgba(78, 205, 196, ${alpha})`,  // 青色
    `rgba(69, 183, 209, ${alpha})`,  // 蓝色
    `rgba(255, 195, 113, ${alpha})`, // 橙色
    `rgba(196, 167, 231, ${alpha})`, // 紫色
    `rgba(72, 219, 251, ${alpha})`   // 天蓝色
  ]
  return colors[index % colors.length]
}

// 响应式处理
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 生命周期
onMounted(() => {
  createChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  destroyChart()
  window.removeEventListener('resize', handleResize)
})

// 监听数据变化
watch(() => props.data, () => {
  if (chartInstance) {
    updateChart()
  } else {
    createChart()
  }
}, { deep: true })

watch(() => props.options, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

// 暴露方法
defineExpose({
  getChart: () => chartInstance,
  updateChart,
  destroyChart
})
</script>

<style scoped>
.line-chart-container {
  position: relative;
  width: 100%;
  background: var(--color-background);
  border-radius: 8px;
  overflow: hidden;
}

.chart-loading,
.chart-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: var(--color-text-soft);
  font-size: 14px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.empty-icon {
  font-size: 48px;
  opacity: 0.5;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .line-chart-container {
    font-size: 12px;
  }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  .line-chart-container {
    border: 1px solid var(--color-border);
  }
}

/* 减少动画支持 */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
}
</style>
