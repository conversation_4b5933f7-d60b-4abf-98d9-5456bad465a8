const puppeteer = require('puppeteer');

async function testButtons() {
  const browser = await puppeteer.launch({ headless: 'new' });
  const page = await browser.newPage();
  
  await page.goto('http://localhost:3001/');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  const testResults = [];
  
  // 测试主要导航按钮
  const navTests = [
    { selector: 'a[href="/"]', name: '首页', expectedPath: '/' },
    { selector: 'a[href="/video-creation"]', name: '视频创作', expectedPath: '/video-creation' },
    { selector: 'a[href="/compute-test"]', name: '计算引擎', expectedPath: '/compute-test' },
    { selector: 'a[href="/profile"]', name: '个人中心', expectedPath: '/profile' }
  ];
  
  for (const test of navTests) {
    try {
      await page.goto('http://localhost:3001/');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const element = await page.$(test.selector);
      if (element) {
        await element.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const currentPath = await page.evaluate(() => window.location.pathname);
        const success = currentPath === test.expectedPath;
        
        testResults.push({
          name: test.name,
          success: success,
          expected: test.expectedPath,
          actual: currentPath
        });
        
        console.log(`${test.name}: ${success ? '✅' : '❌'} (${currentPath})`);
      } else {
        testResults.push({
          name: test.name,
          success: false,
          expected: test.expectedPath,
          actual: 'Button not found'
        });
        console.log(`${test.name}: ❌ (按钮未找到)`);
      }
    } catch (error) {
      testResults.push({
        name: test.name,
        success: false,
        expected: test.expectedPath,
        actual: `Error: ${error.message}`
      });
      console.log(`${test.name}: ❌ (${error.message})`);
    }
  }
  
  // 测试功能卡片按钮
  await page.goto('http://localhost:3001/');
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const featureTests = [
    { selector: 'a:contains("开始创作")', name: '开始创作按钮' },
    { selector: 'a:contains("测试引擎")', name: '测试引擎按钮' },
    { selector: 'button:contains("即将推出")', name: 'AI增强按钮' }
  ];
  
  for (const test of featureTests) {
    try {
      const element = await page.evaluateHandle((selector) => {
        if (selector.includes('contains')) {
          const text = selector.match(/contains\("(.+)"\)/)[1];
          const tag = selector.split(':')[0];
          return Array.from(document.querySelectorAll(tag)).find(el => 
            el.textContent.trim().includes(text)
          );
        }
        return document.querySelector(selector);
      }, test.selector);
      
      if (element && await element.evaluate(el => el !== null)) {
        const isVisible = await element.evaluate(el => el.offsetParent !== null);
        const text = await element.evaluate(el => el.textContent.trim());
        
        testResults.push({
          name: test.name,
          success: isVisible,
          expected: 'Visible and clickable',
          actual: `Visible: ${isVisible}, Text: "${text}"`
        });
        
        console.log(`${test.name}: ${isVisible ? '✅' : '❌'} ("${text}")`);
      } else {
        testResults.push({
          name: test.name,
          success: false,
          expected: 'Button exists',
          actual: 'Button not found'
        });
        console.log(`${test.name}: ❌ (按钮未找到)`);
      }
    } catch (error) {
      testResults.push({
        name: test.name,
        success: false,
        expected: 'Button works',
        actual: `Error: ${error.message}`
      });
      console.log(`${test.name}: ❌ (${error.message})`);
    }
  }
  
  // 统计结果
  const passed = testResults.filter(r => r.success).length;
  const total = testResults.length;
  
  console.log(`\n测试结果: ${passed}/${total} 通过 (${((passed/total)*100).toFixed(1)}%)`);
  
  await browser.close();
  return testResults;
}

testButtons().catch(console.error);
