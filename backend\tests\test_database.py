"""数据库相关测试
测试数据库连接池、安全性和缓存功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import pymysql
import time

# 使用try-except来处理导入，避免测试时的导入错误
try:
    from app.core.database_security import (
        ConnectionPool, 
        SQLInjectionDetector, 
        SafeQueryBuilder
    )
    from app.core.cache import RedisCache, cache_result
    from app.utils.pagination import (
        Paginator, 
        CursorPaginator, 
        PaginationParams,
        paginate
    )
except ImportError:
    # 如果导入失败，创建模拟类
    ConnectionPool = Mock
    SQLInjectionDetector = Mock
    SafeQueryBuilder = Mock
    RedisCache = Mock
    cache_result = lambda **kwargs: lambda func: func
    Paginator = Mock
    CursorPaginator = Mock
    PaginationParams = Mock
    paginate = Mock


class TestConnectionPool:
    """数据库连接池测试"""
    
    @pytest.fixture
    def mock_connection(self):
        """模拟数据库连接"""
        conn = Mock()
        conn.ping.return_value = None
        conn.close.return_value = None
        conn.cursor.return_value = Mock()
        return conn
    
    @pytest.fixture
    def connection_pool(self, mock_connection):
        """创建连接池实例"""
        with patch('app.core.database_security.pymysql.connect') as mock_connect:
            mock_connect.return_value = mock_connection
            pool = ConnectionPool()
            return pool
    
    def test_connection_pool_initialization(self, connection_pool):
        """测试连接池初始化"""
        assert connection_pool.max_connections > 0
        assert connection_pool.connection_timeout > 0
        assert isinstance(connection_pool.connections, list)
        assert isinstance(connection_pool.connection_info, dict)
    
    @patch('app.core.database_security.pymysql.connect')
    def test_create_connection_success(self, mock_connect, connection_pool, mock_connection):
        """测试成功创建连接"""
        mock_connect.return_value = mock_connection
        
        conn = connection_pool._create_connection()
        assert conn is not None
        mock_connect.assert_called_once()
    
    @patch('app.core.database_security.pymysql.connect')
    def test_create_connection_failure(self, mock_connect, connection_pool):
        """测试连接创建失败"""
        mock_connect.side_effect = pymysql.Error("Connection failed")
        
        conn = connection_pool._create_connection()
        assert conn is None
    
    def test_is_connection_healthy_valid(self, connection_pool, mock_connection):
        """测试健康连接检查"""
        mock_connection.ping.return_value = None
        
        is_healthy = connection_pool._is_connection_healthy(mock_connection)
        assert is_healthy is True
        mock_connection.ping.assert_called_once()
    
    def test_is_connection_healthy_invalid(self, connection_pool, mock_connection):
        """测试不健康连接检查"""
        mock_connection.ping.side_effect = pymysql.Error("Connection lost")
        
        is_healthy = connection_pool._is_connection_healthy(mock_connection)
        assert is_healthy is False
    
    def test_cleanup_connections(self, connection_pool, mock_connection):
        """测试连接清理"""
        # 添加一个过期连接
        connection_id = id(mock_connection)
        connection_pool.connections.append(mock_connection)
        connection_pool.connection_info[connection_id] = {
            'created_at': time.time() - 7200,  # 2小时前创建
            'last_used': time.time() - 3700,   # 1小时前使用
            'use_count': 100
        }
        
        initial_count = len(connection_pool.connections)
        connection_pool._cleanup_connections()
        
        # 连接应该被清理
        assert len(connection_pool.connections) < initial_count
    
    @patch('app.core.database_security.pymysql.connect')
    def test_get_connection_success(self, mock_connect, connection_pool, mock_connection):
        """测试成功获取连接"""
        mock_connect.return_value = mock_connection
        
        conn = connection_pool.get_connection()
        assert conn is not None
        
        # 检查连接信息是否更新
        connection_id = id(conn)
        assert connection_id in connection_pool.connection_info
        assert connection_pool.connection_info[connection_id]['use_count'] >= 1
    
    def test_return_connection(self, connection_pool, mock_connection):
        """测试归还连接"""
        # 先添加连接到池中
        connection_pool.connections.append(mock_connection)
        connection_id = id(mock_connection)
        connection_pool.connection_info[connection_id] = {
            'created_at': time.time(),
            'last_used': time.time(),
            'use_count': 1
        }
        
        # 移除连接（模拟获取）
        connection_pool.connections.remove(mock_connection)
        
        # 归还连接
        connection_pool.return_connection(mock_connection)
        
        # 检查连接是否回到池中
        assert mock_connection in connection_pool.connections
        assert connection_pool.connection_info[connection_id]['last_used'] > 0


class TestSQLInjectionDetector:
    """SQL注入检测器测试"""
    
    @pytest.fixture
    def detector(self):
        """创建SQL注入检测器实例"""
        return SQLInjectionDetector()
    
    def test_detect_sql_injection_safe(self, detector):
        """测试安全SQL检测"""
        safe_queries = [
            "SELECT * FROM users WHERE id = %s",
            "INSERT INTO posts (title, content) VALUES (%s, %s)",
            "UPDATE users SET email = %s WHERE id = %s"
        ]
        
        for query in safe_queries:
            is_safe, risk_level = detector.detect_sql_injection(query)
            assert is_safe is True
            assert risk_level == "low"
    
    def test_detect_sql_injection_dangerous(self, detector):
        """测试危险SQL检测"""
        dangerous_queries = [
            "SELECT * FROM users WHERE id = 1; DROP TABLE users; --",
            "SELECT * FROM users WHERE name = 'admin' OR '1'='1'",
            "INSERT INTO users (name) VALUES ('test'); DELETE FROM users; --')"
        ]
        
        for query in dangerous_queries:
            is_safe, risk_level = detector.detect_sql_injection(query)
            assert is_safe is False
            assert risk_level in ["medium", "high"]
    
    def test_analyze_query_patterns(self, detector):
        """测试查询模式分析"""
        # 测试包含多个语句的查询
        query = "SELECT * FROM users; DROP TABLE users;"
        patterns = detector.analyze_query_patterns(query)
        
        assert "multiple_statements" in patterns
        assert patterns["multiple_statements"] is True
    
    def test_check_suspicious_keywords(self, detector):
        """测试可疑关键词检查"""
        # 测试包含DROP的查询
        query = "DROP TABLE users"
        keywords = detector.check_suspicious_keywords(query)
        
        assert "DROP" in keywords
    
    def test_validate_query_structure(self, detector):
        """测试查询结构验证"""
        # 测试正常查询
        normal_query = "SELECT name, email FROM users WHERE active = 1"
        is_valid, issues = detector.validate_query_structure(normal_query)
        assert is_valid is True
        assert len(issues) == 0
        
        # 测试异常查询
        malicious_query = "SELECT * FROM users WHERE 1=1 OR 'a'='a'"
        is_valid, issues = detector.validate_query_structure(malicious_query)
        assert is_valid is False
        assert len(issues) > 0


class TestSafeQueryBuilder:
    """安全查询构建器测试"""
    
    @pytest.fixture
    def query_builder(self, mock_db_connection):
        """创建安全查询构建器实例"""
        return SafeQueryBuilder(mock_db_connection)
    
    def test_build_select_query_basic(self, query_builder):
        """测试基本SELECT查询构建"""
        query, params = query_builder.build_select_query(
            table="users",
            columns=["id", "name", "email"],
            where_conditions={"active": 1}
        )
        
        expected_query = "SELECT id, name, email FROM users WHERE active = %s"
        assert query == expected_query
        assert params == [1]
    
    def test_build_select_query_with_joins(self, query_builder):
        """测试带JOIN的SELECT查询构建"""
        query, params = query_builder.build_select_query(
            table="users u",
            columns=["u.name", "p.title"],
            joins=[
                "LEFT JOIN posts p ON u.id = p.user_id"
            ],
            where_conditions={"u.active": 1}
        )
        
        assert "LEFT JOIN posts p ON u.id = p.user_id" in query
        assert "WHERE u.active = %s" in query
        assert params == [1]
    
    def test_build_insert_query(self, query_builder):
        """测试INSERT查询构建"""
        query, params = query_builder.build_insert_query(
            table="users",
            data={"name": "John", "email": "<EMAIL>"}
        )
        
        assert "INSERT INTO users" in query
        assert "(name, email)" in query
        assert "VALUES (%s, %s)" in query
        assert "John" in params
        assert "<EMAIL>" in params
    
    def test_build_update_query(self, query_builder):
        """测试UPDATE查询构建"""
        query, params = query_builder.build_update_query(
            table="users",
            data={"name": "John Updated"},
            where_conditions={"id": 1}
        )
        
        assert "UPDATE users SET name = %s WHERE id = %s" == query
        assert params == ["John Updated", 1]
    
    def test_build_delete_query(self, query_builder):
        """测试DELETE查询构建"""
        query, params = query_builder.build_delete_query(
            table="users",
            where_conditions={"id": 1}
        )
        
        assert query == "DELETE FROM users WHERE id = %s"
        assert params == [1]
    
    def test_validate_sort_field_safe(self, query_builder):
        """测试安全排序字段验证"""
        safe_fields = ["id", "name", "created_at", "user_id"]
        
        for field in safe_fields:
            is_valid = query_builder.validate_sort_field(field)
            assert is_valid is True
    
    def test_validate_sort_field_unsafe(self, query_builder):
        """测试不安全排序字段验证"""
        unsafe_fields = [
            "id; DROP TABLE users; --",
            "(SELECT password FROM users)",
            "name' OR '1'='1"
        ]
        
        for field in unsafe_fields:
            is_valid = query_builder.validate_sort_field(field)
            assert is_valid is False
    
    def test_validate_sort_direction(self, query_builder):
        """测试排序方向验证"""
        assert query_builder.validate_sort_direction("ASC") is True
        assert query_builder.validate_sort_direction("DESC") is True
        assert query_builder.validate_sort_direction("asc") is True
        assert query_builder.validate_sort_direction("desc") is True
        
        # 测试无效方向
        assert query_builder.validate_sort_direction("INVALID") is False
        assert query_builder.validate_sort_direction("ASC; DROP TABLE") is False
    
    @pytest.mark.asyncio
    async def test_execute_query_success(self, query_builder, mock_db_connection):
        """测试成功执行查询"""
        mock_cursor = Mock()
        mock_cursor.fetchall.return_value = [(1, "John", "<EMAIL>")]
        mock_db_connection.cursor.return_value.__enter__.return_value = mock_cursor
        
        result = await query_builder.execute_query(
            "SELECT * FROM users WHERE id = %s",
            [1]
        )
        
        assert result == [(1, "John", "<EMAIL>")]
        mock_cursor.execute.assert_called_once_with(
            "SELECT * FROM users WHERE id = %s", [1]
        )
    
    @pytest.mark.asyncio
    async def test_execute_transaction_success(self, query_builder, mock_db_connection):
        """测试成功执行事务"""
        mock_cursor = Mock()
        mock_db_connection.cursor.return_value.__enter__.return_value = mock_cursor
        
        queries = [
            ("INSERT INTO users (name) VALUES (%s)", ["John"]),
            ("UPDATE users SET active = %s WHERE name = %s", [1, "John"])
        ]
        
        result = await query_builder.execute_transaction(queries)
        assert result is True
        
        # 验证事务调用
        mock_db_connection.begin.assert_called_once()
        mock_db_connection.commit.assert_called_once()
        assert mock_cursor.execute.call_count == 2
    
    @pytest.mark.asyncio
    async def test_execute_transaction_failure(self, query_builder, mock_db_connection):
        """测试事务执行失败"""
        mock_cursor = Mock()
        mock_cursor.execute.side_effect = Exception("Transaction failed")
        mock_db_connection.cursor.return_value.__enter__.return_value = mock_cursor
        
        queries = [("INSERT INTO users (name) VALUES (%s)", ["John"])]
        
        result = await query_builder.execute_transaction(queries)
        assert result is False
        mock_db_connection.rollback.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_transaction_rollback(self, query_builder, mock_db_connection):
        """测试事务回滚"""
        mock_cursor = Mock()
        mock_cursor.execute.side_effect = [None, Exception("Query failed")]
        mock_db_connection.cursor.return_value.__enter__.return_value = mock_cursor
        
        queries = [
            ("INSERT INTO users (name) VALUES (%s)", ["John"]),
            ("INVALID QUERY", [])
        ]
        
        result = await query_builder.execute_transaction(queries)
        assert result is False
        
        # 验证回滚调用
        mock_db_connection.rollback.assert_called_once()


class TestRedisCache:
    """Redis缓存测试"""
    
    @pytest.fixture
    def redis_cache(self, mock_redis):
        """创建Redis缓存实例"""
        return RedisCache(redis_client=mock_redis)
    
    @pytest.mark.asyncio
    async def test_set_and_get_string(self, redis_cache, mock_redis):
        """测试设置和获取字符串"""
        mock_redis.get.return_value = b'"test_value"'
        
        await redis_cache.set("test_key", "test_value")
        result = await redis_cache.get("test_key")
        
        assert result == "test_value"
        mock_redis.set.assert_called_once()
        mock_redis.get.assert_called_once_with("test_key")
    
    @pytest.mark.asyncio
    async def test_set_and_get_dict(self, redis_cache, mock_redis):
        """测试设置和获取字典"""
        test_data = {"name": "John", "age": 30}
        mock_redis.get.return_value = b'{"name": "John", "age": 30}'
        
        await redis_cache.set("user:1", test_data)
        result = await redis_cache.get("user:1")
        
        assert result == test_data
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_key(self, redis_cache, mock_redis):
        """测试获取不存在的键"""
        mock_redis.get.return_value = None
        
        result = await redis_cache.get("nonexistent_key")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_delete_key(self, redis_cache, mock_redis):
        """测试删除键"""
        mock_redis.delete.return_value = 1
        
        result = await redis_cache.delete("test_key")
        assert result is True
        mock_redis.delete.assert_called_once_with("test_key")
    
    @pytest.mark.asyncio
    async def test_exists_key(self, redis_cache, mock_redis):
        """测试检查键是否存在"""
        mock_redis.exists.return_value = 1
        
        result = await redis_cache.exists("test_key")
        assert result is True
        mock_redis.exists.assert_called_once_with("test_key")
    
    @pytest.mark.asyncio
    async def test_expire_key(self, redis_cache, mock_redis):
        """测试设置键过期时间"""
        mock_redis.expire.return_value = True
        
        result = await redis_cache.expire("test_key", 3600)
        assert result is True
        mock_redis.expire.assert_called_once_with("test_key", 3600)
    
    @pytest.mark.asyncio
    async def test_increment_key(self, redis_cache, mock_redis):
        """测试递增键值"""
        mock_redis.incr.return_value = 5
        
        result = await redis_cache.increment("counter", 2)
        assert result == 5
        mock_redis.incr.assert_called_once_with("counter", 2)
    
    @pytest.mark.asyncio
    async def test_get_or_set_cache_hit(self, redis_cache, mock_redis):
        """测试缓存命中的get_or_set"""
        mock_redis.get.return_value = b'"cached_value"'
        
        async def expensive_function():
            return "computed_value"
        
        result = await redis_cache.get_or_set(
            "test_key", expensive_function, ttl=3600
        )
        
        assert result == "cached_value"
        mock_redis.get.assert_called_once_with("test_key")
        # set不应该被调用，因为缓存命中
        mock_redis.set.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_get_or_set_cache_miss(self, redis_cache, mock_redis):
        """测试缓存未命中的get_or_set"""
        mock_redis.get.return_value = None
        
        async def expensive_function():
            return "computed_value"
        
        result = await redis_cache.get_or_set(
            "test_key", expensive_function, ttl=3600
        )
        
        assert result == "computed_value"
        mock_redis.get.assert_called_once_with("test_key")
        mock_redis.set.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_pattern(self, redis_cache, mock_redis):
        """测试模式删除"""
        mock_redis.keys.return_value = [b"user:1", b"user:2", b"user:3"]
        mock_redis.delete.return_value = 3
        
        result = await redis_cache.delete_pattern("user:*")
        assert result == 3
        mock_redis.keys.assert_called_once_with("user:*")
        mock_redis.delete.assert_called_once_with("user:1", "user:2", "user:3")


class TestCacheDecorator:
    """缓存装饰器测试"""
    
    @pytest.mark.asyncio
    async def test_cache_result_decorator(self, mock_redis):
        """测试缓存结果装饰器"""
        call_count = 0
        
        @cache_result(ttl=3600, key_prefix="test")
        async def expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            return x + y
        
        # 模拟缓存未命中
        mock_redis.get.return_value = None
        
        # 第一次调用
        result1 = await expensive_function(1, 2)
        assert result1 == 3
        assert call_count == 1
        
        # 模拟缓存命中
        mock_redis.get.return_value = b'3'
        
        # 第二次调用（应该从缓存获取）
        result2 = await expensive_function(1, 2)
        assert result2 == 3
        assert call_count == 1  # 函数不应该再次被调用


class TestPagination:
    """分页测试"""
    
    def test_pagination_params_validation(self):
        """测试分页参数验证"""
        # 测试有效参数
        params = PaginationParams(page=1, size=10)
        assert params.page == 1
        assert params.size == 10
        assert params.offset == 0
        
        # 测试计算偏移量
        params = PaginationParams(page=3, size=20)
        assert params.offset == 40
    
    def test_paginator_with_list(self):
        """测试列表分页"""
        data = list(range(100))  # 0-99
        paginator = Paginator()
        
        result = paginator.paginate_list(
            data, page=2, size=10
        )
        
        assert result.items == list(range(10, 20))
        assert result.meta.page == 2
        assert result.meta.size == 10
        assert result.meta.total == 100
        assert result.meta.pages == 10
        assert result.meta.has_next is True
        assert result.meta.has_prev is True
    
    def test_paginator_first_page(self):
        """测试第一页分页"""
        data = list(range(25))
        paginator = Paginator()
        
        result = paginator.paginate_list(data, page=1, size=10)
        
        assert result.meta.has_prev is False
        assert result.meta.has_next is True
    
    def test_paginator_last_page(self):
        """测试最后一页分页"""
        data = list(range(25))
        paginator = Paginator()
        
        result = paginator.paginate_list(data, page=3, size=10)
        
        assert len(result.items) == 5  # 最后一页只有5个项目
        assert result.meta.has_prev is True
        assert result.meta.has_next is False
    
    def test_paginator_empty_list(self):
        """测试空列表分页"""
        data = []
        paginator = Paginator()
        
        result = paginator.paginate_list(data, page=1, size=10)
        
        assert len(result.items) == 0
        assert result.meta.total == 0
        assert result.meta.pages == 0
        assert not result.meta.has_next
        assert not result.meta.has_prev
    
    def test_cursor_paginator(self):
        """测试游标分页"""
        data = [
            {"id": i, "name": f"Item {i}"} 
            for i in range(1, 101)
        ]
        
        paginator = CursorPaginator()
        
        # 第一页
        result = paginator.paginate(
            data, size=10, cursor_field="id"
        )
        
        assert len(result.items) == 10
        assert result.items[0]["id"] == 1
        assert result.items[-1]["id"] == 10
        assert result.meta.has_next is True
        assert result.meta.next_cursor == "10"
    
    def test_cursor_paginator_with_cursor(self):
        """测试带游标的游标分页"""
        data = [
            {"id": i, "name": f"Item {i}"} 
            for i in range(1, 101)
        ]
        
        paginator = CursorPaginator()
        
        # 从游标位置开始
        result = paginator.paginate(
            data, size=10, cursor="10", cursor_field="id"
        )
        
        assert len(result.items) == 10
        assert result.items[0]["id"] == 11
        assert result.items[-1]["id"] == 20
    
    def test_paginate_helper_function(self):
        """测试分页辅助函数"""
        data = list(range(50))
        
        result = paginate(data, page=2, size=15)
        
        assert len(result.items) == 15
        assert result.items[0] == 15
        assert result.meta.total == 50
        assert result.meta.pages == 4


@pytest.mark.integration
class TestDatabaseIntegration:
    """数据库集成测试"""
    
    @pytest.mark.asyncio
    async def test_connection_pool_with_cache(self):
        """测试连接池与缓存的集成"""
        # 这里可以测试连接池和缓存的组合使用
        pass
    
    @pytest.mark.asyncio
    async def test_query_builder_with_pagination(self):
        """测试查询构建器与分页的集成"""
        # 这里可以测试查询构建器和分页的组合使用
        pass
    
    @pytest.mark.asyncio
    async def test_full_database_workflow(self):
        """测试完整的数据库工作流"""
        # 这里可以测试从连接获取到查询执行到结果缓存的完整流程
        pass