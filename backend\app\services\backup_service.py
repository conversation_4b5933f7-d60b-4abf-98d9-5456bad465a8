import os
import shutil
from datetime import datetime

from app.core.config import settings

BACKUP_DIR = os.path.join(settings.BASE_DIR, "backups")
os.makedirs(BACKUP_DIR, exist_ok=True)


def create_backup() -> str:
    """
    创建数据库备份

    Returns:
        备份文件的路径
    """
    db_path = settings.DATABASE_URL.split("///")[-1]
    if not os.path.exists(db_path):
        raise FileNotFoundError(f"数据库文件不存在: {db_path}")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"backup_{timestamp}.db"
    backup_path = os.path.join(BACKUP_DIR, backup_filename)

    shutil.copy2(db_path, backup_path)

    return backup_path


def list_backups() -> list:
    """
    列出所有备份文件
    """
    backups = []
    for filename in sorted(os.listdir(BACKUP_DIR), reverse=True):
        if filename.endswith(".db"):
            file_path = os.path.join(BACKUP_DIR, filename)
            backups.append(
                {
                    "filename": filename,
                    "path": file_path,
                    "size": os.path.getsize(file_path),
                    "created_at": datetime.fromtimestamp(os.path.getctime(file_path)),
                }
            )
    return backups


def get_backup_path(filename: str) -> str:
    """
    获取指定备份文件的安全路径
    """
    # 安全性检查：防止路径遍历攻击
    if ".." in filename or filename.startswith("/"):
        raise ValueError("无效的文件名")

    path = os.path.join(BACKUP_DIR, filename)
    if not os.path.exists(path):
        return None
    return path
