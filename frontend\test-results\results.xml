<testsuites id="" name="" tests="10" failures="10" skipped="0" errors="0" time="51.395886">
<testsuite name="login-comprehensive.spec.ts" timestamp="2025-07-24T02:01:57.867Z" hostname="chromium" tests="10" failures="10" skipped="0" time="190.084" errors="0">
<testcase name="登录页面全面检测 › 页面基本元素检测" classname="login-comprehensive.spec.ts" time="9.197">
<failure message="login-comprehensive.spec.ts:9:3 页面基本元素检测" type="FAILURE">
<![CDATA[  [chromium] › login-comprehensive.spec.ts:9:3 › 登录页面全面检测 › 页面基本元素检测 ───────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('h1, h2').filter({ hasText: '系统登录' })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('h1, h2').filter({ hasText: '系统登录' })


      12 |
      13 |     // 检查主要UI元素
    > 14 |     await expect(page.locator('h1, h2').filter({ hasText: '系统登录' })).toBeVisible();
         |                                                                      ^
      15 |
      16 |     // 检查表单元素
      17 |     await expect(page.locator('input[type="email"]')).toBeVisible();
        at D:\二创\二创短视频分发\frontend\e2e\login-comprehensive.spec.ts:14:70

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-页面基本元素检测-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-页面基本元素检测-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\login-comprehensive-登录页面全面检测-页面基本元素检测-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|login-comprehensive-登录页面全面检测-页面基本元素检测-chromium\test-failed-1.png]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-页面基本元素检测-chromium\video.webm]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-页面基本元素检测-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="登录页面全面检测 › 表单验证功能检测" classname="login-comprehensive.spec.ts" time="30.53">
<failure message="login-comprehensive.spec.ts:32:3 表单验证功能检测" type="FAILURE">
<![CDATA[  [chromium] › login-comprehensive.spec.ts:32:3 › 登录页面全面检测 › 表单验证功能检测 ──────────────────────────────

    Test timeout of 30000ms exceeded.

    Error: locator.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('button[type="submit"]')


      34 |
      35 |     // 测试空表单提交
    > 36 |     await submitButton.click();
         |                        ^
      37 |
      38 |     // 检查HTML5验证或自定义验证消息
      39 |     const emailInput = page.locator('input[type="email"]');
        at D:\二创\二创短视频分发\frontend\e2e\login-comprehensive.spec.ts:36:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-表单验证功能检测-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-表单验证功能检测-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\login-comprehensive-登录页面全面检测-表单验证功能检测-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|login-comprehensive-登录页面全面检测-表单验证功能检测-chromium\test-failed-1.png]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-表单验证功能检测-chromium\video.webm]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-表单验证功能检测-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="登录页面全面检测 › 登录功能测试 - 成功场景" classname="login-comprehensive.spec.ts" time="30.549">
<failure message="login-comprehensive.spec.ts:62:3 登录功能测试 - 成功场景" type="FAILURE">
<![CDATA[  [chromium] › login-comprehensive.spec.ts:62:3 › 登录页面全面检测 › 登录功能测试 - 成功场景 ─────────────────────────

    Test timeout of 30000ms exceeded.

    Error: locator.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[type="email"]')


      81 |
      82 |     // 填写登录表单
    > 83 |     await page.locator('input[type="email"]').fill('<EMAIL>');
         |                                               ^
      84 |     await page.locator('input[type="password"]').fill('password123');
      85 |
      86 |     // 提交表单
        at D:\二创\二创短视频分发\frontend\e2e\login-comprehensive.spec.ts:83:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-登录功能测试---成功场景-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-登录功能测试---成功场景-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\login-comprehensive-登录页面全面检测-登录功能测试---成功场景-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|login-comprehensive-登录页面全面检测-登录功能测试---成功场景-chromium\test-failed-1.png]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-登录功能测试---成功场景-chromium\video.webm]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-登录功能测试---成功场景-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="登录页面全面检测 › 登录功能测试 - 失败场景" classname="login-comprehensive.spec.ts" time="30.559">
<failure message="login-comprehensive.spec.ts:102:3 登录功能测试 - 失败场景" type="FAILURE">
<![CDATA[  [chromium] › login-comprehensive.spec.ts:102:3 › 登录页面全面检测 › 登录功能测试 - 失败场景 ────────────────────────

    Test timeout of 30000ms exceeded.

    Error: locator.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[type="email"]')


      114 |
      115 |     // 填写错误的登录信息
    > 116 |     await page.locator('input[type="email"]').fill('<EMAIL>');
          |                                               ^
      117 |     await page.locator('input[type="password"]').fill('wrongpassword');
      118 |
      119 |     // 提交表单
        at D:\二创\二创短视频分发\frontend\e2e\login-comprehensive.spec.ts:116:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-登录功能测试---失败场景-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-登录功能测试---失败场景-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\login-comprehensive-登录页面全面检测-登录功能测试---失败场景-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|login-comprehensive-登录页面全面检测-登录功能测试---失败场景-chromium\test-failed-1.png]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-登录功能测试---失败场景-chromium\video.webm]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-登录功能测试---失败场景-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="登录页面全面检测 › 加载状态检测" classname="login-comprehensive.spec.ts" time="30.566">
<failure message="login-comprehensive.spec.ts:130:3 加载状态检测" type="FAILURE">
<![CDATA[  [chromium] › login-comprehensive.spec.ts:130:3 › 登录页面全面检测 › 加载状态检测 ───────────────────────────────

    Test timeout of 30000ms exceeded.

    Error: locator.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[type="email"]')


      139 |     });
      140 |
    > 141 |     await page.locator('input[type="email"]').fill('<EMAIL>');
          |                                               ^
      142 |     await page.locator('input[type="password"]').fill('password123');
      143 |
      144 |     // 点击提交按钮
        at D:\二创\二创短视频分发\frontend\e2e\login-comprehensive.spec.ts:141:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-加载状态检测-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-加载状态检测-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\login-comprehensive-登录页面全面检测-加载状态检测-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|login-comprehensive-登录页面全面检测-加载状态检测-chromium\test-failed-1.png]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-加载状态检测-chromium\video.webm]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-加载状态检测-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="登录页面全面检测 › 响应式设计检测" classname="login-comprehensive.spec.ts" time="9.098">
<failure message="login-comprehensive.spec.ts:156:3 响应式设计检测" type="FAILURE">
<![CDATA[  [chromium] › login-comprehensive.spec.ts:156:3 › 登录页面全面检测 › 响应式设计检测 ──────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('form')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('form')


      157 |     // 测试桌面视图
      158 |     await page.setViewportSize({ width: 1200, height: 800 });
    > 159 |     await expect(page.locator('form')).toBeVisible();
          |                                        ^
      160 |
      161 |     // 测试平板视图
      162 |     await page.setViewportSize({ width: 768, height: 1024 });
        at D:\二创\二创短视频分发\frontend\e2e\login-comprehensive.spec.ts:159:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-响应式设计检测-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-响应式设计检测-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\login-comprehensive-登录页面全面检测-响应式设计检测-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|login-comprehensive-登录页面全面检测-响应式设计检测-chromium\test-failed-1.png]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-响应式设计检测-chromium\video.webm]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-响应式设计检测-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="登录页面全面检测 › 键盘导航检测" classname="login-comprehensive.spec.ts" time="6.632">
<failure message="login-comprehensive.spec.ts:179:3 键盘导航检测" type="FAILURE">
<![CDATA[  [chromium] › login-comprehensive.spec.ts:179:3 › 登录页面全面检测 › 键盘导航检测 ───────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeFocused()

    Locator: locator('input[type="email"]')
    Expected: focused
    Received: <element(s) not found>
    Call log:
      - Expect "toBeFocused" with timeout 5000ms
      - waiting for locator('input[type="email"]')


      180 |     // 使用Tab键导航
      181 |     await page.keyboard.press('Tab');
    > 182 |     await expect(page.locator('input[type="email"]')).toBeFocused();
          |                                                       ^
      183 |
      184 |     await page.keyboard.press('Tab');
      185 |     await expect(page.locator('input[type="password"]')).toBeFocused();
        at D:\二创\二创短视频分发\frontend\e2e\login-comprehensive.spec.ts:182:55

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-键盘导航检测-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-键盘导航检测-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\login-comprehensive-登录页面全面检测-键盘导航检测-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|login-comprehensive-登录页面全面检测-键盘导航检测-chromium\test-failed-1.png]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-键盘导航检测-chromium\video.webm]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-键盘导航检测-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="登录页面全面检测 › 安全性检测" classname="login-comprehensive.spec.ts" time="6.604">
<failure message="login-comprehensive.spec.ts:199:3 安全性检测" type="FAILURE">
<![CDATA[  [chromium] › login-comprehensive.spec.ts:199:3 › 登录页面全面检测 › 安全性检测 ────────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveAttribute(expected)

    Locator: locator('input[type="password"]')
    Expected string: "password"
    Received: <element(s) not found>
    Call log:
      - Expect "toHaveAttribute" with timeout 5000ms
      - waiting for locator('input[type="password"]')


      200 |     // 检查密码字段是否正确隐藏
      201 |     const passwordInput = page.locator('input[type="password"]');
    > 202 |     await expect(passwordInput).toHaveAttribute('type', 'password');
          |                                 ^
      203 |
      204 |     // 检查是否有密码显示/隐藏功能
      205 |     const toggleButton = page.locator('button').filter({ hasText: /显示|隐藏|👁/ });
        at D:\二创\二创短视频分发\frontend\e2e\login-comprehensive.spec.ts:202:33

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-安全性检测-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-安全性检测-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\login-comprehensive-登录页面全面检测-安全性检测-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|login-comprehensive-登录页面全面检测-安全性检测-chromium\test-failed-1.png]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-安全性检测-chromium\video.webm]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-安全性检测-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="登录页面全面检测 › 错误处理和用户反馈" classname="login-comprehensive.spec.ts" time="30.891">
<failure message="login-comprehensive.spec.ts:222:3 错误处理和用户反馈" type="FAILURE">
<![CDATA[  [chromium] › login-comprehensive.spec.ts:222:3 › 登录页面全面检测 › 错误处理和用户反馈 ────────────────────────────

    Test timeout of 30000ms exceeded.

    Error: locator.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[type="email"]')


      224 |     await page.route('**/api/v1/auth/login', route => route.abort());
      225 |
    > 226 |     await page.locator('input[type="email"]').fill('<EMAIL>');
          |                                               ^
      227 |     await page.locator('input[type="password"]').fill('password123');
      228 |     await page.locator('button[type="submit"]').click();
      229 |
        at D:\二创\二创短视频分发\frontend\e2e\login-comprehensive.spec.ts:226:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-错误处理和用户反馈-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-错误处理和用户反馈-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\login-comprehensive-登录页面全面检测-错误处理和用户反馈-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|login-comprehensive-登录页面全面检测-错误处理和用户反馈-chromium\test-failed-1.png]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-错误处理和用户反馈-chromium\video.webm]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-错误处理和用户反馈-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="登录页面全面检测 › 页面性能检测" classname="login-comprehensive.spec.ts" time="5.458">
<failure message="login-comprehensive.spec.ts:244:3 页面性能检测" type="FAILURE">
<![CDATA[  [chromium] › login-comprehensive.spec.ts:244:3 › 登录页面全面检测 › 页面性能检测 ───────────────────────────────

    Error: Timed out 3000ms waiting for expect(locator).toBeVisible()

    Locator: locator('input[type="email"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 3000ms
      - waiting for locator('input[type="email"]')


      257 |
      258 |     // 检查关键元素是否快速显示
    > 259 |     await expect(page.locator('input[type="email"]')).toBeVisible({ timeout: 3000 });
          |                                                       ^
      260 |     await expect(page.locator('input[type="password"]')).toBeVisible({ timeout: 3000 });
      261 |     await expect(page.locator('button[type="submit"]')).toBeVisible({ timeout: 3000 });
      262 |   });
        at D:\二创\二创短视频分发\frontend\e2e\login-comprehensive.spec.ts:259:55

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-页面性能检测-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\login-comprehensive-登录页面全面检测-页面性能检测-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\login-comprehensive-登录页面全面检测-页面性能检测-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|login-comprehensive-登录页面全面检测-页面性能检测-chromium\test-failed-1.png]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-页面性能检测-chromium\video.webm]]

[[ATTACHMENT|login-comprehensive-登录页面全面检测-页面性能检测-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>