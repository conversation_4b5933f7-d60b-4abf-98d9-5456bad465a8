<testsuites id="" name="" tests="14" failures="12" skipped="0" errors="0" time="112.36528200000001">
<testsuite name="mobile-responsive.spec.js" timestamp="2025-07-24T03:16:59.852Z" hostname="chromium" tests="14" failures="12" skipped="0" time="97.46" errors="0">
<testcase name="iPhone 15 Pro 响应式测试 › 首页 - 响应式布局测试" classname="mobile-responsive.spec.js" time="8.101">
<failure message="mobile-responsive.spec.js:83:7 首页 - 响应式布局测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:83:7 › iPhone 15 Pro 响应式测试 › 首页 - 响应式布局测试 ─────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.app-header')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.app-header')


      190 |   if (device.isMobile) {
      191 |     // 移动端布局检查
    > 192 |     await expect(header).toBeVisible();
          |                          ^
      193 |     
      194 |     // 移动端侧边栏可能隐藏或折叠
      195 |     const sidebarVisible = await sidebar.isVisible();
        at testUnifiedLayoutResponsive (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:192:26)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:96:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-iPhone-15-Pro-响应式测试-首页---响应式布局测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-iPhone-15-Pro-响应式测试-首页---响应式布局测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-iPhone-15-Pro-响应式测试-首页---响应式布局测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-iPhone-15-Pro-响应式测试-首页---响应式布局测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-iPhone-15-Pro-响应式测试-首页---响应式布局测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-iPhone-15-Pro-响应式测试-首页---响应式布局测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="iPhone 15 Pro 响应式测试 › 登录页面 - 响应式布局测试" classname="mobile-responsive.spec.js" time="8.11">
<failure message="mobile-responsive.spec.js:83:7 登录页面 - 响应式布局测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:83:7 › iPhone 15 Pro 响应式测试 › 登录页面 - 响应式布局测试 ───────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.login-container')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.login-container')


      158 |   // 检查登录容器
      159 |   const loginContainer = page.locator('.login-container');
    > 160 |   await expect(loginContainer).toBeVisible();
          |                                ^
      161 |   
      162 |   // 检查表单元素
      163 |   const loginForm = page.locator('.login-form');
        at testLoginPageResponsive (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:160:32)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:93:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-iPhone-15-Pro-响应式测试-登录页面---响应式布局测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-iPhone-15-Pro-响应式测试-登录页面---响应式布局测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-iPhone-15-Pro-响应式测试-登录页面---响应式布局测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-iPhone-15-Pro-响应式测试-登录页面---响应式布局测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-iPhone-15-Pro-响应式测试-登录页面---响应式布局测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-iPhone-15-Pro-响应式测试-登录页面---响应式布局测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="iPhone 15 Pro 响应式测试 › 触摸交互测试" classname="mobile-responsive.spec.js" time="1.019">
</testcase>
<testcase name="iPhone 15 Pro 响应式测试 › 横竖屏切换测试" classname="mobile-responsive.spec.js" time="7.055">
<failure message="mobile-responsive.spec.js:128:7 横竖屏切换测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:128:7 › iPhone 15 Pro 响应式测试 › 横竖屏切换测试 ─────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.app-header')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.app-header')


      243 | async function checkPortraitLayout(page) {
      244 |   const header = page.locator('.app-header');
    > 245 |   await expect(header).toBeVisible();
          |                        ^
      246 |   
      247 |   // 竖屏模式下，导航可能折叠
      248 |   const navItems = page.locator('.app-header__nav-item');
        at checkPortraitLayout (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:245:24)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:140:15

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-iPhone-15-Pro-响应式测试-横竖屏切换测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-iPhone-15-Pro-响应式测试-横竖屏切换测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-iPhone-15-Pro-响应式测试-横竖屏切换测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-iPhone-15-Pro-响应式测试-横竖屏切换测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-iPhone-15-Pro-响应式测试-横竖屏切换测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-iPhone-15-Pro-响应式测试-横竖屏切换测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Samsung Galaxy S24 响应式测试 › 首页 - 响应式布局测试" classname="mobile-responsive.spec.js" time="8.09">
<failure message="mobile-responsive.spec.js:83:7 首页 - 响应式布局测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:83:7 › Samsung Galaxy S24 响应式测试 › 首页 - 响应式布局测试 ────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.app-header')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.app-header')


      190 |   if (device.isMobile) {
      191 |     // 移动端布局检查
    > 192 |     await expect(header).toBeVisible();
          |                          ^
      193 |     
      194 |     // 移动端侧边栏可能隐藏或折叠
      195 |     const sidebarVisible = await sidebar.isVisible();
        at testUnifiedLayoutResponsive (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:192:26)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:96:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Samsung-Galaxy-S24-响应式测试-首页---响应式布局测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Samsung-Galaxy-S24-响应式测试-首页---响应式布局测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-Samsung-Galaxy-S24-响应式测试-首页---响应式布局测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-Samsung-Galaxy-S24-响应式测试-首页---响应式布局测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-Samsung-Galaxy-S24-响应式测试-首页---响应式布局测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-Samsung-Galaxy-S24-响应式测试-首页---响应式布局测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Samsung Galaxy S24 响应式测试 › 登录页面 - 响应式布局测试" classname="mobile-responsive.spec.js" time="8.076">
<failure message="mobile-responsive.spec.js:83:7 登录页面 - 响应式布局测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:83:7 › Samsung Galaxy S24 响应式测试 › 登录页面 - 响应式布局测试 ──────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.login-container')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.login-container')


      158 |   // 检查登录容器
      159 |   const loginContainer = page.locator('.login-container');
    > 160 |   await expect(loginContainer).toBeVisible();
          |                                ^
      161 |   
      162 |   // 检查表单元素
      163 |   const loginForm = page.locator('.login-form');
        at testLoginPageResponsive (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:160:32)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:93:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Samsung-Galaxy-S24-响应式测试-登录页面---响应式布局测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Samsung-Galaxy-S24-响应式测试-登录页面---响应式布局测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-Samsung-Galaxy-S24-响应式测试-登录页面---响应式布局测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-Samsung-Galaxy-S24-响应式测试-登录页面---响应式布局测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-Samsung-Galaxy-S24-响应式测试-登录页面---响应式布局测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-Samsung-Galaxy-S24-响应式测试-登录页面---响应式布局测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Samsung Galaxy S24 响应式测试 › 触摸交互测试" classname="mobile-responsive.spec.js" time="1.03">
</testcase>
<testcase name="Samsung Galaxy S24 响应式测试 › 横竖屏切换测试" classname="mobile-responsive.spec.js" time="7.069">
<failure message="mobile-responsive.spec.js:128:7 横竖屏切换测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:128:7 › Samsung Galaxy S24 响应式测试 › 横竖屏切换测试 ────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.app-header')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.app-header')


      243 | async function checkPortraitLayout(page) {
      244 |   const header = page.locator('.app-header');
    > 245 |   await expect(header).toBeVisible();
          |                        ^
      246 |   
      247 |   // 竖屏模式下，导航可能折叠
      248 |   const navItems = page.locator('.app-header__nav-item');
        at checkPortraitLayout (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:245:24)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:140:15

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Samsung-Galaxy-S24-响应式测试-横竖屏切换测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Samsung-Galaxy-S24-响应式测试-横竖屏切换测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-Samsung-Galaxy-S24-响应式测试-横竖屏切换测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-Samsung-Galaxy-S24-响应式测试-横竖屏切换测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-Samsung-Galaxy-S24-响应式测试-横竖屏切换测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-Samsung-Galaxy-S24-响应式测试-横竖屏切换测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="iPad Pro 12.9 响应式测试 › 首页 - 响应式布局测试" classname="mobile-responsive.spec.js" time="8.322">
<failure message="mobile-responsive.spec.js:83:7 首页 - 响应式布局测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:83:7 › iPad Pro 12.9 响应式测试 › 首页 - 响应式布局测试 ─────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.app-header')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.app-header')


      219 |   } else {
      220 |     // 桌面端布局检查
    > 221 |     await expect(header).toBeVisible();
          |                          ^
      222 |     await expect(sidebar).toBeVisible();
      223 |     await expect(main).toBeVisible();
      224 |     
        at testUnifiedLayoutResponsive (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:221:26)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:96:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-iPad-Pro-12-9-响应式测试-首页---响应式布局测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-iPad-Pro-12-9-响应式测试-首页---响应式布局测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-iPad-Pro-12-9-响应式测试-首页---响应式布局测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-iPad-Pro-12-9-响应式测试-首页---响应式布局测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-iPad-Pro-12-9-响应式测试-首页---响应式布局测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-iPad-Pro-12-9-响应式测试-首页---响应式布局测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="iPad Pro 12.9 响应式测试 › 登录页面 - 响应式布局测试" classname="mobile-responsive.spec.js" time="8.253">
<failure message="mobile-responsive.spec.js:83:7 登录页面 - 响应式布局测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:83:7 › iPad Pro 12.9 响应式测试 › 登录页面 - 响应式布局测试 ───────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.login-container')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.login-container')


      158 |   // 检查登录容器
      159 |   const loginContainer = page.locator('.login-container');
    > 160 |   await expect(loginContainer).toBeVisible();
          |                                ^
      161 |   
      162 |   // 检查表单元素
      163 |   const loginForm = page.locator('.login-form');
        at testLoginPageResponsive (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:160:32)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:93:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-iPad-Pro-12-9-响应式测试-登录页面---响应式布局测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-iPad-Pro-12-9-响应式测试-登录页面---响应式布局测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-iPad-Pro-12-9-响应式测试-登录页面---响应式布局测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-iPad-Pro-12-9-响应式测试-登录页面---响应式布局测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-iPad-Pro-12-9-响应式测试-登录页面---响应式布局测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-iPad-Pro-12-9-响应式测试-登录页面---响应式布局测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Desktop 1920x1080 响应式测试 › 首页 - 响应式布局测试" classname="mobile-responsive.spec.js" time="8.112">
<failure message="mobile-responsive.spec.js:83:7 首页 - 响应式布局测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:83:7 › Desktop 1920x1080 响应式测试 › 首页 - 响应式布局测试 ─────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.app-header')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.app-header')


      219 |   } else {
      220 |     // 桌面端布局检查
    > 221 |     await expect(header).toBeVisible();
          |                          ^
      222 |     await expect(sidebar).toBeVisible();
      223 |     await expect(main).toBeVisible();
      224 |     
        at testUnifiedLayoutResponsive (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:221:26)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:96:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Desktop-1920x1080-响应式测试-首页---响应式布局测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Desktop-1920x1080-响应式测试-首页---响应式布局测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-Desktop-1920x1080-响应式测试-首页---响应式布局测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-Desktop-1920x1080-响应式测试-首页---响应式布局测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-Desktop-1920x1080-响应式测试-首页---响应式布局测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-Desktop-1920x1080-响应式测试-首页---响应式布局测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Desktop 1920x1080 响应式测试 › 登录页面 - 响应式布局测试" classname="mobile-responsive.spec.js" time="8.092">
<failure message="mobile-responsive.spec.js:83:7 登录页面 - 响应式布局测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:83:7 › Desktop 1920x1080 响应式测试 › 登录页面 - 响应式布局测试 ───────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.login-container')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.login-container')


      158 |   // 检查登录容器
      159 |   const loginContainer = page.locator('.login-container');
    > 160 |   await expect(loginContainer).toBeVisible();
          |                                ^
      161 |   
      162 |   // 检查表单元素
      163 |   const loginForm = page.locator('.login-form');
        at testLoginPageResponsive (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:160:32)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:93:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Desktop-1920x1080-响应式测试-登录页面---响应式布局测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Desktop-1920x1080-响应式测试-登录页面---响应式布局测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-Desktop-1920x1080-响应式测试-登录页面---响应式布局测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-Desktop-1920x1080-响应式测试-登录页面---响应式布局测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-Desktop-1920x1080-响应式测试-登录页面---响应式布局测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-Desktop-1920x1080-响应式测试-登录页面---响应式布局测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Desktop 1366x768 响应式测试 › 首页 - 响应式布局测试" classname="mobile-responsive.spec.js" time="8.058">
<failure message="mobile-responsive.spec.js:83:7 首页 - 响应式布局测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:83:7 › Desktop 1366x768 响应式测试 › 首页 - 响应式布局测试 ──────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.app-header')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.app-header')


      219 |   } else {
      220 |     // 桌面端布局检查
    > 221 |     await expect(header).toBeVisible();
          |                          ^
      222 |     await expect(sidebar).toBeVisible();
      223 |     await expect(main).toBeVisible();
      224 |     
        at testUnifiedLayoutResponsive (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:221:26)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:96:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Desktop-1366x768-响应式测试-首页---响应式布局测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Desktop-1366x768-响应式测试-首页---响应式布局测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-Desktop-1366x768-响应式测试-首页---响应式布局测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-Desktop-1366x768-响应式测试-首页---响应式布局测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-Desktop-1366x768-响应式测试-首页---响应式布局测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-Desktop-1366x768-响应式测试-首页---响应式布局测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Desktop 1366x768 响应式测试 › 登录页面 - 响应式布局测试" classname="mobile-responsive.spec.js" time="8.073">
<failure message="mobile-responsive.spec.js:83:7 登录页面 - 响应式布局测试" type="FAILURE">
<![CDATA[  [chromium] › mobile-responsive.spec.js:83:7 › Desktop 1366x768 响应式测试 › 登录页面 - 响应式布局测试 ────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.login-container')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.login-container')


      158 |   // 检查登录容器
      159 |   const loginContainer = page.locator('.login-container');
    > 160 |   await expect(loginContainer).toBeVisible();
          |                                ^
      161 |   
      162 |   // 检查表单元素
      163 |   const loginForm = page.locator('.login-form');
        at testLoginPageResponsive (D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:160:32)
        at D:\二创\二创短视频分发\frontend\e2e\mobile-responsive.spec.js:93:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Desktop-1366x768-响应式测试-登录页面---响应式布局测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\mobile-responsive-Desktop-1366x768-响应式测试-登录页面---响应式布局测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-responsive-Desktop-1366x768-响应式测试-登录页面---响应式布局测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-responsive-Desktop-1366x768-响应式测试-登录页面---响应式布局测试-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-responsive-Desktop-1366x768-响应式测试-登录页面---响应式布局测试-chromium\video.webm]]

[[ATTACHMENT|mobile-responsive-Desktop-1366x768-响应式测试-登录页面---响应式布局测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>