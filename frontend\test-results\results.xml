<testsuites id="" name="" tests="6" failures="2" skipped="0" errors="0" time="10.852974">
<testsuite name="performance.spec.js" timestamp="2025-07-24T02:55:17.536Z" hostname="chromium" tests="6" failures="2" skipped="0" time="37.9" errors="0">
<testcase name="性能优化检查 › Core Web Vitals 测试" classname="performance.spec.js" time="9.049">
<system-out>
<![CDATA[Core Web Vitals: { FCP: [33m8.089999999850988[39m, TTI: [33m3222.109999999404[39m }
]]>
</system-out>
</testcase>
<testcase name="性能优化检查 › 路由切换性能测试" classname="performance.spec.js" time="8.737">
<failure message="performance.spec.js:102:3 路由切换性能测试" type="FAILURE">
<![CDATA[  [chromium] › performance.spec.js:102:3 › 性能优化检查 › 路由切换性能测试 ───────────────────────────────────────

    Error: expect(received).toBeLessThan(expected)

    Expected: < 1000
    Received:   1206

      144 |     
      145 |     // 验证平均切换时间
    > 146 |     expect(avgTransitionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.routeTransition * 2);
          |                               ^
      147 |   });
      148 |
      149 |   test('布局重排性能测试', async ({ page }) => {
        at D:\二创\二创短视频分发\frontend\e2e\performance.spec.js:146:31

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\performance-性能优化检查-路由切换性能测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\performance-性能优化检查-路由切换性能测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\performance-性能优化检查-路由切换性能测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[视频创作 路由切换时间: 1194ms
计算引擎测试 路由切换时间: 1247ms
个人中心 路由切换时间: 1214ms
首页 路由切换时间: 1169ms
平均路由切换时间: 1206ms

[[ATTACHMENT|performance-性能优化检查-路由切换性能测试-chromium\test-failed-1.png]]

[[ATTACHMENT|performance-性能优化检查-路由切换性能测试-chromium\video.webm]]

[[ATTACHMENT|performance-性能优化检查-路由切换性能测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="性能优化检查 › 布局重排性能测试" classname="performance.spec.js" time="7.018">
<failure message="performance.spec.js:149:3 布局重排性能测试" type="FAILURE">
<![CDATA[  [chromium] › performance.spec.js:149:3 › 性能优化检查 › 布局重排性能测试 ───────────────────────────────────────

    Error: expect(received).toBeLessThan(expected)

    Expected: < 0.1
    Received:   0.428226323445638

      201 |     
      202 |     // 验证布局稳定性
    > 203 |     expect(totalShift).toBeLessThan(PERFORMANCE_THRESHOLDS.CLS);
          |                        ^
      204 |   });
      205 |
      206 |   test('内存使用情况测试', async ({ page }) => {
        at D:\二创\二创短视频分发\frontend\e2e\performance.spec.js:203:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\performance-性能优化检查-布局重排性能测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\performance-性能优化检查-布局重排性能测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\performance-性能优化检查-布局重排性能测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[布局重排事件: [
  {
    value: [33m0.0021162312825520834[39m,
    startTime: [33m4607.605000000447[39m,
    sources: [ [36m[Object][39m ]
  },
  {
    value: [33m0.0027211791144476994[39m,
    startTime: [33m4624.039999999106[39m,
    sources: [ [36m[Object][39m ]
  },
  {
    value: [33m0.0032534953223334415[39m,
    startTime: [33m4640.1899999994785[39m,
    sources: [ [36m[Object][39m ]
  },
  {
    value: [33m0.003618573718600803[39m,
    startTime: [33m4657.584999999031[39m,
    sources: [ [36m[Object][39m ]
  },
  {
    value: [33m0.0037676080067952475[39m,
    startTime: [33m4673.324999999255[39m,
    sources: [ [36m[Object][39m ]
  },
  {
    value: [33m0.4044034461975098[39m,
    startTime: [33m4691.0800000000745[39m,
    sources: [ [36m[Object][39m, [36m[Object][39m ]
  },
  {
    value: [33m0.0033283600277370876[39m,
    startTime: [33m4706.769999999553[39m,
    sources: [ [36m[Object][39m ]
  },
  {
    value: [33m0.002813022189670139[39m,
    startTime: [33m4724.629999998957[39m,
    sources: [ [36m[Object][39m ]
  },
  {
    value: [33m0.0022044075859917536[39m,
    startTime: [33m4739.929999999702[39m,
    sources: [ [36m[Object][39m ]
  }
]
总布局偏移: 0.428226323445638

[[ATTACHMENT|performance-性能优化检查-布局重排性能测试-chromium\test-failed-1.png]]

[[ATTACHMENT|performance-性能优化检查-布局重排性能测试-chromium\video.webm]]

[[ATTACHMENT|performance-性能优化检查-布局重排性能测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="性能优化检查 › 内存使用情况测试" classname="performance.spec.js" time="4.186">
<system-out>
<![CDATA[初始内存使用: {
  usedJSHeapSize: [33m53500000[39m,
  totalJSHeapSize: [33m56800000[39m,
  jsHeapSizeLimit: [33m3760000000[39m
}
最终内存使用: {
  usedJSHeapSize: [33m53500000[39m,
  totalJSHeapSize: [33m56800000[39m,
  jsHeapSizeLimit: [33m3760000000[39m
}
内存增长: 0 bytes
]]>
</system-out>
</testcase>
<testcase name="性能优化检查 › 资源加载性能测试" classname="performance.spec.js" time="3.985">
<system-out>
<![CDATA[总请求数: 1289
总响应数: 1289
大文件资源: [
  {
    url: [32m'http://localhost:3000/node_modules/.vite/deps/@sentry_vue.js?v=50cf79d0'[39m,
    status: [33m200[39m,
    size: [32m'1605369'[39m,
    endTime: [33m1753325719906[39m
  },
  {
    url: [32m'http://localhost:3000/node_modules/.vite/deps/lucide-vue-next.js?v=50cf79d0'[39m,
    status: [33m200[39m,
    size: [32m'1235031'[39m,
    endTime: [33m1753325719950[39m
  }
]
]]>
</system-out>
</testcase>
<testcase name="性能优化检查 › 渲染性能测试" classname="performance.spec.js" time="4.925">
<system-out>
<![CDATA[滚动性能: {
  fps: [33m61.781468648535764[39m,
  duration: [33m971.1649999991059[39m,
  frameCount: [33m60[39m
}
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>