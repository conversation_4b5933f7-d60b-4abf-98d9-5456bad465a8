<testsuites id="" name="" tests="3" failures="3" skipped="0" errors="0" time="13.47684">
<testsuite name="basic.spec.ts" timestamp="2025-07-24T01:08:11.214Z" hostname="chromium" tests="3" failures="3" skipped="0" time="26.136" errors="0">
<testcase name="基础功能测试 › 应用程序可以正常加载" classname="basic.spec.ts" time="7.099">
<failure message="basic.spec.ts:4:3 应用程序可以正常加载" type="FAILURE">
<![CDATA[  [chromium] › basic.spec.ts:4:3 › 基础功能测试 › 应用程序可以正常加载 ─────────────────────────────────────────────

    Error: Timed out 5000ms waiting for expect(page).toHaveTitle(expected)

    Expected pattern: /AI视频内容创作系统/
    Received string:  "首页 - AI视频创作系统"
    Call log:
      - Expect "toHaveTitle" with timeout 5000ms
        9 × unexpected value "首页 - AI视频创作系统"


       6 |
       7 |     // 检查页面是否成功加载
    >  8 |     await expect(page).toHaveTitle(/AI视频内容创作系统/);
         |                        ^
       9 |
      10 |     // 检查页面内容是否存在
      11 |     await expect(page.locator('h1')).toContainText('AI视频内容创作系统');
        at D:\二创\二创短视频分发\frontend\e2e\basic.spec.ts:8:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\basic-基础功能测试-应用程序可以正常加载-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\basic-基础功能测试-应用程序可以正常加载-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\basic-基础功能测试-应用程序可以正常加载-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-基础功能测试-应用程序可以正常加载-chromium\test-failed-1.png]]

[[ATTACHMENT|basic-基础功能测试-应用程序可以正常加载-chromium\video.webm]]

[[ATTACHMENT|basic-基础功能测试-应用程序可以正常加载-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="基础功能测试 › 页面基本功能可用" classname="basic.spec.ts" time="7.061">
<failure message="basic.spec.ts:15:3 页面基本功能可用" type="FAILURE">
<![CDATA[  [chromium] › basic.spec.ts:15:3 › 基础功能测试 › 页面基本功能可用 ──────────────────────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=图片生成')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=图片生成')


      21 |     // 检查功能卡片
      22 |     await expect(page.locator('text=脚本生成')).toBeVisible();
    > 23 |     await expect(page.locator('text=图片生成')).toBeVisible();
         |                                             ^
      24 |     await expect(page.locator('text=视频合成')).toBeVisible();
      25 |
      26 |     // 检查测试按钮
        at D:\二创\二创短视频分发\frontend\e2e\basic.spec.ts:23:45

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\basic-基础功能测试-页面基本功能可用-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\basic-基础功能测试-页面基本功能可用-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\basic-基础功能测试-页面基本功能可用-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-基础功能测试-页面基本功能可用-chromium\test-failed-1.png]]

[[ATTACHMENT|basic-基础功能测试-页面基本功能可用-chromium\video.webm]]

[[ATTACHMENT|basic-基础功能测试-页面基本功能可用-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="基础功能测试 › 工作流页面可访问" classname="basic.spec.ts" time="11.976">
<failure message="basic.spec.ts:30:3 工作流页面可访问" type="FAILURE">
<![CDATA[  [chromium] › basic.spec.ts:30:3 › 基础功能测试 › 工作流页面可访问 ──────────────────────────────────────────────

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=AI视频内容创作工作流') to be visible


      32 |
      33 |     // 等待页面加载
    > 34 |     await page.waitForSelector('text=AI视频内容创作工作流', { timeout: 10000 });
         |                ^
      35 |
      36 |     // 检查工作流步骤 - 使用first()避免重复元素问题
      37 |     await expect(page.locator('text=视频上传与处理').first()).toBeVisible();
        at D:\二创\二创短视频分发\frontend\e2e\basic.spec.ts:34:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\basic-基础功能测试-工作流页面可访问-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\basic-基础功能测试-工作流页面可访问-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\basic-基础功能测试-工作流页面可访问-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-基础功能测试-工作流页面可访问-chromium\test-failed-1.png]]

[[ATTACHMENT|basic-基础功能测试-工作流页面可访问-chromium\video.webm]]

[[ATTACHMENT|basic-基础功能测试-工作流页面可访问-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>