#!/usr/bin/env python3
"""
增强的密码安全服务
提供密码策略、强度检测、多因素认证等功能
"""

import base64
import logging
import re
import secrets
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from io import BytesIO
from typing import Dict, List, Optional

import bcrypt
import pyotp
import qrcode


class PasswordStrength(Enum):
    """密码强度级别"""

    VERY_WEAK = 0
    WEAK = 1
    MEDIUM = 2
    STRONG = 3
    VERY_STRONG = 4


@dataclass
class PasswordPolicy:
    """密码策略配置"""

    min_length: int = 6
    max_length: int = 128
    require_uppercase: bool = False
    require_lowercase: bool = False
    require_numbers: bool = True
    require_symbols: bool = False
    prohibit_common_passwords: bool = False
    prohibit_personal_info: bool = False
    max_login_attempts: int = 5
    lockout_duration: int = 30  # 分钟
    password_history_count: int = 5
    password_expiry_days: int = 90
    min_password_age: int = 1  # 天


@dataclass
class PasswordValidationResult:
    """密码验证结果"""

    is_valid: bool
    strength: PasswordStrength
    score: int
    feedback: List[str]
    requirements: Dict[str, bool]


@dataclass
class LoginAttempt:
    """登录尝试记录"""

    username: str
    timestamp: datetime
    success: bool
    ip_address: str
    user_agent: str


class EnhancedPasswordService:
    """增强的密码安全服务"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.policy = PasswordPolicy()
        self.login_attempts: Dict[str, List[LoginAttempt]] = {}
        self.locked_accounts: Dict[str, datetime] = {}

        # 常见密码列表
        self.common_passwords = {
            "password",
            "123456",
            "*********",
            "qwerty",
            "abc123",
            "password123",
            "admin",
            "root",
            "user",
            "test",
            "welcome",
            "login",
            "pass",
            "000000",
            "111111",
            "222222",
            "333333",
            "444444",
            "555555",
            "666666",
            "777777",
            "888888",
            "999999",
            "**********",
            "qwertyuiop",
            "asdfghjkl",
            "zxcvbnm",
            "password1",
            "password12",
            "letmein",
            "monkey",
            "dragon",
            "sunshine",
            "princess",
        }

    def update_policy(self, **kwargs) -> None:
        """更新密码策略"""
        for key, value in kwargs.items():
            if hasattr(self.policy, key):
                setattr(self.policy, key, value)
                self.logger.info(f"密码策略更新: {key} = {value}")

    def validate_password(
        self, password: str, user_info: Optional[Dict] = None
    ) -> PasswordValidationResult:
        """
        验证密码强度和策略合规性

        Args:
            password: 待验证的密码
            user_info: 用户信息，用于检查是否包含个人信息

        Returns:
            PasswordValidationResult: 验证结果
        """
        feedback = []
        requirements = {}
        score = 0

        # 简化的密码要求：只要求包含数字和字母
        has_letters = bool(re.search(r"[a-zA-Z]", password))
        has_numbers = bool(re.search(r"[0-9]", password))

        # 检查长度
        requirements["min_length"] = len(password) >= self.policy.min_length
        requirements["max_length"] = len(password) <= self.policy.max_length
        requirements["has_letters"] = has_letters
        requirements["has_numbers"] = has_numbers

        if not requirements["min_length"]:
            feedback.append(f"密码长度至少需要 {self.policy.min_length} 位")
        elif not requirements["max_length"]:
            feedback.append(f"密码长度不能超过 {self.policy.max_length} 位")
        else:
            score += 20
            # 长度奖励
            if len(password) >= 12:
                score += 5
            if len(password) >= 16:
                score += 5

        if not requirements["has_letters"]:
            feedback.append("密码需要包含字母")
        else:
            score += 30

        if not requirements["has_numbers"]:
            feedback.append("密码需要包含数字")
        else:
            score += 30

        # 可选要求（不强制）
        requirements["has_uppercase"] = not self.policy.require_uppercase or bool(
            re.search(r"[A-Z]", password)
        )
        requirements["has_lowercase"] = not self.policy.require_lowercase or bool(
            re.search(r"[a-z]", password)
        )
        requirements["has_symbols"] = not self.policy.require_symbols or bool(
            re.search(r"[^A-Za-z0-9]", password)
        )

        if requirements["has_uppercase"]:
            score += 10

        if requirements["has_lowercase"]:
            score += 10

        if requirements["has_symbols"]:
            score += 10

        # 检查常见密码（可选）
        requirements["not_common"] = (
            not self.policy.prohibit_common_passwords
            or not self._is_common_password(password)
        )
        if requirements["not_common"]:
            score += 10

        # 检查个人信息（可选）
        requirements["not_personal"] = (
            not self.policy.prohibit_personal_info
            or not self._contains_personal_info(password, user_info)
        )
        if requirements["not_personal"]:
            score += 10

        # 额外的复杂度检查
        score += self._calculate_additional_score(password)

        # 确保分数在 0-100 之间
        score = max(0, min(100, score))

        # 确定强度等级
        strength = self._get_strength_from_score(score)

        # 简化验证：只要满足基本要求（长度、字母、数字）就认为有效
        is_valid = (
            requirements["min_length"]
            and requirements["has_letters"]
            and requirements["has_numbers"]
        )

        return PasswordValidationResult(
            is_valid=is_valid,
            strength=strength,
            score=score,
            feedback=feedback,
            requirements=requirements,
        )

    def _is_common_password(self, password: str) -> bool:
        """检查是否为常见密码"""
        return password.lower() in self.common_passwords

    def _contains_personal_info(self, password: str, user_info: Optional[Dict]) -> bool:
        """检查密码是否包含个人信息"""
        if not user_info:
            return False

        password_lower = password.lower()
        checks = [
            user_info.get("username", "").lower(),
            user_info.get("email", "").lower().split("@")[0],
            user_info.get("name", "").lower(),
            user_info.get("phone", ""),
            user_info.get("birthday", ""),
        ]

        for info in checks:
            if info and len(info) >= 3 and info in password_lower:
                return True

        return False

    def _calculate_additional_score(self, password: str) -> int:
        """计算额外的复杂度分数"""
        score = 0

        # 字符多样性
        unique_chars = len(set(password))
        if unique_chars >= len(password) * 0.7:
            score += 5

        # 没有重复字符
        if not re.search(r"(.)\1{2,}", password):
            score += 5

        # 不是键盘序列
        if not self._is_keyboard_sequence(password):
            score += 5

        # 不是字典单词
        if not self._contains_dictionary_word(password):
            score += 5

        # 熵值检查
        entropy = self._calculate_entropy(password)
        if entropy >= 60:
            score += 10
        elif entropy >= 50:
            score += 5

        return score

    def _is_keyboard_sequence(self, password: str) -> bool:
        """检查是否包含键盘序列"""
        sequences = [
            "qwertyuiop",
            "asdfghjkl",
            "zxcvbnm",
            "**********",
            "0987654321",
            "qwerty",
            "asdf",
            "zxcv",
            "1234",
            "4321",
        ]

        password_lower = password.lower()
        for seq in sequences:
            if seq in password_lower:
                return True

        return False

    def _contains_dictionary_word(self, password: str) -> bool:
        """检查是否包含字典单词（简化版）"""
        # 这里可以集成更完整的字典检查
        common_words = {
            "hello",
            "world",
            "computer",
            "internet",
            "security",
            "system",
            "database",
            "server",
            "client",
            "network",
        }

        password_lower = password.lower()
        for word in common_words:
            if word in password_lower:
                return True

        return False

    def _calculate_entropy(self, password: str) -> float:
        """计算密码熵值"""
        if not password:
            return 0

        # 计算字符集大小
        charset_size = 0
        if re.search(r"[a-z]", password):
            charset_size += 26
        if re.search(r"[A-Z]", password):
            charset_size += 26
        if re.search(r"[0-9]", password):
            charset_size += 10
        if re.search(r"[^A-Za-z0-9]", password):
            charset_size += 32

        # 熵值 = 长度 * log2(字符集大小)
        import math

        entropy = len(password) * math.log2(charset_size) if charset_size > 0 else 0
        return entropy

    def _get_strength_from_score(self, score: int) -> PasswordStrength:
        """根据分数获取强度等级"""
        if score >= 90:
            return PasswordStrength.VERY_STRONG
        elif score >= 70:
            return PasswordStrength.STRONG
        elif score >= 50:
            return PasswordStrength.MEDIUM
        elif score >= 30:
            return PasswordStrength.WEAK
        else:
            return PasswordStrength.VERY_WEAK

    def hash_password(self, password: str) -> str:
        """使用bcrypt哈希密码"""
        salt = bcrypt.gensalt(rounds=12)
        return bcrypt.hashpw(password.encode("utf-8"), salt).decode("utf-8")

    def verify_password(self, password: str, hashed_password: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode("utf-8"), hashed_password.encode("utf-8"))

    def generate_secure_password(self, length: int = 12) -> str:
        """生成安全密码"""
        uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        lowercase = "abcdefghijklmnopqrstuvwxyz"
        numbers = "0*********"
        symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"

        # 确保包含各种类型的字符
        password = []
        if self.policy.require_uppercase:
            password.append(secrets.choice(uppercase))
        if self.policy.require_lowercase:
            password.append(secrets.choice(lowercase))
        if self.policy.require_numbers:
            password.append(secrets.choice(numbers))
        if self.policy.require_symbols:
            password.append(secrets.choice(symbols))

        # 构建完整字符集
        all_chars = ""
        if self.policy.require_uppercase:
            all_chars += uppercase
        if self.policy.require_lowercase:
            all_chars += lowercase
        if self.policy.require_numbers:
            all_chars += numbers
        if self.policy.require_symbols:
            all_chars += symbols

        # 填充剩余长度
        while len(password) < length:
            password.append(secrets.choice(all_chars))

        # 打乱顺序
        secrets.SystemRandom().shuffle(password)

        return "".join(password)

    def record_login_attempt(
        self, username: str, success: bool, ip_address: str, user_agent: str
    ) -> None:
        """记录登录尝试"""
        attempt = LoginAttempt(
            username=username,
            timestamp=datetime.now(),
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
        )

        if username not in self.login_attempts:
            self.login_attempts[username] = []

        self.login_attempts[username].append(attempt)

        # 只保留最近的尝试记录
        self.login_attempts[username] = self.login_attempts[username][-20:]

        # 检查是否需要锁定账户
        if not success:
            self._check_account_lockout(username)

    def _check_account_lockout(self, username: str) -> None:
        """检查是否需要锁定账户"""
        if username not in self.login_attempts:
            return

        # 获取最近的失败尝试
        recent_attempts = [
            attempt
            for attempt in self.login_attempts[username]
            if not attempt.success
            and attempt.timestamp
            > datetime.now() - timedelta(minutes=self.policy.lockout_duration)
        ]

        if len(recent_attempts) >= self.policy.max_login_attempts:
            self.locked_accounts[username] = datetime.now()
            self.logger.warning(f"账户 {username} 因登录失败次数过多被锁定")

    def is_account_locked(self, username: str) -> bool:
        """检查账户是否被锁定"""
        if username not in self.locked_accounts:
            return False

        locked_time = self.locked_accounts[username]
        unlock_time = locked_time + timedelta(minutes=self.policy.lockout_duration)

        if datetime.now() > unlock_time:
            # 解锁账户
            del self.locked_accounts[username]
            return False

        return True

    def get_lockout_remaining_time(self, username: str) -> Optional[int]:
        """获取账户锁定剩余时间（分钟）"""
        if username not in self.locked_accounts:
            return None

        locked_time = self.locked_accounts[username]
        unlock_time = locked_time + timedelta(minutes=self.policy.lockout_duration)
        remaining = unlock_time - datetime.now()

        return max(0, int(remaining.total_seconds() / 60))

    def unlock_account(self, username: str) -> bool:
        """手动解锁账户"""
        if username in self.locked_accounts:
            del self.locked_accounts[username]
            self.logger.info(f"账户 {username} 已手动解锁")
            return True
        return False

    def get_password_history(self, user_id: str) -> List[str]:
        """获取用户密码历史（应该从数据库获取）"""
        # 这里应该从数据库获取用户的密码历史
        # 暂时返回空列表
        return []

    def is_password_in_history(
        self, password: str, password_history: List[str]
    ) -> bool:
        """检查密码是否在历史记录中"""
        for historical_password in password_history:
            if self.verify_password(password, historical_password):
                return True
        return False

    def get_password_strength_description(
        self, strength: PasswordStrength
    ) -> Dict[str, str]:
        """获取密码强度描述"""
        descriptions = {
            PasswordStrength.VERY_WEAK: {"text": "非常弱", "color": "red"},
            PasswordStrength.WEAK: {"text": "弱", "color": "orange"},
            PasswordStrength.MEDIUM: {"text": "中等", "color": "yellow"},
            PasswordStrength.STRONG: {"text": "强", "color": "blue"},
            PasswordStrength.VERY_STRONG: {"text": "非常强", "color": "green"},
        }
        return descriptions.get(strength, {"text": "未知", "color": "gray"})

    def generate_password_reset_token(self, user_id: str) -> str:
        """生成密码重置令牌"""
        token = secrets.token_urlsafe(32)
        # 这里应该将令牌存储到数据库，设置过期时间
        return token

    def verify_password_reset_token(self, token: str) -> Optional[str]:
        """验证密码重置令牌"""
        # 这里应该从数据库验证令牌
        # 暂时返回None
        return None

    def get_login_statistics(self, username: str) -> Dict:
        """获取登录统计信息"""
        if username not in self.login_attempts:
            return {
                "total_attempts": 0,
                "successful_attempts": 0,
                "failed_attempts": 0,
                "last_success": None,
                "last_failure": None,
            }

        attempts = self.login_attempts[username]
        successful = [a for a in attempts if a.success]
        failed = [a for a in attempts if not a.success]

        return {
            "total_attempts": len(attempts),
            "successful_attempts": len(successful),
            "failed_attempts": len(failed),
            "last_success": max([a.timestamp for a in successful], default=None),
            "last_failure": max([a.timestamp for a in failed], default=None),
        }


class TOTPService:
    """TOTP（时间基础一次性密码）服务"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def generate_secret(self) -> str:
        """生成TOTP密钥"""
        return pyotp.random_base32()

    def generate_qr_code(
        self, secret: str, user_email: str, issuer_name: str = "AI视频创作系统"
    ) -> str:
        """生成二维码"""
        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(
            name=user_email, issuer_name=issuer_name
        )

        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # 转换为base64
        buffer = BytesIO()
        img.save(buffer, format="PNG")
        img_str = base64.b64encode(buffer.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"

    def verify_token(self, secret: str, token: str, window: int = 1) -> bool:
        """验证TOTP令牌"""
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=window)

    def get_current_token(self, secret: str) -> str:
        """获取当前令牌（仅用于测试）"""
        totp = pyotp.TOTP(secret)
        return totp.now()


# 创建全局实例
enhanced_password_service = EnhancedPasswordService()
totp_service = TOTPService()
