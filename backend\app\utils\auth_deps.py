import uuid
from datetime import datetime, timedelta  # 🔒 证据链: 添加时间处理导入

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel
from sqlalchemy.orm import Session
from typing import Union, Optional

from app.core.config import settings
from app.core.database import get_db
from app.models import User

# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 antd 密码流
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/admin/login")


class TokenData(BaseModel):
    username: Optional[str] = None
    role: Optional[str] = None


def verify_password(plain_password, hashed_password):
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    """生成密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: timedelta = None):
    """创建JWT访问令牌"""
    # 🔒 证据链: 启用JWT过期机制，防止令牌永久有效的安全风险
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        # 默认过期时间：15分钟（生产环境推荐值）
        expire_minutes = getattr(settings, "ACCESS_TOKEN_EXPIRE_MINUTES", 15)
        expire = datetime.utcnow() + timedelta(minutes=expire_minutes)

    # 添加过期时间到令牌载荷
    to_encode.update({"exp": expire})

    # 添加签发时间和令牌ID用于追踪
    to_encode.update(
        {"iat": datetime.utcnow(), "jti": str(uuid.uuid4())}  # JWT ID，用于令牌撤销
    )

    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


async def get_token_from_cookie(request: Request) -> Optional[str]:
    """从HTTPOnly Cookie中获取令牌"""
    token = request.cookies.get("access_token")
    if not token:
        return None
    # "Bearer " 前缀处理
    if token.startswith("Bearer "):
        return token.split("Bearer ")[1]
    return token


async def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(get_token_from_cookie)
):
    """
    解码JWT令牌，验证并返回用户
    """
    if token is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        # 🔒 证据链: JWT解码时自动验证过期时间
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception

        # 验证令牌是否过期（JWT库会自动检查exp字段）
        exp = payload.get("exp")
        if exp is None:
            # 如果没有过期时间，拒绝令牌（向后兼容旧令牌）
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token missing expiration time",
                headers={"WWW-Authenticate": "Bearer"},
            )

        token_data = TokenData(username=username)
    except JWTError as e:
        # 🔒 证据链: 详细的JWT错误处理
        if "expired" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        else:
            raise credentials_exception

    user = db.query(User).filter(User.username == token_data.username).first()
    if user is None:
        raise credentials_exception
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
):
    """
    获取当前活跃用户
    """
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
