#!/usr/bin/env python3
"""
系统监控服务 - 集成Prometheus监控
实现系统指标收集、监控和警报
"""

import logging
import os
import time
from datetime import datetime
from threading import Thread
from typing import Any, Dict, Optional

import psutil

try:
    from prometheus_client import (
        CollectorRegistry,
        Counter,
        Gauge,
        Histogram,
        generate_latest,
    )

    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False


class SystemMonitoringService:
    """系统监控服务"""

    def __init__(self, collection_interval: float = 10.0):
        """
        初始化监控服务

        Args:
            collection_interval: 指标收集间隔(秒)
        """
        self.logger = logging.getLogger(__name__)
        self.collection_interval = collection_interval
        self.running = False

        # 检查Prometheus可用性
        if not PROMETHEUS_AVAILABLE:
            self.logger.error("prometheus-client 不可用")
            return

        # 创建自定义注册表
        self.registry = CollectorRegistry()

        # 初始化系统指标
        self._init_system_metrics()

        # 初始化应用指标
        self._init_application_metrics()

        # 监控线程
        self.monitor_thread = None

    def _init_system_metrics(self):
        """初始化系统级指标"""
        if not PROMETHEUS_AVAILABLE:
            return

        # CPU使用率
        self.cpu_usage = Gauge(
            "system_cpu_usage_percent",
            "System CPU usage percentage",
            registry=self.registry,
        )

        # 内存使用
        self.memory_usage = Gauge(
            "system_memory_usage_bytes",
            "System memory usage in bytes",
            ["type"],  # available, used, total
            registry=self.registry,
        )

        self.memory_usage_percent = Gauge(
            "system_memory_usage_percent",
            "System memory usage percentage",
            registry=self.registry,
        )

        # 磁盘使用
        self.disk_usage = Gauge(
            "system_disk_usage_bytes",
            "System disk usage in bytes",
            ["device", "type"],  # device, used/free/total
            registry=self.registry,
        )

        self.disk_usage_percent = Gauge(
            "system_disk_usage_percent",
            "System disk usage percentage",
            ["device"],
            registry=self.registry,
        )

        # 网络I/O
        self.network_io = Counter(
            "system_network_io_bytes_total",
            "System network I/O in bytes",
            ["direction"],  # sent, received
            registry=self.registry,
        )

        # 进程信息
        self.process_count = Gauge(
            "system_process_count",
            "Number of running processes",
            registry=self.registry,
        )

        # 系统负载
        self.load_average = Gauge(
            "system_load_average",
            "System load average",
            ["period"],  # 1min, 5min, 15min
            registry=self.registry,
        )

    def _init_application_metrics(self):
        """初始化应用级指标"""
        if not PROMETHEUS_AVAILABLE:
            return

        # HTTP请求指标
        self.http_requests_total = Counter(
            "http_requests_total",
            "Total HTTP requests",
            ["method", "endpoint", "status"],
            registry=self.registry,
        )

        self.http_request_duration = Histogram(
            "http_request_duration_seconds",
            "HTTP request duration in seconds",
            ["method", "endpoint"],
            registry=self.registry,
        )

        # 任务执行指标
        self.task_executions_total = Counter(
            "task_executions_total",
            "Total task executions",
            ["task_type", "status"],
            registry=self.registry,
        )

        self.task_duration = Histogram(
            "task_duration_seconds",
            "Task execution duration in seconds",
            ["task_type"],
            registry=self.registry,
        )

        # 视频处理指标
        self.video_processing_total = Counter(
            "video_processing_total",
            "Total video processing tasks",
            ["operation", "status"],
            registry=self.registry,
        )

        self.video_processing_duration = Histogram(
            "video_processing_duration_seconds",
            "Video processing duration in seconds",
            ["operation"],
            registry=self.registry,
        )

        # 用户活动指标
        self.active_users = Gauge(
            "active_users", "Number of active users", registry=self.registry
        )

        self.user_sessions_total = Counter(
            "user_sessions_total",
            "Total user sessions",
            ["action"],  # login, logout
            registry=self.registry,
        )

        # 缓存指标
        self.cache_operations_total = Counter(
            "cache_operations_total",
            "Total cache operations",
            ["operation", "result"],  # get/set/delete, hit/miss/success/error
            registry=self.registry,
        )

        # 数据库指标
        self.database_connections = Gauge(
            "database_connections",
            "Number of database connections",
            ["status"],  # active, idle
            registry=self.registry,
        )

        self.database_queries_total = Counter(
            "database_queries_total",
            "Total database queries",
            [
                "operation",
                "status",
            ],  # select/insert/update/delete, success/error
            registry=self.registry,
        )

        self.database_query_duration = Histogram(
            "database_query_duration_seconds",
            "Database query duration in seconds",
            ["operation"],
            registry=self.registry,
        )

    def start_monitoring(self):
        """启动监控"""
        if not PROMETHEUS_AVAILABLE:
            self.logger.error("Prometheus不可用，无法启动监控")
            return False

        if self.running:
            self.logger.warning("监控已在运行")
            return True

        self.running = True
        self.monitor_thread = Thread(target=self._monitoring_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

        self.logger.info("系统监控已启动")
        return True

    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("系统监控已停止")

    def _monitoring_loop(self):
        """监控主循环"""
        while self.running:
            try:
                self._collect_system_metrics()
                time.sleep(self.collection_interval)
            except Exception as e:
                self.logger.error(f"监控指标收集失败: {e}")
                time.sleep(1)

    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage.set(cpu_percent)

            # 内存使用
            memory = psutil.virtual_memory()
            self.memory_usage.labels(type="total").set(memory.total)
            self.memory_usage.labels(type="used").set(memory.used)
            self.memory_usage.labels(type="available").set(memory.available)
            self.memory_usage_percent.set(memory.percent)

            # 磁盘使用
            for disk in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(disk.mountpoint)
                    device = disk.device.replace("\\", "").replace(":", "")

                    self.disk_usage.labels(device=device, type="total").set(usage.total)
                    self.disk_usage.labels(device=device, type="used").set(usage.used)
                    self.disk_usage.labels(device=device, type="free").set(usage.free)

                    percent = (usage.used / usage.total) * 100
                    self.disk_usage_percent.labels(device=device).set(percent)
                except (OSError, PermissionError):
                    continue

            # 网络I/O
            net_io = psutil.net_io_counters()
            if hasattr(net_io, "bytes_sent"):
                self.network_io.labels(direction="sent")._value._value = (
                    net_io.bytes_sent
                )
                self.network_io.labels(direction="received")._value._value = (
                    net_io.bytes_recv
                )

            # 进程数
            process_count = len(psutil.pids())
            self.process_count.set(process_count)

            # 系统负载 (仅在Linux/Unix系统上可用)
            try:
                load_avg = os.getloadavg()
                self.load_average.labels(period="1min").set(load_avg[0])
                self.load_average.labels(period="5min").set(load_avg[1])
                self.load_average.labels(period="15min").set(load_avg[2])
            except (OSError, AttributeError):
                # Windows系统不支持getloadavg
                pass

        except Exception as e:
            self.logger.error(f"系统指标收集失败: {e}")

    def record_http_request(
        self, method: str, endpoint: str, status_code: int, duration: float
    ):
        """记录HTTP请求指标"""
        if not PROMETHEUS_AVAILABLE:
            return

        self.http_requests_total.labels(
            method=method, endpoint=endpoint, status=str(status_code)
        ).inc()

        self.http_request_duration.labels(method=method, endpoint=endpoint).observe(
            duration
        )

    def record_task_execution(self, task_type: str, status: str, duration: float):
        """记录任务执行指标"""
        if not PROMETHEUS_AVAILABLE:
            return

        self.task_executions_total.labels(task_type=task_type, status=status).inc()

        self.task_duration.labels(task_type=task_type).observe(duration)

    def record_video_processing(self, operation: str, status: str, duration: float):
        """记录视频处理指标"""
        if not PROMETHEUS_AVAILABLE:
            return

        self.video_processing_total.labels(operation=operation, status=status).inc()

        self.video_processing_duration.labels(operation=operation).observe(duration)

    def record_user_activity(self, action: str, user_count: Optional[int] = None):
        """记录用户活动指标"""
        if not PROMETHEUS_AVAILABLE:
            return

        self.user_sessions_total.labels(action=action).inc()

        if user_count is not None:
            self.active_users.set(user_count)

    def record_cache_operation(self, operation: str, result: str):
        """记录缓存操作指标"""
        if not PROMETHEUS_AVAILABLE:
            return

        self.cache_operations_total.labels(operation=operation, result=result).inc()

    def record_database_operation(self, operation: str, status: str, duration: float):
        """记录数据库操作指标"""
        if not PROMETHEUS_AVAILABLE:
            return

        self.database_queries_total.labels(operation=operation, status=status).inc()

        self.database_query_duration.labels(operation=operation).observe(duration)

    def update_database_connections(self, active: int, idle: int):
        """更新数据库连接数指标"""
        if not PROMETHEUS_AVAILABLE:
            return

        self.database_connections.labels(status="active").set(active)
        self.database_connections.labels(status="idle").set(idle)

    def get_metrics(self) -> str:
        """获取Prometheus格式的指标数据"""
        if not PROMETHEUS_AVAILABLE:
            return "# Prometheus not available\n"

        try:
            metrics_bytes = generate_latest(self.registry)
            if isinstance(metrics_bytes, bytes):
                return metrics_bytes.decode("utf-8")
            return str(metrics_bytes)
        except Exception as e:
            self.logger.error(f"生成Prometheus指标失败: {e}")
            return f"# Error generating metrics: {str(e)}\n"

    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        try:
            # 获取当前系统状态
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk_usage = psutil.disk_usage("/")

            summary = {
                "timestamp": datetime.now().isoformat(),
                "system": {
                    "cpu_usage_percent": cpu_percent,
                    "memory_usage_percent": memory.percent,
                    "memory_available_gb": memory.available / (1024**3),
                    "disk_usage_percent": (disk_usage.used / disk_usage.total) * 100,
                    "disk_free_gb": disk_usage.free / (1024**3),
                    "process_count": len(psutil.pids()),
                },
                "monitoring": {
                    "prometheus_available": PROMETHEUS_AVAILABLE,
                    "collection_interval": self.collection_interval,
                    "monitoring_active": self.running,
                },
            }

            return summary

        except Exception as e:
            self.logger.error(f"获取指标摘要失败: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
