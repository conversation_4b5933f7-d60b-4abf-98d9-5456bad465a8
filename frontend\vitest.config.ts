// 🔒 证据链: Vitest配置 - 测试环境和覆盖率设置
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    // 使用happy-dom作为测试环境，提供DOM API
    environment: 'happy-dom',
    
    // 全局测试设置
    globals: true,
    
    // 测试文件匹配模式 - 2025年最佳实践
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx,vue}',
      'src/tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'tests/unit/**/*.{test,spec}.{js,ts}',
      'tests/integration/**/*.{test,spec}.{js,ts}'
    ],
    
    // 排除文件
    exclude: [
      'node_modules',
      'dist',
      '.nuxt',
      'coverage',
      '**/*.d.ts'
    ],
    
    // 测试超时设置
    testTimeout: 5000,
    hookTimeout: 5000,
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      exclude: [
        'coverage/**',
        'dist/**',
        'packages/*/test{,s}/**',
        '**/*.d.ts',
        'cypress/**',
        'test{,s}/**',
        'test{,-*}.{js,cjs,mjs,ts,tsx,jsx}',
        '**/*{.,-}test.{js,cjs,mjs,ts,tsx,jsx}',
        '**/*{.,-}spec.{js,cjs,mjs,ts,tsx,jsx}',
        '**/__tests__/**',
        '**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*',
        '**/.{eslint,mocha,prettier}rc.{js,cjs,yml}',
        'src/main.ts',
        'src/router/**',
        'src/types/**',
        'src/assets/**',
        'src/styles/**'
      ],
      // 覆盖率阈值 - 2025年质量标准
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        },
        // 关键模块更高标准
        'src/utils/**': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90
        },
        'src/composables/**': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85
        }
      }
    },
    
    // 设置文件，在每个测试文件之前运行
    setupFiles: ['./src/tests/setup.ts'],
    
    // 并发设置 - 减少线程数以避免内存问题
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 2,
        minThreads: 1
      }
    },
    
    // 内存管理
    isolate: false,
    maxConcurrency: 2,
    
    // 监听模式配置
    watch: {
      exclude: ['node_modules/**', 'dist/**', 'coverage/**']
    },
    
    // 报告器配置
    reporter: ['verbose', 'json', 'html'],
    outputFile: {
      json: './coverage/test-results.json',
      html: './coverage/test-results.html'
    }
  },
  
  // 路径别名，与主配置保持一致
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '~': resolve(__dirname, './src')
    }
  },
  
  // 定义全局变量
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
  }
})