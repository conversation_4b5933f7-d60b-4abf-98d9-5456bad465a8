const puppeteer = require('puppeteer');

async function testWorkflow() {
  const browser = await puppeteer.launch({ headless: 'new' });
  const page = await browser.newPage();
  
  await page.goto('http://localhost:3001/');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  console.log('🎯 测试新的四步工作流程界面...\n');
  
  // 检查工作流程步骤
  const steps = await page.evaluate(() => {
    const stepElements = document.querySelectorAll('.workflow-step');
    return Array.from(stepElements).map((step, index) => {
      const number = step.querySelector('.step-number')?.textContent;
      const title = step.querySelector('.step-title')?.textContent;
      const isActive = step.classList.contains('active');
      const isDisabled = step.classList.contains('disabled');
      return { number, title, isActive, isDisabled };
    });
  });
  
  console.log('📋 工作流程步骤:');
  steps.forEach((step, index) => {
    const status = step.isActive ? '🟢 活跃' : step.isDisabled ? '🔴 禁用' : '⚪ 等待';
    console.log(`  ${step.number}. ${step.title} - ${status}`);
  });
  
  // 检查输入方法
  const inputMethods = await page.evaluate(() => {
    const urlTextarea = document.querySelector('.url-textarea');
    const uploadArea = document.querySelector('.upload-area');
    return {
      hasUrlInput: !!urlTextarea,
      hasUploadArea: !!uploadArea,
      urlPlaceholder: urlTextarea?.placeholder || '',
      uploadText: uploadArea?.textContent?.trim() || ''
    };
  });
  
  console.log('\n📝 输入方法:');
  console.log(`  URL输入: ${inputMethods.hasUrlInput ? '✅' : '❌'}`);
  console.log(`  文件上传: ${inputMethods.hasUploadArea ? '✅' : '❌'}`);
  
  // 检查AI模型选择
  const modelOptions = await page.evaluate(() => {
    const models = document.querySelectorAll('.model-option');
    return Array.from(models).map(model => {
      const name = model.querySelector('.model-name')?.textContent;
      const tag = model.querySelector('.model-tag')?.textContent;
      const isRecommended = model.classList.contains('recommended');
      return { name, tag, isRecommended };
    });
  });
  
  console.log('\n🤖 AI模型选择:');
  modelOptions.forEach(model => {
    const icon = model.isRecommended ? '⭐' : '🔧';
    console.log(`  ${icon} ${model.name} (${model.tag})`);
  });
  
  // 检查按钮状态
  const buttonStatus = await page.evaluate(() => {
    const startBtn = document.querySelector('.step-actions .btn-primary');
    return {
      exists: !!startBtn,
      text: startBtn?.textContent?.trim() || '',
      disabled: startBtn?.disabled || false
    };
  });
  
  console.log('\n🔘 操作按钮:');
  console.log(`  按钮存在: ${buttonStatus.exists ? '✅' : '❌'}`);
  console.log(`  按钮文本: "${buttonStatus.text}"`);
  console.log(`  按钮状态: ${buttonStatus.disabled ? '🔒 禁用' : '🟢 可用'}`);
  
  // 测试URL输入功能
  console.log('\n🧪 测试URL输入功能...');
  await page.type('.url-textarea', 'https://www.douyin.com/video/test1\nhttps://www.kuaishou.com/video/test2');
  
  const urlCount = await page.evaluate(() => {
    return document.querySelector('.url-count')?.textContent || '0/10';
  });
  
  console.log(`  URL计数: ${urlCount}`);
  
  // 检查按钮是否变为可用
  const buttonEnabled = await page.evaluate(() => {
    const btn = document.querySelector('.step-actions .btn-primary');
    return !btn?.disabled;
  });
  
  console.log(`  按钮状态: ${buttonEnabled ? '🟢 已启用' : '🔒 仍禁用'}`);
  
  // 截图保存
  await page.screenshot({ path: 'workflow-interface.png', fullPage: true });
  console.log('\n📸 界面截图已保存: workflow-interface.png');
  
  await browser.close();
  
  console.log('\n✅ 工作流程界面测试完成！');
  return {
    stepsCount: steps.length,
    hasInputMethods: inputMethods.hasUrlInput && inputMethods.hasUploadArea,
    modelsCount: modelOptions.length,
    buttonWorking: buttonEnabled
  };
}

testWorkflow().catch(console.error);
