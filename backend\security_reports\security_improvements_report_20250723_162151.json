{"report_info": {"generated_at": "2025-07-23T16:21:51.603834", "project_root": "D:\\二创\\二创短视频分发", "report_type": "security_improvements"}, "audit_results": {"timestamp": "2025-07-23T16:20:58.321253", "pip_audit": {"error": "Traceback (most recent call last):\n  File \"c:\\program files\\python\\lib\\site-packages\\urllib3\\response.py\", line 436, in _error_catcher\n    yield\n  File \"c:\\program files\\python\\lib\\site-packages\\urllib3\\response.py\", line 518, in read\n    data = self._fp.read(amt) if not fp_closed else b\"\"\n  File \"c:\\program files\\python\\lib\\site-packages\\cachecontrol\\filewrapper.py\", line 98, in read\n    data: bytes = self.__fp.read(amt)\n  File \"c:\\program files\\python\\lib\\http\\client.py\", line 458, in read\n    n = self.readinto(b)\n  File \"c:\\program files\\python\\lib\\http\\client.py\", line 502, in readinto\n    n = self.fp.readinto(b)\n  File \"c:\\program files\\python\\lib\\socket.py\", line 669, in readinto\n    return self._sock.recv_into(b)\n  File \"c:\\program files\\python\\lib\\ssl.py\", line 1241, in recv_into\n    return self.read(nbytes, buffer)\n  File \"c:\\program files\\python\\lib\\ssl.py\", line 1099, in read\n    return self._sslobj.read(len, buffer)\nsocket.timeout: The read operation timed out\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"c:\\program files\\python\\lib\\site-packages\\requests\\models.py\", line 820, in generate\n    yield from self.raw.stream(chunk_size, decode_content=True)\n  File \"c:\\program files\\python\\lib\\site-packages\\urllib3\\response.py\", line 575, in stream\n    data = self.read(amt=amt, decode_content=decode_content)\n  File \"c:\\program files\\python\\lib\\site-packages\\urllib3\\response.py\", line 540, in read\n    raise IncompleteRead(self._fp_bytes_read, self.length_remaining)\n  File \"c:\\program files\\python\\lib\\contextlib.py\", line 131, in __exit__\n    self.gen.throw(type, value, traceback)\n  File \"c:\\program files\\python\\lib\\site-packages\\urllib3\\response.py\", line 441, in _error_catcher\n    raise ReadTimeoutError(self._pool, None, \"Read timed out.\")\nurllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='pypi.org', port=443): Read timed out.\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"c:\\program files\\python\\lib\\runpy.py\", line 194, in _run_module_as_main\n    return _run_code(code, main_globals, None,\n  File \"c:\\program files\\python\\lib\\runpy.py\", line 87, in _run_code\n    exec(code, run_globals)\n  File \"C:\\Program Files\\python\\Scripts\\pip-audit.exe\\__main__.py\", line 7, in <module>\n  File \"c:\\program files\\python\\lib\\site-packages\\pip_audit\\_cli.py\", line 509, in audit\n    for spec, vulns in auditor.audit(source):\n  File \"c:\\program files\\python\\lib\\site-packages\\pip_audit\\_audit.py\", line 68, in audit\n    for dep, vulns in self._service.query_all(specs):\n  File \"c:\\program files\\python\\lib\\site-packages\\pip_audit\\_service\\interface.py\", line 155, in query_all\n    yield self.query(spec)\n  File \"c:\\program files\\python\\lib\\site-packages\\pip_audit\\_service\\pypi.py\", line 62, in query\n    response: requests.Response = self.session.get(url=url, timeout=self.timeout)\n  File \"c:\\program files\\python\\lib\\site-packages\\requests\\sessions.py\", line 602, in get\n    return self.request(\"GET\", url, **kwargs)\n  File \"c:\\program files\\python\\lib\\site-packages\\requests\\sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n  File \"c:\\program files\\python\\lib\\site-packages\\requests\\sessions.py\", line 746, in send\n    r.content\n  File \"c:\\program files\\python\\lib\\site-packages\\requests\\models.py\", line 902, in content\n    self._content = b\"\".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b\"\"\n  File \"c:\\program files\\python\\lib\\site-packages\\requests\\models.py\", line 826, in generate\n    raise ConnectionError(e)\nrequests.exceptions.ConnectionError: HTTPSConnectionPool(host='pypi.org', port=443): Read timed out.\n"}, "safety_check": {"error": "Your authentication credential is invalid. See https://docs.safetycli.com/safety-docs/support/invalid-api-key-error. Reason: <!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n<HTML><HEAD><META HTTP-EQUIV=\"Content-Type\" CONTENT=\"text/html; charset=iso-8859-1\">\n<TITLE>ERROR: The request could not be satisfied</TITLE>\n</HEAD><BODY>\n<H1>403 ERROR</H1>\n<H2>The request could not be satisfied.</H2>\n<HR noshade size=\"1px\">\nRequest blocked.\nWe can't connect to the server for this app or website at this time. There might be too much traffic or a configuration error. Try again later, or contact the app or website owner.\n<BR clear=\"all\">\nIf you provide content to customers through CloudFront, you can find steps to troubleshoot and help prevent this error by reviewing the CloudFront documentation.\n<BR clear=\"all\">\n<HR noshade size=\"1px\">\n<PRE>\nGenerated by cloudfront (CloudFront)\nRequest ID: 7UBUNtP6l9vDm60IawWj1piVi_66sDZ8BdCjnV6KoNNJzxaTQ0x9Kg==\n</PRE>\n<ADDRESS>\n</ADDRESS>\n</BODY></HTML>\n"}, "bandit_scan": {"error": "[main]\tINFO\tprofile include tests: None\n[main]\tINFO\tprofile exclude tests: None\n[main]\tINFO\tcli include tests: None\n[main]\tINFO\tcli exclude tests: None\n"}, "dependency_check": {}}, "configuration_check": {"timestamp": "2025-07-23T16:21:51.603834", "env_file_check": {"exists": true, "exposed_secrets": ["SECRET_KEY", "DATABASE_URL", "REDIS_URL", "TOKEN"]}, "ssl_config": {"https_required": true}, "database_config": {}, "cors_config": {}, "recommendations": ["启用HTTPS强制重定向", "配置安全头部 (HSTS, CSP, X-Frame-Options)", "实施速率限制", "启用CSRF保护", "配置安全的会话管理", "实施输入验证和输出编码", "配置日志监控和告警"]}, "security_config": {"min_password_length": 12, "session_timeout": 3600, "max_login_attempts": 5, "token_expiry": 86400, "require_https": true, "enable_csrf_protection": true, "enable_rate_limiting": true}, "summary": {"total_vulnerabilities": 0, "critical_issues": 0, "high_issues": 0, "medium_issues": 0, "low_issues": 0, "recommendations_count": 7, "overall_risk_level": "LOW"}}