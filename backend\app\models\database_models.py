#!/usr/bin/env python3
"""
数据库模型定义
定义用户、任务、视频等业务实体的数据模型
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class UserRole(str, PyEnum):
    """用户角色枚举"""

    ADMIN = "admin"
    CREATOR = "creator"
    EDITOR = "editor"
    VIEWER = "viewer"


class UserStatus(str, PyEnum):
    """用户状态枚举"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    BANNED = "banned"


class TaskStatus(str, PyEnum):
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class VideoStatus(str, PyEnum):
    """视频状态枚举"""

    UPLOADING = "uploading"
    PROCESSING = "processing"
    READY = "ready"
    FAILED = "failed"
    DELETED = "deleted"


class User(Base):
    """用户模型"""

    __tablename__ = "users"
    __table_args__ = (
        Index("idx_user_email_status", "email", "status"),
        Index("idx_user_role_status", "role", "status"),
        {"extend_existing": True},
    )

    # 主键
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(50), unique=True, index=True, nullable=False)

    # 基本信息
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)

    # 用户属性
    role = Column(String(20), default=UserRole.CREATOR.value, nullable=False)
    status = Column(String(20), default=UserStatus.ACTIVE.value, nullable=False)

    # 个人信息
    display_name = Column(String(100))
    avatar_url = Column(String(500))
    bio = Column(Text)

    # 统计信息
    video_count = Column(Integer, default=0)
    total_views = Column(Integer, default=0)
    total_likes = Column(Integer, default=0)

    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_login_at = Column(DateTime)

    # 安全设置
    otp_secret = Column(String(255))
    otp_enabled = Column(Boolean, default=False)

    # 关系
    videos = relationship("Video", back_populates="user", cascade="all, delete")
    tasks = relationship("Task", back_populates="user", cascade="all, delete")


class Video(Base):
    """视频模型"""

    __tablename__ = "videos"
    __table_args__ = (
        Index("idx_video_user_status", "user_id", "status"),
        Index("idx_video_created_at", "created_at"),
        Index("idx_video_compliance", "compliance_level", "compliance_score"),
        {"extend_existing": True},
    )

    # 主键
    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(String(50), unique=True, index=True, nullable=False)

    # 基本信息
    title = Column(String(200), nullable=False)
    description = Column(Text)
    tags = Column(JSON)  # 存储标签列表

    # 文件信息
    original_url = Column(String(1000))  # 原始视频URL
    file_path = Column(String(500))  # 本地文件路径
    file_size = Column(Integer)  # 文件大小(bytes)
    duration = Column(Float)  # 时长(秒)

    # 视频属性
    format = Column(String(20))  # 视频格式
    resolution = Column(String(20))  # 分辨率
    bitrate = Column(Integer)  # 比特率
    fps = Column(Float)  # 帧率

    # 状态信息
    status = Column(String(20), default=VideoStatus.UPLOADING.value)
    processing_progress = Column(Float, default=0.0)  # 处理进度
    error_message = Column(Text)

    # 内容分析结果
    compliance_level = Column(String(20))  # 合规等级
    compliance_score = Column(Float)  # 合规评分
    quality_score = Column(Float)  # 质量评分
    engagement_score = Column(Float)  # 互动潜力评分
    recommended_platforms = Column(JSON)  # 推荐平台列表
    categories = Column(JSON)  # 内容分类

    # 统计信息
    view_count = Column(Integer, default=0)
    like_count = Column(Integer, default=0)
    share_count = Column(Integer, default=0)
    download_count = Column(Integer, default=0)

    # 外键
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    processed_at = Column(DateTime)

    # 关系
    user = relationship("User", back_populates="videos")
    tasks = relationship("Task", back_populates="video", cascade="all, delete")


class Task(Base):
    """任务模型"""

    __tablename__ = "tasks"
    __table_args__ = (
        Index("idx_task_user_status", "user_id", "status"),
        Index("idx_task_type_status", "task_type", "status"),
        Index("idx_task_created_at", "created_at"),
        Index("idx_task_priority_status", "priority", "status"),
        {"extend_existing": True},
    )

    # 主键
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(50), unique=True, index=True, nullable=False)

    # 基本信息
    name = Column(String(200), nullable=False)
    description = Column(Text)
    task_type = Column(String(50), nullable=False)  # 任务类型
    priority = Column(String(20), default="normal")  # 优先级

    # 状态信息
    status = Column(String(20), default=TaskStatus.PENDING.value)
    progress = Column(Float, default=0.0)  # 进度百分比
    error_message = Column(Text)

    # 任务数据
    input_data = Column(JSON)  # 输入数据
    output_data = Column(JSON)  # 输出数据
    task_metadata = Column(JSON)  # 元数据

    # 执行信息
    worker_id = Column(String(100))  # 执行工作进程ID
    execution_time = Column(Float)  # 执行时间(秒)
    retry_count = Column(Integer, default=0)  # 重试次数
    max_retries = Column(Integer, default=3)  # 最大重试次数

    # 外键
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=True)

    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    started_at = Column(DateTime)
    completed_at = Column(DateTime)

    # 关系
    user = relationship("User", back_populates="tasks")
    video = relationship("Video", back_populates="tasks")


class ContentAnalysis(Base):
    """内容分析记录模型"""

    __tablename__ = "content_analyses"
    __table_args__ = (
        Index("idx_analysis_content", "content_id", "content_type"),
        Index("idx_analysis_compliance", "compliance_level", "compliance_score"),
        Index("idx_analysis_created_at", "created_at"),
        {"extend_existing": True},
    )

    # 主键
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(String(50), unique=True, index=True, nullable=False)

    # 关联信息
    content_id = Column(String(50), nullable=False)  # 内容ID
    content_type = Column(String(20), nullable=False)  # 内容类型

    # 分析结果
    compliance_level = Column(String(20), nullable=False)
    compliance_score = Column(Float, nullable=False)
    compliance_reasons = Column(JSON)  # 合规问题原因列表

    quality_score = Column(Float, nullable=False)
    engagement_score = Column(Float, nullable=False)

    # 推荐信息
    recommended_platforms = Column(JSON)  # 推荐平台列表
    platform_scores = Column(JSON)  # 各平台评分
    categories = Column(JSON)  # 内容分类
    tags = Column(JSON)  # 推荐标签

    # 元数据
    analysis_version = Column(String(20), default="1.0")
    analyzer_info = Column(JSON)  # 分析器信息

    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False)


class SystemMetrics(Base):
    """系统指标模型"""

    __tablename__ = "system_metrics"
    __table_args__ = (
        Index("idx_metrics_name_time", "metric_name", "timestamp"),
        Index("idx_metrics_service", "service_name", "timestamp"),
        {"extend_existing": True},
    )

    # 主键
    id = Column(Integer, primary_key=True, index=True)

    # 指标信息
    metric_name = Column(String(100), nullable=False)
    metric_type = Column(String(50), nullable=False)  # counter, gauge, histogram
    metric_value = Column(Float, nullable=False)
    metric_labels = Column(JSON)  # 标签信息

    # 元数据
    service_name = Column(String(100), nullable=False)
    instance_id = Column(String(100))

    # 时间戳
    timestamp = Column(DateTime, default=func.now(), nullable=False)


class ProjectStatus(Base):
    """开源项目状态模型"""

    __tablename__ = "project_statuses"
    __table_args__ = (
        Index("idx_project_name_status", "project_name", "status"),
        Index("idx_project_type", "project_type"),
        Index("idx_project_updated_at", "updated_at"),
        {"extend_existing": True},
    )

    # 主键
    id = Column(Integer, primary_key=True, index=True)

    # 项目信息
    project_name = Column(String(100), unique=True, nullable=False)
    project_type = Column(String(50), nullable=False)
    github_url = Column(String(500))

    # 状态信息
    status = Column(String(50), nullable=False)
    version = Column(String(50))
    last_check_status = Column(String(50))
    error_message = Column(Text)

    # 配置信息
    package_names = Column(JSON)  # Python包名列表
    dependencies = Column(JSON)  # 依赖项列表
    config_data = Column(JSON)  # 配置数据

    # 统计信息
    check_count = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    failure_count = Column(Integer, default=0)

    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_checked_at = Column(DateTime)


class AuditLog(Base):
    """审计日志模型"""

    __tablename__ = "audit_logs"
    __table_args__ = (
        Index("idx_audit_user_action", "user_id", "action"),
        Index("idx_audit_timestamp", "timestamp"),
        {"extend_existing": True},
    )

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(
        Integer, ForeignKey("users.id"), nullable=True
    )  # 系统操作可能没有用户
    ip_address = Column(String(50))
    action = Column(
        String(100), nullable=False
    )  # 例如: "user_login", "update_settings"
    details = Column(JSON)  # 存储操作的详细信息，如修改前后的值
    timestamp = Column(DateTime, default=func.now(), nullable=False)

    user = relationship("User")
