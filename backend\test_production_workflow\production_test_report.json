{"workflow_id": "test_1751870218", "start_time": "2025-07-07T14:36:58.532172", "steps": {"step1": {"status": "partial_success", "duration": 0.15601038932800293, "video_path": "test_production_workflow\\test_video.mp4", "note": "使用模拟视频文件"}, "step2": {"status": "success", "duration": 0.0010619163513183594, "frame_analysis": {"total_frames": 250, "key_frames": 12, "scene_changes": 5, "dominant_colors": ["#2E86AB", "#A23B72", "#F18F01"], "objects_detected": ["person", "computer", "text"], "text_regions": 3}, "audio_analysis": {"duration": 10.5, "sample_rate": 44100, "channels": 2, "volume_levels": [0.7, 0.8, 0.6, 0.9], "speech_segments": [{"start": 0.5, "end": 3.2, "confidence": 0.95}, {"start": 4.1, "end": 7.8, "confidence": 0.88}, {"start": 8.2, "end": 10.0, "confidence": 0.92}]}}, "step3": {"status": "success", "duration": 5.0207695960998535, "extracted_text_length": 503, "ai_service_used": "<PERSON><PERSON>", "output_file": "test_production_workflow\\extracted_text.txt"}, "step4": {"status": "success", "duration": 0.0010302066802978516, "optimization_results": {"original_length": 503, "optimized_versions": {"short_form": "AI技术发展：从历史到未来 #AI #科技 #未来", "medium_form": "10秒了解AI完整发展历程！从智能助手到自动驾驶，看AI如何改变生活 🚀 #人工智能 #科技发展", "long_form": "🤖 AI技术全景解读！\n\n✨ 历史回顾：AI发展里程碑\n🔥 现状分析：智能应用遍地开花\n🚀 未来展望：AI赋能美好生活\n\n从医疗诊断到金融分析，从智能助手到自动驾驶，AI正在各行各业发挥重要作用。这个视频将带你快速了解AI的核心原理和发展趋势！"}, "seo_keywords": ["AI技术", "人工智能", "科技发展", "智能应用", "未来科技"], "sentiment_score": 0.85, "readability_score": 0.78}, "output_file": "test_production_workflow\\optimized_content.json"}, "step5": {"status": "partial_success", "duration": 0.05245375633239746, "processing_results": {"status": "simulated", "operations": ["format_conversion", "compression", "audio_extraction"], "output_files": ["processed_video.mp4", "compressed_video.mp4", "extracted_audio.mp3"]}, "note": "使用模拟处理结果"}, "step6": {"status": "success", "duration": 0.0010340213775634766, "platform_versions": {"douyin": {"title": "AI技术发展全景！10秒看懂未来科技", "description": "10秒了解AI完整发展历程！从智能助手到自动驾驶，看AI如何改变生活 🚀 #人工智能 #科技发展", "hashtags": ["#AI技术", "#科技发展", "#人工智能", "#未来科技"], "video_specs": {"resolution": "1080x1920", "duration": "≤15秒", "format": "mp4"}}, "bilibili": {"title": "AI技术发展全景：从历史到未来的完整解读", "description": "🤖 AI技术全景解读！\n\n✨ 历史回顾：AI发展里程碑\n🔥 现状分析：智能应用遍地开花\n🚀 未来展望：AI赋能美好生活\n\n从医疗诊断到金融分析，从智能助手到自动驾驶，AI正在各行各业发挥重要作用。这个视频将带你快速了解AI的核心原理和发展趋势！", "tags": ["科技", "人工智能", "技术解读", "科普"], "video_specs": {"resolution": "1920x1080", "duration": "无限制", "format": "mp4"}}, "xiaohongshu": {"title": "AI改变生活的8个瞬间✨", "description": "从智能助手到自动驾驶，AI正在悄悄改变我们的生活方式～", "hashtags": ["#AI生活", "#智能科技", "#未来生活", "#科技分享"], "video_specs": {"resolution": "1080x1350", "duration": "≤60秒", "format": "mp4"}}, "wechat_channels": {"title": "AI技术发展趋势解读", "description": "10秒了解AI完整发展历程！从智能助手到自动驾驶，看AI如何改变生活 🚀 #人工智能 #科技发展", "hashtags": ["#人工智能", "#科技发展"], "video_specs": {"resolution": "1280x720", "duration": "≤30秒", "format": "mp4"}}}, "output_file": "test_production_workflow\\platform_versions.json"}, "step7": {"status": "success", "duration": 0.0010378360748291016, "compliance_results": {"text_safety": {"status": "safe", "confidence": 0.95, "issues_found": [], "suggestions": []}, "image_safety": {"status": "safe", "confidence": 0.92, "nsfw_score": 0.05, "violence_score": 0.02}, "audio_safety": {"status": "safe", "confidence": 0.88, "sensitive_content": false}, "overall_compliance": {"status": "approved", "compliance_score": 0.92, "platform_suitability": {"douyin": "approved", "bilibili": "approved", "xiaohongshu": "approved", "wechat_channels": "approved"}}}, "output_file": "test_production_workflow\\compliance_results.json"}, "step8": {"status": "success", "duration": 0.00357818603515625, "publish_packages": {"douyin": {"package_dir": "test_production_workflow\\publish_packages\\douyin", "metadata_file": "test_production_workflow\\publish_packages\\douyin\\publish_metadata.json", "status": "ready"}, "bilibili": {"package_dir": "test_production_workflow\\publish_packages\\bilibili", "metadata_file": "test_production_workflow\\publish_packages\\bilibili\\publish_metadata.json", "status": "ready"}, "xiaohongshu": {"package_dir": "test_production_workflow\\publish_packages\\xiaohongshu", "metadata_file": "test_production_workflow\\publish_packages\\xiaohongshu\\publish_metadata.json", "status": "ready"}, "wechat_channels": {"package_dir": "test_production_workflow\\publish_packages\\wechat_channels", "metadata_file": "test_production_workflow\\publish_packages\\wechat_channels\\publish_metadata.json", "status": "ready"}}, "publish_schedule": {"total_platforms": 4, "estimated_total_reach": 20000, "publish_timeline": {"immediate": ["do<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "evening_peak": ["bilibili", "wechat_channels"], "weekend": []}, "monitoring_plan": {"metrics_to_track": ["views", "likes", "comments", "shares"], "reporting_frequency": "hourly_first_24h", "alert_thresholds": {"low_engagement": 0.02, "negative_sentiment": -0.3}}}, "output_dir": "test_production_workflow\\publish_packages"}}, "overall_status": "completed", "errors": [], "summary": {"total_steps": 8, "successful_steps": 8, "failed_steps": 0, "success_rate": 100.0, "total_duration": 5.236975908279419}}