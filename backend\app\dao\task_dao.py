#!/usr/bin/env python3
"""
数据访问层 (DAO) - 任务管理
提供任务相关的数据库操作
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import desc, func, or_
from sqlalchemy.orm import Session

from app.models.database_models import Task, TaskStatus


class TaskDAO:
    """任务数据访问对象"""

    def __init__(self, db: Session):
        self.db = db

    def create_task(self, task_data: Dict[str, Any]) -> Task:
        """创建新任务"""
        task = Task(**task_data)
        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)
        return task

    def get_task_by_id(self, task_id: str) -> Optional[Task]:
        """根据任务ID获取任务"""
        return self.db.query(Task).filter(Task.task_id == task_id).first()

    def get_tasks_by_user(self, user_id: int) -> List[Task]:
        """获取用户的所有任务"""
        return self.db.query(Task).filter(Task.user_id == user_id).all()

    def get_tasks_by_status(self, status: TaskStatus) -> List[Task]:
        """根据状态获取任务列表"""
        return self.db.query(Task).filter(Task.status == status.value).all()

    def get_tasks_by_type(self, task_type: str) -> List[Task]:
        """根据类型获取任务列表"""
        return self.db.query(Task).filter(Task.task_type == task_type).all()

    def get_pending_tasks(self, limit: int = 10) -> List[Task]:
        """获取待处理任务"""
        return (
            self.db.query(Task)
            .filter(Task.status == TaskStatus.PENDING.value)
            .order_by(Task.priority.desc(), Task.created_at.asc())
            .limit(limit)
            .all()
        )

    def get_running_tasks(self) -> List[Task]:
        """获取正在运行的任务"""
        return self.get_tasks_by_status(TaskStatus.RUNNING)

    def update_task(self, task_id: str, update_data: Dict[str, Any]) -> bool:
        """更新任务信息"""
        result = self.db.query(Task).filter(Task.task_id == task_id).update(update_data)
        self.db.commit()
        return result > 0

    def update_task_status(
        self,
        task_id: str,
        status: TaskStatus,
        progress: float = None,
        error_message: str = None,
    ) -> bool:
        """更新任务状态"""
        update_data = {"status": status.value}

        if progress is not None:
            update_data["progress"] = progress

        if error_message is not None:
            update_data["error_message"] = error_message

        # 设置时间戳
        if status == TaskStatus.RUNNING:
            update_data["started_at"] = datetime.now()
        elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
            update_data["completed_at"] = datetime.now()

        return self.update_task(task_id, update_data)

    def update_task_progress(self, task_id: str, progress: float) -> bool:
        """更新任务进度"""
        return self.update_task(task_id, {"progress": progress})

    def increment_retry_count(self, task_id: str) -> bool:
        """增加任务重试次数"""
        task = self.get_task_by_id(task_id)
        if task:
            new_count = task.retry_count + 1
            return self.update_task(task_id, {"retry_count": new_count})
        return False

    def assign_worker(self, task_id: str, worker_id: str) -> bool:
        """分配任务给工作进程"""
        return self.update_task(task_id, {"worker_id": worker_id})

    def complete_task(
        self,
        task_id: str,
        output_data: Dict[str, Any],
        execution_time: float = None,
    ) -> bool:
        """完成任务"""
        update_data = {
            "status": TaskStatus.COMPLETED.value,
            "output_data": output_data,
            "completed_at": datetime.now(),
            "progress": 100.0,
        }

        if execution_time is not None:
            update_data["execution_time"] = execution_time

        return self.update_task(task_id, update_data)

    def fail_task(self, task_id: str, error_message: str) -> bool:
        """任务失败"""
        return self.update_task_status(
            task_id, TaskStatus.FAILED, error_message=error_message
        )

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        return self.update_task_status(task_id, TaskStatus.CANCELLED)

    def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        result = self.db.query(Task).filter(Task.task_id == task_id).delete()
        self.db.commit()
        return result > 0

    def get_tasks_paginated(self, skip: int = 0, limit: int = 10) -> List[Task]:
        """分页获取任务列表"""
        return (
            self.db.query(Task)
            .order_by(desc(Task.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def search_tasks(self, query: str, limit: int = 10) -> List[Task]:
        """搜索任务"""
        return (
            self.db.query(Task)
            .filter(
                or_(
                    Task.name.ilike(f"%{query}%"),
                    Task.description.ilike(f"%{query}%"),
                    Task.task_type.ilike(f"%{query}%"),
                )
            )
            .limit(limit)
            .all()
        )

    def count_tasks(self) -> int:
        """获取任务总数"""
        return self.db.query(Task).count()

    def count_tasks_by_status(self) -> Dict[str, int]:
        """按状态统计任务数量"""
        result = (
            self.db.query(Task.status, func.count(Task.id)).group_by(Task.status).all()
        )
        return dict(result)

    def count_tasks_by_type(self) -> Dict[str, int]:
        """按类型统计任务数量"""
        result = (
            self.db.query(Task.task_type, func.count(Task.id))
            .group_by(Task.task_type)
            .all()
        )
        return dict(result)

    def count_tasks_by_user(self, user_id: int) -> int:
        """统计用户的任务数量"""
        return self.db.query(Task).filter(Task.user_id == user_id).count()

    def get_task_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        total_tasks = self.count_tasks()
        status_stats = self.count_tasks_by_status()
        type_stats = self.count_tasks_by_type()

        # 计算平均执行时间
        avg_execution_time = (
            self.db.query(func.avg(Task.execution_time))
            .filter(
                Task.execution_time.isnot(None),
                Task.status == TaskStatus.COMPLETED.value,
            )
            .scalar()
            or 0.0
        )

        # 计算成功率
        completed_count = status_stats.get(TaskStatus.COMPLETED.value, 0)
        failed_count = status_stats.get(TaskStatus.FAILED.value, 0)
        total_finished = completed_count + failed_count
        success_rate = (
            (completed_count / total_finished) * 100 if total_finished > 0 else 0.0
        )

        return {
            "total_tasks": total_tasks,
            "status_distribution": status_stats,
            "type_distribution": type_stats,
            "average_execution_time": round(avg_execution_time, 2),
            "success_rate": round(success_rate, 2),
        }
