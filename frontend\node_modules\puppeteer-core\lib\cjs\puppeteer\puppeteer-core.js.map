{"version": 3, "file": "puppeteer-core.js", "sourceRoot": "", "sources": ["../../../src/puppeteer-core.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,6CAA2B;AAE3B,sDAAyB;AACzB,0DAA6B;AAE7B,qDAA6C;AAE7C,sDAAwC;AAExC,iDAAiD;AACjD,4BAAW,CAAC,KAAK,GAAG;IAClB,EAAE,EAAF,iBAAE;IACF,IAAI,EAAJ,mBAAI;IACJ,cAAc,EAAE,SAAS,CAAC,cAAc;CACzC,CAAC;AACF;;GAEG;AACH,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,aAAa,CAAC;IAC5C,eAAe,EAAE,IAAI;CACtB,CAAC,CAAC;AAGD;;GAEG;AACH,eAAO,GAaL,SAAS;AAZX;;GAEG;AACH,mBAAW,GAST,SAAS;AARX;;GAEG;AACH,sBAAc,GAKZ,SAAS;AAJX;;GAEG;AACH,cAAM,GACJ,SAAS,QAAC;AAEd,kBAAe,SAAS,CAAC"}