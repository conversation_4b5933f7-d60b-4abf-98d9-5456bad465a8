enum DOMExceptionNameEnum {
	invalidStateError = 'InvalidStateError',
	indexSizeError = 'IndexSizeError',
	syntaxError = 'SyntaxError',
	hierarchyRequestError = 'HierarchyRequestError',
	notSupportedError = 'NotSupportedError',
	wrongDocumentError = 'WrongDocumentError',
	invalidNodeTypeError = 'InvalidNodeTypeError',
	invalidCharacterError = 'InvalidCharacterError',
	notFoundError = 'NotFoundError',
	securityError = 'SecurityError',
	networkError = 'NetworkError',
	domException = 'DOMException',
	invalidAccessError = 'InvalidAccessError',
	unknownError = 'UnknownError',
	abortError = 'AbortError',
	timeoutError = 'TimeoutError',
	encodingError = 'EncodingError',
	uriMismatchError = 'URIMismatchError'
}
export default DOMExceptionNameEnum;
