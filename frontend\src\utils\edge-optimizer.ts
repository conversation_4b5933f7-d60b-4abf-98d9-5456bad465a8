/**
 * 边缘计算优化系统 - 2025年最佳实践
 * CDN和边缘缓存策略实现
 */

// 边缘优化配置
interface EdgeOptimizerConfig {
  enableEdgeCaching: boolean
  enableServiceWorkerCaching: boolean
  enableCDNOptimization: boolean
  enableGeoOptimization: boolean
  cacheStrategy: 'aggressive' | 'balanced' | 'conservative'
  maxCacheSize: number // MB
  cacheTTL: number // seconds
  edgeLocations: string[]
}

// 缓存策略
interface CacheStrategy {
  name: string
  pattern: RegExp
  ttl: number
  priority: number
  compression: boolean
  edgeCache: boolean
}

// 地理位置信息
interface GeoLocation {
  country: string
  region: string
  city: string
  latitude: number
  longitude: number
  timezone: string
  isp: string
}

// 边缘节点信息
interface EdgeNode {
  id: string
  location: GeoLocation
  latency: number
  bandwidth: number
  load: number
  available: boolean
}

// 性能指标
interface EdgeMetrics {
  cacheHitRate: number
  averageLatency: number
  bandwidthSaved: number
  requestsServed: number
  errorRate: number
}

class EdgeOptimizer {
  private config: EdgeOptimizerConfig
  private cacheStrategies: CacheStrategy[] = []
  private edgeNodes: Map<string, EdgeNode> = new Map()
  private metrics: EdgeMetrics = {
    cacheHitRate: 0,
    averageLatency: 0,
    bandwidthSaved: 0,
    requestsServed: 0,
    errorRate: 0
  }
  private userLocation: GeoLocation | null = null
  private optimalEdgeNode: EdgeNode | null = null

  constructor(config: Partial<EdgeOptimizerConfig> = {}) {
    this.config = {
      enableEdgeCaching: true,
      enableServiceWorkerCaching: true,
      enableCDNOptimization: true,
      enableGeoOptimization: true,
      cacheStrategy: 'balanced',
      maxCacheSize: 100, // 100MB
      cacheTTL: 3600, // 1小时
      edgeLocations: ['us-east-1', 'eu-west-1', 'ap-southeast-1'],
      ...config
    }

    this.init()
  }

  private init(): void {
    this.setupCacheStrategies()
    this.detectUserLocation()
    this.initializeEdgeNodes()
    this.setupServiceWorkerIntegration()
    this.startMetricsCollection()

    console.log('🌐 边缘计算优化系统已启动')
  }

  // 设置缓存策略
  private setupCacheStrategies(): void {
    const strategies: CacheStrategy[] = [
      {
        name: 'static-assets',
        pattern: /\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/,
        ttl: 31536000, // 1年
        priority: 1,
        compression: true,
        edgeCache: true
      },
      {
        name: 'api-responses',
        pattern: /^\/api\//,
        ttl: this.config.cacheStrategy === 'aggressive' ? 300 : 
             this.config.cacheStrategy === 'balanced' ? 60 : 30,
        priority: 2,
        compression: true,
        edgeCache: true
      },
      {
        name: 'html-pages',
        pattern: /\.html$|^\/[^.]*$/,
        ttl: this.config.cacheStrategy === 'aggressive' ? 3600 : 
             this.config.cacheStrategy === 'balanced' ? 1800 : 300,
        priority: 3,
        compression: true,
        edgeCache: false
      },
      {
        name: 'dynamic-content',
        pattern: /\/(profile|dashboard|settings)/,
        ttl: 60,
        priority: 4,
        compression: false,
        edgeCache: false
      }
    ]

    this.cacheStrategies = strategies
  }

  // 检测用户地理位置
  private async detectUserLocation(): Promise<void> {
    try {
      // 尝试使用地理位置API
      if ('geolocation' in navigator) {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            timeout: 5000,
            maximumAge: 300000 // 5分钟缓存
          })
        })

        // 通过IP获取详细位置信息
        const response = await fetch('/api/geo/location', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          })
        })

        if (response.ok) {
          this.userLocation = await response.json()
        }
      }

      // 备用方案：通过IP检测
      if (!this.userLocation) {
        const response = await fetch('/api/geo/ip')
        if (response.ok) {
          this.userLocation = await response.json()
        }
      }

      if (this.userLocation) {
        this.selectOptimalEdgeNode()
      }
    } catch (error) {
      console.warn('地理位置检测失败:', error)
      // 使用默认位置
      this.userLocation = {
        country: 'US',
        region: 'California',
        city: 'San Francisco',
        latitude: 37.7749,
        longitude: -122.4194,
        timezone: 'America/Los_Angeles',
        isp: 'Unknown'
      }
    }
  }

  // 初始化边缘节点
  private initializeEdgeNodes(): void {
    const nodes: EdgeNode[] = [
      {
        id: 'us-east-1',
        location: {
          country: 'US',
          region: 'Virginia',
          city: 'Ashburn',
          latitude: 39.0458,
          longitude: -77.5017,
          timezone: 'America/New_York',
          isp: 'AWS'
        },
        latency: 0,
        bandwidth: 1000,
        load: 0.3,
        available: true
      },
      {
        id: 'eu-west-1',
        location: {
          country: 'IE',
          region: 'Dublin',
          city: 'Dublin',
          latitude: 53.3498,
          longitude: -6.2603,
          timezone: 'Europe/Dublin',
          isp: 'AWS'
        },
        latency: 0,
        bandwidth: 1000,
        load: 0.4,
        available: true
      },
      {
        id: 'ap-southeast-1',
        location: {
          country: 'SG',
          region: 'Singapore',
          city: 'Singapore',
          latitude: 1.3521,
          longitude: 103.8198,
          timezone: 'Asia/Singapore',
          isp: 'AWS'
        },
        latency: 0,
        bandwidth: 1000,
        load: 0.2,
        available: true
      }
    ]

    nodes.forEach(node => {
      this.edgeNodes.set(node.id, node)
    })

    // 测量到各节点的延迟
    this.measureEdgeLatencies()
  }

  // 测量边缘节点延迟
  private async measureEdgeLatencies(): Promise<void> {
    const promises = Array.from(this.edgeNodes.values()).map(async (node) => {
      try {
        const startTime = performance.now()
        const response = await fetch(`https://${node.id}.example.com/ping`, {
          method: 'HEAD',
          cache: 'no-cache'
        })
        const endTime = performance.now()
        
        if (response.ok) {
          node.latency = endTime - startTime
        } else {
          node.available = false
        }
      } catch (error) {
        node.available = false
        node.latency = Infinity
      }
    })

    await Promise.all(promises)
  }

  // 选择最优边缘节点
  private selectOptimalEdgeNode(): void {
    if (!this.userLocation) return

    let bestNode: EdgeNode | null = null
    let bestScore = Infinity

    for (const node of this.edgeNodes.values()) {
      if (!node.available) continue

      // 计算地理距离
      const distance = this.calculateDistance(
        this.userLocation.latitude,
        this.userLocation.longitude,
        node.location.latitude,
        node.location.longitude
      )

      // 综合评分：距离 + 延迟 + 负载
      const score = distance * 0.4 + node.latency * 0.4 + node.load * 100 * 0.2

      if (score < bestScore) {
        bestScore = score
        bestNode = node
      }
    }

    this.optimalEdgeNode = bestNode
    
    if (bestNode) {
      console.log(`🎯 选择最优边缘节点: ${bestNode.id} (延迟: ${bestNode.latency.toFixed(2)}ms)`)
    }
  }

  // 计算地理距离（哈弗辛公式）
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371 // 地球半径（公里）
    const dLat = this.toRadians(lat2 - lat1)
    const dLon = this.toRadians(lon2 - lon1)
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2)
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  // Service Worker集成
  private setupServiceWorkerIntegration(): void {
    if (!this.config.enableServiceWorkerCaching) return

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(registration => {
        // 向Service Worker发送缓存策略
        registration.active?.postMessage({
          type: 'EDGE_CACHE_CONFIG',
          strategies: this.cacheStrategies,
          edgeNode: this.optimalEdgeNode
        })
      })
    }
  }

  // 优化请求
  public async optimizeRequest(url: string, options: RequestInit = {}): Promise<Response> {
    const strategy = this.getCacheStrategy(url)
    
    if (!strategy) {
      return fetch(url, options)
    }

    // 检查本地缓存
    if (this.config.enableServiceWorkerCaching) {
      const cachedResponse = await this.checkServiceWorkerCache(url)
      if (cachedResponse) {
        this.updateMetrics('cache-hit')
        return cachedResponse
      }
    }

    // 使用最优边缘节点
    const optimizedUrl = this.getOptimizedUrl(url)
    const optimizedOptions = this.getOptimizedOptions(options, strategy)

    try {
      const response = await fetch(optimizedUrl, optimizedOptions)
      
      if (response.ok) {
        this.updateMetrics('cache-miss')
        
        // 缓存响应
        if (strategy.edgeCache && this.config.enableEdgeCaching) {
          this.cacheResponse(url, response.clone(), strategy)
        }
      } else {
        this.updateMetrics('error')
      }

      return response
    } catch (error) {
      this.updateMetrics('error')
      throw error
    }
  }

  // 获取缓存策略
  private getCacheStrategy(url: string): CacheStrategy | null {
    return this.cacheStrategies.find(strategy => strategy.pattern.test(url)) || null
  }

  // 检查Service Worker缓存
  private async checkServiceWorkerCache(url: string): Promise<Response | null> {
    if (!('serviceWorker' in navigator)) return null

    try {
      const cache = await caches.open('edge-cache-v1')
      return await cache.match(url)
    } catch (error) {
      return null
    }
  }

  // 获取优化后的URL
  private getOptimizedUrl(url: string): string {
    if (!this.optimalEdgeNode || !this.config.enableCDNOptimization) {
      return url
    }

    // 如果是相对URL，转换为绝对URL
    if (url.startsWith('/')) {
      const baseUrl = `https://${this.optimalEdgeNode.id}.cdn.example.com`
      return baseUrl + url
    }

    return url
  }

  // 获取优化后的请求选项
  private getOptimizedOptions(options: RequestInit, strategy: CacheStrategy): RequestInit {
    const optimizedOptions = { ...options }

    // 添加缓存头
    optimizedOptions.headers = {
      ...optimizedOptions.headers,
      'Cache-Control': `max-age=${strategy.ttl}`,
      'Edge-Cache': strategy.edgeCache ? 'true' : 'false'
    }

    // 添加压缩支持
    if (strategy.compression) {
      optimizedOptions.headers = {
        ...optimizedOptions.headers,
        'Accept-Encoding': 'gzip, deflate, br'
      }
    }

    return optimizedOptions
  }

  // 缓存响应
  private async cacheResponse(url: string, response: Response, strategy: CacheStrategy): Promise<void> {
    if (!('serviceWorker' in navigator)) return

    try {
      const cache = await caches.open('edge-cache-v1')
      
      // 添加缓存元数据
      const responseWithMetadata = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: {
          ...response.headers,
          'X-Cache-Strategy': strategy.name,
          'X-Cache-TTL': strategy.ttl.toString(),
          'X-Cache-Timestamp': Date.now().toString()
        }
      })

      await cache.put(url, responseWithMetadata)
    } catch (error) {
      console.warn('缓存响应失败:', error)
    }
  }

  // 更新指标
  private updateMetrics(type: 'cache-hit' | 'cache-miss' | 'error'): void {
    this.metrics.requestsServed++

    switch (type) {
      case 'cache-hit':
        this.metrics.cacheHitRate = 
          (this.metrics.cacheHitRate * (this.metrics.requestsServed - 1) + 1) / this.metrics.requestsServed
        break
      case 'cache-miss':
        this.metrics.cacheHitRate = 
          (this.metrics.cacheHitRate * (this.metrics.requestsServed - 1)) / this.metrics.requestsServed
        break
      case 'error':
        this.metrics.errorRate = 
          (this.metrics.errorRate * (this.metrics.requestsServed - 1) + 1) / this.metrics.requestsServed
        break
    }
  }

  // 开始指标收集
  private startMetricsCollection(): void {
    setInterval(() => {
      this.collectPerformanceMetrics()
    }, 60000) // 每分钟收集一次
  }

  // 收集性能指标
  private collectPerformanceMetrics(): void {
    if (this.optimalEdgeNode) {
      // 更新平均延迟
      this.metrics.averageLatency = this.optimalEdgeNode.latency
    }

    // 估算带宽节省
    const estimatedSavings = this.metrics.requestsServed * this.metrics.cacheHitRate * 0.1 // 假设每个请求节省100KB
    this.metrics.bandwidthSaved = estimatedSavings
  }

  // 公共方法
  public getMetrics(): EdgeMetrics {
    return { ...this.metrics }
  }

  public getOptimalEdgeNode(): EdgeNode | null {
    return this.optimalEdgeNode
  }

  public getUserLocation(): GeoLocation | null {
    return this.userLocation
  }

  public getCacheStrategies(): CacheStrategy[] {
    return [...this.cacheStrategies]
  }

  public async clearCache(): Promise<void> {
    if ('serviceWorker' in navigator) {
      const cache = await caches.open('edge-cache-v1')
      const keys = await cache.keys()
      await Promise.all(keys.map(key => cache.delete(key)))
      console.log('🗑️ 边缘缓存已清理')
    }
  }

  public updateConfig(newConfig: Partial<EdgeOptimizerConfig>): void {
    this.config = { ...this.config, ...newConfig }
    this.setupCacheStrategies()
    console.log('⚙️ 边缘优化配置已更新')
  }
}

// 全局边缘优化器实例
let edgeOptimizer: EdgeOptimizer | null = null

// 初始化边缘优化器
export function initEdgeOptimizer(config?: Partial<EdgeOptimizerConfig>): EdgeOptimizer {
  if (edgeOptimizer) {
    return edgeOptimizer
  }
  
  edgeOptimizer = new EdgeOptimizer(config)
  return edgeOptimizer
}

// 获取边缘优化器实例
export function getEdgeOptimizer(): EdgeOptimizer | null {
  return edgeOptimizer
}

// 优化的fetch函数
export async function optimizedFetch(url: string, options?: RequestInit): Promise<Response> {
  const optimizer = getEdgeOptimizer()
  if (optimizer) {
    return optimizer.optimizeRequest(url, options)
  }
  return fetch(url, options)
}

// 导出类型和类
export type { EdgeOptimizerConfig, CacheStrategy, GeoLocation, EdgeNode, EdgeMetrics }
export { EdgeOptimizer }
