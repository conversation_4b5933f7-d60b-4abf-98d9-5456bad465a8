"""
Redis缓存服务
"""

import json
import pickle
from typing import Any, Dict, List, Optional

import redis

from app.core.config import settings


class RedisService:
    """Redis缓存服务类"""

    def __init__(self):
        """初始化Redis连接"""
        self.redis_client = redis.from_url(
            settings.REDIS_URL,
            decode_responses=False,  # 使用字节模式以支持pickle
        )
        self.redis_text = redis.from_url(
            settings.REDIS_URL, decode_responses=True  # 文本模式用于JSON数据
        )

    def set(self, key: str, value: Any, expire: int = 3600) -> bool:
        """
        设置缓存值

        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间（秒），默认1小时

        Returns:
            是否设置成功
        """
        try:
            if isinstance(value, (dict, list)):
                # JSON可序列化的对象使用JSON
                serialized_value = json.dumps(value, ensure_ascii=False)
                return self.redis_text.setex(key, expire, serialized_value)
            else:
                # 其他对象使用pickle
                serialized_value = pickle.dumps(value)
                return self.redis_client.setex(key, expire, serialized_value)
        except Exception:
            return False

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存值

        Args:
            key: 缓存键
            default: 默认值

        Returns:
            缓存值或默认值
        """
        try:
            # 先尝试文本模式（JSON）
            value = self.redis_text.get(key)
            if value is not None:
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    pass

            # 再尝试字节模式（pickle）
            value = self.redis_client.get(key)
            if value is not None:
                try:
                    return pickle.loads(value)
                except (pickle.PickleError, TypeError):
                    pass

            return default
        except Exception:
            return default

    def delete(self, key: str) -> bool:
        """
        删除缓存

        Args:
            key: 缓存键

        Returns:
            是否删除成功
        """
        try:
            return bool(self.redis_client.delete(key))
        except Exception:
            return False

    def exists(self, key: str) -> bool:
        """
        检查键是否存在

        Args:
            key: 缓存键

        Returns:
            是否存在
        """
        try:
            return bool(self.redis_client.exists(key))
        except Exception:
            return False

    def expire(self, key: str, seconds: int) -> bool:
        """
        设置键的过期时间

        Args:
            key: 缓存键
            seconds: 过期秒数

        Returns:
            是否设置成功
        """
        try:
            return bool(self.redis_client.expire(key, seconds))
        except Exception:
            return False

    def get_ttl(self, key: str) -> int:
        """
        获取键的剩余生存时间

        Args:
            key: 缓存键

        Returns:
            剩余秒数，-1表示永不过期，-2表示不存在
        """
        try:
            return self.redis_client.ttl(key)
        except Exception:
            return -2

    def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """
        原子性增加计数

        Args:
            key: 缓存键
            amount: 增加量

        Returns:
            增加后的值
        """
        try:
            return self.redis_client.incr(key, amount)
        except Exception:
            return None

    def set_hash(self, key: str, mapping: Dict[str, Any], expire: int = 3600) -> bool:
        """
        设置哈希表

        Args:
            key: 哈希键
            mapping: 哈希映射
            expire: 过期时间

        Returns:
            是否设置成功
        """
        try:
            # 序列化哈希值
            serialized_mapping = {}
            for field, value in mapping.items():
                if isinstance(value, (dict, list)):
                    serialized_mapping[field] = json.dumps(value, ensure_ascii=False)
                else:
                    serialized_mapping[field] = str(value)

            self.redis_text.hset(key, mapping=serialized_mapping)
            if expire > 0:
                self.redis_text.expire(key, expire)
            return True
        except Exception:
            return False

    def get_hash(self, key: str, field: str = None) -> Any:
        """
        获取哈希值

        Args:
            key: 哈希键
            field: 字段名，如果为None则获取所有字段

        Returns:
            哈希值
        """
        try:
            if field:
                value = self.redis_text.hget(key, field)
                if value:
                    try:
                        return json.loads(value)
                    except json.JSONDecodeError:
                        return value
                return None
            else:
                values = self.redis_text.hgetall(key)
                result = {}
                for k, v in values.items():
                    try:
                        result[k] = json.loads(v)
                    except json.JSONDecodeError:
                        result[k] = v
                return result
        except Exception:
            return None if field else {}

    def add_to_list(self, key: str, value: Any, expire: int = 3600) -> bool:
        """
        添加到列表

        Args:
            key: 列表键
            value: 值
            expire: 过期时间

        Returns:
            是否添加成功
        """
        try:
            serialized_value = (
                json.dumps(value, ensure_ascii=False)
                if isinstance(value, (dict, list))
                else str(value)
            )
            result = self.redis_text.lpush(key, serialized_value)
            if expire > 0:
                self.redis_text.expire(key, expire)
            return result > 0
        except Exception:
            return False

    def get_list(self, key: str, start: int = 0, end: int = -1) -> List[Any]:
        """
        获取列表

        Args:
            key: 列表键
            start: 开始索引
            end: 结束索引

        Returns:
            列表值
        """
        try:
            values = self.redis_text.lrange(key, start, end)
            result = []
            for value in values:
                try:
                    result.append(json.loads(value))
                except json.JSONDecodeError:
                    result.append(value)
            return result
        except Exception:
            return []

    def clear_pattern(self, pattern: str) -> int:
        """
        清除匹配模式的所有键

        Args:
            pattern: 键名模式（支持*通配符）

        Returns:
            删除的键数量
        """
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception:
            return 0


# 创建全局Redis实例
redis_service = RedisService()


# 缓存装饰器
def cache(key_prefix: str, expire: int = 3600):
    """
    缓存装饰器

    Args:
        key_prefix: 缓存键前缀
        expire: 过期时间
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"

            # 尝试从缓存获取
            cached_result = redis_service.get(cache_key)
            if cached_result is not None:
                return cached_result

            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            redis_service.set(cache_key, result, expire)
            return result

        return wrapper

    return decorator
