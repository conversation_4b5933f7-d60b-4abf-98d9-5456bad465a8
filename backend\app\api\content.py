"""
内容管理API - 集成标准化错误处理和日志
"""

import json
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.auth import get_current_user
from app.core.database import get_db
from app.core.json_validator import (
    safe_parse_json,
    validate_metadata_json,
)  # 🔒 证据链: 导入JSON验证
from app.core.transaction_manager import transactional, safe_create

# 导入标准化错误处理和日志
from app.core.exceptions import ValidationException, create_http_exception
from app.core.logging import get_api_logger
from app.models import Content
from app.schemas import ContentCreate, ContentResponse

# 初始化日志
logger = get_api_logger()

router = APIRouter(prefix="/content", tags=["内容管理"])


@router.post("/", response_model=ContentResponse)
@transactional()  # 🔒 证据链: 使用事务装饰器确保数据一致性
async def create_content(
    content: ContentCreate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """创建新内容"""
    try:
        logger.info(
            "创建新内容",
            extra_data={
                "user_id": current_user["user_id"],
                "content_type": content.content_type,
                "project_id": content.project_id,
            },
        )

        # 验证输入
        if not content.title or not content.title.strip():
            raise ValidationException("标题不能为空", field="title")

        # 处理元数据
        metadata_json = None
        if content.metadata:
            metadata_json = json.dumps(content.metadata)

        db_content = Content(
            title=content.title,
            content_type=content.content_type,
            original_text=content.original_text,
            creator_id=current_user["user_id"],
            project_id=content.project_id,
            status="draft",
            content_metadata=metadata_json,
        )

        # 🔒 证据链: 使用安全创建函数，事务装饰器会自动处理提交和回滚
        db_content = safe_create(db, db_content)
        db.refresh(db_content)

        # 🔒 证据链: 使用安全的JSON解析，防止反序列化攻击
        response_metadata = {}
        if db_content.content_metadata:
            response_metadata = safe_parse_json(db_content.content_metadata)

        logger.info(
            "内容创建成功",
            extra_data={
                "content_id": db_content.id,
                "user_id": current_user["user_id"],
            },
        )

        return ContentResponse(
            id=db_content.id,
            title=db_content.title,
            content_type=db_content.content_type,
            original_text=db_content.original_text,
            rewritten_text=db_content.rewritten_text,
            creator_id=db_content.creator_id,
            project_id=db_content.project_id,
            status=db_content.status,
            compliance_score=db_content.compliance_score,
            compliance_issues=[],
            metadata=response_metadata,
            created_at=db_content.created_at,
            updated_at=db_content.updated_at,
        )

    except ValidationException as e:
        logger.warning(
            "创建内容验证失败",
            extra_data={"error": str(e), "user_id": current_user["user_id"]},
        )
        raise create_http_exception(e)
    except Exception as e:
        logger.error(
            "创建内容失败",
            extra_data={"error": str(e), "user_id": current_user["user_id"]},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建内容失败: {str(e)}",
        )


@router.get("/", response_model=List[ContentResponse])
async def list_content(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取内容列表"""
    logger.info(
        "获取内容列表",
        extra_data={
            "user_id": current_user["user_id"],
            "skip": skip,
            "limit": limit,
        },
    )

    contents = (
        db.query(Content)
        .filter(Content.creator_id == current_user["user_id"])
        .offset(skip)
        .limit(limit)
        .all()
    )

    result = []
    for content in contents:
        content_metadata = {}
        if content.content_metadata:
            content_metadata = safe_parse_json(content.content_metadata)

        result.append(
            ContentResponse(
                id=content.id,
                title=content.title,
                content_type=content.content_type,
                original_text=content.original_text,
                rewritten_text=content.rewritten_text,
                creator_id=content.creator_id,
                project_id=content.project_id,
                status=content.status,
                compliance_score=content.compliance_score,
                compliance_issues=[],
                metadata=content_metadata,
                created_at=content.created_at,
                updated_at=content.updated_at,
            )
        )

    return result


@router.get("/{content_id}", response_model=ContentResponse)
async def get_content(
    content_id: int,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取单个内容详情"""
    content = (
        db.query(Content)
        .filter(
            Content.id == content_id,
            Content.creator_id == current_user["user_id"],
        )
        .first()
    )

    if not content:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="内容不存在")
    # 🔒 证据链: 使用安全的JSON解析
    content_metadata = {}
    if content.content_metadata:
        content_metadata = safe_parse_json(content.content_metadata)

    return ContentResponse(
        id=content.id,
        title=content.title,
        content_type=content.content_type,
        original_text=content.original_text,
        rewritten_text=content.rewritten_text,
        creator_id=content.creator_id,
        project_id=content.project_id,
        status=content.status,
        compliance_score=content.compliance_score,
        compliance_issues=[],
        metadata=content_metadata,
        created_at=content.created_at,
        updated_at=content.updated_at,
    )
