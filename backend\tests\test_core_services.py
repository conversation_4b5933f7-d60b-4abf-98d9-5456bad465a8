"""
核心服务测试
"""

from unittest.mock import Mock, patch


from app.core.config import settings
from app.core.database import check_database_health, init_database
from app.core.exceptions import (
    AuthenticationException,
    BaseServiceException,
    ValidationException,
    format_error_response,
)


class TestConfig:
    """配置测试"""

    def test_settings_basic_config(self):
        """测试基础配置"""
        assert settings.PROJECT_NAME == "AI内容合规服务"
        assert settings.VERSION == "1.0.0"
        assert settings.API_V1_STR == "/api/v1"
        assert settings.HOST == "0.0.0.0"
        assert settings.PORT == 8000
        assert isinstance(settings.DEBUG, bool)

    def test_settings_database_config(self):
        """测试数据库配置"""
        assert settings.DATABASE_URL is not None
        assert "sqlite" in settings.DATABASE_URL.lower()
        assert isinstance(settings.DB_POOL_SIZE, int)
        assert isinstance(settings.DB_MAX_OVERFLOW, int)

    def test_settings_security_config(self):
        """测试安全配置"""
        assert settings.SECRET_KEY is not None
        assert len(settings.SECRET_KEY) > 10
        assert settings.ALGORITHM == "HS256"
        assert isinstance(settings.ACCESS_TOKEN_EXPIRE_MINUTES, int)

    def test_settings_cors_config(self):
        """测试CORS配置"""
        assert isinstance(settings.CORS_ORIGINS, list)
        assert len(settings.CORS_ORIGINS) > 0
        assert isinstance(settings.ALLOWED_HOSTS, list)


class TestDatabase:
    """数据库测试"""

    def test_check_database_health(self):
        """测试数据库健康检查"""
        result = check_database_health()
        assert isinstance(result, bool)

    @patch("app.core.database.Base.metadata.create_all")
    def test_init_database(self, mock_create_all):
        """测试数据库初始化"""
        init_database()
        mock_create_all.assert_called_once()


class TestExceptions:
    """异常处理测试"""

    def test_base_service_exception(self):
        """测试基础服务异常"""
        exception = BaseServiceException(message="Test error", status_code=400)

        assert exception.message == "Test error"
        assert exception.status_code == 400
        assert exception.timestamp is not None

        error_dict = exception.to_dict()
        assert "error" in error_dict
        assert error_dict["error"]["message"] == "Test error"

    def test_validation_exception(self):
        """测试验证异常"""
        exception = ValidationException(message="Validation failed", field="username")

        assert exception.message == "Validation failed"
        assert exception.status_code == 400
        assert exception.details["field"] == "username"

    def test_authentication_exception(self):
        """测试认证异常"""
        exception = AuthenticationException(message="Authentication failed")

        assert exception.message == "Authentication failed"
        assert exception.status_code == 401

    def test_format_error_response_base_exception(self):
        """测试格式化基础异常响应"""
        exception = BaseServiceException("Test error")
        response = format_error_response(exception)

        assert "error" in response
        assert response["error"]["message"] == "Test error"
        assert "timestamp" in response["error"]

    def test_format_error_response_generic_exception(self):
        """测试格式化通用异常响应"""
        exception = ValueError("Generic error")
        response = format_error_response(exception)

        assert "error" in response
        assert response["error"]["message"] == "Generic error"
        assert response["error"]["details"]["type"] == "ValueError"

    def test_format_error_response_with_traceback(self):
        """测试包含堆栈跟踪的错误响应"""
        exception = ValueError("Error with traceback")
        response = format_error_response(exception, include_traceback=True)

        assert "error" in response
        assert "traceback" in response["error"]

    def test_custom_exception(self):
        """测试自定义异常"""
        exception = BaseServiceException(message="Custom error", status_code=403)
        assert exception.status_code == 403

    def test_exception_with_details(self):
        """测试带细节的异常"""
        exception = ValidationException(message="Detail test", details={"field": "email"})
        assert "field" in exception.details

    def test_format_error_response_with_traceback(self):
        """测试包含堆栈跟踪的错误响应"""
        exception = ValueError("Error with traceback")
        response = format_error_response(exception, include_traceback=True)

        assert "error" in response
        assert "traceback" in response["error"]


class TestMiddleware:
    """中间件测试"""

    @patch("app.core.middleware.Request")
    def test_ip_whitelist_middleware_allowed_ip(self, mock_request):
        """测试IP白名单中间件 - 允许的IP"""
        from app.core.middleware import IPWhitelistMiddleware

        mock_request.client.host = "127.0.0.1"
        middleware = IPWhitelistMiddleware(Mock())

        # 这里需要更详细的测试，由于中间件的复杂性，
        # 我们先测试基本的实例化
        assert middleware is not None

    def test_middleware_configuration(self):
        """测试中间件配置"""
        # 测试中间件是否正确配置
        # 这里可以添加更多具体的中间件测试


class TestLogging:
    """日志测试"""

    def test_logging_configuration(self):
        """测试日志配置"""
        import logging

        # 测试日志级别配置
        logger = logging.getLogger("app")
        assert logger is not None

        # 测试日志格式
        # 这里可以添加更多日志相关的测试

    @patch("app.core.logging.logging")
    def test_log_request(self, mock_logging):
        """测试请求日志"""
        # 这里可以添加请求日志的测试


class TestScheduler:
    """调度器测试"""

    @patch("app.core.scheduler.BackgroundScheduler")
    def test_scheduler_start(self, mock_scheduler):
        """测试调度器启动"""
        from app.core.scheduler import start_scheduler

        start_scheduler()
        # 验证调度器是否被正确启动
        # 这里需要根据实际的调度器实现来测试

    @patch("app.core.scheduler.BackgroundScheduler")
    def test_scheduler_stop(self, mock_scheduler):
        """测试调度器停止"""
        from app.core.scheduler import stop_scheduler

        stop_scheduler()
        # 验证调度器是否被正确停止

    def test_schedule_daily_backup(self):
        """测试每日备份调度"""
        from app.core.scheduler import schedule_daily_backup

        # 测试备份调度配置
        schedule_daily_backup()
        # 这里可以验证备份任务是否被正确调度


class TestSecurity:
    """安全测试"""

    def test_password_hashing(self):
        """测试密码哈希"""
        # 这里可以添加密码哈希相关的测试

    def test_jwt_token_generation(self):
        """测试JWT令牌生成"""
        # 这里可以添加JWT令牌相关的测试

    def test_rate_limiting(self):
        """测试速率限制"""
        # 这里可以添加速率限制相关的测试


class TestCaching:
    """缓存测试"""

    def test_cache_configuration(self):
        """测试缓存配置"""
        assert settings.CACHE_DEFAULT_TTL > 0
        assert settings.CACHE_KEY_PREFIX is not None

    @patch("app.core.redis_manager.Redis")
    def test_redis_connection(self, mock_redis):
        """测试Redis连接"""
        # 这里可以添加Redis连接相关的测试
