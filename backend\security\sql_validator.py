"""
SQL查询安全验证器
确保所有SQL查询都使用参数化查询，防止SQL注入
"""

import re
from typing import List, Dict
from pathlib import Path


class SQLSecurityValidator:
    """SQL安全验证器"""

    def __init__(self):
        self.violations = []
        self.safe_patterns = [
            # 参数化查询模式
            r'cursor\.execute\(["\'].*\?.*["\'],\s*\(',
            r'cursor\.executemany\(["\'].*\?.*["\'],\s*\[',
            # 静态SQL（无变量）
            r'cursor\.execute\(["\'][^"\']*["\'](?:\s*\)|\s*$)',
        ]
        self.dangerous_patterns = [
            # f-string拼接
            r'cursor\.execute\(f["\'].*\{.*\}.*["\']',
            # 字符串格式化
            r'cursor\.execute\(["\'].*%.*["\'].*%',
            r'cursor\.execute\(["\'].*\.format\(',
            # 字符串拼接
            r'cursor\.execute\(["\'].*["\'].*\+',
        ]

    def validate_file(self, file_path: Path) -> List[Dict]:
        """验证单个文件的SQL安全性"""
        violations = []

        try:
            content = file_path.read_text(encoding="utf-8")
            lines = content.split("\n")

            for i, line in enumerate(lines, 1):
                if "cursor.execute" in line:
                    violation = self._check_line_security(line, file_path, i)
                    if violation:
                        violations.append(violation)

        except Exception as e:
            violations.append(
                {
                    "file": str(file_path),
                    "line": 0,
                    "type": "file_error",
                    "message": f"无法读取文件: {e}",
                }
            )

        return violations

    def _check_line_security(self, line: str, file_path: Path, line_num: int) -> Dict:
        """检查单行SQL的安全性"""
        line = line.strip()

        # 检查危险模式
        for pattern in self.dangerous_patterns:
            if re.search(pattern, line):
                return {
                    "file": str(file_path),
                    "line": line_num,
                    "type": "sql_injection_risk",
                    "message": f"检测到潜在SQL注入风险: {line}",
                    "pattern": pattern,
                }

        # 检查是否使用了安全模式
        is_safe = False
        for pattern in self.safe_patterns:
            if re.search(pattern, line):
                is_safe = True
                break

        # 如果包含变量但不是安全模式，标记为可疑
        if not is_safe and ("{" in line or "%" in line or "+" in line):
            return {
                "file": str(file_path),
                "line": line_num,
                "type": "suspicious_sql",
                "message": f"可疑的SQL查询，请确认是否安全: {line}",
            }

        return None

    def validate_project(self, project_root: Path) -> Dict:
        """验证整个项目的SQL安全性"""
        all_violations = []

        # 扫描所有Python文件
        for py_file in project_root.rglob("*.py"):
            if "venv" in str(py_file) or "__pycache__" in str(py_file):
                continue

            violations = self.validate_file(py_file)
            all_violations.extend(violations)

        # 生成报告
        report = {
            "total_violations": len(all_violations),
            "violations": all_violations,
            "summary": self._generate_summary(all_violations),
        }

        return report

    def _generate_summary(self, violations: List[Dict]) -> Dict:
        """生成违规摘要"""
        summary = {
            "sql_injection_risks": 0,
            "suspicious_queries": 0,
            "file_errors": 0,
            "affected_files": set(),
        }

        for violation in violations:
            summary["affected_files"].add(violation["file"])

            if violation["type"] == "sql_injection_risk":
                summary["sql_injection_risks"] += 1
            elif violation["type"] == "suspicious_sql":
                summary["suspicious_queries"] += 1
            elif violation["type"] == "file_error":
                summary["file_errors"] += 1

        summary["affected_files"] = len(summary["affected_files"])
        return summary


def validate_sql_security(project_root: str) -> Dict:
    """验证项目SQL安全性的主函数"""
    validator = SQLSecurityValidator()
    return validator.validate_project(Path(project_root))


# 预定义的安全SQL模板
SAFE_SQL_TEMPLATES = {
    # 用户管理
    "select_users": "SELECT id, username, email, status, role, created_at FROM users ORDER BY id",
    "select_user_by_id": "SELECT id, username, email, status, role, created_at FROM users WHERE id = ?",
    "insert_user": "INSERT INTO users (username, email, status, role) VALUES (?, ?, ?, ?)",
    "update_user_username": "UPDATE users SET username = ? WHERE id = ?",
    "update_user_email": "UPDATE users SET email = ? WHERE id = ?",
    "update_user_status": "UPDATE users SET status = ? WHERE id = ?",
    "update_user_username_email": "UPDATE users SET username = ?, email = ? WHERE id = ?",
    "update_user_username_status": "UPDATE users SET username = ?, status = ? WHERE id = ?",
    "update_user_email_status": "UPDATE users SET email = ?, status = ? WHERE id = ?",
    "update_user_all": "UPDATE users SET username = ?, email = ?, status = ? WHERE id = ?",
    "delete_user": "DELETE FROM users WHERE id = ?",
    "count_users": "SELECT COUNT(*) FROM users",
    "count_active_users": "SELECT COUNT(*) FROM users WHERE status = 'active'",
    # 项目管理
    "select_projects": "SELECT id, name, description, status, type, created_at FROM projects ORDER BY id",
    "select_project_by_id": "SELECT id, name, description, status, type, created_at FROM projects WHERE id = ?",
    "insert_project": "INSERT INTO projects (name, description, status, type) VALUES (?, ?, ?, ?)",
    "update_project_name": "UPDATE projects SET name = ? WHERE id = ?",
    "update_project_description": "UPDATE projects SET description = ? WHERE id = ?",
    "update_project_status": "UPDATE projects SET status = ? WHERE id = ?",
    "update_project_name_description": "UPDATE projects SET name = ?, description = ? WHERE id = ?",
    "update_project_name_status": "UPDATE projects SET name = ?, status = ? WHERE id = ?",
    "update_project_description_status": "UPDATE projects SET description = ?, status = ? WHERE id = ?",
    "update_project_all": "UPDATE projects SET name = ?, description = ?, status = ? WHERE id = ?",
    "delete_project": "DELETE FROM projects WHERE id = ?",
    "count_projects": "SELECT COUNT(*) FROM projects",
    "count_active_projects": "SELECT COUNT(*) FROM projects WHERE status = 'active'",
    # 设置管理
    "select_setting": "SELECT value FROM settings WHERE key = ?",
    "insert_setting": "INSERT INTO settings (key, value) VALUES (?, ?)",
    "update_setting": "UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?",
    "delete_setting": "DELETE FROM settings WHERE key = ?",
}


def get_safe_sql(template_name: str) -> str:
    """获取安全的SQL模板"""
    if template_name not in SAFE_SQL_TEMPLATES:
        raise ValueError(f"未知的SQL模板: {template_name}")
    return SAFE_SQL_TEMPLATES[template_name]


if __name__ == "__main__":
    # 运行SQL安全验证
    import sys

    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = Path(__file__).parent.parent

    report = validate_sql_security(project_root)

    print("🔍 SQL安全验证报告")
    print("=" * 50)
    print(f"总违规数: {report['total_violations']}")
    print(f"SQL注入风险: {report['summary']['sql_injection_risks']}")
    print(f"可疑查询: {report['summary']['suspicious_queries']}")
    print(f"受影响文件: {report['summary']['affected_files']}")

    if report["violations"]:
        print("\n详细违规信息:")
        for violation in report["violations"]:
            print(f"  📁 {violation['file']}:{violation['line']}")
            print(f"     {violation['type']}: {violation['message']}")
    else:
        print("\n✅ 未发现SQL安全问题")
