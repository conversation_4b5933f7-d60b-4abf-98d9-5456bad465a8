"""
内容安全检测服务 - 基于开源项目detoxify和nudenet
detoxify GitHub: https://github.com/unitaryai/detoxify
nudenet GitHub: https://github.com/notAI-tech/NudeNet
用途: AI生成内容的安全检测，包括文本毒性和图像不当内容检测
"""

import os
from datetime import datetime
from typing import Any, Dict, List


class ContentSafetyService:
    """基于detoxify和nudenet的内容安全检测服务"""

    def __init__(self):
        """初始化内容安全检测服务"""
        self.text_models_available = False
        self.image_models_available = False

        # 安全阈值配置
        self.safety_thresholds = {
            "toxicity": 0.8,
            "severe_toxicity": 0.7,
            "obscene": 0.8,
            "threat": 0.8,
            "insult": 0.8,
            "identity_attack": 0.8,
        }

        # 不当内容类别
        self.unsafe_image_classes = [
            "EXPOSED_ANUS",
            "EXPOSED_BREAST_F",
            "EXPOSED_GENITALIA_F",
            "EXPOSED_GENITALIA_M",
            "EXPOSED_BUTTOCKS",
            "EXPOSED_BELLY",
        ]

    async def check_dependencies(self) -> Dict[str, Any]:
        """检查依赖模型是否可用"""
        dependencies = {}

        # 检查detoxify
        try:
            import detoxify

            self.text_models_available = True
            dependencies["detoxify"] = {
                "available": True,
                "version": (
                    detoxify.__version__
                    if hasattr(detoxify, "__version__")
                    else "unknown"
                ),
            }
        except ImportError:
            dependencies["detoxify"] = {
                "available": False,
                "error": "detoxify not installed",
            }

        # 检查nudenet
        try:
            pass

            self.image_models_available = True
            dependencies["nudenet"] = {
                "available": True,
                "version": "2.0+",  # NudeNet版本
            }
        except ImportError:
            dependencies["nudenet"] = {
                "available": False,
                "error": "nudenet not installed",
            }

        # 检查其他依赖
        other_deps = ["PIL", "torch", "transformers"]
        for dep in other_deps:
            try:
                __import__(dep)
                dependencies[dep] = {"available": True}
            except ImportError:
                dependencies[dep] = {
                    "available": False,
                    "error": f"{dep} not installed",
                }

        return {
            "dependencies": dependencies,
            "text_detection_ready": self.text_models_available,
            "image_detection_ready": self.image_models_available,
            "timestamp": datetime.now().isoformat(),
        }

    async def check_text_safety_mock(self, text: str) -> Dict[str, Any]:
        """
        文本安全检测（模拟实现，实际需要集成detoxify）

        Args:
            text: 待检测文本

        Returns:
            Dict[str, Any]: 检测结果
        """
        try:
            # 简单的关键词检测（实际应使用detoxify模型）
            harmful_keywords = [
                "暴力",
                "色情",
                "赌博",
                "毒品",
                "仇恨",
                "violence",
                "porn",
                "gambling",
                "drugs",
                "hate",
            ]

            # 模拟检测分数
            text_lower = text.lower()
            detected_keywords = [kw for kw in harmful_keywords if kw in text_lower]

            # 根据关键词计算风险分数
            if detected_keywords:
                base_score = min(0.9, 0.3 + len(detected_keywords) * 0.2)
            else:
                base_score = 0.1  # 基础安全分数

            scores = {
                "toxicity": base_score,
                "severe_toxicity": max(0, base_score - 0.2),
                "obscene": max(0, base_score - 0.1),
                "threat": max(0, base_score - 0.3),
                "insult": max(0, base_score - 0.2),
                "identity_attack": max(0, base_score - 0.4),
            }

            # 判断是否安全
            max_score = max(scores.values())
            is_safe = max_score < 0.8

            # 计算风险等级
            if max_score < 0.3:
                risk_level = "low"
            elif max_score < 0.6:
                risk_level = "medium"
            else:
                risk_level = "high"

            return {
                "success": True,
                "is_safe": is_safe,
                "risk_level": risk_level,
                "max_score": round(max_score, 3),
                "scores": {k: round(v, 3) for k, v in scores.items()},
                "detected_issues": detected_keywords,
                "text_length": len(text),
                "model": "mock_detoxify",
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"文本安全检测失败: {str(e)}",
                "text_length": len(text) if text else 0,
            }

    async def check_image_safety_mock(self, image_path: str) -> Dict[str, Any]:
        """
        图像安全检测（模拟实现，实际需要集成nudenet）

        Args:
            image_path: 图像文件路径

        Returns:
            Dict[str, Any]: 检测结果
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                return {
                    "success": False,
                    "error": f"图像文件不存在: {image_path}",
                }

            file_size = os.path.getsize(image_path)

            # 模拟检测结果（实际应使用nudenet模型）
            # 根据文件名或随机生成模拟结果
            filename = os.path.basename(image_path).lower()

            # 模拟检测到的对象
            mock_detections = []

            # 根据文件名判断（简化模拟）
            if any(word in filename for word in ["safe", "clean", "normal"]):
                # 安全图像
                mock_detections = [
                    {
                        "class": "FACE_F",
                        "score": 0.95,
                        "box": [100, 100, 200, 200],
                    },
                    {
                        "class": "ARMPITS_COVERED",
                        "score": 0.88,
                        "box": [150, 150, 250, 250],
                    },
                ]
                is_safe = True
                risk_level = "low"
            else:
                # 默认为相对安全，但可能有轻微风险
                mock_detections = [
                    {
                        "class": "BELLY_COVERED",
                        "score": 0.75,
                        "box": [80, 120, 180, 220],
                    }
                ]
                is_safe = True
                risk_level = "low"

            # 检查是否有不安全内容
            unsafe_detections = [
                d
                for d in mock_detections
                if d["class"] in self.unsafe_image_classes and d["score"] > 0.8
            ]

            if unsafe_detections:
                is_safe = False
                risk_level = "high"

            return {
                "success": True,
                "is_safe": is_safe,
                "risk_level": risk_level,
                "detections": mock_detections,
                "unsafe_detections": unsafe_detections,
                "detection_count": len(mock_detections),
                "file_size": file_size,
                "image_path": image_path,
                "model": "mock_nudenet",
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"图像安全检测失败: {str(e)}",
                "image_path": image_path,
            }

    async def comprehensive_content_check(
        self, text: str = None, image_path: str = None, video_path: str = None
    ) -> Dict[str, Any]:
        """
        综合内容安全检测

        Args:
            text: 文本内容
            image_path: 图像路径
            video_path: 视频路径

        Returns:
            Dict[str, Any]: 综合检测结果
        """
        results = {}
        overall_safe = True
        max_risk_level = "low"

        # 文本检测
        if text:
            text_result = await self.check_text_safety_mock(text)
            results["text_safety"] = text_result

            if text_result["success"] and not text_result["is_safe"]:
                overall_safe = False
                if text_result["risk_level"] == "high":
                    max_risk_level = "high"
                elif text_result["risk_level"] == "medium" and max_risk_level != "high":
                    max_risk_level = "medium"

        # 图像检测
        if image_path:
            image_result = await self.check_image_safety_mock(image_path)
            results["image_safety"] = image_result

            if image_result["success"] and not image_result["is_safe"]:
                overall_safe = False
                if image_result["risk_level"] == "high":
                    max_risk_level = "high"
                elif (
                    image_result["risk_level"] == "medium" and max_risk_level != "high"
                ):
                    max_risk_level = "medium"

        # 视频检测（提取关键帧进行图像检测）
        if video_path:
            video_result = await self.check_video_safety_mock(video_path)
            results["video_safety"] = video_result

            if video_result["success"] and not video_result["is_safe"]:
                overall_safe = False
                if video_result["risk_level"] == "high":
                    max_risk_level = "high"
                elif (
                    video_result["risk_level"] == "medium" and max_risk_level != "high"
                ):
                    max_risk_level = "medium"

        return {
            "success": True,
            "overall_safe": overall_safe,
            "risk_level": max_risk_level,
            "checks_performed": list(results.keys()),
            "detailed_results": results,
            "timestamp": datetime.now().isoformat(),
        }

    async def check_video_safety_mock(self, video_path: str) -> Dict[str, Any]:
        """
        视频安全检测（模拟实现）

        Args:
            video_path: 视频文件路径

        Returns:
            Dict[str, Any]: 检测结果
        """
        try:
            if not os.path.exists(video_path):
                return {
                    "success": False,
                    "error": f"视频文件不存在: {video_path}",
                }

            file_size = os.path.getsize(video_path)

            # 模拟视频安全检测
            # 实际实现需要提取关键帧并进行图像检测

            return {
                "success": True,
                "is_safe": True,
                "risk_level": "low",
                "frames_checked": 10,  # 模拟检查了10帧
                "unsafe_frames": 0,
                "file_size": file_size,
                "video_path": video_path,
                "model": "mock_video_safety",
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"视频安全检测失败: {str(e)}",
                "video_path": video_path,
            }

    async def batch_text_check(self, texts: List[str]) -> Dict[str, Any]:
        """
        批量文本安全检测

        Args:
            texts: 文本列表

        Returns:
            Dict[str, Any]: 批量检测结果
        """
        results = []
        safe_count = 0

        for i, text in enumerate(texts, 1):
            result = await self.check_text_safety_mock(text)
            results.append(result)

            if result["success"] and result["is_safe"]:
                safe_count += 1

        return {
            "success": True,
            "total_texts": len(texts),
            "safe_count": safe_count,
            "unsafe_count": len(texts) - safe_count,
            "safety_rate": (round(safe_count / len(texts) * 100, 2) if texts else 0),
            "results": results,
            "timestamp": datetime.now().isoformat(),
        }


# 全局内容安全检测服务实例
content_safety_service = ContentSafetyService()


# 快捷函数
async def check_text_safety(text: str) -> Dict[str, Any]:
    """快捷文本安全检测函数"""
    return await content_safety_service.check_text_safety_mock(text)


async def check_image_safety(image_path: str) -> Dict[str, Any]:
    """快捷图像安全检测函数"""
    return await content_safety_service.check_image_safety_mock(image_path)


async def comprehensive_safety_check(
    text: str = None, image_path: str = None, video_path: str = None
) -> Dict[str, Any]:
    """快捷综合安全检测函数"""
    return await content_safety_service.comprehensive_content_check(
        text, image_path, video_path
    )
