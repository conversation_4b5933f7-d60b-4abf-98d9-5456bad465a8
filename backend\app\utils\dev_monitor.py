"""
后端开发监控工具
提供API调用统计、性能监控、错误追踪等功能
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime
from functools import wraps
from typing import Any, Dict, List, Optional

import psutil
from fastapi import Request, Response


class APIMetrics:
    """API调用指标收集器"""

    def __init__(self):
        self.calls: List[Dict[str, Any]] = []
        self.errors: List[Dict[str, Any]] = []
        self.performance: List[Dict[str, Any]] = []

    def log_api_call(self, request: Request, response: Response, duration: float):
        """记录API调用"""
        call_data = {
            "method": request.method,
            "path": str(request.url.path),
            "query_params": dict(request.query_params),
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2),
            "timestamp": datetime.now().isoformat(),
            "client_ip": request.client.host if request.client else "unknown",
            "user_agent": request.headers.get("user-agent", "unknown"),
        }

        self.calls.append(call_data)

        # 只保留最近1000条记录
        if len(self.calls) > 1000:
            self.calls = self.calls[-1000:]

        # 记录性能指标
        self.performance.append(
            {
                "endpoint": f"{request.method} {request.url.path}",
                "duration_ms": round(duration * 1000, 2),
                "timestamp": datetime.now().isoformat(),
            }
        )

        if len(self.performance) > 500:
            self.performance = self.performance[-500:]

    def log_error(self, request: Request, error: Exception, duration: float = 0):
        """记录错误"""
        error_data = {
            "method": request.method,
            "path": str(request.url.path),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "duration_ms": round(duration * 1000, 2),
            "timestamp": datetime.now().isoformat(),
            "client_ip": request.client.host if request.client else "unknown",
        }

        self.errors.append(error_data)

        if len(self.errors) > 100:
            self.errors = self.errors[-100:]

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.calls:
            return {
                "total_calls": 0,
                "avg_response_time": 0,
                "error_rate": 0,
                "endpoints": {},
            }

        # 基础统计
        total_calls = len(self.calls)
        avg_response_time = (
            sum(call["duration_ms"] for call in self.calls) / total_calls
        )
        error_count = len(self.errors)
        error_rate = (error_count / total_calls) * 100 if total_calls > 0 else 0

        # 端点统计
        endpoints = {}
        for call in self.calls:
            endpoint = f"{call['method']} {call['path']}"
            if endpoint not in endpoints:
                endpoints[endpoint] = {
                    "count": 0,
                    "total_duration": 0,
                    "avg_duration": 0,
                    "status_codes": {},
                }

            endpoints[endpoint]["count"] += 1
            endpoints[endpoint]["total_duration"] += call["duration_ms"]

            status_code = call["status_code"]
            if status_code not in endpoints[endpoint]["status_codes"]:
                endpoints[endpoint]["status_codes"][status_code] = 0
            endpoints[endpoint]["status_codes"][status_code] += 1

        # 计算平均响应时间
        for endpoint_data in endpoints.values():
            endpoint_data["avg_duration"] = round(
                endpoint_data["total_duration"] / endpoint_data["count"], 2
            )

        return {
            "total_calls": total_calls,
            "avg_response_time": round(avg_response_time, 2),
            "error_rate": round(error_rate, 2),
            "error_count": error_count,
            "endpoints": endpoints,
            "recent_calls": self.calls[-10:],
            "recent_errors": self.errors[-5:],
        }


class SystemMonitor:
    """系统资源监控"""

    @staticmethod
    def get_system_info() -> Dict[str, Any]:
        """获取系统信息"""
        try:
            # CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()

            # 内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = round(memory.available / (1024**3), 2)  # GB
            memory_total = round(memory.total / (1024**3), 2)  # GB

            # 磁盘信息
            disk = psutil.disk_usage("/")
            disk_percent = round((disk.used / disk.total) * 100, 2)
            disk_free = round(disk.free / (1024**3), 2)  # GB

            # 进程信息
            process = psutil.Process()
            process_memory = round(process.memory_info().rss / (1024**2), 2)  # MB
            process_cpu = process.cpu_percent()

            return {
                "cpu_percent": cpu_percent,
                "cpu_count": cpu_count,
                "memory_percent": memory_percent,
                "memory_available_gb": memory_available,
                "memory_total_gb": memory_total,
                "disk_percent": disk_percent,
                "disk_free_gb": disk_free,
                "process_memory_mb": process_memory,
                "process_cpu_percent": process_cpu,
                "python_version": sys.version,
                "timestamp": datetime.now().isoformat(),
            }
        except Exception as e:
            return {"error": str(e), "timestamp": datetime.now().isoformat()}


class DatabaseMonitor:
    """数据库监控"""

    def __init__(self, db_session=None):
        self.db_session = db_session
        self.query_logs: List[Dict[str, Any]] = []

    def log_query(self, query: str, duration: float, params: Optional[Dict] = None):
        """记录数据库查询"""
        query_data = {
            "query": query,
            "duration_ms": round(duration * 1000, 2),
            "params": params,
            "timestamp": datetime.now().isoformat(),
        }

        self.query_logs.append(query_data)

        if len(self.query_logs) > 100:
            self.query_logs = self.query_logs[-100:]

    def get_slow_queries(self, threshold_ms: float = 100) -> List[Dict[str, Any]]:
        """获取慢查询"""
        return [
            query for query in self.query_logs if query["duration_ms"] > threshold_ms
        ]


# 全局监控实例
api_metrics = APIMetrics()
system_monitor = SystemMonitor()
db_monitor = DatabaseMonitor()


def monitor_api_call(func):
    """API调用监控装饰器"""

    @wraps(func)
    async def wrapper(request: Request, *args, **kwargs):
        start_time = time.time()

        try:
            if asyncio.iscoroutinefunction(func):
                response = await func(request, *args, **kwargs)
            else:
                response = func(request, *args, **kwargs)

            duration = time.time() - start_time

            # 如果返回的是Response对象，直接使用；否则创建成功响应
            if hasattr(response, "status_code"):
                api_metrics.log_api_call(request, response, duration)
            else:
                # 创建一个模拟响应对象来记录日志
                class MockResponse:
                    status_code = 200

                api_metrics.log_api_call(request, MockResponse(), duration)

            return response

        except Exception as e:
            duration = time.time() - start_time
            api_metrics.log_error(request, e, duration)
            raise

    return wrapper


def monitor_database_query(func):
    """数据库查询监控装饰器"""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()

        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)

            duration = time.time() - start_time

            # 记录查询信息
            query_info = f"{func.__name__}() in {func.__module__}"
            db_monitor.log_query(query_info, duration, kwargs)

            return result

        except Exception as e:
            duration = time.time() - start_time
            db_monitor.log_query(f"ERROR: {func.__name__}", duration, {"error": str(e)})
            raise

    return wrapper


class DevToolsLogger:
    """开发工具日志记录器"""

    def __init__(self):
        self.logger = logging.getLogger("dev_tools")
        self.logger.setLevel(logging.INFO)

        # 创建格式化器
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # 创建文件处理器
        try:
            file_handler = logging.FileHandler("logs/dev_tools.log")
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        except:
            pass  # 如果无法创建文件处理器，只使用控制台

    def log_api_stats(self):
        """记录API统计信息"""
        stats = api_metrics.get_stats()
        self.logger.info(f"API Stats: {json.dumps(stats, indent=2)}")

    def log_system_info(self):
        """记录系统信息"""
        info = system_monitor.get_system_info()
        self.logger.info(f"System Info: {json.dumps(info, indent=2)}")

    def log_slow_queries(self, threshold_ms: float = 100):
        """记录慢查询"""
        slow_queries = db_monitor.get_slow_queries(threshold_ms)
        if slow_queries:
            self.logger.warning(f"Slow Queries: {json.dumps(slow_queries, indent=2)}")


# 全局日志记录器
dev_logger = DevToolsLogger()


def export_monitoring_data() -> Dict[str, Any]:
    """导出所有监控数据"""
    return {
        "api_metrics": api_metrics.get_stats(),
        "system_info": system_monitor.get_system_info(),
        "slow_queries": db_monitor.get_slow_queries(),
        "export_timestamp": datetime.now().isoformat(),
    }


def print_monitoring_summary():
    """打印监控摘要到控制台"""
    stats = api_metrics.get_stats()
    system_info = system_monitor.get_system_info()

    print("\n" + "=" * 50)
    print("🛠️  开发监控摘要")
    print("=" * 50)
    print(f"📊 API调用总数: {stats['total_calls']}")
    print(f"⏱️  平均响应时间: {stats['avg_response_time']}ms")
    print(f"❌ 错误率: {stats['error_rate']}%")
    print(f"💾 CPU使用率: {system_info.get('cpu_percent', 'N/A')}%")
    print(f"🧠 内存使用率: {system_info.get('memory_percent', 'N/A')}%")
    print(f"🔧 进程内存: {system_info.get('process_memory_mb', 'N/A')}MB")
    print("=" * 50)

    # 显示最活跃的端点
    if stats["endpoints"]:
        print("\n📈 最活跃的API端点:")
        sorted_endpoints = sorted(
            stats["endpoints"].items(), key=lambda x: x[1]["count"], reverse=True
        )
        for endpoint, data in sorted_endpoints[:5]:
            print(f"  {endpoint}: {data['count']}次调用, 平均{data['avg_duration']}ms")

    # 显示最近错误
    if stats["recent_errors"]:
        print("\n🚨 最近错误:")
        for error in stats["recent_errors"][-3:]:
            print(f"  {error['error_type']}: {error['error_message']}")

    print("\n")


# 定期监控任务
async def periodic_monitoring():
    """定期监控任务"""
    while True:
        try:
            # 每5分钟记录一次系统信息
            dev_logger.log_system_info()

            # 每10分钟记录一次API统计
            dev_logger.log_api_stats()

            # 检查慢查询
            dev_logger.log_slow_queries()

            await asyncio.sleep(300)  # 5分钟

        except Exception as e:
            print(f"监控任务错误: {e}")
            await asyncio.sleep(60)  # 发生错误时等待1分钟后重试
