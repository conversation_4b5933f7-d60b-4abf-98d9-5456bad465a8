"""
系统监控和指标收集服务
提供全面的系统性能监控和健康检查
"""

import asyncio
import json
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import psutil

from app.core.logging import get_service_logger, log_function_call


@dataclass
class SystemMetrics:
    """系统指标数据类"""

    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    process_count: int
    load_average: Optional[List[float]] = None


@dataclass
class ServiceMetrics:
    """服务指标数据类"""

    service_name: str
    timestamp: str
    status: str
    response_time_ms: float
    request_count: int
    error_count: int
    success_rate: float
    last_error: Optional[str] = None


@dataclass
class AIServiceMetrics:
    """AI服务指标数据类"""

    service_name: str
    timestamp: str
    model_loaded: bool
    inference_time_ms: float
    queue_size: int
    cache_hit_rate: float
    total_requests: int
    error_rate: float


class MetricsCollector:
    """指标收集器"""

    def __init__(self):
        self.logger = get_service_logger("metrics_collector")
        self.collection_interval = 60  # 60秒
        self.running = False
        self.metrics_history: List[SystemMetrics] = []
        self.service_metrics: Dict[str, List[ServiceMetrics]] = {}
        self.ai_metrics: Dict[str, List[AIServiceMetrics]] = {}

        # 性能基准
        self.performance_thresholds = {
            "cpu_warning": 70.0,
            "cpu_critical": 85.0,
            "memory_warning": 75.0,
            "memory_critical": 90.0,
            "disk_warning": 80.0,
            "disk_critical": 95.0,
            "response_time_warning": 5000.0,  # 5秒
            "response_time_critical": 10000.0,  # 10秒
            "error_rate_warning": 5.0,  # 5%
            "error_rate_critical": 10.0,  # 10%
        }

    @log_function_call
    async def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / (1024**3)
            memory_total_gb = memory.total / (1024**3)

            # 磁盘信息
            disk = psutil.disk_usage("/")
            disk_percent = disk.percent
            disk_used_gb = disk.used / (1024**3)
            disk_total_gb = disk.total / (1024**3)

            # 网络信息
            network = psutil.net_io_counters()
            network_bytes_sent = network.bytes_sent
            network_bytes_recv = network.bytes_recv

            # 进程数量
            process_count = len(psutil.pids())

            # 负载平均值(Unix系统)
            load_average = None
            try:
                load_average = list(psutil.getloadavg())
            except AttributeError:
                # Windows系统没有load average
                pass

            metrics = SystemMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_gb=round(memory_used_gb, 2),
                memory_total_gb=round(memory_total_gb, 2),
                disk_percent=disk_percent,
                disk_used_gb=round(disk_used_gb, 2),
                disk_total_gb=round(disk_total_gb, 2),
                network_bytes_sent=network_bytes_sent,
                network_bytes_recv=network_bytes_recv,
                process_count=process_count,
                load_average=load_average,
            )

            # 保存到历史记录
            self.metrics_history.append(metrics)

            # 保持最近1000条记录
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-1000:]

            self.logger.debug("系统指标收集完成", extra_data=asdict(metrics))

            return metrics

        except Exception as e:
            self.logger.error(
                "系统指标收集失败",
                extra_data={"error": str(e), "error_type": type(e).__name__},
            )
            raise

    async def check_service_health(
        self, service_name: str, health_check_func
    ) -> ServiceMetrics:
        """检查服务健康状态"""
        start_time = time.time()

        try:
            # 执行健康检查
            health_result = await health_check_func()
            response_time_ms = (time.time() - start_time) * 1000

            # 从历史记录中获取统计信息
            service_history = self.service_metrics.get(service_name, [])

            # 计算请求数和错误数
            recent_metrics = [
                m
                for m in service_history
                if datetime.fromisoformat(m.timestamp)
                > datetime.now() - timedelta(hours=1)
            ]

            request_count = len(recent_metrics) + 1
            error_count = len([m for m in recent_metrics if m.status == "error"])
            success_rate = ((request_count - error_count) / request_count) * 100

            healthy = health_result.get("healthy", False)
            status = "healthy" if healthy else "unhealthy"

            metrics = ServiceMetrics(
                service_name=service_name,
                timestamp=datetime.now().isoformat(),
                status=status,
                response_time_ms=round(response_time_ms, 2),
                request_count=request_count,
                error_count=error_count,
                success_rate=round(success_rate, 2),
                last_error=health_result.get("error"),
            )

            # 保存到历史记录
            if service_name not in self.service_metrics:
                self.service_metrics[service_name] = []

            self.service_metrics[service_name].append(metrics)

            # 保持最近100条记录
            if len(self.service_metrics[service_name]) > 100:
                self.service_metrics[service_name] = self.service_metrics[service_name][
                    -100:
                ]

            self.logger.debug(
                "服务健康检查完成",
                extra_data={
                    "service_name": service_name,
                    "status": status,
                    "response_time_ms": response_time_ms,
                },
            )

            return metrics

        except Exception as e:
            response_time_ms = (time.time() - start_time) * 1000

            metrics = ServiceMetrics(
                service_name=service_name,
                timestamp=datetime.now().isoformat(),
                status="error",
                response_time_ms=round(response_time_ms, 2),
                request_count=1,
                error_count=1,
                success_rate=0.0,
                last_error=str(e),
            )

            self.logger.error(
                "服务健康检查失败",
                extra_data={
                    "service_name": service_name,
                    "error": str(e),
                    "response_time_ms": response_time_ms,
                },
            )

            return metrics

    async def collect_ai_service_metrics(
        self, service_name: str, ai_service
    ) -> AIServiceMetrics:
        """收集AI服务指标"""
        try:
            # 检查模型加载状态
            has_model = hasattr(ai_service, "model")
            model_loaded = has_model and ai_service.model is not None

            # 模拟推理时间测试
            start_time = time.time()
            try:
                # 执行简单的测试推理
                if hasattr(ai_service, "health_check"):
                    await ai_service.health_check()
                elif hasattr(ai_service, "check_system_status"):
                    await ai_service.check_system_status()
            except Exception:
                pass
            inference_time_ms = (time.time() - start_time) * 1000

            # 获取队列大小(如果有)
            queue_size = getattr(ai_service, "queue_size", 0)

            # 缓存命中率(如果有)
            cache_hit_rate = 0.0
            if hasattr(ai_service, "cache_stats"):
                stats = ai_service.cache_stats
                total = stats.get("hits", 0) + stats.get("misses", 0)
                if total > 0:
                    cache_hit_rate = (stats.get("hits", 0) / total) * 100

            # 从历史记录计算统计信息
            ai_history = self.ai_metrics.get(service_name, [])
            recent_metrics = [
                m
                for m in ai_history
                if datetime.fromisoformat(m.timestamp)
                > datetime.now() - timedelta(hours=1)
            ]

            total_requests = len(recent_metrics) + 1
            error_rate = 0.0  # 这里需要根据实际错误统计

            metrics = AIServiceMetrics(
                service_name=service_name,
                timestamp=datetime.now().isoformat(),
                model_loaded=model_loaded,
                inference_time_ms=round(inference_time_ms, 2),
                queue_size=queue_size,
                cache_hit_rate=round(cache_hit_rate, 2),
                total_requests=total_requests,
                error_rate=round(error_rate, 2),
            )

            # 保存到历史记录
            if service_name not in self.ai_metrics:
                self.ai_metrics[service_name] = []

            self.ai_metrics[service_name].append(metrics)

            # 保持最近100条记录
            if len(self.ai_metrics[service_name]) > 100:
                self.ai_metrics[service_name] = self.ai_metrics[service_name][-100:]

            self.logger.debug(
                "AI服务指标收集完成",
                extra_data={
                    "service_name": service_name,
                    "model_loaded": model_loaded,
                    "inference_time_ms": inference_time_ms,
                },
            )

            return metrics

        except Exception as e:
            self.logger.error(
                "AI服务指标收集失败",
                extra_data={"service_name": service_name, "error": str(e)},
            )
            raise

    def analyze_system_health(self) -> Dict[str, Any]:
        """分析系统健康状态"""
        if not self.metrics_history:
            return {"status": "unknown", "message": "暂无数据"}

        latest_metrics = self.metrics_history[-1]

        # 健康状态评估
        issues = []
        warnings = []

        # CPU检查
        cpu_critical = self.performance_thresholds["cpu_critical"]
        cpu_warning = self.performance_thresholds["cpu_warning"]

        if latest_metrics.cpu_percent > cpu_critical:
            issues.append(f"CPU使用率过高: {latest_metrics.cpu_percent}%")
        elif latest_metrics.cpu_percent > cpu_warning:
            warnings.append(f"CPU使用率较高: {latest_metrics.cpu_percent}%")

        # 内存检查
        memory_critical = self.performance_thresholds["memory_critical"]
        memory_warning = self.performance_thresholds["memory_warning"]

        if latest_metrics.memory_percent > memory_critical:
            issues.append(f"内存使用率过高: {latest_metrics.memory_percent}%")
        elif latest_metrics.memory_percent > memory_warning:
            warnings.append(f"内存使用率较高: {latest_metrics.memory_percent}%")

        # 磁盘检查
        disk_critical = self.performance_thresholds["disk_critical"]
        disk_warning = self.performance_thresholds["disk_warning"]

        if latest_metrics.disk_percent > disk_critical:
            issues.append(f"磁盘使用率过高: {latest_metrics.disk_percent}%")
        elif latest_metrics.disk_percent > disk_warning:
            warnings.append(f"磁盘使用率较高: {latest_metrics.disk_percent}%")

        # 确定整体状态
        if issues:
            status = "critical"
        elif warnings:
            status = "warning"
        else:
            status = "healthy"

        return {
            "status": status,
            "latest_metrics": asdict(latest_metrics),
            "issues": issues,
            "warnings": warnings,
            "recommendations": self._get_recommendations(issues, warnings),
            "timestamp": datetime.now().isoformat(),
        }

    def _get_recommendations(self, issues: List[str], warnings: List[str]) -> List[str]:
        """获取性能优化建议"""
        recommendations = []

        for issue in issues:
            if "CPU" in issue:
                recommendations.append(
                    "建议检查CPU密集型进程，考虑优化算法或增加计算资源"
                )
            elif "内存" in issue:
                recommendations.append(
                    "建议释放不必要的内存，检查内存泄漏，或增加内存容量"
                )
            elif "磁盘" in issue:
                recommendations.append("建议清理磁盘空间，删除临时文件，或扩展存储容量")

        for warning in warnings:
            if "CPU" in warning:
                recommendations.append("监控CPU使用趋势，准备性能优化方案")
            elif "内存" in warning:
                recommendations.append("监控内存使用趋势，考虑缓存优化")
            elif "磁盘" in warning:
                recommendations.append("监控磁盘使用趋势，计划存储扩展")

        return recommendations

    async def start_monitoring(self):
        """开始监控"""
        self.running = True
        self.logger.info("系统监控开始")

        while self.running:
            try:
                # 收集系统指标
                await self.collect_system_metrics()

                # 等待下一次收集
                await asyncio.sleep(self.collection_interval)

            except Exception as e:
                self.logger.error("监控循环错误", extra_data={"error": str(e)})
                await asyncio.sleep(5)  # 错误后短暂等待

    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        self.logger.info("系统监控停止")

    async def export_metrics(self, file_path: str):
        """导出指标数据"""
        try:
            export_data = {
                "system_metrics": [asdict(m) for m in self.metrics_history],
                "service_metrics": {
                    name: [asdict(m) for m in metrics]
                    for name, metrics in self.service_metrics.items()
                },
                "ai_metrics": {
                    name: [asdict(m) for m in metrics]
                    for name, metrics in self.ai_metrics.items()
                },
                "export_timestamp": datetime.now().isoformat(),
            }

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            self.logger.info(
                "指标数据导出成功",
                extra_data={
                    "file_path": file_path,
                    "system_metrics_count": len(self.metrics_history),
                    "service_count": len(self.service_metrics),
                    "ai_service_count": len(self.ai_metrics),
                },
            )

        except Exception as e:
            self.logger.error(
                "指标数据导出失败",
                extra_data={"file_path": file_path, "error": str(e)},
            )
            raise


class SystemMonitoringService:
    """系统监控服务"""

    def __init__(self):
        self.logger = get_service_logger("system_monitoring")
        self.metrics_collector = MetricsCollector()
        self.monitoring_task: Optional[asyncio.Task] = None

        # 注册的服务健康检查函数
        self.health_checks: Dict[str, callable] = {}

        # AI服务实例
        self.ai_services: Dict[str, Any] = {}

    def register_health_check(self, service_name: str, health_check_func):
        """注册服务健康检查函数"""
        self.health_checks[service_name] = health_check_func
        self.logger.info("注册健康检查", extra_data={"service_name": service_name})

    def register_ai_service(self, service_name: str, ai_service):
        """注册AI服务实例"""
        self.ai_services[service_name] = ai_service
        self.logger.info("注册AI服务", extra_data={"service_name": service_name})

    @log_function_call
    async def start_monitoring(self):
        """启动监控"""
        if self.monitoring_task and not self.monitoring_task.done():
            self.logger.warning("监控已经在运行")
            return

        self.monitoring_task = asyncio.create_task(
            self.metrics_collector.start_monitoring()
        )

        self.logger.info("系统监控服务启动")

    def stop_monitoring(self):
        """停止监控"""
        if self.monitoring_task:
            self.monitoring_task.cancel()

        self.metrics_collector.stop_monitoring()
        self.logger.info("系统监控服务停止")

    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        # 系统健康分析
        system_health = self.metrics_collector.analyze_system_health()

        # 服务状态检查
        service_status = {}
        for service_name, health_check_func in self.health_checks.items():
            try:
                metrics = await self.metrics_collector.check_service_health(
                    service_name, health_check_func
                )
                service_status[service_name] = asdict(metrics)
            except Exception as e:
                service_status[service_name] = {
                    "status": "error",
                    "error": str(e),
                }

        # AI服务状态检查
        ai_service_status = {}
        for service_name, ai_service in self.ai_services.items():
            try:
                metrics = await self.metrics_collector.collect_ai_service_metrics(
                    service_name, ai_service
                )
                ai_service_status[service_name] = asdict(metrics)
            except Exception as e:
                ai_service_status[service_name] = {
                    "status": "error",
                    "error": str(e),
                }

        return {
            "system_health": system_health,
            "services": service_status,
            "ai_services": ai_service_status,
            "monitoring_active": self.metrics_collector.running,
            "timestamp": datetime.now().isoformat(),
        }

    async def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_collector.metrics_history:
            return {"status": "no_data"}

        # 最近1小时的数据
        hour_ago = datetime.now() - timedelta(hours=1)
        recent_metrics = [
            m
            for m in self.metrics_collector.metrics_history
            if datetime.fromisoformat(m.timestamp) > hour_ago
        ]

        if not recent_metrics:
            return {"status": "insufficient_data"}

        # 计算平均值
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_disk = sum(m.disk_percent for m in recent_metrics) / len(recent_metrics)

        # 峰值
        max_cpu = max(m.cpu_percent for m in recent_metrics)
        max_memory = max(m.memory_percent for m in recent_metrics)
        max_disk = max(m.disk_percent for m in recent_metrics)

        return {
            "time_period": "last_hour",
            "data_points": len(recent_metrics),
            "averages": {
                "cpu_percent": round(avg_cpu, 2),
                "memory_percent": round(avg_memory, 2),
                "disk_percent": round(avg_disk, 2),
            },
            "peaks": {
                "cpu_percent": max_cpu,
                "memory_percent": max_memory,
                "disk_percent": max_disk,
            },
            "latest": asdict(recent_metrics[-1]),
            "timestamp": datetime.now().isoformat(),
        }


# 全局监控服务实例
monitoring_service = SystemMonitoringService()


# 工具函数
async def init_monitoring_service() -> bool:
    """初始化监控服务"""
    try:
        await monitoring_service.start_monitoring()
        return True
    except Exception as e:
        monitoring_service.logger.error(
            "监控服务初始化失败", extra_data={"error": str(e)}
        )
        return False


async def get_system_health() -> Dict[str, Any]:
    """获取系统健康状态"""
    return await monitoring_service.get_system_status()


def register_service_health_check(service_name: str, health_check_func):
    """注册服务健康检查"""
    monitoring_service.register_health_check(service_name, health_check_func)


def register_ai_service_monitoring(service_name: str, ai_service):
    """注册AI服务监控"""
    monitoring_service.register_ai_service(service_name, ai_service)
