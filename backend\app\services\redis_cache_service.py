#!/usr/bin/env python3
"""
Redis缓存服务 - 生产级分布式缓存
"""

import json
import logging
import pickle
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional

from pydantic import BaseModel
from redis.asyncio import Redis

from ..core.redis_manager import redis_manager


class SerializationMethod(str, Enum):
    """序列化方法"""

    JSON = "json"
    PICKLE = "pickle"
    STRING = "string"


class CacheMetrics(BaseModel):
    """缓存指标"""

    hit_count: int = 0
    miss_count: int = 0
    set_count: int = 0
    delete_count: int = 0

    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0


class RedisCache:
    """Redis缓存服务"""

    def __init__(self, namespace: str = "default"):
        self.namespace = namespace
        self.logger = logging.getLogger(__name__)
        self.metrics = CacheMetrics()
        self._redis: Optional[Redis] = None

    async def _get_redis(self) -> Redis:
        """获取Redis客户端"""
        if self._redis is None:
            self._redis = await redis_manager.get_redis()
        return self._redis

    def _get_key(self, key: str) -> str:
        """生成带命名空间的键"""
        return redis_manager.get_cache_key(f"{self.namespace}:{key}")

    def _serialize(self, value: Any, method: SerializationMethod) -> str:
        """序列化数据"""
        try:
            if method == SerializationMethod.JSON:
                return json.dumps(value, ensure_ascii=False, default=str)
            elif method == SerializationMethod.PICKLE:
                return pickle.dumps(value).hex()
            elif method == SerializationMethod.STRING:
                return str(value)
            else:
                raise ValueError(f"不支持的序列化方法: {method}")
        except Exception as e:
            self.logger.error(f"序列化失败: {e}")
            raise

    def _deserialize(self, data: str, method: SerializationMethod) -> Any:
        """反序列化数据"""
        try:
            if method == SerializationMethod.JSON:
                return json.loads(data)
            elif method == SerializationMethod.PICKLE:
                return pickle.loads(bytes.fromhex(data))
            elif method == SerializationMethod.STRING:
                return data
            else:
                raise ValueError(f"不支持的反序列化方法: {method}")
        except Exception as e:
            self.logger.error(f"反序列化失败: {e}")
            raise

    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        method: SerializationMethod = SerializationMethod.JSON,
    ) -> bool:
        """设置缓存"""
        try:
            redis_client = await self._get_redis()
            cache_key = self._get_key(key)

            # 序列化数据
            serialized_value = self._serialize(value, method)

            # 存储序列化方法信息
            cache_data = {
                "data": serialized_value,
                "method": method.value,
                "timestamp": datetime.now().isoformat(),
            }

            # 设置缓存
            if ttl:
                await redis_client.setex(cache_key, ttl, json.dumps(cache_data))
            else:
                await redis_client.set(cache_key, json.dumps(cache_data))

            # 更新指标
            self.metrics.set_count += 1

            self.logger.debug(f"缓存设置成功: {key}")
            return True

        except Exception as e:
            self.logger.error(f"设置缓存失败: {e}")
            return False

    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            redis_client = await self._get_redis()
            cache_key = self._get_key(key)

            # 获取缓存数据
            cached_data = await redis_client.get(cache_key)

            if cached_data is None:
                self.metrics.miss_count += 1
                return None

            # 解析缓存数据
            cache_info = json.loads(cached_data)
            method = SerializationMethod(cache_info["method"])

            # 反序列化
            value = self._deserialize(cache_info["data"], method)

            # 更新指标
            self.metrics.hit_count += 1

            self.logger.debug(f"缓存命中: {key}")
            return value

        except Exception as e:
            self.logger.error(f"获取缓存失败: {e}")
            self.metrics.miss_count += 1
            return None

    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            redis_client = await self._get_redis()
            cache_key = self._get_key(key)

            result = await redis_client.delete(cache_key)

            if result:
                self.metrics.delete_count += 1
                self.logger.debug(f"缓存删除成功: {key}")

            return bool(result)

        except Exception as e:
            self.logger.error(f"删除缓存失败: {e}")
            return False

    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            redis_client = await self._get_redis()
            cache_key = self._get_key(key)
            return bool(await redis_client.exists(cache_key))
        except Exception as e:
            self.logger.error(f"检查缓存存在性失败: {e}")
            return False

    async def clear_namespace(self) -> int:
        """清空命名空间下的所有缓存"""
        try:
            redis_client = await self._get_redis()
            pattern = self._get_key("*")

            keys = await redis_client.keys(pattern)
            if keys:
                deleted = await redis_client.delete(*keys)
                self.logger.info(f"清空命名空间 {self.namespace}: {deleted} 个键")
                return deleted

            return 0

        except Exception as e:
            self.logger.error(f"清空命名空间失败: {e}")
            return 0

    async def get_metrics(self) -> CacheMetrics:
        """获取缓存指标"""
        return self.metrics

    async def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        try:
            redis_client = await self._get_redis()
            pattern = self._get_key("*")

            keys = await redis_client.keys(pattern)
            metrics = await self.get_metrics()

            return {
                "namespace": self.namespace,
                "total_keys": len(keys),
                "metrics": metrics.dict(),
                "hit_rate": f"{metrics.hit_rate:.2%}",
                "redis_status": "connected",
            }

        except Exception as e:
            self.logger.error(f"获取缓存信息失败: {e}")
            return {"error": str(e)}


# 预定义的缓存实例
user_cache = RedisCache("users")
content_cache = RedisCache("content")
video_cache = RedisCache("videos")
task_cache = RedisCache("tasks")


async def get_cache(namespace: str) -> RedisCache:
    """获取指定命名空间的缓存实例"""
    return RedisCache(namespace)


# 为了向后兼容，创建RedisService别名
RedisService = RedisCache
