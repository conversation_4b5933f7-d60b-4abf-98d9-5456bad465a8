#!/usr/bin/env python3
"""
开源项目集成管理API
提供开源项目集成的REST API接口
"""

from datetime import datetime
from typing import Any, Dict, List

from fastapi import APIRouter, BackgroundTasks, HTTPException
from pydantic import BaseModel

from app.services.open_source_integration_service import (
    OpenSourceIntegrationService,
    ProjectType,
)

router = APIRouter(prefix="/open-source", tags=["开源项目管理"])

# 初始化集成服务
integration_service = OpenSourceIntegrationService()


class ProjectInstallRequest(BaseModel):
    """项目安装请求"""

    project_names: List[str]
    force_reinstall: bool = False


class IntegrationReportResponse(BaseModel):
    """集成报告响应"""

    total_projects: int
    installed_count: int
    available_count: int
    error_count: int
    projects: List[Dict[str, Any]]
    generated_at: str


@router.get("/projects", response_model=List[Dict[str, Any]])
async def list_projects():
    """获取所有注册的开源项目列表"""
    try:
        projects = integration_service.get_all_projects()
        return [project.dict() for project in projects]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")


@router.get("/projects/{project_name}/status")
async def get_project_status(project_name: str):
    """获取指定项目的状态"""
    try:
        status = integration_service.check_project_status(project_name)
        return {
            "project_name": project_name,
            "status": status.value,
            "message": f"项目 {project_name} 状态: {status.value}",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检查项目状态失败: {str(e)}")


@router.post("/projects/install")
async def install_projects(
    request: ProjectInstallRequest, background_tasks: BackgroundTasks
):
    """安装指定的开源项目（支持批量）"""
    try:
        # 启动后台任务进行安装
        background_tasks.add_task(
            _install_projects_background,
            request.project_names,
            request.force_reinstall,
        )

        return {
            "message": f"开始安装项目: {', '.join(request.project_names)}",
            "projects": request.project_names,
            "status": "installing",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"安装项目失败: {str(e)}")


@router.post("/projects/{project_name}/install")
async def install_single_project(
    project_name: str,
    force_reinstall: bool = False,
    background_tasks: BackgroundTasks = None,
):
    """安装单个开源项目"""
    try:
        if background_tasks:
            background_tasks.add_task(
                _install_single_project_background,
                project_name,
                force_reinstall,
            )
            return {
                "message": f"开始安装项目: {project_name}",
                "project": project_name,
                "status": "installing",
            }
        else:
            # 同步安装
            result = integration_service.install_project(project_name, force_reinstall)
            return {
                "message": f"项目 {project_name} 安装完成",
                "project": project_name,
                "result": result,
                "status": "completed",
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"安装项目失败: {str(e)}")


@router.post("/projects/check-all")
async def check_all_projects():
    """检查所有项目的状态"""
    try:
        results = integration_service.check_all_projects()
        return {
            "total": len(results),
            "results": results,
            "timestamp": integration_service._get_current_time(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检查项目状态失败: {str(e)}")


@router.post("/projects/update-all")
async def update_all_projects(background_tasks: BackgroundTasks):
    """更新所有已安装的项目"""
    try:
        background_tasks.add_task(_update_all_projects_background)
        return {"message": "开始更新所有项目", "status": "updating"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新项目失败: {str(e)}")


@router.get("/report", response_model=IntegrationReportResponse)
async def get_integration_report():
    """生成开源项目集成报告"""
    try:
        report = integration_service.generate_integration_report()
        return IntegrationReportResponse(**report)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成报告失败: {str(e)}")


@router.get("/types")
async def get_project_types():
    """获取支持的项目类型"""
    return {
        "types": [
            {
                "value": ptype.value,
                "name": ptype.value.replace("_", " ").title(),
            }
            for ptype in ProjectType
        ]
    }


@router.post("/initialize")
async def initialize_core_projects(background_tasks: BackgroundTasks):
    """初始化所有核心项目（首次部署时使用）"""
    try:
        # 启动后台任务进行批量初始化
        background_tasks.add_task(_initialize_core_projects_background)

        return {"message": "开始初始化核心开源项目", "status": "initializing"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"初始化失败: {str(e)}")


@router.get("/health")
async def get_system_health():
    """获取系统健康状况报告"""
    try:
        health_report = integration_service.get_system_health_report()
        return health_report
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取健康报告失败: {str(e)}")


@router.get("/performance")
async def get_performance_stats():
    """获取性能统计信息"""
    try:
        perf_stats = integration_service.get_performance_stats()
        return perf_stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能统计失败: {str(e)}")


@router.post("/projects/check-all-parallel")
async def check_all_projects_parallel():
    """并行检查所有项目状态（高性能版本）"""
    try:
        results = integration_service.check_all_projects_parallel()
        return {
            "total": len(results),
            "results": {name: status.value for name, status in results.items()},
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"并行检查失败: {str(e)}")


@router.post("/projects/install-parallel")
async def install_projects_parallel(request: ProjectInstallRequest):
    """并行批量安装项目（高性能版本）"""
    try:
        results = integration_service.bulk_install_parallel(request.project_names)

        success_count = sum(1 for r in results if r.success)
        total_time = sum(r.execution_time for r in results)

        return {
            "message": f"并行安装完成: {success_count}/{len(results)} 成功",
            "total_execution_time": round(total_time, 2),
            "results": [r.dict() for r in results],
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"并行安装失败: {str(e)}")


@router.post("/cache/clear")
async def clear_cache():
    """清空缓存"""
    try:
        integration_service.cache.clear()
        return {
            "message": "缓存已清空",
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")


@router.get("/projects/{project_name}/status-with-cache")
async def get_project_status_with_cache_info(project_name: str):
    """获取项目状态（包含缓存信息）"""
    try:
        # 检查缓存
        cache_key = f"project_status_{project_name}"
        cached_status = integration_service.cache.get(cache_key)

        # 获取实际状态
        actual_status = integration_service.check_project_status(project_name)

        return {
            "project_name": project_name,
            "status": actual_status.value,
            "cache_info": {
                "cached": cached_status is not None,
                "cache_key": cache_key,
                "cache_entries_total": len(integration_service.cache.cache),
            },
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目状态失败: {str(e)}")


# 后台任务函数
async def _install_projects_background(project_names: List[str], force_reinstall: bool):
    """后台安装项目任务"""
    for project_name in project_names:
        try:
            integration_service.install_project(project_name, force_reinstall)
        except Exception as e:
            print(f"安装项目 {project_name} 失败: {e}")


async def _install_single_project_background(project_name: str, force_reinstall: bool):
    """后台安装单个项目任务"""
    try:
        integration_service.install_project(project_name, force_reinstall)
    except Exception as e:
        print(f"安装项目 {project_name} 失败: {e}")


async def _update_all_projects_background():
    """后台更新所有项目任务"""
    try:
        integration_service.update_all_projects()
    except Exception as e:
        print(f"更新项目失败: {e}")


async def _initialize_core_projects_background():
    """后台初始化核心项目任务"""
    try:
        # 获取所有注册的项目
        projects = integration_service.get_all_projects()
        core_project_names = [p.name for p in projects]

        # 批量安装
        for project_name in core_project_names:
            try:
                integration_service.install_project(project_name, force_reinstall=False)
            except Exception as e:
                print(f"初始化项目 {project_name} 失败: {e}")
    except Exception as e:
        print(f"初始化核心项目失败: {e}")
