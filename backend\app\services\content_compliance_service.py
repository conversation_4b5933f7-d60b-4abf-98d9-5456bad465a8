"""
内容合规服务
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, List

from app.schemas import ComplianceRequest, ComplianceResult, ContentType

# 配置日志
logger = logging.getLogger(__name__)


class ContentComplianceService:
    """内容合规检测服务"""

    def __init__(self):
        self.risk_keywords = self._load_risk_keywords()
        self.compliance_rules = self._load_compliance_rules()

    def _load_risk_keywords(self) -> Dict[str, List[str]]:
        """加载风险关键词库"""
        return {
            "政治敏感": ["政治敏感词1", "政治敏感词2"],
            "暴力内容": ["暴力", "血腥", "杀害", "攻击", "伤害"],
            "色情内容": ["色情", "裸体", "性行为"],
            "违法内容": ["毒品", "赌博", "诈骗", "洗钱"],
            "仇恨言论": ["歧视", "仇恨", "种族主义"],
            "虚假信息": ["假新闻", "虚假宣传", "误导信息"],
        }

    def _load_compliance_rules(self) -> Dict[str, Any]:
        """加载合规规则配置"""
        return {
            "text_rules": {
                "min_length": 1,
                "max_length": 10000,
                "forbidden_patterns": [
                    r"联系方式：\d+",
                    r"微信：\w+",
                    r"QQ：\d+",
                ],
            },
            "video_rules": {
                "max_duration": 300,  # 秒
                "min_duration": 1,
                "forbidden_content": ["violence", "adult", "illegal"],
            },
            "image_rules": {
                "max_size_mb": 10,
                "forbidden_content": ["adult", "violence", "illegal"],
            },
        }

    async def check_compliance(self, request: ComplianceRequest) -> ComplianceResult:
        """
        检查内容合规性

        Args:
            request: 合规检查请求

        Returns:
            ComplianceResult: 合规检查结果
        """
        try:
            logger.info(f"开始合规检查: {request.content_type}")

            # 根据内容类型选择检查方法
            if request.content_type == ContentType.TEXT:
                result = await self._check_text_compliance(request.content)
            elif request.content_type == ContentType.VIDEO:
                result = await self._check_video_compliance(request.content)
            elif request.content_type == ContentType.IMAGE:
                result = await self._check_image_compliance(request.content)
            else:
                raise ValueError(f"不支持的内容类型: {request.content_type}")

            logger.info(f"合规检查完成: {result.is_compliant}")
            return result

        except Exception as e:
            logger.error(f"合规检查失败: {e}")
            return ComplianceResult(
                is_compliant=False,
                confidence=0.0,
                risk_categories=["系统错误"],
                suggestions=["请联系技术支持"],
                details={"error": str(e)},
            )

    async def _check_text_compliance(self, content: str) -> ComplianceResult:
        """检查文本内容合规性"""
        risk_categories = []
        suggestions = []
        confidence = 1.0
        details = {}

        # 长度检查
        rules = self.compliance_rules["text_rules"]
        if len(content) < rules["min_length"]:
            risk_categories.append("内容过短")
            suggestions.append("请提供更详细的内容")

        if len(content) > rules["max_length"]:
            risk_categories.append("内容过长")
            suggestions.append("请缩短内容长度")

        # 关键词检查
        detected_keywords = {}
        for category, keywords in self.risk_keywords.items():
            found_keywords = []
            for keyword in keywords:
                if keyword in content:
                    found_keywords.append(keyword)

            if found_keywords:
                risk_categories.append(category)
                detected_keywords[category] = found_keywords
                confidence -= 0.2

        # 模式检查
        forbidden_patterns = rules["forbidden_patterns"]
        detected_patterns = []
        for pattern in forbidden_patterns:
            matches = re.findall(pattern, content)
            if matches:
                detected_patterns.extend(matches)
                risk_categories.append("包含联系方式")
                suggestions.append("请移除个人联系信息")
                confidence -= 0.1

        # 生成建议
        if detected_keywords:
            suggestions.append("请移除敏感词汇并使用更合适的表达")

        if not risk_categories:
            suggestions.append("内容符合平台规范")

        details = {
            "content_length": len(content),
            "detected_keywords": detected_keywords,
            "detected_patterns": detected_patterns,
            "scan_time": datetime.now().isoformat(),
        }

        return ComplianceResult(
            is_compliant=len(risk_categories) == 0,
            confidence=max(0.0, min(1.0, confidence)),
            risk_categories=risk_categories,
            suggestions=suggestions,
            details=details,
        )

    async def _check_video_compliance(self, content: str) -> ComplianceResult:
        """检查视频内容合规性"""
        # 模拟视频合规检查
        # 实际实现中需要调用视频分析API

        return ComplianceResult(
            is_compliant=True,
            confidence=0.85,
            risk_categories=[],
            suggestions=["视频内容通过初步检查"],
            details={
                "content_type": "video",
                "scan_method": "simulated",
                "scan_time": datetime.now().isoformat(),
            },
        )

    async def _check_image_compliance(self, content: str) -> ComplianceResult:
        """检查图片内容合规性"""
        # 模拟图片合规检查
        # 实际实现中需要调用图像识别API

        return ComplianceResult(
            is_compliant=True,
            confidence=0.90,
            risk_categories=[],
            suggestions=["图片内容通过检查"],
            details={
                "content_type": "image",
                "scan_method": "simulated",
                "scan_time": datetime.now().isoformat(),
            },
        )

    def get_compliance_statistics(self) -> Dict[str, Any]:
        """获取合规统计信息"""
        return {
            "risk_categories": list(self.risk_keywords.keys()),
            "total_keywords": sum(
                len(keywords) for keywords in self.risk_keywords.values()
            ),
            "supported_content_types": [
                ContentType.TEXT,
                ContentType.VIDEO,
                ContentType.IMAGE,
            ],
            "rules_version": "1.0.0",
            "last_updated": datetime.now().isoformat(),
        }


# 全局服务实例
content_compliance_service = ContentComplianceService()
