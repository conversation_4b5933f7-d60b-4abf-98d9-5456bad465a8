#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全中间件实现
基于安全审查结果实施的后端安全改进措施

功能:
1. 安全头部设置
2. CORS配置
3. 速率限制
4. 输入验证
5. 认证和授权
6. 日志记录
"""

import json
import time
import hashlib
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from functools import wraps
from pathlib import Path

from fastapi import Request, Response, HTTPException, status
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import RequestResponseEndpoint
from starlette.types import ASGIApp

# 配置日志
logger = logging.getLogger(__name__)

class SecurityMiddleware(BaseHTTPMiddleware):
    """
    安全中间件类
    实现各种安全功能
    """
    
    def __init__(
        self,
        app: ASGIApp,
        config_file: Optional[str] = None,
        enable_rate_limiting: bool = True,
        enable_security_headers: bool = True,
        enable_input_validation: bool = True
    ):
        """
        初始化安全中间件
        
        Args:
            app: ASGI应用
            config_file: 配置文件路径
            enable_rate_limiting: 是否启用速率限制
            enable_security_headers: 是否启用安全头部
            enable_input_validation: 是否启用输入验证
        """
        super().__init__(app)
        
        # 加载配置
        self.config = self._load_config(config_file)
        
        # 功能开关
        self.enable_rate_limiting = enable_rate_limiting
        self.enable_security_headers = enable_security_headers
        self.enable_input_validation = enable_input_validation
        
        # 速率限制存储
        self.rate_limit_storage = {}
        
        # 安全事件日志
        self.security_logger = self._setup_security_logger()
    
    def _load_config(self, config_file: Optional[str]) -> Dict:
        """
        加载安全配置
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            配置字典
        """
        default_config = {
            "security_headers": {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "X-XSS-Protection": "1; mode=block",
                "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
                "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
                "Referrer-Policy": "strict-origin-when-cross-origin"
            },
            "rate_limiting": {
                "enabled": True,
                "requests_per_minute": 60,
                "burst_limit": 10
            },
            "cors": {
                "allow_origins": ["https://localhost:3000"],
                "allow_methods": ["GET", "POST", "PUT", "DELETE"],
                "allow_headers": ["Authorization", "Content-Type"],
                "allow_credentials": True
            }
        }
        
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    default_config.update(file_config)
            except Exception as e:
                logger.warning(f"加载配置文件失败: {e}，使用默认配置")
        
        return default_config
    
    def _setup_security_logger(self) -> logging.Logger:
        """
        设置安全事件日志记录器
        
        Returns:
            日志记录器
        """
        security_logger = logging.getLogger('security')
        security_logger.setLevel(logging.INFO)
        
        # 创建文件处理器
        log_file = Path('logs/security.log')
        log_file.parent.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        security_logger.addHandler(file_handler)
        
        return security_logger
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        中间件主要处理逻辑
        
        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器
            
        Returns:
            HTTP响应
        """
        start_time = time.time()
        
        try:
            # 1. 速率限制检查
            if self.enable_rate_limiting:
                rate_limit_result = await self._check_rate_limit(request)
                if rate_limit_result:
                    return rate_limit_result
            
            # 2. 输入验证
            if self.enable_input_validation:
                validation_result = await self._validate_input(request)
                if validation_result:
                    return validation_result
            
            # 3. 安全日志记录
            await self._log_request(request)
            
            # 4. 处理请求
            response = await call_next(request)
            
            # 5. 设置安全头部
            if self.enable_security_headers:
                self._set_security_headers(response)
            
            # 6. 记录响应时间
            process_time = time.time() - start_time
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # 记录安全异常
            self.security_logger.error(
                f"安全中间件异常: {str(e)}, IP: {self._get_client_ip(request)}, "
                f"路径: {request.url.path}"
            )
            
            # 返回通用错误响应
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "内部服务器错误"}
            )
    
    async def _check_rate_limit(self, request: Request) -> Optional[Response]:
        """
        检查速率限制
        
        Args:
            request: HTTP请求
            
        Returns:
            如果超出限制则返回错误响应，否则返回None
        """
        if not self.config.get("rate_limiting", {}).get("enabled", True):
            return None
        
        client_ip = self._get_client_ip(request)
        current_time = time.time()
        window_size = 60  # 1分钟窗口
        max_requests = self.config.get("rate_limiting", {}).get("requests_per_minute", 60)
        
        # 清理过期记录
        if client_ip in self.rate_limit_storage:
            self.rate_limit_storage[client_ip] = [
                timestamp for timestamp in self.rate_limit_storage[client_ip]
                if current_time - timestamp < window_size
            ]
        else:
            self.rate_limit_storage[client_ip] = []
        
        # 检查请求数量
        if len(self.rate_limit_storage[client_ip]) >= max_requests:
            self.security_logger.warning(
                f"速率限制触发: IP {client_ip}, 路径: {request.url.path}"
            )
            
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "detail": "请求过于频繁，请稍后再试",
                    "retry_after": window_size
                },
                headers={"Retry-After": str(window_size)}
            )
        
        # 记录当前请求
        self.rate_limit_storage[client_ip].append(current_time)
        
        return None
    
    async def _validate_input(self, request: Request) -> Optional[Response]:
        """
        验证输入数据
        
        Args:
            request: HTTP请求
            
        Returns:
            如果验证失败则返回错误响应，否则返回None
        """
        try:
            # 检查请求大小
            content_length = request.headers.get("content-length")
            if content_length:
                size = int(content_length)
                max_size = 100 * 1024 * 1024  # 100MB
                
                if size > max_size:
                    self.security_logger.warning(
                        f"请求体过大: {size} bytes, IP: {self._get_client_ip(request)}"
                    )
                    
                    return JSONResponse(
                        status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                        content={"detail": "请求体过大"}
                    )
            
            # 检查Content-Type
            content_type = request.headers.get("content-type", "")
            allowed_types = [
                "application/json",
                "application/x-www-form-urlencoded",
                "multipart/form-data",
                "text/plain"
            ]
            
            if content_type and not any(allowed in content_type for allowed in allowed_types):
                self.security_logger.warning(
                    f"不支持的Content-Type: {content_type}, IP: {self._get_client_ip(request)}"
                )
                
                return JSONResponse(
                    status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                    content={"detail": "不支持的媒体类型"}
                )
            
            # 检查危险路径
            dangerous_patterns = [
                "../", "..\\\\", "..", "~", "/etc/", "/proc/", "/sys/",
                "<script", "javascript:", "vbscript:", "onload=", "onerror="
            ]
            
            path = str(request.url.path)
            query = str(request.url.query)
            
            for pattern in dangerous_patterns:
                if pattern.lower() in path.lower() or pattern.lower() in query.lower():
                    self.security_logger.warning(
                        f"检测到危险模式: {pattern}, 路径: {path}, IP: {self._get_client_ip(request)}"
                    )
                    
                    return JSONResponse(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        content={"detail": "请求包含不安全内容"}
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"输入验证异常: {e}")
            return None
    
    def _set_security_headers(self, response: Response) -> None:
        """
        设置安全头部
        
        Args:
            response: HTTP响应
        """
        headers = self.config.get("security_headers", {})
        
        for header_name, header_value in headers.items():
            response.headers[header_name] = header_value
        
        # 添加时间戳
        response.headers["X-Security-Timestamp"] = str(int(time.time()))
    
    async def _log_request(self, request: Request) -> None:
        """
        记录请求日志
        
        Args:
            request: HTTP请求
        """
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "Unknown")
        
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "ip": client_ip,
            "method": request.method,
            "path": str(request.url.path),
            "query": str(request.url.query),
            "user_agent": user_agent,
            "referer": request.headers.get("referer", ""),
            "content_type": request.headers.get("content-type", ""),
            "content_length": request.headers.get("content-length", "0")
        }
        
        self.security_logger.info(f"请求日志: {json.dumps(log_data, ensure_ascii=False)}")
    
    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端IP地址
        
        Args:
            request: HTTP请求
            
        Returns:
            客户端IP地址
        """
        # 检查代理头部
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 返回直接连接的IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"

class CSRFProtectionMiddleware(BaseHTTPMiddleware):
    """
    CSRF保护中间件
    """
    
    def __init__(self, app: ASGIApp, secret_key: str):
        """
        初始化CSRF保护中间件
        
        Args:
            app: ASGI应用
            secret_key: 密钥
        """
        super().__init__(app)
        self.secret_key = secret_key
        self.safe_methods = {"GET", "HEAD", "OPTIONS", "TRACE"}
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        CSRF保护处理逻辑
        
        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器
            
        Returns:
            HTTP响应
        """
        # 安全方法不需要CSRF保护
        if request.method in self.safe_methods:
            return await call_next(request)
        
        # 检查CSRF令牌
        csrf_token = request.headers.get("X-CSRF-Token")
        if not csrf_token:
            csrf_token = request.cookies.get("csrf_token")
        
        if not self._validate_csrf_token(csrf_token):
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={"detail": "CSRF令牌无效"}
            )
        
        response = await call_next(request)
        
        # 设置新的CSRF令牌
        new_token = self._generate_csrf_token()
        response.set_cookie(
            "csrf_token",
            new_token,
            httponly=True,
            secure=True,
            samesite="strict"
        )
        
        return response
    
    def _generate_csrf_token(self) -> str:
        """
        生成CSRF令牌
        
        Returns:
            CSRF令牌
        """
        timestamp = str(int(time.time()))
        data = f"{timestamp}:{self.secret_key}"
        token = hashlib.sha256(data.encode()).hexdigest()
        return f"{timestamp}:{token}"
    
    def _validate_csrf_token(self, token: Optional[str]) -> bool:
        """
        验证CSRF令牌
        
        Args:
            token: CSRF令牌
            
        Returns:
            验证结果
        """
        if not token:
            return False
        
        try:
            timestamp_str, token_hash = token.split(":", 1)
            timestamp = int(timestamp_str)
            
            # 检查令牌是否过期（1小时）
            if time.time() - timestamp > 3600:
                return False
            
            # 验证令牌
            expected_data = f"{timestamp_str}:{self.secret_key}"
            expected_hash = hashlib.sha256(expected_data.encode()).hexdigest()
            
            return token_hash == expected_hash
            
        except (ValueError, IndexError):
            return False

def create_security_middleware_stack(app: ASGIApp, config: Optional[Dict] = None) -> ASGIApp:
    """
    创建安全中间件堆栈
    
    Args:
        app: ASGI应用
        config: 配置字典
        
    Returns:
        配置了安全中间件的应用
    """
    if config is None:
        config = {}
    
    # 添加CORS中间件
    cors_config = config.get("cors", {})
    app = CORSMiddleware(
        app,
        allow_origins=cors_config.get("allow_origins", ["*"]),
        allow_credentials=cors_config.get("allow_credentials", True),
        allow_methods=cors_config.get("allow_methods", ["*"]),
        allow_headers=cors_config.get("allow_headers", ["*"])
    )
    
    # 添加CSRF保护中间件
    secret_key = config.get("secret_key", "default-secret-key")
    app = CSRFProtectionMiddleware(app, secret_key)
    
    # 添加主安全中间件
    app = SecurityMiddleware(app)
    
    return app

# 安全装饰器
def require_auth(func: Callable) -> Callable:
    """
    需要认证的装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 这里应该实现具体的认证逻辑
        # 例如检查JWT令牌、会话等
        return await func(*args, **kwargs)
    
    return wrapper

def require_permission(permission: str) -> Callable:
    """
    需要特定权限的装饰器
    
    Args:
        permission: 所需权限
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 这里应该实现具体的权限检查逻辑
            return await func(*args, **kwargs)
        
        return wrapper
    
    return decorator

def rate_limit(requests_per_minute: int = 60) -> Callable:
    """
    速率限制装饰器
    
    Args:
        requests_per_minute: 每分钟允许的请求数
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 这里应该实现具体的速率限制逻辑
            return await func(*args, **kwargs)
        
        return wrapper
    
    return decorator
"}}}