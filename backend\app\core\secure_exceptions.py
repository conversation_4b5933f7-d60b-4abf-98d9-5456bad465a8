"""
安全异常处理模块
🔒 证据链: 防止敏感信息泄露，提供安全的错误响应
"""

import logging
import traceback
import uuid
from datetime import datetime
from typing import Any, Dict

from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse

from app.core.config import settings


class SecurityException(Exception):
    """安全异常基类"""

    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        self.message = message
        self.error_code = error_code or "SECURITY_ERROR"
        self.details = details or {}
        self.incident_id = str(uuid.uuid4())
        super().__init__(message)


class AuthenticationException(SecurityException):
    """认证异常"""

    def __init__(self, message: str = "认证失败", details: Dict = None):
        super().__init__(message, "AUTH_FAILED", details)


class AuthorizationException(SecurityException):
    """授权异常"""

    def __init__(self, message: str = "权限不足", details: Dict = None):
        super().__init__(message, "PERMISSION_DENIED", details)


class ValidationException(SecurityException):
    """验证异常"""

    def __init__(
        self, message: str = "输入验证失败", field: str = None, details: Dict = None
    ):
        self.field = field
        super().__init__(message, "VALIDATION_ERROR", details)


class RateLimitException(SecurityException):
    """频率限制异常"""

    def __init__(self, message: str = "请求过于频繁", details: Dict = None):
        super().__init__(message, "RATE_LIMITED", details)


class SecureExceptionHandler:
    """安全异常处理器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 🔒 证据链: 通用错误消息，避免信息泄露
        self.generic_messages = {
            "auth_failed": "认证失败，请检查您的凭据",
            "permission_denied": "您没有权限执行此操作",
            "validation_error": "输入数据格式不正确",
            "rate_limited": "请求过于频繁，请稍后再试",
            "resource_not_found": "请求的资源不存在",
            "internal_error": "服务暂时不可用，请稍后重试",
            "database_error": "数据处理失败，请稍后重试",
            "external_service_error": "外部服务暂时不可用",
        }

    def log_security_incident(
        self,
        exception: Exception,
        request: Request = None,
        user_id: str = None,
        additional_context: Dict = None,
    ) -> str:
        """记录安全事件"""
        incident_id = str(uuid.uuid4())

        # 🔒 证据链: 详细的安全日志记录
        log_data = {
            "incident_id": incident_id,
            "timestamp": datetime.utcnow().isoformat(),
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "user_id": user_id,
            "additional_context": additional_context or {},
        }

        if request:
            log_data.update(
                {
                    "method": request.method,
                    "url": str(request.url),
                    "client_ip": self._get_client_ip(request),
                    "user_agent": request.headers.get("user-agent"),
                    "referer": request.headers.get("referer"),
                }
            )

        # 在开发环境记录详细信息
        if settings.DEBUG:
            log_data["traceback"] = traceback.format_exc()

        self.logger.error(f"Security incident: {incident_id}", extra=log_data)
        return incident_id

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"

    def create_safe_response(
        self, exception: Exception, request: Request = None, user_id: str = None
    ) -> JSONResponse:
        """创建安全的错误响应"""

        # 记录安全事件
        incident_id = self.log_security_incident(exception, request, user_id)

        # 🔒 证据链: 根据异常类型返回适当的通用消息
        if isinstance(exception, AuthenticationException):
            status_code = status.HTTP_401_UNAUTHORIZED
            message = self.generic_messages["auth_failed"]
            error_code = "AUTH_FAILED"
        elif isinstance(exception, AuthorizationException):
            status_code = status.HTTP_403_FORBIDDEN
            message = self.generic_messages["permission_denied"]
            error_code = "PERMISSION_DENIED"
        elif isinstance(exception, ValidationException):
            status_code = status.HTTP_400_BAD_REQUEST
            message = self.generic_messages["validation_error"]
            error_code = "VALIDATION_ERROR"
        elif isinstance(exception, RateLimitException):
            status_code = status.HTTP_429_TOO_MANY_REQUESTS
            message = self.generic_messages["rate_limited"]
            error_code = "RATE_LIMITED"
        elif isinstance(exception, HTTPException):
            status_code = exception.status_code
            # 对于HTTP异常，也使用通用消息
            if status_code == 404:
                message = self.generic_messages["resource_not_found"]
                error_code = "NOT_FOUND"
            elif status_code == 401:
                message = self.generic_messages["auth_failed"]
                error_code = "AUTH_FAILED"
            elif status_code == 403:
                message = self.generic_messages["permission_denied"]
                error_code = "PERMISSION_DENIED"
            else:
                message = self.generic_messages["internal_error"]
                error_code = "INTERNAL_ERROR"
        else:
            # 未知异常，使用通用内部错误消息
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            message = self.generic_messages["internal_error"]
            error_code = "INTERNAL_ERROR"

        response_data = {
            "success": False,
            "error": {
                "code": error_code,
                "message": message,
                "incident_id": incident_id,
                "timestamp": datetime.utcnow().isoformat(),
            },
        }

        # 在开发环境提供更多调试信息
        if settings.DEBUG and hasattr(exception, "details"):
            response_data["error"]["debug_details"] = exception.details

        return JSONResponse(status_code=status_code, content=response_data)

    def handle_database_error(
        self, exception: Exception, operation: str = "database operation"
    ) -> HTTPException:
        """处理数据库错误"""
        # 🔒 证据链: 数据库错误不暴露内部结构
        self.logger.error(f"Database error during {operation}: {str(exception)}")

        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=self.generic_messages["database_error"],
        )

    def handle_external_service_error(
        self, service_name: str, exception: Exception
    ) -> HTTPException:
        """处理外部服务错误"""
        self.logger.error(f"External service error ({service_name}): {str(exception)}")

        return HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=self.generic_messages["external_service_error"],
        )


# 全局异常处理器实例
secure_exception_handler = SecureExceptionHandler()


# 装饰器函数
def handle_exceptions(func):
    """异常处理装饰器"""

    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except SecurityException as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=e.message
            )
        except HTTPException:
            raise
        except Exception as e:
            # 🔒 证据链: 捕获所有未处理的异常
            incident_id = secure_exception_handler.log_security_incident(e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"服务暂时不可用，请稍后重试。事件ID: {incident_id}",
            )

    return wrapper


def safe_database_operation(operation_name: str = "database operation"):
    """安全数据库操作装饰器"""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                raise secure_exception_handler.handle_database_error(e, operation_name)

        return wrapper

    return decorator


def safe_external_service_call(service_name: str):
    """安全外部服务调用装饰器"""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                raise secure_exception_handler.handle_external_service_error(
                    service_name, e
                )

        return wrapper

    return decorator


# 常用的安全异常创建函数
def create_auth_error(message: str = None) -> AuthenticationException:
    """创建认证错误"""
    return AuthenticationException(message or "认证失败")


def create_permission_error(required_permission: str = None) -> AuthorizationException:
    """创建权限错误"""
    message = f"需要权限: {required_permission}" if required_permission else "权限不足"
    return AuthorizationException(message)


def create_validation_error(
    field: str = None, message: str = None
) -> ValidationException:
    """创建验证错误"""
    return ValidationException(message or "输入验证失败", field)


def create_rate_limit_error(retry_after: int = None) -> RateLimitException:
    """创建频率限制错误"""
    details = {"retry_after": retry_after} if retry_after else {}
    return RateLimitException("请求过于频繁", details)


# 响应模型
class ErrorResponse:
    """错误响应模型"""

    @staticmethod
    def create_error_response(
        error_code: str, message: str, status_code: int = 400, details: Dict = None
    ) -> JSONResponse:
        """创建标准错误响应"""
        response_data = {
            "success": False,
            "error": {
                "code": error_code,
                "message": message,
                "timestamp": datetime.utcnow().isoformat(),
            },
        }

        if details and settings.DEBUG:
            response_data["error"]["details"] = details

        return JSONResponse(status_code=status_code, content=response_data)

    @staticmethod
    def create_success_response(data: Any = None, message: str = "操作成功") -> Dict:
        """创建标准成功响应"""
        return {
            "success": True,
            "message": message,
            "data": data,
            "timestamp": datetime.utcnow().isoformat(),
        }
