/**
 * Real User Monitoring (RUM) 系统 - 2025年最佳实践
 * 实时监控用户体验和性能指标
 */

// RUM配置接口
interface RUMConfig {
  apiEndpoint: string
  sampleRate: number
  enableConsoleCapture: boolean
  enableErrorCapture: boolean
  enablePerformanceCapture: boolean
  enableUserInteractionCapture: boolean
  maxBatchSize: number
  flushInterval: number
}

// 性能指标接口
interface PerformanceMetrics {
  // Core Web Vitals
  LCP?: number // Largest Contentful Paint
  FID?: number // First Input Delay
  CLS?: number // Cumulative Layout Shift
  
  // 其他重要指标
  FCP?: number // First Contentful Paint
  TTI?: number // Time to Interactive
  TBT?: number // Total Blocking Time
  
  // 自定义指标
  routeChangeTime?: number
  componentLoadTime?: number
  apiResponseTime?: number
}

// 用户交互事件
interface UserInteraction {
  type: 'click' | 'scroll' | 'input' | 'navigation'
  target: string
  timestamp: number
  duration?: number
  metadata?: Record<string, any>
}

// 错误信息
interface ErrorInfo {
  message: string
  stack?: string
  source: string
  line?: number
  column?: number
  timestamp: number
  userAgent: string
  url: string
  userId?: string
}

// RUM数据批次
interface RUMBatch {
  sessionId: string
  userId?: string
  timestamp: number
  url: string
  userAgent: string
  performance: PerformanceMetrics
  interactions: UserInteraction[]
  errors: ErrorInfo[]
  customMetrics: Record<string, any>
}

class RUMMonitor {
  private config: RUMConfig
  private sessionId: string
  private userId?: string
  private performanceObserver?: PerformanceObserver
  private mutationObserver?: MutationObserver
  private batch: RUMBatch
  private flushTimer?: number

  constructor(config: Partial<RUMConfig> = {}) {
    this.config = {
      apiEndpoint: '/api/rum',
      sampleRate: 0.1, // 10%采样率
      enableConsoleCapture: true,
      enableErrorCapture: true,
      enablePerformanceCapture: true,
      enableUserInteractionCapture: true,
      maxBatchSize: 50,
      flushInterval: 30000, // 30秒
      ...config
    }

    this.sessionId = this.generateSessionId()
    this.batch = this.createEmptyBatch()
    
    this.init()
  }

  private init(): void {
    // 采样检查
    if (Math.random() > this.config.sampleRate) {
      return
    }

    this.setupPerformanceMonitoring()
    this.setupErrorMonitoring()
    this.setupUserInteractionMonitoring()
    this.setupConsoleMonitoring()
    this.setupPageVisibilityMonitoring()
    this.startFlushTimer()

    console.log('🔍 RUM监控已启动', { sessionId: this.sessionId })
  }

  private generateSessionId(): string {
    return `rum_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private createEmptyBatch(): RUMBatch {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      performance: {},
      interactions: [],
      errors: [],
      customMetrics: {}
    }
  }

  private setupPerformanceMonitoring(): void {
    if (!this.config.enablePerformanceCapture) return

    // Core Web Vitals监控
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          switch (entry.entryType) {
            case 'largest-contentful-paint':
              this.batch.performance.LCP = entry.startTime
              break
            case 'first-input':
              this.batch.performance.FID = (entry as any).processingStart - entry.startTime
              break
            case 'layout-shift':
              if (!(entry as any).hadRecentInput) {
                this.batch.performance.CLS = (this.batch.performance.CLS || 0) + (entry as any).value
              }
              break
            case 'navigation':
              const navEntry = entry as PerformanceNavigationTiming
              this.batch.performance.FCP = navEntry.responseStart - navEntry.fetchStart
              this.batch.performance.TTI = navEntry.loadEventEnd - navEntry.fetchStart
              break
          }
        }
      })

      this.performanceObserver.observe({ 
        entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift', 'navigation'] 
      })
    }
  }

  private setupErrorMonitoring(): void {
    if (!this.config.enableErrorCapture) return

    // JavaScript错误监控
    window.addEventListener('error', (event) => {
      this.captureError({
        message: event.message,
        stack: event.error?.stack,
        source: event.filename,
        line: event.lineno,
        column: event.colno,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: this.userId
      })
    })

    // Promise rejection监控
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        source: 'promise',
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: this.userId
      })
    })
  }

  private setupUserInteractionMonitoring(): void {
    if (!this.config.enableUserInteractionCapture) return

    // 点击事件监控
    document.addEventListener('click', (event) => {
      this.captureInteraction({
        type: 'click',
        target: this.getElementSelector(event.target as Element),
        timestamp: Date.now(),
        metadata: {
          x: event.clientX,
          y: event.clientY,
          button: event.button
        }
      })
    })

    // 滚动事件监控（节流）
    let scrollTimer: number
    document.addEventListener('scroll', () => {
      clearTimeout(scrollTimer)
      scrollTimer = window.setTimeout(() => {
        this.captureInteraction({
          type: 'scroll',
          target: 'document',
          timestamp: Date.now(),
          metadata: {
            scrollY: window.scrollY,
            scrollX: window.scrollX
          }
        })
      }, 100)
    })
  }

  private setupConsoleMonitoring(): void {
    if (!this.config.enableConsoleCapture) return

    const originalConsoleError = console.error
    console.error = (...args) => {
      this.captureError({
        message: args.join(' '),
        source: 'console',
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: this.userId
      })
      originalConsoleError.apply(console, args)
    }
  }

  private setupPageVisibilityMonitoring(): void {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.flush() // 页面隐藏时立即发送数据
      }
    })

    // 页面卸载时发送数据
    window.addEventListener('beforeunload', () => {
      this.flush(true)
    })
  }

  private startFlushTimer(): void {
    this.flushTimer = window.setInterval(() => {
      this.flush()
    }, this.config.flushInterval)
  }

  private getElementSelector(element: Element): string {
    if (element.id) return `#${element.id}`
    if (element.className) return `.${element.className.split(' ')[0]}`
    return element.tagName.toLowerCase()
  }

  private captureError(error: ErrorInfo): void {
    this.batch.errors.push(error)
    this.checkBatchSize()
  }

  private captureInteraction(interaction: UserInteraction): void {
    this.batch.interactions.push(interaction)
    this.checkBatchSize()
  }

  private checkBatchSize(): void {
    const totalItems = this.batch.errors.length + this.batch.interactions.length
    if (totalItems >= this.config.maxBatchSize) {
      this.flush()
    }
  }

  // 公共方法
  public setUserId(userId: string): void {
    this.userId = userId
    this.batch.userId = userId
  }

  public captureCustomMetric(key: string, value: any): void {
    this.batch.customMetrics[key] = value
  }

  public captureRouteChange(from: string, to: string, duration: number): void {
    this.batch.performance.routeChangeTime = duration
    this.captureInteraction({
      type: 'navigation',
      target: to,
      timestamp: Date.now(),
      duration,
      metadata: { from, to }
    })
  }

  public flush(sync = false): void {
    if (this.isEmpty()) return

    const data = { ...this.batch }
    this.batch = this.createEmptyBatch()

    if (sync) {
      // 同步发送（页面卸载时）
      navigator.sendBeacon(this.config.apiEndpoint, JSON.stringify(data))
    } else {
      // 异步发送
      fetch(this.config.apiEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      }).catch(error => {
        console.warn('RUM数据发送失败:', error)
      })
    }
  }

  private isEmpty(): boolean {
    return this.batch.errors.length === 0 && 
           this.batch.interactions.length === 0 && 
           Object.keys(this.batch.performance).length === 0 &&
           Object.keys(this.batch.customMetrics).length === 0
  }

  public destroy(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect()
    }
    if (this.mutationObserver) {
      this.mutationObserver.disconnect()
    }
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    this.flush(true)
  }
}

// 全局RUM实例
let rumInstance: RUMMonitor | null = null

// 初始化RUM监控
export function initRUM(config?: Partial<RUMConfig>): RUMMonitor {
  if (rumInstance) {
    rumInstance.destroy()
  }
  
  rumInstance = new RUMMonitor(config)
  return rumInstance
}

// 获取RUM实例
export function getRUM(): RUMMonitor | null {
  return rumInstance
}

// 导出类型和类
export type { RUMConfig, PerformanceMetrics, UserInteraction, ErrorInfo }
export { RUMMonitor }
