/**
 * 最终生产系统验证
 */

const axios = require('axios');

async function finalSystemTest() {
  console.log('🎯 最终生产系统验证\n');
  
  const results = {
    backend: false,
    database: false,
    users: 0,
    api_endpoints: 0,
    frontend: false
  };
  
  // 1. 后端API测试
  console.log('📡 后端API测试:');
  try {
    // 健康检查
    const health = await axios.get('http://localhost:8001/health');
    console.log('  ✅ 健康检查:', health.data.status);
    results.backend = health.data.status === 'healthy';
    results.database = health.data.database === 'connected';
    results.users = health.data.users;
    
    // 用户列表
    const users = await axios.get('http://localhost:8001/users');
    console.log('  👥 用户数量:', users.data.users.length);
    results.api_endpoints++;
    
    // 系统统计
    const stats = await axios.get('http://localhost:8001/stats');
    console.log('  📊 系统统计:', stats.data);
    results.api_endpoints++;
    
    // 登录测试
    const login = await axios.post('http://localhost:8001/auth/login', {
      username: 'admin_user',
      password: 'Admin123!'
    });
    console.log('  🔐 登录测试:', login.data.success ? '成功' : '失败');
    results.api_endpoints++;
    
    // 视频处理测试
    const process = await axios.post('http://localhost:8001/workflow/process', null, {
      params: {
        step: 1,
        input_type: 'url',
        input_data: 'test-video.mp4',
        ai_model: 'gpt4-vision',
        user_id: 1
      }
    });
    console.log('  🎬 视频处理:', process.data.success ? '成功' : '失败');
    results.api_endpoints++;
    
  } catch (error) {
    console.log('  ❌ 后端API错误:', error.message);
  }
  
  // 2. 前端测试
  console.log('\n🌐 前端测试:');
  try {
    const frontend = await axios.get('http://localhost:3001/');
    console.log('  ✅ 前端页面:', frontend.status === 200 ? '正常' : '异常');
    results.frontend = frontend.status === 200;
    
    // 检查页面内容
    const hasTitle = frontend.data.includes('二创短视频分发系统');
    console.log('  📄 页面标题:', hasTitle ? '正确' : '缺失');
    
    const hasApp = frontend.data.includes('id="app"');
    console.log('  🎯 Vue应用:', hasApp ? '存在' : '缺失');
    
  } catch (error) {
    console.log('  ❌ 前端错误:', error.message);
  }
  
  // 3. 数据库验证
  console.log('\n💾 数据库验证:');
  console.log('  📊 用户数量:', results.users);
  console.log('  🔗 连接状态:', results.database ? '正常' : '异常');
  
  // 4. 生成最终报告
  console.log('\n📋 最终系统状态:');
  console.log('  后端API:', results.backend ? '✅ 正常' : '❌ 异常');
  console.log('  数据库:', results.database ? '✅ 正常' : '❌ 异常');
  console.log('  前端页面:', results.frontend ? '✅ 正常' : '❌ 异常');
  console.log('  API端点:', `${results.api_endpoints}/4 正常`);
  console.log('  测试用户:', `${results.users}个`);
  
  const overallHealth = results.backend && results.database && results.frontend && results.api_endpoints >= 3;
  console.log('\n🎯 系统整体状态:', overallHealth ? '✅ 生产就绪' : '⚠️ 需要修复');
  
  // 5. 用户账号信息
  if (results.backend) {
    console.log('\n👥 可用测试账号:');
    console.log('  管理员: admin_user / Admin123!');
    console.log('  创作者: content_creator / Creator123!');
    console.log('  商业用户: business_user / Business123!');
    console.log('  演示用户: demo_user / Demo123!');
    console.log('  开发者: test_developer / Dev123!');
    console.log('  营销团队: marketing_team / Marketing123!');
  }
  
  console.log('\n🚀 系统访问地址:');
  console.log('  前端界面: http://localhost:3001/');
  console.log('  后端API: http://localhost:8001/');
  console.log('  API文档: http://localhost:8001/docs');
  
  return {
    status: overallHealth ? 'READY' : 'NEEDS_FIXES',
    backend: results.backend,
    database: results.database,
    frontend: results.frontend,
    api_endpoints: results.api_endpoints,
    users: results.users
  };
}

// 运行最终测试
finalSystemTest()
  .then(result => {
    console.log('\n✨ 测试完成，系统状态:', result.status);
    process.exit(result.status === 'READY' ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 测试失败:', error.message);
    process.exit(1);
  });
