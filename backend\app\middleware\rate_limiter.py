"""API访问频率限制中间件
基于Redis的高性能限流器
"""

import time
import json
import logging
from typing import Dict, Optional, Tuple
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

try:
    import redis
except ImportError:
    redis = None

from app.core.config import settings

logger = logging.getLogger(__name__)


class RateLimitExceeded(HTTPException):
    """频率限制超出异常"""
    def __init__(self, detail: str = "Rate limit exceeded", retry_after: int = 60):
        super().__init__(status_code=429, detail=detail)
        self.retry_after = retry_after


class RateLimiter:
    """Redis基础的频率限制器"""
    
    def __init__(self, redis_client=None):
        self.redis_client = redis_client
        if not self.redis_client and redis:
            try:
                self.redis_client = redis.from_url(
                    settings.REDIS_URL,
                    decode_responses=True,
                    socket_timeout=settings.REDIS_SOCKET_TIMEOUT,
                    socket_connect_timeout=settings.REDIS_SOCKET_CONNECT_TIMEOUT,
                    retry_on_timeout=settings.REDIS_RETRY_ON_TIMEOUT
                )
            except Exception as e:
                logger.warning(f"Redis连接失败，使用内存限流: {e}")
                self.redis_client = None
        
        # 内存存储（Redis不可用时的备选方案）
        self._memory_store: Dict[str, Dict] = {}
        self._last_cleanup = time.time()
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用用户ID（如果已认证）
        if hasattr(request.state, 'user_id') and request.state.user_id:
            return f"user:{request.state.user_id}"
        
        # 使用IP地址
        client_ip = request.client.host
        
        # 检查代理头
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            client_ip = forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            client_ip = real_ip
        
        return f"ip:{client_ip}"
    
    def _cleanup_memory_store(self):
        """清理过期的内存存储记录"""
        current_time = time.time()
        
        # 每5分钟清理一次
        if current_time - self._last_cleanup < 300:
            return
        
        expired_keys = []
        for key, data in self._memory_store.items():
            if current_time - data.get('last_reset', 0) > 3600:  # 1小时过期
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._memory_store[key]
        
        self._last_cleanup = current_time
    
    def _check_rate_limit_redis(self, key: str, limit: int, window: int) -> Tuple[bool, int, int]:
        """使用Redis检查频率限制"""
        try:
            pipe = self.redis_client.pipeline()
            current_time = int(time.time())
            window_start = current_time - window
            
            # 使用滑动窗口算法
            pipe.zremrangebyscore(key, 0, window_start)
            pipe.zcard(key)
            pipe.zadd(key, {str(current_time): current_time})
            pipe.expire(key, window + 1)
            
            results = pipe.execute()
            current_requests = results[1]
            
            if current_requests >= limit:
                return False, current_requests, window
            
            return True, current_requests, window
            
        except Exception as e:
            logger.error(f"Redis频率限制检查失败: {e}")
            # 降级到内存存储
            return self._check_rate_limit_memory(key, limit, window)
    
    def _check_rate_limit_memory(self, key: str, limit: int, window: int) -> Tuple[bool, int, int]:
        """使用内存检查频率限制"""
        self._cleanup_memory_store()
        
        current_time = time.time()
        
        if key not in self._memory_store:
            self._memory_store[key] = {
                'requests': [],
                'last_reset': current_time
            }
        
        data = self._memory_store[key]
        
        # 移除过期请求
        window_start = current_time - window
        data['requests'] = [req_time for req_time in data['requests'] if req_time > window_start]
        
        current_requests = len(data['requests'])
        
        if current_requests >= limit:
            return False, current_requests, window
        
        # 添加当前请求
        data['requests'].append(current_time)
        
        return True, current_requests, window
    
    def check_rate_limit(self, request: Request, limit: int, window: int = 60) -> Tuple[bool, int, int]:
        """检查频率限制
        
        Args:
            request: FastAPI请求对象
            limit: 限制次数
            window: 时间窗口（秒）
        
        Returns:
            Tuple[bool, int, int]: (是否允许, 当前请求数, 重试时间)
        """
        client_id = self._get_client_id(request)
        endpoint = f"{request.method}:{request.url.path}"
        key = f"rate_limit:{client_id}:{endpoint}"
        
        if self.redis_client:
            return self._check_rate_limit_redis(key, limit, window)
        else:
            return self._check_rate_limit_memory(key, limit, window)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """频率限制中间件"""
    
    def __init__(self, app, rate_limiter: RateLimiter = None):
        super().__init__(app)
        self.rate_limiter = rate_limiter or RateLimiter()
        
        # 默认限制规则
        self.default_limits = {
            # API端点限制（每分钟）
            'POST:/api/v1/auth/login': (5, 60),  # 登录限制
            'POST:/api/v1/auth/register': (3, 60),  # 注册限制
            'POST:/api/v1/videos/upload': (10, 60),  # 上传限制
            'GET:/api/v1/videos': (100, 60),  # 列表查询限制
            'POST:/api/v1/videos/*/like': (20, 60),  # 点赞限制
            
            # 全局限制
            'default': (60, 60),  # 默认每分钟60次
        }
        
        # 严格限制的端点（每小时）
        self.strict_limits = {
            'POST:/api/v1/auth/forgot-password': (3, 3600),  # 忘记密码
            'POST:/api/v1/auth/reset-password': (5, 3600),  # 重置密码
        }
        
        # 白名单路径（不限制）
        self.whitelist_paths = {
            '/health',
            '/metrics',
            '/docs',
            '/openapi.json',
            '/favicon.ico'
        }
    
    def _get_rate_limit(self, request: Request) -> Tuple[int, int]:
        """获取请求的频率限制"""
        endpoint = f"{request.method}:{request.url.path}"
        
        # 检查严格限制
        if endpoint in self.strict_limits:
            return self.strict_limits[endpoint]
        
        # 检查默认限制
        if endpoint in self.default_limits:
            return self.default_limits[endpoint]
        
        # 模式匹配（用于动态路径）
        for pattern, (limit, window) in self.default_limits.items():
            if '*' in pattern:
                pattern_parts = pattern.split('*')
                if len(pattern_parts) == 2 and endpoint.startswith(pattern_parts[0]) and endpoint.endswith(pattern_parts[1]):
                    return limit, window
        
        # 返回默认限制
        return self.default_limits['default']
    
    def _is_whitelisted(self, request: Request) -> bool:
        """检查是否在白名单中"""
        path = request.url.path
        return any(path.startswith(whitelist_path) for whitelist_path in self.whitelist_paths)
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """处理请求"""
        # 检查白名单
        if self._is_whitelisted(request):
            return await call_next(request)
        
        try:
            # 获取频率限制
            limit, window = self._get_rate_limit(request)
            
            # 检查频率限制
            allowed, current_requests, retry_after = self.rate_limiter.check_rate_limit(
                request, limit, window
            )
            
            if not allowed:
                # 记录限流日志
                client_id = self.rate_limiter._get_client_id(request)
                logger.warning(
                    f"Rate limit exceeded for {client_id} on {request.method} {request.url.path}: "
                    f"{current_requests}/{limit} requests in {window}s"
                )
                
                # 返回429错误
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "Rate limit exceeded",
                        "message": f"Too many requests. Limit: {limit} requests per {window} seconds",
                        "retry_after": retry_after,
                        "current_requests": current_requests,
                        "limit": limit
                    },
                    headers={
                        "Retry-After": str(retry_after),
                        "X-RateLimit-Limit": str(limit),
                        "X-RateLimit-Remaining": str(max(0, limit - current_requests)),
                        "X-RateLimit-Reset": str(int(time.time()) + retry_after)
                    }
                )
            
            # 处理请求
            response = await call_next(request)
            
            # 添加频率限制头
            response.headers["X-RateLimit-Limit"] = str(limit)
            response.headers["X-RateLimit-Remaining"] = str(max(0, limit - current_requests - 1))
            response.headers["X-RateLimit-Reset"] = str(int(time.time()) + window)
            
            return response
            
        except Exception as e:
            logger.error(f"Rate limit middleware error: {e}")
            # 发生错误时允许请求通过
            return await call_next(request)


# 创建全局限流器实例
rate_limiter = RateLimiter()


def create_rate_limit_middleware():
    """创建频率限制中间件"""
    return RateLimitMiddleware


# 装饰器形式的频率限制
def rate_limit(limit: int, window: int = 60):
    """频率限制装饰器
    
    Args:
        limit: 限制次数
        window: 时间窗口（秒）
    """
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            allowed, current_requests, retry_after = rate_limiter.check_rate_limit(
                request, limit, window
            )
            
            if not allowed:
                raise RateLimitExceeded(
                    detail=f"Rate limit exceeded: {limit} requests per {window} seconds",
                    retry_after=retry_after
                )
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator