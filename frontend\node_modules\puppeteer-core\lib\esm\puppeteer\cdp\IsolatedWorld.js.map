{"version": 3, "file": "IsolatedWorld.js", "sourceRoot": "", "sources": ["../../../../src/cdp/IsolatedWorld.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAC,cAAc,EAAE,GAAG,EAAE,QAAQ,EAAC,MAAM,gCAAgC,CAAC;AAI7E,OAAO,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAC;AACtC,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AAGvD,OAAO,EACL,gBAAgB,EAChB,OAAO,EACP,4BAA4B,GAC7B,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAC,aAAa,EAAC,MAAM,uBAAuB,CAAC;AAEpD,OAAO,EAAC,gBAAgB,EAAC,MAAM,oBAAoB,CAAC;AAIpD,OAAO,EAAC,WAAW,EAAC,MAAM,eAAe,CAAC;AAmC1C;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,KAAK;IACtC,QAAQ,CAAoB;IAC5B,QAAQ,GAAyB,IAAI,YAAY,EAAE,CAAC;IAE3C,cAAc,CAA0B;IAEjD,YACE,aAAsC,EACtC,eAAgC;QAEhC,KAAK,CAAC,eAAe,CAAC,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;IACtC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;IACpC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,UAAU,CAAC,OAAyB;QAClC,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC;QACjC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3E,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACvC,KAAK,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC1B,IAAI,qBAAqB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACjD,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,0BAA0B,CACxB,KAA6C;QAE7C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,uBAAuB,CAAC,KAA0C;QAChE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,UAAU;QACR,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,iBAAiB;QACf,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CACb,mEAAmE,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,iCAAiC,CAC3H,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB;QAC5B,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAC3D,MAAM,MAAM,GAAG,MAAM,cAAc,CACjC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,IAAI,CAC7C,QAAQ,CACN,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,IAAI,CAC9C,GAAG,CAAC,GAAG,EAAE;YACP,2EAA2E;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CACH,EACD,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CACxC,CACF,CACF,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,4BAA4B,CACzC,IAAI,CAAC,cAAc,CAAC,IAAI,EACxB,YAAY,CACb,CAAC;QACF,oEAAoE;QACpE,gEAAgE;QAChE,SAAS;QACT,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAClD,CAAC;QACD,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,4BAA4B,CACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAClB,YAAY,CACb,CAAC;QACF,8DAA8D;QAC9D,gEAAgE;QAChE,SAAS;QACT,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAClD,CAAC;QACD,OAAO,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACvD,CAAC;IAEQ,KAAK,CAAC,gBAAgB,CAC7B,aAA0C;QAE1C,iEAAiE;QACjE,gEAAgE;QAChE,SAAS;QACT,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAClD,CAAC;QACD,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzD,aAAa,EAAE,aAAa;YAC5B,kBAAkB,EAAE,OAAO,CAAC,EAAE;SAC/B,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAmB,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,WAAW,CAA2B,MAAS;QACnD,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1B,yEAAyE;YACzE,oCAAoC;YACpC,OAAO,CAAC,MAAM,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAC1C,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAiB,CAAC;QACtB,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1D,QAAQ,EAAE,MAAM,CAAC,EAAE;SACpB,CAAC,CAAC;QACH,OAAO,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAM,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,cAAc,CAA2B,MAAS;QACtD,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1B,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,4CAA4C;QAC5C,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACjD,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACtD,QAAQ,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,QAAQ;SACzC,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAC5C,IAAI,CAAC,IAAI,CAAC,aAAa,CACxB,CAAM,CAAC;QACR,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,eAAe,CACb,YAA2C;QAE3C,IAAI,YAAY,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YACpC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IAC7C,CAAC;IAEQ,CAAC,aAAa,CAAC;QACtB,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC;QACjC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAC1C,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;IACrC,CAAC;CACF"}