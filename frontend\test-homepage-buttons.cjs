/**
 * 前端主页面功能按钮自动化测试
 * 测试所有按钮的可点击性和功能正确性
 */

const puppeteer = require('puppeteer');

async function testHomepageButtons() {
  console.log('🎯 开始测试前端主页面功能按钮...');
  
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // 设置视口大小
  await page.setViewport({ width: 1920, height: 1080 });
  
  try {
    // 访问主页
    console.log('🌐 访问主页...');
    await page.goto('http://localhost:3001/', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('✅ 页面加载完成');
    
    // 测试结果统计
    const testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      details: []
    };
    
    // 1. 测试顶部导航栏按钮
    console.log('\n📋 测试顶部导航栏按钮...');
    
    const navButtons = [
      { selector: 'a[href="/"]', name: '首页', expectedPath: '/' },
      { selector: 'a[href="/video-creation"]', name: '视频创作', expectedPath: '/video-creation' },
      { selector: 'a[href="/compute-test"]', name: '计算引擎', expectedPath: '/compute-test' },
      { selector: 'a[href="/profile"]', name: '个人中心', expectedPath: '/profile' }
    ];
    
    for (const button of navButtons) {
      testResults.total++;
      try {
        const element = await page.$(button.selector);
        if (element) {
          const isVisible = await element.isIntersectingViewport();
          const text = await element.evaluate(el => el.textContent.trim());
          
          console.log(`  🔍 测试 "${button.name}" 按钮:`);
          console.log(`    - 可见性: ${isVisible ? '✅' : '❌'}`);
          console.log(`    - 文本内容: "${text}"`);
          
          // 点击测试
          await element.click();
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const currentPath = await page.evaluate(() => window.location.pathname);
          const pathCorrect = currentPath === button.expectedPath;
          
          console.log(`    - 路由跳转: ${pathCorrect ? '✅' : '❌'} (${currentPath})`);
          
          if (isVisible && pathCorrect) {
            testResults.passed++;
            testResults.details.push({ name: button.name, status: 'PASS', issue: null });
          } else {
            testResults.failed++;
            testResults.details.push({ 
              name: button.name, 
              status: 'FAIL', 
              issue: !isVisible ? '按钮不可见' : '路由跳转错误' 
            });
          }
          
          // 返回首页继续测试
          if (currentPath !== '/') {
            await page.goto('http://localhost:3001/');
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          
        } else {
          console.log(`  ❌ 未找到 "${button.name}" 按钮`);
          testResults.failed++;
          testResults.details.push({ name: button.name, status: 'FAIL', issue: '按钮不存在' });
        }
      } catch (error) {
        console.log(`  ❌ 测试 "${button.name}" 时出错: ${error.message}`);
        testResults.failed++;
        testResults.details.push({ name: button.name, status: 'ERROR', issue: error.message });
      }
    }
    
    // 2. 测试品牌Logo按钮
    console.log('\n🏷️ 测试品牌Logo按钮...');
    testResults.total++;
    
    try {
      const logoButton = await page.$('.app-header__brand');
      if (logoButton) {
        const isVisible = await logoButton.isIntersectingViewport();
        console.log(`  - Logo可见性: ${isVisible ? '✅' : '❌'}`);
        
        await logoButton.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const currentPath = await page.evaluate(() => window.location.pathname);
        const pathCorrect = currentPath === '/';
        
        console.log(`  - Logo点击跳转: ${pathCorrect ? '✅' : '❌'}`);
        
        if (isVisible && pathCorrect) {
          testResults.passed++;
          testResults.details.push({ name: '品牌Logo', status: 'PASS', issue: null });
        } else {
          testResults.failed++;
          testResults.details.push({ name: '品牌Logo', status: 'FAIL', issue: '功能异常' });
        }
      } else {
        console.log('  ❌ 未找到品牌Logo');
        testResults.failed++;
        testResults.details.push({ name: '品牌Logo', status: 'FAIL', issue: '元素不存在' });
      }
    } catch (error) {
      console.log(`  ❌ 测试品牌Logo时出错: ${error.message}`);
      testResults.failed++;
      testResults.details.push({ name: '品牌Logo', status: 'ERROR', issue: error.message });
    }
    
    // 3. 测试主页功能卡片按钮
    console.log('\n🎴 测试主页功能卡片按钮...');
    
    const featureButtons = [
      { selector: 'a[href="/video-creation"].btn-primary', name: '开始创作按钮' },
      { selector: 'a[href="/compute-test"].btn-primary', name: '测试引擎按钮' },
      { selector: 'button.btn-secondary[disabled]', name: 'AI增强按钮(即将推出)' }
    ];
    
    for (const button of featureButtons) {
      testResults.total++;
      try {
        const element = await page.$(button.selector);
        if (element) {
          const isVisible = await element.isIntersectingViewport();
          const text = await element.evaluate(el => el.textContent.trim());
          
          console.log(`  🔍 测试 "${button.name}":`);
          console.log(`    - 可见性: ${isVisible ? '✅' : '❌'}`);
          console.log(`    - 按钮文本: "${text}"`);
          
          if (isVisible) {
            testResults.passed++;
            testResults.details.push({ name: button.name, status: 'PASS', issue: null });
          } else {
            testResults.failed++;
            testResults.details.push({ name: button.name, status: 'FAIL', issue: '按钮不可见' });
          }
        } else {
          console.log(`  ❌ 未找到 "${button.name}"`);
          testResults.failed++;
          testResults.details.push({ name: button.name, status: 'FAIL', issue: '按钮不存在' });
        }
      } catch (error) {
        console.log(`  ❌ 测试 "${button.name}" 时出错: ${error.message}`);
        testResults.failed++;
        testResults.details.push({ name: button.name, status: 'ERROR', issue: error.message });
      }
    }
    
    // 4. 测试移动端响应式按钮
    console.log('\n📱 测试移动端响应式按钮...');
    
    // 切换到移动端视口
    await page.setViewport({ width: 375, height: 667 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    testResults.total++;
    try {
      const mobileMenuToggle = await page.$('.mobile-menu-toggle');
      if (mobileMenuToggle) {
        const isVisible = await mobileMenuToggle.isIntersectingViewport();
        console.log(`  - 汉堡菜单按钮可见性: ${isVisible ? '✅' : '❌'}`);
        
        if (isVisible) {
          testResults.passed++;
          testResults.details.push({ name: '移动端汉堡菜单', status: 'PASS', issue: null });
        } else {
          testResults.failed++;
          testResults.details.push({ name: '移动端汉堡菜单', status: 'FAIL', issue: '按钮不可见' });
        }
      } else {
        console.log('  ❌ 未找到移动端汉堡菜单按钮');
        testResults.failed++;
        testResults.details.push({ name: '移动端汉堡菜单', status: 'FAIL', issue: '按钮不存在' });
      }
    } catch (error) {
      console.log(`  ❌ 测试移动端按钮时出错: ${error.message}`);
      testResults.failed++;
      testResults.details.push({ name: '移动端汉堡菜单', status: 'ERROR', issue: error.message });
    }
    
    // 输出测试总结
    console.log('\n📊 测试结果总结:');
    console.log(`总测试项: ${testResults.total}`);
    console.log(`通过: ${testResults.passed} ✅`);
    console.log(`失败: ${testResults.failed} ❌`);
    console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
    
    console.log('\n📋 详细结果:');
    testResults.details.forEach((result, index) => {
      const status = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
      console.log(`${index + 1}. ${result.name}: ${status} ${result.issue ? `(${result.issue})` : ''}`);
    });
    
    // 保存测试报告
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: testResults.total,
        passed: testResults.passed,
        failed: testResults.failed,
        successRate: ((testResults.passed / testResults.total) * 100).toFixed(1) + '%'
      },
      details: testResults.details
    };
    
    require('fs').writeFileSync('homepage-button-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n💾 测试报告已保存: homepage-button-test-report.json');
    
  } catch (error) {
    console.error('💥 测试过程中出现错误:', error.message);
  } finally {
    await browser.close();
  }
}

// 运行测试
testHomepageButtons().catch(console.error);
