<template>
  <!-- 登录页面或特殊布局不使用统一布局 -->
  <div v-if="isAuthLayout" class="auth-layout">
    <slot />
  </div>

  <!-- 其他页面使用统一布局 -->
  <div v-else class="app-layout">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <router-link to="/" class="app-header__brand">
        <div class="app-header__brand-icon">🎬</div>
        <span>二创短视频分发系统</span>
      </router-link>
      
      <nav class="app-header__nav">
        <router-link to="/" class="app-header__nav-item" :class="{ active: $route.path === '/' }">
          首页
        </router-link>
        <router-link to="/video-creation" class="app-header__nav-item" :class="{ active: $route.path === '/video-creation' }">
          视频创作
        </router-link>
        <router-link to="/compute-test" class="app-header__nav-item" :class="{ active: $route.path === '/compute-test' }">
          计算引擎
        </router-link>
        <router-link to="/profile" class="app-header__nav-item" :class="{ active: $route.path === '/profile' }">
          个人中心
        </router-link>
      </nav>
    </header>

    <!-- 主体内容区 -->
    <div class="app-body">
      <!-- 左侧功能区 -->
      <aside class="app-sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="app-sidebar__header">
          <h3 class="app-sidebar__title">功能导航</h3>
        </div>
        
        <nav class="app-sidebar__nav">
          <router-link to="/" class="app-sidebar__nav-item" :class="{ active: $route.path === '/' }">
            <svg class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
            </svg>
            <span v-show="!sidebarCollapsed">首页概览</span>
          </router-link>
          
          <router-link to="/video-creation" class="app-sidebar__nav-item" :class="{ active: $route.path === '/video-creation' }">
            <svg class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM5 8a1 1 0 000 2h8a1 1 0 100-2H5z"/>
            </svg>
            <span v-show="!sidebarCollapsed">智能视频生成</span>
          </router-link>
          
          <router-link to="/compute-test" class="app-sidebar__nav-item" :class="{ active: $route.path === '/compute-test' }">
            <svg class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            <span v-show="!sidebarCollapsed">计算引擎测试</span>
          </router-link>
          
          <router-link to="/content-analysis" class="app-sidebar__nav-item" :class="{ active: $route.path === '/content-analysis' }">
            <svg class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
              <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2V6h2v1a1 1 0 102 0v1h1a1 1 0 110 2v1a1 1 0 11-2 0v1H7v-1a1 1 0 10-2 0V9a1 1 0 110-2h2z" clip-rule="evenodd"/>
            </svg>
            <span v-show="!sidebarCollapsed">内容分析</span>
          </router-link>
          
          <router-link to="/batch-processing" class="app-sidebar__nav-item" :class="{ active: $route.path === '/batch-processing' }">
            <svg class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"/>
            </svg>
            <span v-show="!sidebarCollapsed">批量处理</span>
          </router-link>
          
          <router-link to="/profile" class="app-sidebar__nav-item" :class="{ active: $route.path === '/profile' }">
            <svg class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
            </svg>
            <span v-show="!sidebarCollapsed">个人中心</span>
          </router-link>
        </nav>
      </aside>

      <!-- 中央工作区 -->
      <main class="app-main">
        <div class="app-main__header" v-if="showPageHeader">
          <h1 class="app-main__title">{{ pageTitle }}</h1>
          <p class="app-main__subtitle" v-if="pageSubtitle">{{ pageSubtitle }}</p>
        </div>
        
        <div class="app-main__content">
          <slot />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const sidebarCollapsed = ref(false)

// 计算是否是认证布局（登录页面等）
const isAuthLayout = computed(() => {
  return route.path === '/login' || route.meta?.layout === 'auth'
})

const pageTitle = computed(() => {
  const titles: Record<string, string> = {
    '/': '二创短视频分发系统',
    '/video-creation': '智能视频生成',
    '/compute-test': '计算引擎测试',
    '/content-analysis': '内容分析',
    '/batch-processing': '批量处理',
    '/profile': '个人中心'
  }
  return titles[route.path] || '系统功能'
})

const pageSubtitle = computed(() => {
  const subtitles: Record<string, string> = {
    '/': '基于先进的AI技术，为您提供专业的视频内容创作解决方案',
    '/video-creation': '利用AI技术自动生成高质量视频内容，支持多种风格和主题',
    '/compute-test': '强大的本地计算能力，支持FFmpeg、TensorFlow.js等核心技术',
    '/content-analysis': '智能内容分析和优化建议',
    '/batch-processing': '高效的批量视频处理功能',
    '/profile': '管理您的个人信息和系统设置'
  }
  return subtitles[route.path] || ''
})

const showPageHeader = computed(() => {
  return route.path !== '/'
})

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

defineExpose({
  toggleSidebar
})
</script>

<style scoped>
/* 引入统一设计系统 */
@import url('/shared-ui-design.css');

/* 组件特定样式 */
.app-layout {
  font-family: var(--font-family-sans);
}

.auth-layout {
  /* 确保认证布局占满全屏且独立 */
  min-height: 100vh;
  width: 100%;
  font-family: var(--font-family-sans);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .app-header__nav {
    display: none;
  }
  
  .app-sidebar__title {
    display: none;
  }
}
</style>
