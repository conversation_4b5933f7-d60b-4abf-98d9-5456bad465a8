<!--
  性能监控仪表板 - 2025年最佳实践
  可视化RUM数据展示，实时性能监控
-->

<template>
  <div class="performance-dashboard">
    <!-- 仪表板头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1 class="dashboard-title">性能监控仪表板</h1>
        <div class="header-controls">
          <select v-model="selectedTimeRange" class="time-range-selector">
            <option value="1h">最近1小时</option>
            <option value="24h">最近24小时</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
          </select>
          <button @click="refreshData" class="refresh-btn" :disabled="isLoading">
            <RefreshIcon :class="{ 'animate-spin': isLoading }" />
            刷新
          </button>
          <button @click="toggleAutoRefresh" class="auto-refresh-btn" :class="{ active: autoRefresh }">
            <PlayIcon v-if="!autoRefresh" />
            <PauseIcon v-else />
            {{ autoRefresh ? '停止' : '自动' }}刷新
          </button>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <MetricCard
        title="Largest Contentful Paint"
        :value="formatTime(metrics.LCP)"
        :trend="trends.LCP"
        :threshold="2500"
        unit="ms"
        description="最大内容绘制时间"
      />
      <MetricCard
        title="First Input Delay"
        :value="formatTime(metrics.FID)"
        :trend="trends.FID"
        :threshold="100"
        unit="ms"
        description="首次输入延迟"
      />
      <MetricCard
        title="Cumulative Layout Shift"
        :value="formatScore(metrics.CLS)"
        :trend="trends.CLS"
        :threshold="0.1"
        unit=""
        description="累积布局偏移"
      />
      <MetricCard
        title="Time to Interactive"
        :value="formatTime(metrics.TTI)"
        :trend="trends.TTI"
        :threshold="3800"
        unit="ms"
        description="可交互时间"
      />
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- Core Web Vitals 趋势图 -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>Core Web Vitals 趋势</h3>
          <div class="chart-legend">
            <span class="legend-item lcp">LCP</span>
            <span class="legend-item fid">FID</span>
            <span class="legend-item cls">CLS</span>
          </div>
        </div>
        <div class="chart-content">
          <LineChart
            :data="webVitalsData"
            :options="webVitalsOptions"
            height="300"
          />
        </div>
      </div>

      <!-- 页面加载性能 -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>页面加载性能</h3>
          <select v-model="selectedPage" class="page-selector">
            <option value="all">所有页面</option>
            <option value="/">首页</option>
            <option value="/video-creation">视频创作</option>
            <option value="/compute-test">计算引擎</option>
            <option value="/profile">个人中心</option>
          </select>
        </div>
        <div class="chart-content">
          <BarChart
            :data="pageLoadData"
            :options="pageLoadOptions"
            height="300"
          />
        </div>
      </div>

      <!-- 用户交互热力图 -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>用户交互热力图</h3>
          <div class="interaction-stats">
            <span>总交互: {{ totalInteractions }}</span>
            <span>平均响应时间: {{ formatTime(avgResponseTime) }}ms</span>
          </div>
        </div>
        <div class="chart-content">
          <HeatmapChart
            :data="interactionData"
            :options="heatmapOptions"
            height="300"
          />
        </div>
      </div>

      <!-- 错误监控 -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>错误监控</h3>
          <div class="error-stats">
            <span class="error-count" :class="{ critical: errorRate > 5 }">
              错误率: {{ errorRate.toFixed(2) }}%
            </span>
          </div>
        </div>
        <div class="chart-content">
          <ErrorChart
            :data="errorData"
            :options="errorOptions"
            height="300"
          />
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-table-container">
      <div class="table-header">
        <h3>详细性能数据</h3>
        <div class="table-controls">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索页面或用户..."
            class="search-input"
          />
          <button @click="exportData" class="export-btn">
            <DownloadIcon />
            导出数据
          </button>
        </div>
      </div>
      <div class="table-content">
        <PerformanceTable
          :data="filteredTableData"
          :columns="tableColumns"
          :loading="isLoading"
          @sort="handleSort"
          @filter="handleFilter"
        />
      </div>
    </div>

    <!-- 实时监控面板 -->
    <div class="realtime-panel" v-if="realtimeMode">
      <div class="realtime-header">
        <h3>实时监控</h3>
        <div class="realtime-status">
          <div class="status-indicator" :class="{ active: isConnected }"></div>
          <span>{{ isConnected ? '已连接' : '连接中...' }}</span>
        </div>
      </div>
      <div class="realtime-content">
        <RealtimeMetrics
          :metrics="realtimeMetrics"
          :events="realtimeEvents"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { getRUM } from '@/utils/rum-monitoring'
import MetricCard from '@/components/dashboard/MetricCard.vue'
import LineChart from '@/components/charts/LineChart.vue'
import BarChart from '@/components/charts/BarChart.vue'
import HeatmapChart from '@/components/charts/HeatmapChart.vue'
import ErrorChart from '@/components/charts/ErrorChart.vue'
import PerformanceTable from '@/components/dashboard/PerformanceTable.vue'
import RealtimeMetrics from '@/components/dashboard/RealtimeMetrics.vue'
import { RefreshIcon, PlayIcon, PauseIcon, DownloadIcon } from '@/components/icons'

// 响应式数据
const isLoading = ref(false)
const autoRefresh = ref(false)
const selectedTimeRange = ref('24h')
const selectedPage = ref('all')
const searchQuery = ref('')
const realtimeMode = ref(true)
const isConnected = ref(false)

// 性能指标数据
const metrics = ref({
  LCP: 0,
  FID: 0,
  CLS: 0,
  TTI: 0,
  FCP: 0,
  TBT: 0
})

// 趋势数据
const trends = ref({
  LCP: 0,
  FID: 0,
  CLS: 0,
  TTI: 0
})

// 图表数据
const webVitalsData = ref([])
const pageLoadData = ref([])
const interactionData = ref([])
const errorData = ref([])
const realtimeMetrics = ref([])
const realtimeEvents = ref([])

// 表格数据
const tableData = ref([])
const tableColumns = [
  { key: 'timestamp', label: '时间', sortable: true },
  { key: 'page', label: '页面', sortable: true },
  { key: 'user', label: '用户', sortable: true },
  { key: 'lcp', label: 'LCP (ms)', sortable: true },
  { key: 'fid', label: 'FID (ms)', sortable: true },
  { key: 'cls', label: 'CLS', sortable: true },
  { key: 'device', label: '设备', sortable: true }
]

// 计算属性
const totalInteractions = computed(() => {
  return interactionData.value.reduce((sum, item) => sum + item.count, 0)
})

const avgResponseTime = computed(() => {
  const total = interactionData.value.reduce((sum, item) => sum + item.responseTime * item.count, 0)
  return totalInteractions.value > 0 ? total / totalInteractions.value : 0
})

const errorRate = computed(() => {
  const totalRequests = tableData.value.length
  const errorCount = errorData.value.reduce((sum, item) => sum + item.count, 0)
  return totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0
})

const filteredTableData = computed(() => {
  if (!searchQuery.value) return tableData.value
  
  const query = searchQuery.value.toLowerCase()
  return tableData.value.filter(item => 
    item.page.toLowerCase().includes(query) ||
    item.user.toLowerCase().includes(query)
  )
})

// 图表配置
const webVitalsOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      beginAtZero: true,
      title: {
        display: true,
        text: '时间 (ms)'
      }
    }
  },
  plugins: {
    legend: {
      display: false
    }
  }
}

const pageLoadOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      beginAtZero: true,
      title: {
        display: true,
        text: '加载时间 (ms)'
      }
    }
  }
}

const heatmapOptions = {
  responsive: true,
  maintainAspectRatio: false
}

const errorOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      beginAtZero: true,
      title: {
        display: true,
        text: '错误数量'
      }
    }
  }
}

// 方法
const formatTime = (value: number) => {
  if (!value) return '-'
  return value < 1000 ? `${value.toFixed(0)}` : `${(value / 1000).toFixed(1)}k`
}

const formatScore = (value: number) => {
  if (!value) return '-'
  return value.toFixed(3)
}

const refreshData = async () => {
  isLoading.value = true
  try {
    await fetchPerformanceData()
  } finally {
    isLoading.value = false
  }
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
}

const fetchPerformanceData = async () => {
  // 模拟API调用
  const response = await fetch(`/api/performance/metrics?range=${selectedTimeRange.value}&page=${selectedPage.value}`)
  const data = await response.json()
  
  metrics.value = data.metrics
  trends.value = data.trends
  webVitalsData.value = data.webVitals
  pageLoadData.value = data.pageLoad
  interactionData.value = data.interactions
  errorData.value = data.errors
  tableData.value = data.details
}

const exportData = () => {
  const csvContent = generateCSV(filteredTableData.value)
  downloadCSV(csvContent, `performance-data-${new Date().toISOString().split('T')[0]}.csv`)
}

const generateCSV = (data: any[]) => {
  const headers = tableColumns.map(col => col.label).join(',')
  const rows = data.map(row => 
    tableColumns.map(col => row[col.key]).join(',')
  ).join('\n')
  return `${headers}\n${rows}`
}

const downloadCSV = (content: string, filename: string) => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = filename
  link.click()
}

const handleSort = (column: string, direction: 'asc' | 'desc') => {
  // 实现排序逻辑
  console.log('Sort:', column, direction)
}

const handleFilter = (filters: Record<string, any>) => {
  // 实现过滤逻辑
  console.log('Filter:', filters)
}

// 实时监控
const setupRealtimeMonitoring = () => {
  const rum = getRUM()
  if (!rum) return
  
  // 模拟WebSocket连接
  const ws = new WebSocket('ws://localhost:8000/ws/performance')
  
  ws.onopen = () => {
    isConnected.value = true
  }
  
  ws.onmessage = (event) => {
    const data = JSON.parse(event.data)
    realtimeMetrics.value.unshift(data)
    if (realtimeMetrics.value.length > 100) {
      realtimeMetrics.value.pop()
    }
  }
  
  ws.onclose = () => {
    isConnected.value = false
  }
  
  return ws
}

// 生命周期
let refreshInterval: number
let websocket: WebSocket

onMounted(async () => {
  await refreshData()
  
  if (realtimeMode.value) {
    websocket = setupRealtimeMonitoring()
  }
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
  if (websocket) {
    websocket.close()
  }
})

// 监听器
watch(autoRefresh, (enabled) => {
  if (enabled) {
    refreshInterval = setInterval(refreshData, 30000) // 30秒刷新
  } else if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})

watch(selectedTimeRange, refreshData)
watch(selectedPage, refreshData)
</script>

<style scoped>
.performance-dashboard {
  padding: 24px;
  background: var(--color-background);
  min-height: 100vh;
}

.dashboard-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.dashboard-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-range-selector,
.page-selector {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
}

.refresh-btn,
.auto-refresh-btn,
.export-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover,
.auto-refresh-btn:hover,
.export-btn:hover {
  background: var(--color-background-soft);
}

.auto-refresh-btn.active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.chart-container {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  position: relative;
  padding-left: 20px;
  font-size: 14px;
  color: var(--color-text-soft);
}

.legend-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-item.lcp::before { background: #ff6b6b; }
.legend-item.fid::before { background: #4ecdc4; }
.legend-item.cls::before { background: #45b7d1; }

.interaction-stats,
.error-stats {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: var(--color-text-soft);
}

.error-count.critical {
  color: #ff6b6b;
  font-weight: 600;
}

.data-table-container {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
}

.table-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 14px;
  width: 200px;
}

.realtime-panel {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 24px;
}

.realtime-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.realtime-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
}

.realtime-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--color-text-soft);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ccc;
  transition: background 0.3s ease;
}

.status-indicator.active {
  background: #4ecdc4;
  box-shadow: 0 0 8px rgba(78, 205, 196, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-dashboard {
    padding: 16px;
  }

  .dashboard-title {
    font-size: 24px;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .header-controls {
    justify-content: center;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .table-controls {
    justify-content: center;
  }

  .search-input {
    width: 100%;
  }
}
</style>
