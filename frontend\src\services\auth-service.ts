/**
 * 认证服务 - 直接连接生产API
 */

export interface LoginCredentials {
  username: string
  password: string
  rememberMe?: boolean
  userType?: string
}

export interface User {
  id: number
  username: string
  email: string
  role: string
}

export interface LoginResponse {
  success: boolean
  user: User
}

export class AuthService {
  private static readonly API_BASE = 'http://localhost:8001'
  
  /**
   * 用户登录
   */
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await fetch(`${this.API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: credentials.username,
          password: credentials.password
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `HTTP ${response.status}: 登录失败`)
      }

      const data = await response.json()
      
      if (!data.success) {
        throw new Error('登录失败：服务器返回失败状态')
      }

      return data
    } catch (error) {
      console.error('登录请求失败:', error)
      throw error
    }
  }

  /**
   * 测试API连接
   */
  static async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_BASE}/health`)
      return response.ok
    } catch (error) {
      console.error('API连接测试失败:', error)
      return false
    }
  }

  /**
   * 获取API健康状态
   */
  static async getHealthStatus(): Promise<any> {
    try {
      const response = await fetch(`${this.API_BASE}/health`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error('获取健康状态失败:', error)
      throw error
    }
  }

  /**
   * 保存用户信息到本地存储
   */
  static saveUserInfo(user: User, token: string = 'simple-token'): void {
    localStorage.setItem('user_info', JSON.stringify(user))
    localStorage.setItem('access_token', token)
    localStorage.setItem('login_time', Date.now().toString())
  }

  /**
   * 从本地存储获取用户信息
   */
  static getUserInfo(): User | null {
    try {
      const userStr = localStorage.getItem('user_info')
      return userStr ? JSON.parse(userStr) : null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 检查是否已登录
   */
  static isLoggedIn(): boolean {
    const token = localStorage.getItem('access_token')
    const user = this.getUserInfo()
    return !!(token && user)
  }

  /**
   * 登出
   */
  static logout(): void {
    localStorage.removeItem('user_info')
    localStorage.removeItem('access_token')
    localStorage.removeItem('login_time')
  }

  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | null {
    return localStorage.getItem('access_token')
  }
}
