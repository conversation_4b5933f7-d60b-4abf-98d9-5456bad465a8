"""
数据库性能优化模块
"""

import logging
import time
from contextlib import contextmanager
from typing import Any, Dict, List

from sqlalchemy import Index, event, text
from sqlalchemy.engine import Engine

from app.core.database import engine
from app.models import AuditLog, Content, Project, User

logger = logging.getLogger(__name__)


class DatabaseOptimizer:
    """数据库优化器"""

    def __init__(self, engine: Engine):
        self.engine = engine
        self.query_stats = []

    def create_indexes(self):
        """创建性能优化索引"""
        indexes = [
            # 用户表索引
            Index("idx_users_email", User.email),
            Index("idx_users_username", User.username),
            Index("idx_users_is_active", User.is_active),
            Index("idx_users_created_at", User.created_at),
            # 项目表索引
            Index("idx_projects_owner_id", Project.owner_id),
            Index("idx_projects_status", Project.status),
            Index("idx_projects_created_at", Project.created_at),
            Index("idx_projects_owner_status", Project.owner_id, Project.status),
            # 内容表索引
            Index("idx_contents_creator_id", Content.creator_id),
            Index("idx_contents_project_id", Content.project_id),
            Index("idx_contents_content_type", Content.content_type),
            Index("idx_contents_status", Content.status),
            Index("idx_contents_created_at", Content.created_at),
            Index("idx_contents_creator_status", Content.creator_id, Content.status),
            Index(
                "idx_contents_project_type", Content.project_id, Content.content_type
            ),
            # 审计日志索引
            Index("idx_audit_logs_user_id", AuditLog.user_id),
            Index("idx_audit_logs_action", AuditLog.action),
            Index("idx_audit_logs_resource_type", AuditLog.resource_type),
            Index("idx_audit_logs_resource_id", AuditLog.resource_id),
            Index("idx_audit_logs_created_at", AuditLog.created_at),
            Index("idx_audit_logs_user_action", AuditLog.user_id, AuditLog.action),
        ]

        with self.engine.connect() as conn:
            for index in indexes:
                try:
                    index.create(conn, checkfirst=True)
                    logger.info(f"创建索引: {index.name}")
                except Exception as e:
                    logger.warning(f"索引创建失败 {index.name}: {e}")

    def analyze_slow_queries(self, threshold_ms: float = 100) -> List[Dict[str, Any]]:
        """分析慢查询"""
        slow_queries = []

        for query_info in self.query_stats:
            if query_info["duration_ms"] > threshold_ms:
                slow_queries.append(query_info)

        # 按执行时间排序
        slow_queries.sort(key=lambda x: x["duration_ms"], reverse=True)

        return slow_queries

    def get_query_statistics(self) -> Dict[str, Any]:
        """获取查询统计信息"""
        if not self.query_stats:
            return {
                "total_queries": 0,
                "avg_duration_ms": 0,
                "slow_queries_count": 0,
                "fastest_query_ms": 0,
                "slowest_query_ms": 0,
            }

        durations = [q["duration_ms"] for q in self.query_stats]
        slow_queries = [q for q in self.query_stats if q["duration_ms"] > 100]

        return {
            "total_queries": len(self.query_stats),
            "avg_duration_ms": sum(durations) / len(durations),
            "slow_queries_count": len(slow_queries),
            "fastest_query_ms": min(durations),
            "slowest_query_ms": max(durations),
            "queries_per_second": len(self.query_stats)
            / max(
                1,
                (
                    (time.time() - self.query_stats[0]["timestamp"])
                    if self.query_stats
                    else 1
                ),
            ),
        }

    def optimize_database_settings(self):
        """优化数据库设置"""
        optimizations = []

        # SQLite优化设置
        if "sqlite" in str(self.engine.url):
            sqlite_optimizations = [
                "PRAGMA journal_mode = WAL;",
                "PRAGMA synchronous = NORMAL;",
                "PRAGMA cache_size = 10000;",
                "PRAGMA temp_store = MEMORY;",
                "PRAGMA mmap_size = 268435456;",  # 256MB
            ]

            with self.engine.connect() as conn:
                for pragma in sqlite_optimizations:
                    try:
                        conn.execute(text(pragma))
                        optimizations.append(pragma)
                        logger.info(f"应用优化设置: {pragma}")
                    except Exception as e:
                        logger.warning(f"优化设置失败 {pragma}: {e}")

        return optimizations

    def vacuum_database(self):
        """清理数据库"""
        try:
            with self.engine.connect() as conn:
                if "sqlite" in str(self.engine.url):
                    conn.execute(text("VACUUM;"))
                    logger.info("数据库清理完成")
                else:
                    logger.info("非SQLite数据库，跳过VACUUM操作")
        except Exception as e:
            logger.error(f"数据库清理失败: {e}")

    def analyze_table_stats(self) -> Dict[str, Any]:
        """分析表统计信息"""
        stats = {}

        try:
            with self.engine.connect() as conn:
                if "sqlite" in str(self.engine.url):
                    # SQLite表统计
                    tables = ["users", "projects", "contents", "audit_logs"]

                    for table in tables:
                        try:
                            result = conn.execute(
                                text(f"SELECT COUNT(*) FROM {table}")
                            ).scalar()
                            stats[table] = {"row_count": result}
                        except Exception as e:
                            stats[table] = {"error": str(e)}

        except Exception as e:
            logger.error(f"获取表统计信息失败: {e}")

        return stats


# 查询监控装饰器
def monitor_query_performance(func):
    """查询性能监控装饰器"""

    def wrapper(*args, **kwargs):
        start_time = time.time()

        try:
            result = func(*args, **kwargs)
            duration = (time.time() - start_time) * 1000  # 转换为毫秒

            # 记录查询信息
            query_info = {
                "function": func.__name__,
                "duration_ms": round(duration, 2),
                "timestamp": time.time(),
                "success": True,
            }

            # 如果有全局优化器实例，记录统计信息
            if hasattr(func, "_optimizer"):
                func._optimizer.query_stats.append(query_info)

            # 记录慢查询
            if duration > 100:  # 超过100ms的查询
                logger.warning(f"慢查询检测: {func.__name__} 耗时 {duration:.2f}ms")

            return result

        except Exception as e:
            duration = (time.time() - start_time) * 1000

            query_info = {
                "function": func.__name__,
                "duration_ms": round(duration, 2),
                "timestamp": time.time(),
                "success": False,
                "error": str(e),
            }

            if hasattr(func, "_optimizer"):
                func._optimizer.query_stats.append(query_info)

            raise

    return wrapper


@contextmanager
def query_timer(operation_name: str):
    """查询计时上下文管理器"""
    start_time = time.time()
    try:
        yield
    finally:
        duration = (time.time() - start_time) * 1000
        if duration > 50:  # 超过50ms记录
            logger.info(f"查询操作 '{operation_name}' 耗时: {duration:.2f}ms")


# 全局数据库优化器实例
db_optimizer = DatabaseOptimizer(engine)


def setup_query_monitoring():
    """设置查询监控"""

    @event.listens_for(Engine, "before_cursor_execute")
    def receive_before_cursor_execute(
        conn, cursor, statement, parameters, context, executemany
    ):
        context._query_start_time = time.time()

    @event.listens_for(Engine, "after_cursor_execute")
    def receive_after_cursor_execute(
        conn, cursor, statement, parameters, context, executemany
    ):
        total = time.time() - context._query_start_time

        # 记录查询统计
        query_info = {
            "statement": statement[:200] + "..." if len(statement) > 200 else statement,
            "duration_ms": round(total * 1000, 2),
            "timestamp": time.time(),
            "parameters": str(parameters)[:100] if parameters else None,
        }

        db_optimizer.query_stats.append(query_info)

        # 保持最近1000条记录
        if len(db_optimizer.query_stats) > 1000:
            db_optimizer.query_stats = db_optimizer.query_stats[-1000:]

        # 记录慢查询
        if total > 0.1:  # 超过100ms
            logger.warning(f"慢查询: {total*1000:.2f}ms - {statement[:100]}...")


def initialize_database_optimization():
    """初始化数据库优化"""
    logger.info("初始化数据库优化...")

    # 设置查询监控
    setup_query_monitoring()

    # 创建索引
    db_optimizer.create_indexes()

    # 优化数据库设置
    db_optimizer.optimize_database_settings()

    logger.info("数据库优化初始化完成")


if __name__ == "__main__":
    initialize_database_optimization()
