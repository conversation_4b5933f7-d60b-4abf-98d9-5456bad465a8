from fastapi import Depends, Request
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models import AuditLog, User


def create_audit_log(
    db: Session,
    request: Request,
    action: str,
    details: dict = None,
    user: User = None,
):
    """
    创建一个新的审计日志条目

    Args:
        db: 数据库会话
        request: FastAPI请求对象，用于获取IP地址
        action: 操作的描述
        details: 包含操作细节的字典
        user: 执行操作的用户，如果为None，则尝试从token获取
    """

    # 尝试获取IP地址
    ip_address = request.client.host if request else "N/A"

    log_entry = AuditLog(
        user_id=user.id if user else None,
        ip_address=ip_address,
        action=action,
        details=details or {},
    )
    db.add(log_entry)
    db.commit()
    db.refresh(log_entry)
    return log_entry


# 可以创建一个依赖项以便在路由中更方便地使用
def get_audit_logger(request: Request, db: Session = Depends(get_db)):
    """
    一个依赖项，返回一个预配置了request和db的日志记录函数
    """

    def logger(action: str, details: dict = None, user: User = None):
        # 如果未提供用户，尝试从请求上下文中获取
        # 注意：这需要 get_current_user 能够处理可选的token
        # 为了简化，我们假设在需要记录日志的路由中，user会被显式传递
        create_audit_log(
            db=db, request=request, action=action, details=details, user=user
        )

    return logger
