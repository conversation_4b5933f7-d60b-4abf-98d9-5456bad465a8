"""中间件测试
测试各种中间件的功能
"""

import pytest
import json
import time
from unittest.mock import Mock, patch, AsyncMock
from fastapi import Request, Response
from fastapi.responses import JSONResponse

# 使用try-except来处理导入，避免测试时的导入错误
try:
    from app.middleware.rate_limiter import RateLimiter, rate_limiter_middleware
    from app.middleware.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, error_handler_middleware
    from app.middleware.csrf_protection import CSRFProtection, csrf_protection_middleware
    from app.middleware.input_validation import InputValidator, input_validation_middleware
    from app.middleware.file_security import FileSecurityValidator
except ImportError:
    # 如果导入失败，创建模拟类
    RateLimiter = Mock
    ErrorHandler = Mock
    CSRFProtection = Mock
    InputValidator = Mock
    FileSecurityValidator = Mock
    rate_limiter_middleware = AsyncMock()
    error_handler_middleware = AsyncMock()
    csrf_protection_middleware = AsyncMock()
    input_validation_middleware = AsyncMock()


class TestRateLimiter:
    """限流器测试"""
    
    @pytest.fixture
    def rate_limiter(self, mock_redis):
        """创建限流器实例"""
        return RateLimiter(redis_client=mock_redis)
    
    @pytest.fixture
    def mock_request(self):
        """模拟请求"""
        request = Mock(spec=Request)
        request.client.host = "127.0.0.1"
        request.url.path = "/api/v1/test"
        request.method = "POST"
        request.state = Mock()
        request.state.user_id = None
        return request
    
    def test_get_client_identifier_with_user(self, rate_limiter, mock_request):
        """测试获取客户端标识符（有用户）"""
        mock_request.state.user_id = 123
        identifier = rate_limiter.get_client_identifier(mock_request)
        assert identifier == "user:123"
    
    def test_get_client_identifier_without_user(self, rate_limiter, mock_request):
        """测试获取客户端标识符（无用户）"""
        identifier = rate_limiter.get_client_identifier(mock_request)
        assert identifier == "ip:127.0.0.1"
    
    def test_generate_redis_key(self, rate_limiter):
        """测试生成Redis键"""
        key = rate_limiter.generate_redis_key("user:123", "/api/v1/test")
        assert key == "rate_limit:user:123:/api/v1/test"
    
    def test_get_rate_limit_for_endpoint(self, rate_limiter):
        """测试获取端点限制"""
        # 测试登录端点
        limit = rate_limiter.get_rate_limit_for_endpoint("/api/v1/auth/login")
        assert limit == (5, 300)  # 5次/5分钟
        
        # 测试默认端点
        limit = rate_limiter.get_rate_limit_for_endpoint("/api/v1/unknown")
        assert limit == (100, 3600)  # 默认限制
    
    @pytest.mark.asyncio
    async def test_check_rate_limit_allowed(self, rate_limiter, mock_request, mock_redis):
        """测试限流检查（允许）"""
        mock_redis.get.return_value = "5"  # 当前计数
        
        result = await rate_limiter.check_rate_limit(mock_request)
        assert result is None  # 允许通过
    
    @pytest.mark.asyncio
    async def test_check_rate_limit_blocked(self, rate_limiter, mock_request, mock_redis):
        """测试限流检查（阻止）"""
        mock_redis.get.return_value = "100"  # 超过限制
        
        result = await rate_limiter.check_rate_limit(mock_request)
        assert isinstance(result, JSONResponse)
        assert result.status_code == 429
    
    @pytest.mark.asyncio
    async def test_rate_limiter_middleware(self, mock_redis):
        """测试限流中间件"""
        # 模拟请求
        request = Mock(spec=Request)
        request.client.host = "127.0.0.1"
        request.url.path = "/api/v1/test"
        request.method = "POST"
        request.state = Mock()
        request.state.user_id = None
        
        # 模拟下一个处理器
        async def call_next(req):
            return Response("OK")
        
        # 模拟Redis返回允许的计数
        mock_redis.get.return_value = "5"
        
        with patch('app.middleware.rate_limiter.rate_limiter') as mock_rate_limiter:
            mock_rate_limiter.check_rate_limit.return_value = None
            
            response = await rate_limiter_middleware(request, call_next)
            assert response.body == b"OK"


class TestErrorHandler:
    """错误处理器测试"""
    
    @pytest.fixture
    def error_handler(self):
        """创建错误处理器实例"""
        return ErrorHandler()
    
    def test_generate_error_id(self, error_handler):
        """测试生成错误ID"""
        error_id = error_handler.generate_error_id()
        assert len(error_id) == 8
        assert error_id.isalnum()
    
    def test_sanitize_error_message(self, error_handler):
        """测试清理错误消息"""
        # 测试包含敏感信息的错误
        sensitive_error = "Database connection failed: password=secret123"
        sanitized = error_handler.sanitize_error_message(sensitive_error)
        assert "password" not in sanitized
        assert "secret123" not in sanitized
        
        # 测试普通错误
        normal_error = "User not found"
        sanitized = error_handler.sanitize_error_message(normal_error)
        assert sanitized == normal_error
    
    def test_create_error_response(self, error_handler):
        """测试创建错误响应"""
        response = error_handler.create_error_response(
            status_code=400,
            error_type="ValidationError",
            message="Invalid input",
            details={"field": "username"}
        )
        
        assert response.status_code == 400
        content = json.loads(response.body)
        assert content["error"] == "ValidationError"
        assert content["message"] == "Invalid input"
        assert "error_id" in content
        assert content["details"] == {"field": "username"}
    
    @pytest.mark.asyncio
    async def test_error_handler_middleware(self):
        """测试错误处理中间件"""
        request = Mock(spec=Request)
        
        # 模拟抛出异常的处理器
        async def call_next(req):
            raise ValueError("Test error")
        
        response = await error_handler_middleware(request, call_next)
        assert response.status_code == 500
        
        content = json.loads(response.body)
        assert content["error"] == "InternalServerError"
        assert "error_id" in content


class TestCSRFProtection:
    """CSRF保护测试"""
    
    @pytest.fixture
    def csrf_protection(self, mock_redis):
        """创建CSRF保护实例"""
        return CSRFProtection(redis_client=mock_redis)
    
    def test_generate_csrf_token(self, csrf_protection):
        """测试生成CSRF令牌"""
        token = csrf_protection.generate_csrf_token("session123")
        assert isinstance(token, str)
        assert len(token) > 50  # 令牌应该足够长
        assert ":" in token  # 应该包含分隔符
    
    def test_validate_csrf_token_valid(self, csrf_protection):
        """测试验证有效CSRF令牌"""
        session_id = "session123"
        token = csrf_protection.generate_csrf_token(session_id)
        
        # 验证刚生成的令牌
        is_valid = csrf_protection.validate_csrf_token(token, session_id)
        assert is_valid is True
    
    def test_validate_csrf_token_invalid(self, csrf_protection):
        """测试验证无效CSRF令牌"""
        # 测试格式错误的令牌
        is_valid = csrf_protection.validate_csrf_token("invalid_token", "session123")
        assert is_valid is False
        
        # 测试会话ID不匹配
        token = csrf_protection.generate_csrf_token("session123")
        is_valid = csrf_protection.validate_csrf_token(token, "different_session")
        assert is_valid is False
    
    def test_is_exempt_path(self, csrf_protection):
        """测试路径豁免检查"""
        # 测试豁免路径
        assert csrf_protection.is_exempt_path("/api/v1/auth/login") is True
        assert csrf_protection.is_exempt_path("/docs") is True
        assert csrf_protection.is_exempt_path("/static/css/style.css") is True
        
        # 测试非豁免路径
        assert csrf_protection.is_exempt_path("/api/v1/users") is False
    
    @pytest.mark.asyncio
    async def test_csrf_protection_middleware_get_request(self, mock_redis):
        """测试CSRF保护中间件（GET请求）"""
        request = Mock(spec=Request)
        request.method = "GET"
        request.url.path = "/api/v1/users"
        request.client.host = "127.0.0.1"
        request.state = Mock()
        request.cookies = {}
        
        async def call_next(req):
            response = Response("OK")
            response.headers = {}
            return response
        
        with patch('app.middleware.csrf_protection.csrf_protection') as mock_csrf:
            mock_csrf.validate_request.return_value = None
            mock_csrf.get_session_id.return_value = "session123"
            mock_csrf.generate_token_for_session.return_value = "csrf_token_123"
            mock_csrf.is_exempt_path.return_value = False
            
            response = await csrf_protection_middleware(request, call_next)
            assert response.status_code == 200


class TestInputValidation:
    """输入验证测试"""
    
    @pytest.fixture
    def input_validator(self):
        """创建输入验证器实例"""
        return InputValidator()
    
    def test_validate_request_data_valid(self, input_validator):
        """测试验证有效请求数据"""
        data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        is_valid, errors = input_validator.validate_request_data(
            "/api/v1/auth/register", data
        )
        assert is_valid is True
        assert len(errors) == 0
    
    def test_validate_request_data_invalid(self, input_validator):
        """测试验证无效请求数据"""
        data = {
            "username": "ab",  # 太短
            "email": "invalid_email",  # 无效邮箱
            "password": "123"  # 太短
        }
        
        is_valid, errors = input_validator.validate_request_data(
            "/api/v1/auth/register", data
        )
        assert is_valid is False
        assert len(errors) > 0
    
    def test_sanitize_request_data(self, input_validator):
        """测试清理请求数据"""
        data = {
            "title": "<script>alert('xss')</script>Test Title",
            "description": "<p>Safe HTML</p><script>alert('xss')</script>",
            "username": "user'; DROP TABLE users; --"
        }
        
        sanitized = input_validator.sanitize_request_data(data)
        
        # 检查XSS清理
        assert "<script>" not in sanitized["title"]
        assert "alert" not in sanitized["title"]
        
        # 检查SQL注入清理
        assert "DROP TABLE" not in sanitized["username"]
        assert "--" not in sanitized["username"]
    
    @pytest.mark.asyncio
    async def test_input_validation_middleware_valid(self):
        """测试输入验证中间件（有效输入）"""
        request = Mock(spec=Request)
        request.method = "POST"
        request.url.path = "/api/v1/test"
        request.headers = {"content-type": "application/json"}
        request.body = AsyncMock(return_value=b'{"username": "testuser"}')
        request.query_params = {}
        request.state = Mock()
        
        async def call_next(req):
            return Response("OK")
        
        with patch('app.middleware.input_validation.input_validator') as mock_validator:
            mock_validator.sanitize_request_data.return_value = {"username": "testuser"}
            mock_validator.validate_request_data.return_value = (True, [])
            
            response = await input_validation_middleware(request, call_next)
            assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_input_validation_middleware_invalid(self):
        """测试输入验证中间件（无效输入）"""
        request = Mock(spec=Request)
        request.method = "POST"
        request.url.path = "/api/v1/test"
        request.headers = {"content-type": "application/json"}
        request.body = AsyncMock(return_value=b'{"username": "ab"}')
        request.query_params = {}
        request.state = Mock()
        
        async def call_next(req):
            return Response("OK")
        
        with patch('app.middleware.input_validation.input_validator') as mock_validator:
            mock_validator.sanitize_request_data.return_value = {"username": "ab"}
            mock_validator.validate_request_data.return_value = (False, ["用户名太短"])
            
            response = await input_validation_middleware(request, call_next)
            assert response.status_code == 400
            
            content = json.loads(response.body)
            assert content["error"] == "Input validation failed"
            assert "用户名太短" in content["details"]


class TestFileSecurityValidator:
    """文件安全验证测试"""
    
    @pytest.fixture
    def file_validator(self):
        """创建文件安全验证器实例"""
        return FileSecurityValidator()
    
    def test_validate_file_type_allowed(self, file_validator):
        """测试验证允许的文件类型"""
        # 测试图片文件
        is_valid, error = file_validator.validate_file_type("test.jpg", "image/jpeg")
        assert is_valid is True
        assert error is None
        
        # 测试视频文件
        is_valid, error = file_validator.validate_file_type("test.mp4", "video/mp4")
        assert is_valid is True
        assert error is None
    
    def test_validate_file_type_not_allowed(self, file_validator):
        """测试验证不允许的文件类型"""
        is_valid, error = file_validator.validate_file_type("test.exe", "application/exe")
        assert is_valid is False
        assert "不支持的文件类型" in error
    
    def test_validate_file_size_valid(self, file_validator):
        """测试验证有效文件大小"""
        # 1MB文件
        is_valid, error = file_validator.validate_file_size(1024 * 1024, "image")
        assert is_valid is True
        assert error is None
    
    def test_validate_file_size_too_large(self, file_validator):
        """测试验证过大文件"""
        # 100MB图片文件（超过限制）
        is_valid, error = file_validator.validate_file_size(100 * 1024 * 1024, "image")
        assert is_valid is False
        assert "文件大小超过限制" in error
    
    def test_validate_filename_safe(self, file_validator):
        """测试验证安全文件名"""
        is_valid, error = file_validator.validate_filename("test_file.jpg")
        assert is_valid is True
        assert error is None
    
    def test_validate_filename_unsafe(self, file_validator):
        """测试验证不安全文件名"""
        # 包含路径遍历的文件名
        is_valid, error = file_validator.validate_filename("../../../etc/passwd")
        assert is_valid is False
        assert "文件名包含非法字符" in error
    
    def test_scan_malicious_content(self, file_validator, temp_file):
        """测试扫描恶意内容"""
        # 创建包含可疑内容的文件
        malicious_content = b"<?php system($_GET['cmd']); ?>"
        with open(temp_file, "wb") as f:
            f.write(malicious_content)
        
        has_malicious, threats = file_validator.scan_malicious_content(temp_file)
        assert has_malicious is True
        assert len(threats) > 0
    
    def test_validate_file_integrity_image(self, file_validator, temp_file):
        """测试验证图片文件完整性"""
        # 创建一个简单的测试图片文件（实际应用中需要真实的图片数据）
        with open(temp_file, "wb") as f:
            f.write(b"fake_image_data")
        
        # 由于是假数据，验证应该失败
        is_valid, error = file_validator.validate_file_integrity(temp_file, "image")
        assert is_valid is False
        assert "文件损坏或格式错误" in error
    
    def test_comprehensive_file_validation(self, file_validator, temp_file):
        """测试综合文件验证"""
        # 创建测试文件
        test_content = b"test file content"
        with open(temp_file, "wb") as f:
            f.write(test_content)
        
        # 模拟文件对象
        class MockFile:
            def __init__(self, filename, content_type, size):
                self.filename = filename
                self.content_type = content_type
                self.size = size
        
        mock_file = MockFile("test.txt", "text/plain", len(test_content))
        
        is_valid, errors = file_validator.validate_file(
            mock_file, temp_file, "document"
        )
        
        # 由于txt文件可能不在允许列表中，验证可能失败
        # 这里主要测试函数能正常执行
        assert isinstance(is_valid, bool)
        assert isinstance(errors, list)


@pytest.mark.integration
class TestMiddlewareIntegration:
    """中间件集成测试"""
    
    @pytest.mark.asyncio
    async def test_middleware_chain(self):
        """测试中间件链"""
        # 这里可以测试多个中间件的组合使用
        # 例如：限流 -> CSRF保护 -> 输入验证 -> 错误处理
        pass
    
    @pytest.mark.asyncio
    async def test_middleware_error_propagation(self):
        """测试中间件错误传播"""
        # 测试一个中间件抛出错误时，错误处理中间件是否能正确处理
        pass