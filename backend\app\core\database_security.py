"""
数据库安全模块 - MySQL版本
提供安全的MySQL数据库连接和查询构建器
"""

import pymysql
import logging
from contextlib import contextmanager
from typing import List, Dict, Any, Optional, Tuple, Generator
import threading
import time
from dataclasses import dataclass
from app.core.config import settings  # 导入全局设置

# 配置日志
logger = logging.getLogger(__name__)

# 数据库类型配置
USE_MYSQL = True  # 使用MySQL数据库

# MySQL数据库配置
MYSQL_CONFIG = {
    "host": settings.DB_HOST,
    "port": settings.DB_PORT,
    "user": settings.DB_USER,
    "password": settings.DB_PASSWORD,
    "database": settings.DB_NAME,
    "charset": "utf8mb4",
    "autocommit": True,
    "pool_size": settings.DB_POOL_SIZE,
    "max_overflow": settings.DB_MAX_OVERFLOW,
    "pool_timeout": settings.DB_POOL_TIMEOUT,
    "pool_recycle": settings.DB_POOL_RECYCLE,
    "pool_pre_ping": settings.DB_POOL_PRE_PING,
    "connect_timeout": settings.DB_CONNECT_TIMEOUT,
    "read_timeout": settings.DB_READ_TIMEOUT,
    "write_timeout": settings.DB_WRITE_TIMEOUT,
    "max_allowed_packet": 16 * 1024 * 1024,  # 16MB
    "sql_mode": "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO",
    "init_command": "SET SESSION sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'"
}
MAX_CONNECTIONS = settings.DB_POOL_SIZE
CONNECTION_TIMEOUT = settings.DB_CONNECT_TIMEOUT
CONNECTION_MAX_AGE = 3600  # 连接最大存活时间（秒）
CONNECTION_RECYCLE_TIME = 1800  # 连接回收时间（秒）


@dataclass
class QueryResult:
    """查询结果封装 - MySQL版本"""

    rows: List[Dict[str, Any]]  # MySQL结果为字典列表
    total_count: Optional[int] = None
    execution_time: float = 0.0


class SQLInjectionDetector:
    """SQL注入检测器"""

    DANGEROUS_PATTERNS = [
        r"';.*--",  # 注释注入
        r"union\s+select",  # UNION注入
        r"drop\s+table",  # 删除表
        r"delete\s+from.*where.*=.*or",  # OR注入
        r"insert\s+into.*values.*\(",  # 插入注入
        r"update.*set.*where.*=.*or",  # 更新注入
        r"exec\s*\(",  # 执行注入
        r"script\s*>",  # 脚本注入
    ]

    @classmethod
    def validate_query(cls, query: str, params: Tuple = ()) -> bool:
        """
        验证查询是否安全

        Args:
            query: SQL查询语句
            params: 查询参数

        Returns:
            bool: 是否安全

        Raises:
            SecurityError: 检测到SQL注入风险
        """
        import re

        # 检查危险模式
        query_lower = query.lower()
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, query_lower, re.IGNORECASE):
                raise SecurityError(f"检测到潜在SQL注入: {pattern}")

        # 检查参数占位符数量
        placeholder_count = query.count("?")
        if len(params) != placeholder_count:
            raise SecurityError(
                f"参数数量不匹配: 期望{placeholder_count}, 实际{len(params)}"
            )

        # 检查是否包含动态字符串拼接
        if "{" in query or "%s" in query or "%d" in query:
            raise SecurityError("查询包含动态字符串拼接，存在注入风险")

        return True


class SecurityError(Exception):
    """安全错误"""


class ConnectionPool:
    """安全的数据库连接池"""

    def __init__(self, max_connections: int = MAX_CONNECTIONS):
        self.max_connections = max_connections
        self._connections: List[Dict[str, Any]] = []  # 连接信息列表
        self._lock = threading.Lock()
        self._created_connections = 0
        self.connection_max_age = CONNECTION_MAX_AGE
        self.connection_recycle_time = CONNECTION_RECYCLE_TIME
        self._last_cleanup = time.time()

    def _create_connection(self) -> Dict[str, Any]:
        """创建新的MySQL数据库连接"""
        try:
            # 创建MySQL连接
            conn = pymysql.connect(**MYSQL_CONFIG)
            
            # 设置连接属性
            conn.autocommit(False)
            
            # 创建连接信息
            connection_info = {
                'connection': conn,
                'created_at': time.time(),
                'last_used': time.time(),
                'use_count': 0
            }
            
            logger.debug("创建MySQL连接")
            return connection_info
            
        except Exception as e:
            logger.error(f"创建数据库连接失败: {e}")
            raise SecurityError(f"数据库连接失败: {str(e)}")

    def _cleanup_connections(self) -> None:
        """
        清理过期和无效的连接
        """
        current_time = time.time()
        
        # 每5分钟执行一次清理
        if current_time - self._last_cleanup < 300:
            return
        
        with self._lock:
            valid_connections = []
            
            for conn_info in self._connections:
                conn = conn_info['connection']
                created_at = conn_info['created_at']
                last_used = conn_info['last_used']
                
                # 检查连接是否过期
                if (current_time - created_at > self.connection_max_age or
                    current_time - last_used > self.connection_recycle_time):
                    try:
                        conn.close()
                        self._created_connections -= 1
                        logger.debug("关闭过期连接")
                    except:
                        pass
                    continue
                
                # 检查连接是否有效
                try:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.close()
                    valid_connections.append(conn_info)
                except:
                    try:
                        conn.close()
                        self._created_connections -= 1
                        logger.debug("关闭无效连接")
                    except:
                        pass
            
            self._connections = valid_connections
            self._last_cleanup = current_time
    
    def _is_connection_healthy(self, conn: Any) -> bool:
        """
        检查连接是否健康
        
        Args:
            conn: 数据库连接
            
        Returns:
            bool: 连接是否健康
        """
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            return result is not None
        except Exception as e:
            logger.warning(f"连接健康检查失败: {e}")
            return False
    
    @contextmanager
    def get_connection(self) -> Generator[Any, None, None]:
        """
        获取数据库连接的上下文管理器

        Yields:
            Any: 数据库连接 (MySQL连接对象)

        Raises:
            SecurityError: 连接池耗尽或连接失败
        """
        # 定期清理连接
        self._cleanup_connections()
        
        conn_info = None
        conn = None
        
        try:
            with self._lock:
                # 尝试获取可用连接
                while self._connections:
                    conn_info = self._connections.pop()
                    conn = conn_info['connection']
                    
                    # 检查连接是否健康
                    if self._is_connection_healthy(conn):
                        # 更新使用信息
                        conn_info['last_used'] = time.time()
                        conn_info['use_count'] += 1
                        break
                    else:
                        # 连接无效，关闭并继续寻找
                        try:
                            conn.close()
                            self._created_connections -= 1
                        except:
                            pass
                        conn_info = None
                        conn = None
                
                # 如果没有可用连接，创建新连接
                if conn is None:
                    if self._created_connections < self.max_connections:
                        conn_info = self._create_connection()
                        conn = conn_info['connection']
                        self._created_connections += 1
                    else:
                        raise SecurityError("数据库连接池已耗尽")

            yield conn

        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            
            # 如果是连接错误，不要将连接放回池中
            if conn_info and "连接" in str(e).lower():
                try:
                    conn.close()
                    with self._lock:
                        self._created_connections -= 1
                except:
                    pass
                conn_info = None
            
            raise SecurityError(f"数据库连接错误: {str(e)}")
        finally:
            if conn_info and conn:
                try:
                    # 检查连接是否仍然健康
                    if self._is_connection_healthy(conn):
                        # 归还连接到池中
                        with self._lock:
                            if len(self._connections) < self.max_connections:
                                self._connections.append(conn_info)
                            else:
                                conn.close()
                                self._created_connections -= 1
                    else:
                        # 连接不健康，关闭它
                        conn.close()
                        with self._lock:
                            self._created_connections -= 1
                except Exception as e:
                    # 连接损坏，直接关闭
                    logger.warning(f"归还连接时出错: {e}")
                    try:
                        conn.close()
                    except:
                        pass
                    with self._lock:
                        self._created_connections -= 1


class SafeQueryBuilder:
    """安全的查询构建器"""

    def __init__(self, connection_pool: ConnectionPool):
        self.pool = connection_pool
        self.detector = SQLInjectionDetector()

    def _validate_sort_field(self, field: str, allowed_fields: List[str]) -> str:
        """验证排序字段"""
        if field not in allowed_fields:
            raise SecurityError(f"非法排序字段: {field}")
        return field

    def _validate_sort_order(self, order: str) -> str:
        """验证排序方向"""
        order = order.upper()
        if order not in ["ASC", "DESC"]:
            raise SecurityError(f"非法排序方向: {order}")
        return order

    def build_where_clause(self, conditions: Dict[str, Any]) -> Tuple[str, List[Any]]:
        """
        构建安全的WHERE子句

        Args:
            conditions: 查询条件字典

        Returns:
            Tuple[str, List[Any]]: WHERE子句和参数列表
        """
        where_parts = []
        params = []

        for field, value in conditions.items():
            if value is None:
                continue

            # 验证字段名（防止字段名注入）
            if not field.replace("_", "").isalnum():
                raise SecurityError(f"非法字段名: {field}")

            if isinstance(value, (list, tuple)):
                # IN查询
                placeholders = ",".join(["?" for _ in value])
                where_parts.append(f"{field} IN ({placeholders})")
                params.extend(value)
            elif (
                isinstance(value, str) and value.startswith("%") and value.endswith("%")
            ):
                # LIKE查询
                where_parts.append(f"{field} LIKE ?")
                params.append(value)
            else:
                # 等值查询
                where_parts.append(f"{field} = ?")
                params.append(value)

        where_clause = " AND ".join(where_parts) if where_parts else "1=1"
        return where_clause, params

    def execute_safe_query(
        self, query: str, params: Tuple = (), fetch_all: bool = True
    ) -> QueryResult:
        """
        执行安全的查询

        Args:
            query: SQL查询语句
            params: 查询参数
            fetch_all: 是否获取所有结果

        Returns:
            QueryResult: 查询结果

        Raises:
            SecurityError: 安全检查失败
        """
        start_time = time.time()

        # 安全检查
        self.detector.validate_query(query, params)

        with self.pool.get_connection() as conn:
            cursor = conn.cursor()

            try:
                cursor.execute(query, params)

                if fetch_all:
                    rows = cursor.fetchall()
                else:
                    row = cursor.fetchone()
                    rows = [row] if row else []

                execution_time = time.time() - start_time

                logger.info(
                    f"安全查询执行成功: {execution_time:.3f}s, 返回{len(rows)}行"
                )

                return QueryResult(rows=rows, execution_time=execution_time)

            except Exception as e:
                logger.error(f"查询执行失败: {str(e)}")
                raise SecurityError(f"查询执行失败: {str(e)}")

    def execute_safe_transaction(self, operations: List[Tuple[str, Tuple]]) -> bool:
        """
        执行安全的事务操作

        Args:
            operations: 操作列表，每个操作为(query, params)元组

        Returns:
            bool: 是否成功

        Raises:
            SecurityError: 事务执行失败
        """
        # 验证所有操作
        for query, params in operations:
            self.detector.validate_query(query, params)

        with self.pool.get_connection() as conn:
            cursor = conn.cursor()

            try:
                # 开始事务
                if USE_MYSQL:
                    conn.begin()  # MySQL使用begin()
                else:
                    cursor.execute("BEGIN TRANSACTION")  # SQLite使用SQL

                # 执行所有操作
                for query, params in operations:
                    cursor.execute(query, params)

                # 提交事务
                conn.commit()
                logger.info(f"事务执行成功: {len(operations)}个操作")
                return True

            except Exception as e:
                # 回滚事务
                conn.rollback()
                logger.error(f"事务执行失败: {str(e)}")
                raise SecurityError(f"事务执行失败: {str(e)}")


# 全局连接池实例
_connection_pool = None


def get_connection_pool() -> ConnectionPool:
    """获取全局连接池实例"""
    global _connection_pool
    if _connection_pool is None:
        _connection_pool = ConnectionPool()  # MySQL不需要数据库路径
    return _connection_pool


def get_safe_query_builder() -> SafeQueryBuilder:
    """获取安全查询构建器实例"""
    return SafeQueryBuilder(get_connection_pool())


# 导出的安全接口
__all__ = [
    "get_connection_pool",
    "get_safe_query_builder",
    "SafeQueryBuilder",
    "ConnectionPool",
    "SQLInjectionDetector",
    "SecurityError",
    "QueryResult",
]
