"""
基于角色的访问控制 (RBAC) 系统
🔒 证据链: 实现完整的权限控制系统，防止权限绕过攻击
"""

from enum import Enum
from functools import wraps
from typing import Dict, List, Set

from fastapi import Depends, HTTPException, status

from app.models import User
from app.utils.auth_deps import get_current_user


class Permission(str, Enum):
    """权限枚举"""

    # 用户管理权限
    USER_READ = "user:read"
    USER_WRITE = "user:write"
    USER_DELETE = "user:delete"
    USER_ADMIN = "user:admin"

    # 内容管理权限
    CONTENT_READ = "content:read"
    CONTENT_WRITE = "content:write"
    CONTENT_DELETE = "content:delete"
    CONTENT_MODERATE = "content:moderate"

    # 视频处理权限
    VIDEO_UPLOAD = "video:upload"
    VIDEO_PROCESS = "video:process"
    VIDEO_DOWNLOAD = "video:download"
    VIDEO_ADMIN = "video:admin"

    # 系统管理权限
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    SYSTEM_ADMIN = "system:admin"

    # AI服务权限
    AI_SERVICE_USE = "ai:use"
    AI_SERVICE_ADMIN = "ai:admin"


class Role(str, Enum):
    """角色枚举"""

    GUEST = "guest"  # 访客
    USER = "user"  # 普通用户
    MODERATOR = "moderator"  # 内容审核员
    ADMIN = "admin"  # 管理员
    SUPER_ADMIN = "super_admin"  # 超级管理员


# 角色权限映射
ROLE_PERMISSIONS: Dict[Role, Set[Permission]] = {
    Role.GUEST: {
        Permission.CONTENT_READ,
    },
    Role.USER: {
        Permission.USER_READ,
        Permission.CONTENT_READ,
        Permission.CONTENT_WRITE,
        Permission.VIDEO_UPLOAD,
        Permission.VIDEO_PROCESS,
        Permission.VIDEO_DOWNLOAD,
        Permission.AI_SERVICE_USE,
    },
    Role.MODERATOR: {
        Permission.USER_READ,
        Permission.CONTENT_READ,
        Permission.CONTENT_WRITE,
        Permission.CONTENT_MODERATE,
        Permission.CONTENT_DELETE,
        Permission.VIDEO_UPLOAD,
        Permission.VIDEO_PROCESS,
        Permission.VIDEO_DOWNLOAD,
        Permission.AI_SERVICE_USE,
    },
    Role.ADMIN: {
        Permission.USER_READ,
        Permission.USER_WRITE,
        Permission.USER_DELETE,
        Permission.CONTENT_READ,
        Permission.CONTENT_WRITE,
        Permission.CONTENT_DELETE,
        Permission.CONTENT_MODERATE,
        Permission.VIDEO_UPLOAD,
        Permission.VIDEO_PROCESS,
        Permission.VIDEO_DOWNLOAD,
        Permission.VIDEO_ADMIN,
        Permission.SYSTEM_CONFIG,
        Permission.SYSTEM_MONITOR,
        Permission.AI_SERVICE_USE,
        Permission.AI_SERVICE_ADMIN,
    },
    Role.SUPER_ADMIN: set(Permission),  # 所有权限
}


class RBACService:
    """RBAC权限服务"""

    @staticmethod
    def get_user_permissions(user: User) -> Set[Permission]:
        """获取用户权限"""
        # 🔒 证据链: 基于用户角色获取权限集合
        if not user or not user.is_active:
            return set()

        user_role = Role(user.role) if user.role else Role.GUEST
        return ROLE_PERMISSIONS.get(user_role, set())

    @staticmethod
    def has_permission(user: User, permission: Permission) -> bool:
        """检查用户是否有特定权限"""
        # 🔒 证据链: 严格的权限检查逻辑
        if not user or not user.is_active:
            return False

        user_permissions = RBACService.get_user_permissions(user)
        return permission in user_permissions

    @staticmethod
    def has_any_permission(user: User, permissions: List[Permission]) -> bool:
        """检查用户是否有任一权限"""
        if not user or not user.is_active:
            return False

        user_permissions = RBACService.get_user_permissions(user)
        return any(perm in user_permissions for perm in permissions)

    @staticmethod
    def has_all_permissions(user: User, permissions: List[Permission]) -> bool:
        """检查用户是否有所有权限"""
        if not user or not user.is_active:
            return False

        user_permissions = RBACService.get_user_permissions(user)
        return all(perm in user_permissions for perm in permissions)

    @staticmethod
    def can_access_resource(
        user: User, resource_owner_id: int, permission: Permission
    ) -> bool:
        """检查用户是否可以访问特定资源"""
        # 🔒 证据链: 资源级权限控制
        if not user or not user.is_active:
            return False

        # 资源所有者可以访问自己的资源
        if user.id == resource_owner_id:
            return True

        # 检查是否有管理权限
        return RBACService.has_permission(user, permission)


def require_permission(permission: Permission):
    """权限装饰器 - 要求特定权限"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 🔒 证据链: 从依赖注入中获取当前用户
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, User):
                    current_user = value
                    break

            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required",
                )

            if not RBACService.has_permission(current_user, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission denied: {permission.value} required",
                )

            return await func(*args, **kwargs)

        return wrapper

    return decorator


def require_any_permission(permissions: List[Permission]):
    """权限装饰器 - 要求任一权限"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, User):
                    current_user = value
                    break

            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required",
                )

            if not RBACService.has_any_permission(current_user, permissions):
                perm_names = [p.value for p in permissions]
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission denied: one of {perm_names} required",
                )

            return await func(*args, **kwargs)

        return wrapper

    return decorator


def require_role(role: Role):
    """角色装饰器 - 要求特定角色"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, User):
                    current_user = value
                    break

            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required",
                )

            if not current_user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User account is inactive",
                )

            user_role = Role(current_user.role) if current_user.role else Role.GUEST
            if user_role != role and user_role != Role.SUPER_ADMIN:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Role {role.value} required",
                )

            return await func(*args, **kwargs)

        return wrapper

    return decorator


# 依赖注入函数
async def get_current_user_with_permission(
    permission: Permission, current_user: User = Depends(get_current_user)
) -> User:
    """获取具有特定权限的当前用户"""
    if not RBACService.has_permission(current_user, permission):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Permission denied: {permission.value} required",
        )
    return current_user


def create_permission_dependency(permission: Permission):
    """创建权限依赖"""

    async def permission_dependency(
        current_user: User = Depends(get_current_user),
    ) -> User:
        if not RBACService.has_permission(current_user, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied: {permission.value} required",
            )
        return current_user

    return permission_dependency


# 常用权限依赖
RequireUserRead = Depends(create_permission_dependency(Permission.USER_READ))
RequireUserWrite = Depends(create_permission_dependency(Permission.USER_WRITE))
RequireContentWrite = Depends(create_permission_dependency(Permission.CONTENT_WRITE))
RequireContentModerate = Depends(
    create_permission_dependency(Permission.CONTENT_MODERATE)
)
RequireVideoUpload = Depends(create_permission_dependency(Permission.VIDEO_UPLOAD))
RequireSystemAdmin = Depends(create_permission_dependency(Permission.SYSTEM_ADMIN))


# 权限检查工具函数
def check_resource_access(
    user: User, resource_owner_id: int, permission: Permission
) -> bool:
    """检查资源访问权限"""
    return RBACService.can_access_resource(user, resource_owner_id, permission)


def get_user_role_info(user: User) -> Dict:
    """获取用户角色信息"""
    if not user:
        return {"role": Role.GUEST.value, "permissions": []}

    user_role = Role(user.role) if user.role else Role.GUEST
    permissions = RBACService.get_user_permissions(user)

    return {
        "role": user_role.value,
        "permissions": [p.value for p in permissions],
        "is_admin": user_role in [Role.ADMIN, Role.SUPER_ADMIN],
        "is_active": user.is_active if user else False,
    }
