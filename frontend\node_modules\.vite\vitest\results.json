{"version": "1.6.1", "results": [[":src/tests/performance.test.ts", {"duration": 1440, "failed": true}], [":src/tests/integration.test.ts", {"duration": 68, "failed": true}], [":tests/unit/composables/useResponsiveLayout.test.ts", {"duration": 171, "failed": true}], [":tests/integration/layout-system.test.ts", {"duration": 0, "failed": true}], [":src/tests/api.test.ts", {"duration": 26, "failed": false}], [":tests/unit/utils/test-helpers.test.ts", {"duration": 8, "failed": true}]]}