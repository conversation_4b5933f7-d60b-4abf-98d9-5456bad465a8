{"result": [{"scriptId": "866", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/tests/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49811, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49811, "count": 3}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 577, "endOffset": 946, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 981, "endOffset": 996, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 999, "endOffset": 1016, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1019, "endOffset": 1037, "count": 0}], "isBlockCoverage": false}, {"functionName": "observe", "ranges": [{"startOffset": 1175, "endOffset": 1190, "count": 0}], "isBlockCoverage": false}, {"functionName": "unobserve", "ranges": [{"startOffset": 1193, "endOffset": 1210, "count": 0}], "isBlockCoverage": false}, {"functionName": "disconnect", "ranges": [{"startOffset": 1213, "endOffset": 1231, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3284, "endOffset": 3354, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3458, "endOffset": 4555, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4665, "endOffset": 6533, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7131, "endOffset": 7312, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 7339, "endOffset": 8381, "count": 1}], "isBlockCoverage": false}, {"functionName": "getVoices", "ranges": [{"startOffset": 7400, "endOffset": 7745, "count": 0}], "isBlockCoverage": false}, {"functionName": "speak", "ranges": [{"startOffset": 7748, "endOffset": 8170, "count": 0}], "isBlockCoverage": false}, {"functionName": "cancel", "ranges": [{"startOffset": 8173, "endOffset": 8240, "count": 0}], "isBlockCoverage": false}, {"functionName": "pause", "ranges": [{"startOffset": 8243, "endOffset": 8280, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 8283, "endOffset": 8322, "count": 0}], "isBlockCoverage": false}, {"functionName": "addEventListener", "ranges": [{"startOffset": 8325, "endOffset": 8349, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEventListener", "ranges": [{"startOffset": 8352, "endOffset": 8379, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 8633, "endOffset": 9480, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 9703, "endOffset": 11003, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTracks", "ranges": [{"startOffset": 11429, "endOffset": 11618, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAudioTracks", "ranges": [{"startOffset": 11638, "endOffset": 11833, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVideoTracks", "ranges": [{"startOffset": 11853, "endOffset": 11861, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTest", "ranges": [{"startOffset": 13502, "endOffset": 13920, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14018, "endOffset": 14043, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupTest", "ranges": [{"startOffset": 14047, "endOffset": 14164, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14264, "endOffset": 14291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14400, "endOffset": 14432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14542, "endOffset": 14576, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14678, "endOffset": 14704, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14810, "endOffset": 14840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14940, "endOffset": 14964, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15001, "endOffset": 15073, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15108, "endOffset": 15177, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "953", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/tests/integration/layout-system.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37720, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37720, "count": 1}, {"startOffset": 916, "endOffset": 37719, "count": 0}], "isBlockCoverage": true}, {"functionName": "createTestRouter", "ranges": [{"startOffset": 1286, "endOffset": 1654, "count": 0}], "isBlockCoverage": false}, {"functionName": "mockWindowSize", "ranges": [{"startOffset": 1679, "endOffset": 1933, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1978, "endOffset": 12538, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "956", "url": "file:///D:/%E4%BA%8C%E5%88%9B/%E4%BA%8C%E5%88%9B%E7%9F%AD%E8%A7%86%E9%A2%91%E5%88%86%E5%8F%91/frontend/src/components/layout/UnifiedLayout.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 42238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 42238, "count": 1}, {"startOffset": 857, "endOffset": 42237, "count": 0}], "isBlockCoverage": true}, {"functionName": "setup", "ranges": [{"startOffset": 1589, "endOffset": 3344, "count": 0}], "isBlockCoverage": false}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 4108, "endOffset": 23216, "count": 0}], "isBlockCoverage": false}]}]}