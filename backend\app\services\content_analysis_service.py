#!/usr/bin/env python3
"""
AI视频内容创作系统 - 内容分析服务
提供AI内容分析、合规检测、推荐算法等功能
"""

import hashlib
import json
import logging
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ContentType(str, Enum):
    """内容类型"""

    VIDEO = "video"
    AUDIO = "audio"
    TEXT = "text"
    IMAGE = "image"


class ComplianceLevel(str, Enum):
    """合规等级"""

    SAFE = "safe"  # 安全内容
    WARNING = "warning"  # 需要警告
    RESTRICTED = "restricted"  # 受限内容
    BLOCKED = "blocked"  # 禁止内容


class ContentAnalysisResult(BaseModel):
    """内容分析结果"""

    content_id: str = Field(..., description="内容ID")
    content_type: ContentType = Field(..., description="内容类型")
    title: Optional[str] = Field(None, description="内容标题")
    description: Optional[str] = Field(None, description="内容描述")

    # 合规检测
    compliance_level: ComplianceLevel = Field(..., description="合规等级")
    compliance_score: float = Field(..., description="合规评分 0-1")
    compliance_reasons: List[str] = Field(
        default_factory=list, description="合规问题原因"
    )

    # 内容质量
    quality_score: float = Field(..., description="质量评分 0-1")
    engagement_score: float = Field(..., description="互动潜力评分 0-1")

    # 内容标签
    tags: List[str] = Field(default_factory=list, description="内容标签")
    categories: List[str] = Field(default_factory=list, description="内容分类")

    # 分析时间
    analyzed_at: datetime = Field(default_factory=datetime.now)

    # 推荐信息
    recommended_platforms: List[str] = Field(
        default_factory=list, description="推荐发布平台"
    )
    optimal_publish_time: Optional[str] = Field(None, description="最佳发布时间")


class ContentAnalysisService:
    """内容分析服务"""

    def __init__(self, cache_dir: str = "analysis_cache"):
        self.logger = logging.getLogger(__name__)
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

        # 分析结果缓存
        self.analysis_cache: Dict[str, ContentAnalysisResult] = {}

        # 加载缓存
        self._load_cache()

        # 预定义的规则和模板
        self._init_analysis_rules()

    def _load_cache(self):
        """加载分析缓存"""
        cache_file = self.cache_dir / "analysis_cache.json"
        if cache_file.exists():
            try:
                with open(cache_file, "r", encoding="utf-8") as f:
                    cache_data = json.load(f)

                for content_id, data in cache_data.items():
                    self.analysis_cache[content_id] = ContentAnalysisResult.parse_obj(
                        data
                    )

                self.logger.info(f"加载了 {len(self.analysis_cache)} 个缓存结果")
            except Exception as e:
                self.logger.error(f"加载分析缓存失败: {e}")

    def _save_cache(self):
        """保存分析缓存"""
        cache_file = self.cache_dir / "analysis_cache.json"
        try:
            cache_data = {
                content_id: result.dict()
                for content_id, result in self.analysis_cache.items()
            }

            with open(cache_file, "w", encoding="utf-8") as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2, default=str)

            self.logger.debug("分析缓存已保存")
        except Exception as e:
            self.logger.error(f"保存分析缓存失败: {e}")

    def _init_analysis_rules(self):
        """初始化分析规则"""
        # 敏感词列表（示例）
        self.sensitive_words = [
            "暴力",
            "血腥",
            "政治敏感",
            "不当内容",
            "违法",
            "危险行为",
            "仇恨言论",
        ]

        # 高质量内容特征
        self.quality_indicators = [
            "高清画质",
            "专业制作",
            "原创内容",
            "教育价值",
            "娱乐性强",
            "信息丰富",
            "制作精良",
        ]

        # 平台推荐规则
        self.platform_rules = {
            "抖音": {
                "preferred_duration": (15, 60),
                "good_tags": ["搞笑", "教育", "美食", "旅行"],
                "peak_hours": ["19:00-22:00"],
            },
            "B站": {
                "preferred_duration": (180, 1800),
                "good_tags": ["科技", "游戏", "动画", "知识"],
                "peak_hours": ["20:00-23:00"],
            },
            "快手": {
                "preferred_duration": (15, 180),
                "good_tags": ["生活", "搞笑", "音乐", "舞蹈"],
                "peak_hours": ["18:00-21:00"],
            },
        }

    def _generate_content_id(self, content_data: str) -> str:
        """生成内容ID"""
        return hashlib.md5(content_data.encode()).hexdigest()

    def _check_compliance(self, title: str, description: str) -> tuple:
        """检查内容合规性"""
        text = f"{title} {description}".lower()

        # 检查敏感词
        violations = []
        for word in self.sensitive_words:
            if word in text:
                violations.append(f"包含敏感词: {word}")

        # 根据违规数量确定合规等级
        if not violations:
            return ComplianceLevel.SAFE, 1.0, []
        elif len(violations) == 1:
            return ComplianceLevel.WARNING, 0.7, violations
        elif len(violations) <= 3:
            return ComplianceLevel.RESTRICTED, 0.4, violations
        else:
            return ComplianceLevel.BLOCKED, 0.1, violations

    def _calculate_quality_score(self, title: str, description: str) -> float:
        """计算内容质量分数"""
        text = f"{title} {description}".lower()
        score = 0.5  # 基础分数

        # 检查质量指标
        quality_count = 0
        for indicator in self.quality_indicators:
            if indicator in text:
                quality_count += 1

        # 根据质量指标调整分数
        score += min(quality_count * 0.1, 0.4)

        # 根据内容长度调整
        content_length = len(text)
        if 50 <= content_length <= 500:
            score += 0.1
        elif content_length > 500:
            score += 0.05

        return min(score, 1.0)

    def _calculate_engagement_score(self, title: str, tags: List[str]) -> float:
        """计算互动潜力分数"""
        score = 0.5  # 基础分数

        # 标题吸引力
        attractive_words = ["震撼", "惊艳", "必看", "独家", "首发", "爆料"]
        for word in attractive_words:
            if word in title:
                score += 0.1

        # 标签相关性
        popular_tags = ["热门", "推荐", "精选", "原创", "独家"]
        tag_score = sum(0.05 for tag in tags if tag in popular_tags)
        score += min(tag_score, 0.2)

        return min(score, 1.0)

    def _recommend_platforms(
        self,
        content_type: ContentType,
        tags: List[str],
        duration: float = None,
    ) -> List[str]:
        """推荐发布平台"""
        recommendations = []

        for platform, rules in self.platform_rules.items():
            score = 0

            # 检查时长匹配
            if duration and "preferred_duration" in rules:
                min_dur, max_dur = rules["preferred_duration"]
                if min_dur <= duration <= max_dur:
                    score += 0.4
                elif abs(duration - min_dur) <= 30 or abs(duration - max_dur) <= 30:
                    score += 0.2

            # 检查标签匹配
            tag_matches = set(tags) & set(rules.get("good_tags", []))
            score += len(tag_matches) * 0.15

            # 如果评分足够高，推荐该平台
            if score >= 0.3:
                recommendations.append(platform)

        return recommendations

    def analyze_content(
        self,
        content_type: ContentType,
        title: str,
        description: str = "",
        tags: List[str] = None,
        duration: float = None,
    ) -> ContentAnalysisResult:
        """分析内容"""
        if tags is None:
            tags = []

        # 生成内容ID
        content_data = f"{content_type.value}:{title}:{description}"
        content_id = self._generate_content_id(content_data)

        # 检查缓存
        if content_id in self.analysis_cache:
            self.logger.debug(f"使用缓存的分析结果: {content_id}")
            return self.analysis_cache[content_id]

        # 执行分析
        self.logger.info(f"开始分析内容: {title}")

        # 合规检测
        compliance_level, compliance_score, compliance_reasons = self._check_compliance(
            title, description
        )

        # 质量评分
        quality_score = self._calculate_quality_score(title, description)

        # 互动潜力评分
        engagement_score = self._calculate_engagement_score(title, tags)

        # 内容分类（简化版本）
        categories = []
        if any(word in title.lower() for word in ["教育", "学习", "知识"]):
            categories.append("教育")
        if any(word in title.lower() for word in ["搞笑", "幽默", "娱乐"]):
            categories.append("娱乐")
        if any(word in title.lower() for word in ["美食", "料理", "食谱"]):
            categories.append("美食")
        if any(word in title.lower() for word in ["旅行", "旅游", "风景"]):
            categories.append("旅行")

        # 平台推荐
        recommended_platforms = self._recommend_platforms(content_type, tags, duration)

        # 创建分析结果
        result = ContentAnalysisResult(
            content_id=content_id,
            content_type=content_type,
            title=title,
            description=description,
            compliance_level=compliance_level,
            compliance_score=compliance_score,
            compliance_reasons=compliance_reasons,
            quality_score=quality_score,
            engagement_score=engagement_score,
            tags=tags,
            categories=categories,
            recommended_platforms=recommended_platforms,
            optimal_publish_time="19:00-21:00",  # 默认黄金时段
        )

        # 缓存结果
        self.analysis_cache[content_id] = result
        self._save_cache()

        self.logger.info(f"内容分析完成: {content_id}")
        return result

    def get_analysis_by_id(self, content_id: str) -> Optional[ContentAnalysisResult]:
        """通过ID获取分析结果"""
        return self.analysis_cache.get(content_id)

    def get_all_analyses(self) -> List[ContentAnalysisResult]:
        """获取所有分析结果"""
        return list(self.analysis_cache.values())

    def clear_cache(self):
        """清空分析缓存"""
        self.analysis_cache.clear()
        cache_file = self.cache_dir / "analysis_cache.json"
        if cache_file.exists():
            cache_file.unlink()
        self.logger.info("分析缓存已清空")

    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        analyses = list(self.analysis_cache.values())

        if not analyses:
            return {
                "total_analyses": 0,
                "compliance_distribution": {},
                "average_quality_score": 0,
                "average_engagement_score": 0,
                "content_type_distribution": {},
                "platform_recommendations": {},
            }

        # 合规分布
        compliance_dist = {}
        for level in ComplianceLevel:
            compliance_dist[level.value] = sum(
                1 for a in analyses if a.compliance_level == level
            )

        # 内容类型分布
        content_type_dist = {}
        for content_type in ContentType:
            content_type_dist[content_type.value] = sum(
                1 for a in analyses if a.content_type == content_type
            )

        # 平台推荐统计
        platform_recommendations = {}
        for analysis in analyses:
            for platform in analysis.recommended_platforms:
                platform_recommendations[platform] = (
                    platform_recommendations.get(platform, 0) + 1
                )

        return {
            "total_analyses": len(analyses),
            "compliance_distribution": compliance_dist,
            "average_quality_score": sum(a.quality_score for a in analyses)
            / len(analyses),
            "average_engagement_score": sum(a.engagement_score for a in analyses)
            / len(analyses),
            "content_type_distribution": content_type_dist,
            "platform_recommendations": platform_recommendations,
        }


# 全局服务实例
content_analysis_service = ContentAnalysisService()
