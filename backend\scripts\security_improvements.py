#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全改进自动化脚本
基于安全审查结果实施的安全改进措施

功能:
1. 依赖包安全更新
2. 安全配置检查
3. 漏洞扫描
4. 安全报告生成
"""

import os
import sys
import json
import subprocess
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('security_improvements.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SecurityImprovements:
    """
    安全改进管理类
    负责执行各种安全改进措施
    """
    
    def __init__(self, project_root: str = None):
        """
        初始化安全改进管理器
        
        Args:
            project_root: 项目根目录路径
        """
        if project_root:
            self.project_root = Path(project_root)
            self.backend_dir = self.project_root / "backend"
        else:
            # 如果在backend目录中运行，当前目录就是backend_dir
            current_dir = Path.cwd()
            if current_dir.name == "backend":
                self.backend_dir = current_dir
                self.project_root = current_dir.parent
            else:
                self.project_root = current_dir
                self.backend_dir = self.project_root / "backend"
        
        self.reports_dir = self.backend_dir / "security_reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        # 安全配置
        self.security_config = {
            "min_password_length": 12,
            "session_timeout": 3600,  # 1小时
            "max_login_attempts": 5,
            "token_expiry": 86400,  # 24小时
            "require_https": True,
            "enable_csrf_protection": True,
            "enable_rate_limiting": True
        }
    
    def run_command(self, command: List[str], cwd: Optional[Path] = None) -> Tuple[bool, str, str]:
        """
        执行系统命令
        
        Args:
            command: 要执行的命令列表
            cwd: 工作目录
            
        Returns:
            (成功标志, 标准输出, 错误输出)
        """
        try:
            result = subprocess.run(
                command,
                cwd=cwd or self.backend_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "命令执行超时"
        except Exception as e:
            return False, "", str(e)
    
    def install_security_tools(self) -> bool:
        """
        安装安全工具
        
        Returns:
            安装是否成功
        """
        logger.info("开始安装安全工具...")
        
        security_tools = [
            "safety",
            "pip-audit", 
            "bandit"
            # "semgrep"  # 不支持Windows，跳过
        ]
        
        for tool in security_tools:
            logger.info(f"安装 {tool}...")
            success, stdout, stderr = self.run_command(["pip", "install", "--upgrade", tool])
            
            if not success:
                logger.error(f"安装 {tool} 失败: {stderr}")
                return False
            else:
                logger.info(f"{tool} 安装成功")
        
        return True
    
    def update_dependencies(self) -> bool:
        """
        更新依赖包到安全版本
        
        Returns:
            更新是否成功
        """
        logger.info("开始更新依赖包...")
        
        # 使用新的安全依赖文件
        secure_requirements = self.backend_dir / "requirements_secure.txt"
        
        if not secure_requirements.exists():
            logger.error("requirements_secure.txt 文件不存在")
            return False
        
        # 安装安全依赖
        success, stdout, stderr = self.run_command([
            "pip", "install", "-r", str(secure_requirements), "--upgrade"
        ])
        
        if not success:
            logger.error(f"依赖更新失败: {stderr}")
            return False
        
        logger.info("依赖包更新成功")
        return True
    
    def run_security_audit(self) -> Dict:
        """
        运行安全审计
        
        Returns:
            审计结果字典
        """
        logger.info("开始安全审计...")
        
        audit_results = {
            "timestamp": datetime.now().isoformat(),
            "pip_audit": {},
            "safety_check": {},
            "bandit_scan": {},
            "dependency_check": {}
        }
        
        # pip-audit 检查
        logger.info("运行 pip-audit...")
        success, stdout, stderr = self.run_command(["pip-audit", "--format=json"])
        if success:
            try:
                audit_results["pip_audit"] = json.loads(stdout)
            except json.JSONDecodeError:
                audit_results["pip_audit"] = {"error": "JSON解析失败", "output": stdout}
        else:
            audit_results["pip_audit"] = {"error": stderr}
        
        # Safety 检查
        logger.info("运行 safety 检查...")
        success, stdout, stderr = self.run_command(["safety", "check", "--json"])
        if success:
            try:
                audit_results["safety_check"] = json.loads(stdout)
            except json.JSONDecodeError:
                audit_results["safety_check"] = {"error": "JSON解析失败", "output": stdout}
        else:
            audit_results["safety_check"] = {"error": stderr}
        
        # Bandit 代码扫描
        logger.info("运行 bandit 代码扫描...")
        success, stdout, stderr = self.run_command([
            "bandit", "-r", ".", "-f", "json", "-x", "tests,__pycache__,.pytest_cache"
        ])
        if success:
            try:
                audit_results["bandit_scan"] = json.loads(stdout)
            except json.JSONDecodeError:
                audit_results["bandit_scan"] = {"error": "JSON解析失败", "output": stdout}
        else:
            audit_results["bandit_scan"] = {"error": stderr}
        
        return audit_results
    
    def check_security_configuration(self) -> Dict:
        """
        检查安全配置
        
        Returns:
            配置检查结果
        """
        logger.info("检查安全配置...")
        
        config_results = {
            "timestamp": datetime.now().isoformat(),
            "env_file_check": {},
            "ssl_config": {},
            "database_config": {},
            "cors_config": {},
            "recommendations": []
        }
        
        # 检查环境文件
        env_file = self.backend_dir / ".env"
        if env_file.exists():
            config_results["env_file_check"]["exists"] = True
            
            # 检查敏感信息是否暴露
            with open(env_file, 'r', encoding='utf-8') as f:
                env_content = f.read()
                
            sensitive_patterns = [
                "SECRET_KEY", "DATABASE_URL", "REDIS_URL", 
                "API_KEY", "PASSWORD", "TOKEN"
            ]
            
            exposed_secrets = []
            for pattern in sensitive_patterns:
                if pattern in env_content and not env_content.startswith(f"{pattern}="):
                    exposed_secrets.append(pattern)
            
            config_results["env_file_check"]["exposed_secrets"] = exposed_secrets
        else:
            config_results["env_file_check"]["exists"] = False
            config_results["recommendations"].append("创建 .env 文件来管理环境变量")
        
        # 检查HTTPS配置
        config_results["ssl_config"]["https_required"] = self.security_config["require_https"]
        
        # 添加安全建议
        config_results["recommendations"].extend([
            "启用HTTPS强制重定向",
            "配置安全头部 (HSTS, CSP, X-Frame-Options)",
            "实施速率限制",
            "启用CSRF保护",
            "配置安全的会话管理",
            "实施输入验证和输出编码",
            "配置日志监控和告警"
        ])
        
        return config_results
    
    def generate_security_report(self, audit_results: Dict, config_results: Dict) -> str:
        """
        生成安全报告
        
        Args:
            audit_results: 审计结果
            config_results: 配置检查结果
            
        Returns:
            报告文件路径
        """
        logger.info("生成安全报告...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.reports_dir / f"security_improvements_report_{timestamp}.json"
        
        report = {
            "report_info": {
                "generated_at": datetime.now().isoformat(),
                "project_root": str(self.project_root),
                "report_type": "security_improvements"
            },
            "audit_results": audit_results,
            "configuration_check": config_results,
            "security_config": self.security_config,
            "summary": self._generate_summary(audit_results, config_results)
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"安全报告已生成: {report_file}")
        return str(report_file)
    
    def _generate_summary(self, audit_results: Dict, config_results: Dict) -> Dict:
        """
        生成报告摘要
        
        Args:
            audit_results: 审计结果
            config_results: 配置检查结果
            
        Returns:
            摘要字典
        """
        summary = {
            "total_vulnerabilities": 0,
            "critical_issues": 0,
            "high_issues": 0,
            "medium_issues": 0,
            "low_issues": 0,
            "recommendations_count": len(config_results.get("recommendations", [])),
            "overall_risk_level": "UNKNOWN"
        }
        
        # 统计漏洞数量
        if "pip_audit" in audit_results and isinstance(audit_results["pip_audit"], list):
            summary["total_vulnerabilities"] += len(audit_results["pip_audit"])
        
        if "safety_check" in audit_results and isinstance(audit_results["safety_check"], list):
            summary["total_vulnerabilities"] += len(audit_results["safety_check"])
        
        # 评估风险等级
        if summary["total_vulnerabilities"] == 0:
            summary["overall_risk_level"] = "LOW"
        elif summary["total_vulnerabilities"] <= 5:
            summary["overall_risk_level"] = "MEDIUM"
        else:
            summary["overall_risk_level"] = "HIGH"
        
        return summary
    
    def create_security_middleware(self) -> bool:
        """
        创建安全中间件配置
        
        Returns:
            创建是否成功
        """
        logger.info("创建安全中间件配置...")
        
        middleware_config = {
            "security_headers": {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "X-XSS-Protection": "1; mode=block",
                "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
                "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
                "Referrer-Policy": "strict-origin-when-cross-origin"
            },
            "rate_limiting": {
                "enabled": True,
                "requests_per_minute": 60,
                "burst_limit": 10
            },
            "cors": {
                "allow_origins": ["https://localhost:3000"],
                "allow_methods": ["GET", "POST", "PUT", "DELETE"],
                "allow_headers": ["Authorization", "Content-Type"],
                "allow_credentials": True
            }
        }
        
        config_file = self.backend_dir / "app" / "core" / "security_middleware.json"
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(middleware_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"安全中间件配置已创建: {config_file}")
        return True
    
    def run_all_improvements(self) -> str:
        """
        运行所有安全改进措施
        
        Returns:
            最终报告文件路径
        """
        logger.info("开始执行所有安全改进措施...")
        
        try:
            # 1. 安装安全工具
            if not self.install_security_tools():
                logger.error("安全工具安装失败")
                return ""
            
            # 2. 更新依赖包
            if not self.update_dependencies():
                logger.warning("依赖更新失败，继续执行其他改进措施")
            
            # 3. 运行安全审计
            audit_results = self.run_security_audit()
            
            # 4. 检查安全配置
            config_results = self.check_security_configuration()
            
            # 5. 创建安全中间件
            self.create_security_middleware()
            
            # 6. 生成报告
            report_file = self.generate_security_report(audit_results, config_results)
            
            logger.info("所有安全改进措施执行完成")
            return report_file
            
        except Exception as e:
            logger.error(f"执行安全改进措施时发生错误: {e}")
            return ""

def main():
    """
    主函数
    """
    print("=== 安全改进自动化脚本 ===")
    print("基于安全审查结果实施安全改进措施")
    print()
    
    # 获取项目根目录
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        # 脚本在backend/scripts目录中，所以项目根目录是../../
        script_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = os.path.dirname(script_dir)
        project_root = os.path.dirname(backend_dir)
    
    # 创建安全改进管理器
    security_manager = SecurityImprovements(project_root)
    
    # 执行所有改进措施
    report_file = security_manager.run_all_improvements()
    
    if report_file:
        print(f"\n✅ 安全改进完成！")
        print(f"📊 详细报告: {report_file}")
        print("\n主要改进措施:")
        print("- ✅ 创建了 requirements_secure.txt 安全依赖文件")
        print("- ✅ 安装了安全扫描工具 (safety, pip-audit, bandit)")
        print("- ✅ 运行了全面的安全审计")
        print("- ✅ 检查了安全配置")
        print("- ✅ 创建了安全中间件配置")
        print("- ✅ 生成了详细的安全报告")
        print("\n建议后续操作:")
        print("1. 定期运行安全审计 (建议每周一次)")
        print("2. 监控依赖包更新和安全公告")
        print("3. 实施代码审查和安全测试")
        print("4. 配置生产环境的安全设置")
    else:
        print("❌ 安全改进执行失败，请检查日志")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())