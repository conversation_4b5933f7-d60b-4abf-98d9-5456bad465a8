#!/usr/bin/env python3
"""
集成系统测试和优化脚本
"""

import importlib.util
import sys
from pathlib import Path


def test_basic_imports():
    """测试基础导入"""
    print("=" * 50)
    print("🔍 测试基础导入")
    print("=" * 50)

    try:
        # 测试标准库
        pass

        print("✅ 标准库导入成功")

        # 测试第三方库

        print("✅ requests库导入成功")

        print("✅ pydantic库导入成功")

        return True
    except Exception as e:
        print(f"❌ 基础导入失败: {e}")
        return False


def test_service_import():
    """测试服务导入"""
    print("\n" + "=" * 50)
    print("🔍 测试服务导入")
    print("=" * 50)

    try:
        # 添加项目路径
        current_dir = Path(__file__).parent
        backend_dir = current_dir.parent
        sys.path.insert(0, str(backend_dir))

        print(f"添加路径: {backend_dir}")

        # 测试服务导入
        from app.services.open_source_integration_service import (
            OpenSourceIntegrationService,
        )

        print("✅ 集成服务类导入成功")

        # 创建服务实例
        service = OpenSourceIntegrationService()
        print("✅ 服务实例创建成功")

        return service
    except Exception as e:
        print(f"❌ 服务导入失败: {e}")
        import traceback

        traceback.print_exc()
        return None


def test_service_methods(service):
    """测试服务方法"""
    print("\n" + "=" * 50)
    print("🔍 测试服务方法")
    print("=" * 50)

    try:
        # 测试获取项目列表
        projects = service.get_all_projects()
        print(f"✅ 获取项目列表成功: {len(projects)} 个项目")

        # 显示前5个项目
        for i, (name, project) in enumerate(projects.items()):
            if i >= 5:
                break
            print(f"  {i+1}. {name}: {project.project_type.value}")

        # 测试状态摘要
        summary = service.get_project_status_summary()
        print(f"✅ 状态摘要生成成功: {summary['total_projects']} 个项目")

        return True
    except Exception as e:
        print(f"❌ 服务方法测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_package_detection():
    """测试包检测功能"""
    print("\n" + "=" * 50)
    print("🔍 测试包检测功能")
    print("=" * 50)

    test_packages = [
        "requests",
        "pydantic",
        "fastapi",
        "yt-dlp",
        "moviepy",
        "opencv-python",
    ]

    installed_count = 0

    for package in test_packages:
        try:
            importlib.import_module(package.replace("-", "_"))
            print(f"✅ {package}: 已安装")
            installed_count += 1
        except ImportError:
            print(f"❌ {package}: 未安装")

    print(f"\n📊 检测结果: {installed_count}/{len(test_packages)} 包已安装")
    return installed_count


def test_ollama_service():
    """测试Ollama服务连接"""
    print("\n" + "=" * 50)
    print("🔍 测试Ollama服务连接")
    print("=" * 50)

    try:
        import requests

        response = requests.get("http://127.0.0.1:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama服务运行正常")
            models = response.json().get("models", [])
            print(f"✅ 已安装模型数量: {len(models)}")
            return True
        else:
            print(f"⚠️ Ollama服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama服务不可用: {e}")
        return False


def generate_optimization_report(test_results):
    """生成优化建议报告"""
    print("\n" + "=" * 60)
    print("📊 集成系统测试报告与优化建议")
    print("=" * 60)

    print(f"基础导入测试: {'✅ 通过' if test_results['basic_imports'] else '❌ 失败'}")
    print(f"服务导入测试: {'✅ 通过' if test_results['service_import'] else '❌ 失败'}")
    print(
        f"服务方法测试: {'✅ 通过' if test_results['service_methods'] else '❌ 失败'}"
    )
    print(f"包检测测试: {test_results['installed_packages']}/6 包已安装")
    print(
        f"Ollama服务测试: {'✅ 通过' if test_results['ollama_service'] else '❌ 失败'}"
    )

    print("\n🎯 优化建议:")

    if not test_results["basic_imports"]:
        print("1. 检查Python环境和基础依赖安装")

    if not test_results["service_import"]:
        print("2. 检查项目路径配置和服务模块")

    if test_results["installed_packages"] < 4:
        print("3. 需要安装缺失的核心Python包")

    if not test_results["ollama_service"]:
        print("4. 检查Ollama服务状态，确保本地AI功能可用")

    if all(
        [
            test_results["basic_imports"],
            test_results["service_import"],
            test_results["service_methods"],
        ]
    ):
        print("✅ 核心功能正常，可以进行深度优化")

    print("\n📋 下一步行动:")
    print("- 运行包安装优化")
    print("- 进行性能测试")
    print("- 添加监控和日志")
    print("- 完善错误处理")


def main():
    """主测试流程"""
    print("🚀 开始集成系统测试和优化")

    test_results = {
        "basic_imports": False,
        "service_import": False,
        "service_methods": False,
        "installed_packages": 0,
        "ollama_service": False,
    }

    # 1. 基础导入测试
    test_results["basic_imports"] = test_basic_imports()

    # 2. 服务导入测试
    service = test_service_import()
    test_results["service_import"] = service is not None

    # 3. 服务方法测试
    if service:
        test_results["service_methods"] = test_service_methods(service)

    # 4. 包检测测试
    test_results["installed_packages"] = test_package_detection()

    # 5. Ollama服务测试
    test_results["ollama_service"] = test_ollama_service()

    # 6. 生成报告
    generate_optimization_report(test_results)

    return test_results


if __name__ == "__main__":
    results = main()
