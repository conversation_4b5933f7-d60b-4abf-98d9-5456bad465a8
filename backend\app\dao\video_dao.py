#!/usr/bin/env python3
"""
数据访问层 (DAO) - 视频管理
提供视频相关的数据库操作
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import desc, func, or_
from sqlalchemy.orm import Session

from app.dao.user_dao import UserDAO
from app.models.database_models import Video, VideoStatus


class VideoDAO:
    """视频数据访问对象"""

    def __init__(self, db: Session):
        self.db = db
        self.user_dao = UserDAO(db)

    def create_video(self, video_data: Dict[str, Any]) -> Video:
        """创建新视频记录"""
        video = Video(**video_data)
        self.db.add(video)
        self.db.commit()
        self.db.refresh(video)
        return video

    def get_video_by_id(self, video_id: str) -> Optional[Video]:
        """根据视频ID获取视频"""
        return self.db.query(Video).filter(Video.video_id == video_id).first()

    def get_video_by_path(self, file_path: str) -> Optional[Video]:
        """根据文件路径获取视频"""
        return self.db.query(Video).filter(Video.file_path == file_path).first()

    def get_videos_by_user(self, user_id: int) -> List[Video]:
        """获取用户的所有视频"""
        return self.db.query(Video).filter(Video.user_id == user_id).all()

    def get_videos_by_status(self, status: VideoStatus) -> List[Video]:
        """根据状态获取视频列表"""
        return self.db.query(Video).filter(Video.status == status.value).all()

    def update_video(self, video_id: str, update_data: Dict[str, Any]) -> bool:
        """更新视频信息"""
        result = (
            self.db.query(Video).filter(Video.video_id == video_id).update(update_data)
        )
        self.db.commit()
        return result > 0

    def update_video_status(
        self,
        video_id: str,
        status: VideoStatus,
        progress: float = None,
        error_message: str = None,
    ) -> bool:
        """更新视频状态"""
        update_data = {"status": status.value}

        if progress is not None:
            update_data["processing_progress"] = progress

        if error_message is not None:
            update_data["error_message"] = error_message

        if status in [VideoStatus.READY, VideoStatus.FAILED]:
            update_data["processed_at"] = datetime.now()

        return self.update_video(video_id, update_data)

    def update_video_analysis(
        self, video_id: str, analysis_data: Dict[str, Any]
    ) -> bool:
        """更新视频分析结果"""
        return self.update_video(video_id, analysis_data)

    def update_video_stats(
        self,
        video_id: str,
        view_count: int = None,
        like_count: int = None,
        share_count: int = None,
        download_count: int = None,
    ) -> bool:
        """更新视频统计信息"""
        update_data = {}
        if view_count is not None:
            update_data["view_count"] = view_count
        if like_count is not None:
            update_data["like_count"] = like_count
        if share_count is not None:
            update_data["share_count"] = share_count
        if download_count is not None:
            update_data["download_count"] = download_count

        if update_data:
            return self.update_video(video_id, update_data)
        return True

    def delete_video(self, video_id: str) -> bool:
        """删除视频"""
        result = self.db.query(Video).filter(Video.video_id == video_id).delete()
        self.db.commit()
        return result > 0

    def get_videos_paginated(self, skip: int = 0, limit: int = 10) -> List[Video]:
        """分页获取视频列表"""
        return (
            self.db.query(Video)
            .order_by(desc(Video.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def search_videos(self, query: str, limit: int = 10) -> List[Video]:
        """搜索视频"""
        return (
            self.db.query(Video)
            .filter(
                or_(
                    Video.title.ilike(f"%{query}%"),
                    Video.description.ilike(f"%{query}%"),
                )
            )
            .limit(limit)
            .all()
        )

    def get_videos_by_compliance(self, compliance_level: str) -> List[Video]:
        """根据合规等级获取视频"""
        return (
            self.db.query(Video)
            .filter(Video.compliance_level == compliance_level)
            .all()
        )

    def get_recommended_videos(
        self, platforms: List[str] = None, limit: int = 10
    ) -> List[Video]:
        """获取推荐视频"""
        query = self.db.query(Video).filter(
            Video.status == VideoStatus.READY.value, Video.quality_score >= 0.7
        )

        if platforms:
            # 这里需要JSON查询，简化处理
            pass

        return query.order_by(desc(Video.engagement_score)).limit(limit).all()

    def count_videos(self) -> int:
        """获取视频总数"""
        return self.db.query(Video).count()

    def count_videos_by_status(self) -> Dict[str, int]:
        """按状态统计视频数量"""
        result = (
            self.db.query(Video.status, func.count(Video.id))
            .group_by(Video.status)
            .all()
        )

        return {status: count for status, count in result}

    def count_videos_by_user(self, user_id: int) -> int:
        """统计用户的视频数量"""
        return self.db.query(Video).filter(Video.user_id == user_id).count()

    def get_video_stats(self) -> Dict[str, Any]:
        """获取视频统计信息"""
        total_videos = self.count_videos()
        status_stats = self.count_videos_by_status()

        # 计算总时长
        total_duration = self.db.query(func.sum(Video.duration)).scalar() or 0.0

        # 计算平均质量评分
        avg_quality = (
            self.db.query(func.avg(Video.quality_score))
            .filter(Video.quality_score.isnot(None))
            .scalar()
            or 0.0
        )

        # 获取最新视频
        recent_videos = (
            self.db.query(Video).order_by(desc(Video.created_at)).limit(5).all()
        )

        return {
            "total_videos": total_videos,
            "status_distribution": status_stats,
            "total_duration_hours": round(total_duration / 3600, 2),
            "average_quality_score": round(avg_quality, 2),
            "recent_videos": [
                {
                    "video_id": video.video_id,
                    "title": video.title,
                    "status": video.status,
                    "created_at": video.created_at.isoformat(),
                }
                for video in recent_videos
            ],
        }
