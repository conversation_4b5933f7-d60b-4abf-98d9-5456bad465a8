#!/usr/bin/env python3
"""
AI服务API - 基于LocalAI/Ollama的AI推理服务
提供聊天完成、模型管理等核心AI功能
"""

import os
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from app.services.local_ai_service import local_ai_service
from app.models.user import User
from app.core.auth import get_current_user

router = APIRouter(prefix="/ai", tags=["AI服务"])


class ChatMessage(BaseModel):
    """聊天消息模型"""

    role: str = Field(..., description="消息角色：user/assistant/system")
    content: str = Field(..., description="消息内容")


class ChatRequest(BaseModel):
    """聊天请求模型"""

    message: str = Field(..., description="用户消息")
    model: str = Field(default="deepseek-chat", description="使用的AI模型")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="温度参数")
    max_tokens: int = Field(default=2000, gt=0, le=4000, description="最大令牌数")


class ChatResponse(BaseModel):
    """聊天响应模型"""

    success: bool = Field(..., description="请求是否成功")
    content: str = Field(default="", description="AI回复内容")
    model: str = Field(..., description="使用的模型")
    usage: Dict = Field(default={}, description="令牌使用统计")
    timestamp: str = Field(..., description="响应时间戳")
    error: Optional[str] = Field(None, description="错误信息")


class ModelInfo(BaseModel):
    """模型信息模型"""

    id: str = Field(..., description="模型ID")
    name: str = Field(..., description="模型名称")
    description: Optional[str] = Field(None, description="模型描述")
    size: Optional[str] = Field(None, description="模型大小")


class ModelsResponse(BaseModel):
    """模型列表响应"""

    success: bool = Field(..., description="请求是否成功")
    models: List[str] = Field(default=[], description="可用模型列表")
    count: int = Field(..., description="模型数量")
    service: str = Field(default="LocalAI", description="AI服务类型")
    timestamp: str = Field(..., description="响应时间戳")


@router.post("/chat", response_model=ChatResponse)
async def chat_completion(request: ChatRequest):
    """
    AI聊天完成接口

    Args:
        request: 聊天请求参数

    Returns:
        ChatResponse: AI回复结果
    """
    try:
        # 构建消息列表
        messages = [{"role": "user", "content": request.message}]

        # 调用LocalAI服务
        result = await local_ai_service.chat_completion(
            model=request.model,
            messages=messages,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
        )

        if result["success"]:
            return ChatResponse(
                success=True,
                content=result["content"],
                model=request.model,
                usage=result.get("usage", {}),
                timestamp=datetime.now().isoformat(),
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=f"AI服务调用失败: {result.get('error', '未知错误')}",
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"聊天完成失败: {str(e)}")


@router.get("/models", response_model=ModelsResponse)
async def get_available_models():
    """
    获取可用的AI模型列表

    Returns:
        ModelsResponse: 模型列表信息
    """
    try:
        # 检查LocalAI服务健康状态
        health = await local_ai_service.health_check()

        if health["status"] == "healthy":
            models = health.get("available_models", [])
            return ModelsResponse(
                success=True,
                models=models,
                count=len(models),
                service="LocalAI",
                timestamp=datetime.now().isoformat(),
            )
        else:
            return ModelsResponse(
                success=False,
                models=[],
                count=0,
                service="LocalAI (不可用)",
                timestamp=datetime.now().isoformat(),
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@router.get("/health")
async def health_check():
    """
    AI服务健康检查

    Returns:
        Dict: 服务健康状态
    """
    try:
        health = await local_ai_service.health_check()
        return {
            "service": "AI API",
            "status": "healthy" if health["status"] == "healthy" else "degraded",
            "backend_service": health,
            "timestamp": datetime.now().isoformat(),
            "endpoints": {
                "chat": "/api/v1/ai/chat",
                "models": "/api/v1/ai/models",
                "health": "/api/v1/ai/health",
                "config": "/api/v1/ai/config",
            },
        }
    except Exception as e:
        return {
            "service": "AI API",
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
        }


class ModelConfigRequest(BaseModel):
    model_name: str
    api_key: str


@router.post("/config")
async def set_model_key(config: ModelConfigRequest, current_user: User = Depends(get_current_user)):
    """设置模型API KEY（需要认证）"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="仅管理员可配置API KEY")
    # 安全存储KEY，例如设置环境变量或保存到数据库
    os.environ[f"{config.model_name.upper()}_API_KEY"] = config.api_key
    return {"success": True, "message": f"{config.model_name} API KEY 已设置"}


@router.get("/config")
async def get_model_configs(current_user: User = Depends(get_current_user)):
    """获取模型配置（隐藏KEY值）"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="仅管理员可查看配置")
    configs = {key: "****" for key in os.environ if key.endswith("_API_KEY")}
    return configs


@router.post("/test")
async def test_ai_service():
    """
    测试AI服务连接

    Returns:
        Dict: 测试结果
    """
    try:
        # 发送一个简单的测试消息
        test_request = ChatRequest(
            message="你好，请回复'测试成功'",
            model="deepseek-chat",
            temperature=0.1,
            max_tokens=50,
        )

        response = await chat_completion(test_request)

        return {
            "test": "AI服务连接测试",
            "status": "success" if response.success else "failed",
            "response": response.content if response.success else "无回复",
            "model": test_request.model,
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        return {
            "test": "AI服务连接测试",
            "status": "failed",
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
        }


class GenerateRequest(BaseModel):
    """文本生成请求模型"""

    prompt: str = Field(..., description="生成提示词")
    model: str = Field(default="deepseek-coder:7b", description="使用的AI模型")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="温度参数")
    max_tokens: int = Field(default=500, gt=0, le=2000, description="最大令牌数")


class GenerateResponse(BaseModel):
    """文本生成响应模型"""

    success: bool = Field(..., description="请求是否成功")
    content: str = Field(default="", description="生成的内容")
    model: str = Field(..., description="使用的模型")
    usage: Dict = Field(default={}, description="令牌使用统计")
    timestamp: str = Field(..., description="响应时间戳")
    error: Optional[str] = Field(None, description="错误信息")


@router.post("/generate", response_model=GenerateResponse)
async def generate_text(request: GenerateRequest):
    """
    AI文本生成接口

    Args:
        request: 文本生成请求参数

    Returns:
        GenerateResponse: AI生成结果
    """
    try:
        # 构建消息列表
        messages = [{"role": "user", "content": request.prompt}]

        # 调用LocalAI服务
        result = await local_ai_service.chat_completion(
            model=request.model,
            messages=messages,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
        )

        if result["success"]:
            return GenerateResponse(
                success=True,
                content=result["content"],
                model=request.model,
                usage=result.get("usage", {}),
                timestamp=datetime.now().isoformat(),
            )
        else:
            return GenerateResponse(
                success=False,
                content="",
                model=request.model,
                usage={},
                timestamp=datetime.now().isoformat(),
                error=result.get("error", "未知错误"),
            )

    except Exception as e:
        return GenerateResponse(
            success=False,
            content="",
            model=request.model,
            usage={},
            timestamp=datetime.now().isoformat(),
            error=f"文本生成失败: {str(e)}",
        )
