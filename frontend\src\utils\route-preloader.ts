/**
 * 路由预加载系统 - 2025年最佳实践
 * 智能预加载路由组件，提升用户体验
 */

import type { RouteLocationNormalized } from 'vue-router'

// 预加载策略配置
interface PreloadConfig {
  // 预加载优先级
  priority: 'high' | 'medium' | 'low'
  // 预加载条件
  condition?: () => boolean
  // 延迟时间（毫秒）
  delay?: number
  // 是否在空闲时预加载
  onIdle?: boolean
}

// 路由预加载映射
const ROUTE_PRELOAD_MAP: Record<string, PreloadConfig> = {
  // 首页 -> 视频创作（高频跳转）
  '/': {
    priority: 'high',
    delay: 1000,
    onIdle: true
  },
  // 视频创作 -> 计算引擎（工作流程）
  '/video-creation': {
    priority: 'medium',
    delay: 2000,
    onIdle: true
  },
  // 登录 -> 首页（登录后跳转）
  '/login': {
    priority: 'high',
    delay: 500,
    condition: () => !isAuthenticated()
  }
}

// 预加载的路由组件映射
const PRELOAD_COMPONENTS: Record<string, () => Promise<any>> = {
  '/video-creation': () => import(
    /* webpackChunkName: "video-creation" */
    '../pages/VideoCreation.vue'
  ),
  '/compute-test': () => import(
    /* webpackChunkName: "compute-test" */
    '../pages/ComputeTest.vue'
  ),
  '/profile': () => import(
    /* webpackChunkName: "profile" */
    '../pages/Profile.vue'
  ),
  '/': () => import(
    /* webpackChunkName: "home" */
    '../pages/Home.vue'
  )
}

// 已预加载的组件缓存
const preloadedComponents = new Set<string>()

// 预加载队列
const preloadQueue: Array<{
  path: string
  priority: number
  timestamp: number
}> = []

// 检查用户是否已认证
function isAuthenticated(): boolean {
  return !!localStorage.getItem('auth_token')
}

// 检查网络连接质量
function getConnectionQuality(): 'slow' | 'fast' | 'unknown' {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection
    if (connection.effectiveType === '4g' || connection.effectiveType === '3g') {
      return 'fast'
    }
    if (connection.effectiveType === '2g' || connection.effectiveType === 'slow-2g') {
      return 'slow'
    }
  }
  return 'unknown'
}

// 检查设备性能
function getDevicePerformance(): 'high' | 'medium' | 'low' {
  if ('hardwareConcurrency' in navigator) {
    const cores = navigator.hardwareConcurrency
    if (cores >= 8) return 'high'
    if (cores >= 4) return 'medium'
    return 'low'
  }
  return 'medium'
}

// 智能预加载决策
function shouldPreload(config: PreloadConfig): boolean {
  // 检查条件
  if (config.condition && !config.condition()) {
    return false
  }
  
  // 检查网络质量
  const connectionQuality = getConnectionQuality()
  if (connectionQuality === 'slow' && config.priority === 'low') {
    return false
  }
  
  // 检查设备性能
  const devicePerformance = getDevicePerformance()
  if (devicePerformance === 'low' && config.priority !== 'high') {
    return false
  }
  
  // 检查用户偏好（省流量模式）
  if (localStorage.getItem('data_saver_mode') === 'true') {
    return config.priority === 'high'
  }
  
  return true
}

// 预加载组件
async function preloadComponent(path: string): Promise<void> {
  if (preloadedComponents.has(path)) {
    return
  }
  
  const loader = PRELOAD_COMPONENTS[path]
  if (!loader) {
    return
  }
  
  try {
    console.log(`🚀 预加载路由组件: ${path}`)
    await loader()
    preloadedComponents.add(path)
    console.log(`✅ 路由组件预加载完成: ${path}`)
  } catch (error) {
    console.warn(`❌ 路由组件预加载失败: ${path}`, error)
  }
}

// 处理预加载队列
function processPreloadQueue(): void {
  // 按优先级和时间戳排序
  preloadQueue.sort((a, b) => {
    if (a.priority !== b.priority) {
      return b.priority - a.priority // 高优先级优先
    }
    return a.timestamp - b.timestamp // 早加入的优先
  })
  
  // 处理队列中的第一个项目
  if (preloadQueue.length > 0) {
    const item = preloadQueue.shift()!
    preloadComponent(item.path)
    
    // 继续处理下一个（延迟执行避免阻塞）
    if (preloadQueue.length > 0) {
      setTimeout(processPreloadQueue, 100)
    }
  }
}

// 添加到预加载队列
function addToPreloadQueue(path: string, priority: 'high' | 'medium' | 'low'): void {
  const priorityValue = priority === 'high' ? 3 : priority === 'medium' ? 2 : 1
  
  preloadQueue.push({
    path,
    priority: priorityValue,
    timestamp: Date.now()
  })
  
  // 如果队列之前为空，立即开始处理
  if (preloadQueue.length === 1) {
    processPreloadQueue()
  }
}

// 基于当前路由预加载相关组件
export function preloadRelatedRoutes(currentRoute: RouteLocationNormalized): void {
  const currentPath = currentRoute.path
  const config = ROUTE_PRELOAD_MAP[currentPath]
  
  if (!config || !shouldPreload(config)) {
    return
  }
  
  // 获取相关路由
  const relatedRoutes = getRelatedRoutes(currentPath)
  
  relatedRoutes.forEach(route => {
    const executePreload = () => {
      addToPreloadQueue(route, config.priority)
    }
    
    if (config.onIdle && 'requestIdleCallback' in window) {
      // 在浏览器空闲时预加载
      window.requestIdleCallback(() => {
        setTimeout(executePreload, config.delay || 0)
      })
    } else {
      // 延迟预加载
      setTimeout(executePreload, config.delay || 0)
    }
  })
}

// 获取相关路由
function getRelatedRoutes(currentPath: string): string[] {
  const routeRelations: Record<string, string[]> = {
    '/': ['/video-creation', '/profile'],
    '/login': ['/'],
    '/video-creation': ['/compute-test', '/profile'],
    '/compute-test': ['/video-creation'],
    '/profile': ['/video-creation']
  }
  
  return routeRelations[currentPath] || []
}

// 预热关键路由
export function warmupCriticalRoutes(): void {
  const criticalRoutes = ['/', '/login']
  
  criticalRoutes.forEach(route => {
    if (!preloadedComponents.has(route)) {
      addToPreloadQueue(route, 'high')
    }
  })
}

// 清理预加载缓存
export function clearPreloadCache(): void {
  preloadedComponents.clear()
  preloadQueue.length = 0
}

// 获取预加载统计信息
export function getPreloadStats(): {
  preloadedCount: number
  queueLength: number
  preloadedRoutes: string[]
} {
  return {
    preloadedCount: preloadedComponents.size,
    queueLength: preloadQueue.length,
    preloadedRoutes: Array.from(preloadedComponents)
  }
}

// 监听网络状态变化
if ('connection' in navigator) {
  (navigator as any).connection.addEventListener('change', () => {
    const quality = getConnectionQuality()
    console.log(`📶 网络质量变化: ${quality}`)
    
    // 如果网络变慢，清理低优先级预加载
    if (quality === 'slow') {
      const lowPriorityItems = preloadQueue.filter(item => item.priority === 1)
      lowPriorityItems.forEach(item => {
        const index = preloadQueue.indexOf(item)
        if (index > -1) {
          preloadQueue.splice(index, 1)
        }
      })
    }
  })
}

// 导出主要功能
export default {
  preloadRelatedRoutes,
  warmupCriticalRoutes,
  clearPreloadCache,
  getPreloadStats
}
