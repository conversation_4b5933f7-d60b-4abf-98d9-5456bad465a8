#!/usr/bin/env python3
"""
内容创作API - AI驱动的内容改写和生成服务
基于LocalAI/Ollama提供智能文案改写、内容生成等功能
"""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, BackgroundTasks, HTTPException
from pydantic import BaseModel, Field

from app.services.local_ai_service import local_ai_service

router = APIRouter(prefix="/content", tags=["内容创作"])


class ContentRewriteRequest(BaseModel):
    """文案改写请求模型"""

    original_content: str = Field(
        ..., description="原始内容", min_length=1, max_length=10000
    )
    target_style: str = Field(
        default="专业", description="目标风格：专业/随意/创意/商务/娱乐"
    )
    target_length: Optional[str] = Field(None, description="目标长度：短/中等/长")
    model: str = Field(default="deepseek-chat", description="使用的AI模型")
    temperature: float = Field(default=0.7, ge=0.0, le=1.0, description="创意度参数")


class ContentRewriteResponse(BaseModel):
    """文案改写响应模型"""

    success: bool = Field(..., description="是否成功")
    original_content: str = Field(..., description="原始内容")
    rewritten_content: str = Field(default="", description="改写后内容")
    target_style: str = Field(..., description="目标风格")
    model: str = Field(..., description="使用的模型")
    word_count_original: int = Field(..., description="原文字数")
    word_count_rewritten: int = Field(..., description="改写后字数")
    timestamp: str = Field(..., description="创建时间")
    error: Optional[str] = Field(None, description="错误信息")


class ContentGenerationRequest(BaseModel):
    """内容生成请求模型"""

    topic: str = Field(..., description="内容主题", min_length=1, max_length=200)
    content_type: str = Field(
        default="文章", description="内容类型：文章/脚本/描述/标题/大纲"
    )
    length: str = Field(default="中等", description="长度要求：短/中等/长")
    style: str = Field(default="专业", description="风格要求：专业/随意/创意/商务/娱乐")
    keywords: Optional[List[str]] = Field(default=[], description="关键词列表")
    model: str = Field(default="deepseek-chat", description="使用的AI模型")
    temperature: float = Field(default=0.8, ge=0.0, le=1.0, description="创意度参数")


class ContentGenerationResponse(BaseModel):
    """内容生成响应模型"""

    success: bool = Field(..., description="是否成功")
    topic: str = Field(..., description="内容主题")
    content_type: str = Field(..., description="内容类型")
    generated_content: str = Field(default="", description="生成的内容")
    style: str = Field(..., description="风格")
    length: str = Field(..., description="长度")
    word_count: int = Field(..., description="字数统计")
    model: str = Field(..., description="使用的模型")
    timestamp: str = Field(..., description="创建时间")
    error: Optional[str] = Field(None, description="错误信息")


class BatchRewriteRequest(BaseModel):
    """批量改写请求模型"""

    contents: List[str] = Field(..., description="内容列表", min_items=1, max_items=10)
    target_style: str = Field(default="专业", description="目标风格")
    model: str = Field(default="deepseek-chat", description="使用的AI模型")


class BatchRewriteResponse(BaseModel):
    """批量改写响应模型"""

    success: bool = Field(..., description="是否成功")
    total_count: int = Field(..., description="总数量")
    success_count: int = Field(..., description="成功数量")
    results: List[ContentRewriteResponse] = Field(
        default=[], description="改写结果列表"
    )
    timestamp: str = Field(..., description="完成时间")


@router.post("/rewrite", response_model=ContentRewriteResponse)
async def rewrite_content(request: ContentRewriteRequest):
    """
    AI文案改写接口

    Args:
        request: 改写请求参数

    Returns:
        ContentRewriteResponse: 改写结果
    """
    try:
        # 构建改写提示词
        length_instruction = ""
        if request.target_length:
            length_map = {
                "短": "尽量简洁，控制在100字以内",
                "中等": "保持适中长度，200-500字",
                "长": "可以详细展开，500字以上",
            }
            length_instruction = length_map.get(request.target_length, "")

        # 调用LocalAI服务进行改写
        result = await local_ai_service.content_rewrite(
            original_content=request.original_content,
            target_style=request.target_style,
            model=request.model,
        )

        if result["success"]:
            original_word_count = len(request.original_content)
            rewritten_word_count = len(result["rewritten_content"])

            return ContentRewriteResponse(
                success=True,
                original_content=request.original_content,
                rewritten_content=result["rewritten_content"],
                target_style=request.target_style,
                model=request.model,
                word_count_original=original_word_count,
                word_count_rewritten=rewritten_word_count,
                timestamp=datetime.now().isoformat(),
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=f"内容改写失败: {result.get('error', '未知错误')}",
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文案改写服务错误: {str(e)}")


@router.post("/generate", response_model=ContentGenerationResponse)
async def generate_content(request: ContentGenerationRequest):
    """
    AI内容生成接口

    Args:
        request: 生成请求参数

    Returns:
        ContentGenerationResponse: 生成结果
    """
    try:
        # 调用LocalAI服务进行内容生成
        result = await local_ai_service.content_generation(
            topic=request.topic,
            content_type=request.content_type,
            length=request.length,
            style=request.style,
            model=request.model,
        )

        if result["success"]:
            word_count = len(result["generated_content"])

            return ContentGenerationResponse(
                success=True,
                topic=request.topic,
                content_type=request.content_type,
                generated_content=result["generated_content"],
                style=request.style,
                length=request.length,
                word_count=word_count,
                model=request.model,
                timestamp=datetime.now().isoformat(),
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=f"内容生成失败: {result.get('error', '未知错误')}",
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"内容生成服务错误: {str(e)}")


@router.post("/batch-rewrite", response_model=BatchRewriteResponse)
async def batch_rewrite_content(
    request: BatchRewriteRequest, background_tasks: BackgroundTasks
):
    """
    批量文案改写接口

    Args:
        request: 批量改写请求
        background_tasks: 后台任务管理

    Returns:
        BatchRewriteResponse: 批量改写结果
    """
    try:
        results = []
        success_count = 0

        for content in request.contents:
            try:
                # 为每个内容创建改写请求
                rewrite_request = ContentRewriteRequest(
                    original_content=content,
                    target_style=request.target_style,
                    model=request.model,
                )

                # 执行改写
                result = await rewrite_content(rewrite_request)
                results.append(result)
                success_count += 1

            except Exception as e:
                # 单个内容失败，记录错误但继续处理其他内容
                error_result = ContentRewriteResponse(
                    success=False,
                    original_content=content,
                    rewritten_content="",
                    target_style=request.target_style,
                    model=request.model,
                    word_count_original=len(content),
                    word_count_rewritten=0,
                    timestamp=datetime.now().isoformat(),
                    error=str(e),
                )
                results.append(error_result)

        return BatchRewriteResponse(
            success=True,
            total_count=len(request.contents),
            success_count=success_count,
            results=results,
            timestamp=datetime.now().isoformat(),
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量改写服务错误: {str(e)}")


@router.get("/styles")
async def get_content_styles():
    """
    获取支持的内容风格列表

    Returns:
        Dict: 风格列表和描述
    """
    styles = {
        "专业": {
            "description": "正式、严谨、准确的表达方式",
            "suitable_for": ["商务文档", "技术文章", "学术论文"],
        },
        "随意": {
            "description": "轻松、自然、口语化的表达方式",
            "suitable_for": ["社交媒体", "日常交流", "个人博客"],
        },
        "创意": {
            "description": "富有想象力、新颖、有趣的表达方式",
            "suitable_for": ["广告文案", "创意写作", "营销内容"],
        },
        "商务": {
            "description": "商业化、效率导向、结果明确的表达方式",
            "suitable_for": ["商业提案", "销售文案", "企业宣传"],
        },
        "娱乐": {
            "description": "有趣、幽默、轻松的表达方式",
            "suitable_for": ["娱乐内容", "短视频文案", "社交分享"],
        },
    }

    return {
        "success": True,
        "styles": styles,
        "count": len(styles),
        "timestamp": datetime.now().isoformat(),
    }


@router.get("/types")
async def get_content_types():
    """
    获取支持的内容类型列表

    Returns:
        Dict: 内容类型列表和描述
    """
    content_types = {
        "文章": {
            "description": "完整的文章内容，包含开头、正文、结尾",
            "typical_length": "500-2000字",
        },
        "脚本": {
            "description": "视频或音频脚本，适合口播",
            "typical_length": "200-1000字",
        },
        "描述": {"description": "产品或服务的详细描述", "typical_length": "100-500字"},
        "标题": {"description": "吸引人的标题或标语", "typical_length": "10-50字"},
        "大纲": {"description": "内容的结构化大纲", "typical_length": "100-300字"},
    }

    return {
        "success": True,
        "content_types": content_types,
        "count": len(content_types),
        "timestamp": datetime.now().isoformat(),
    }


@router.get("/health")
async def health_check():
    """
    内容创作服务健康检查

    Returns:
        Dict: 服务健康状态
    """
    try:
        # 检查后端AI服务状态
        ai_health = await local_ai_service.health_check()

        return {
            "service": "内容创作API",
            "status": "healthy" if ai_health["status"] == "healthy" else "degraded",
            "ai_backend": ai_health,
            "features": {
                "content_rewrite": True,
                "content_generation": True,
                "batch_processing": True,
                "style_conversion": True,
            },
            "supported_styles": ["专业", "随意", "创意", "商务", "娱乐"],
            "supported_types": ["文章", "脚本", "描述", "标题", "大纲"],
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        return {
            "service": "内容创作API",
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
        }
