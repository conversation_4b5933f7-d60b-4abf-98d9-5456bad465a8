"""
管理界面后端API实现
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, List
import json
import io
import pandas as pd
import pymysql
from datetime import datetime
from pathlib import Path
from contextlib import contextmanager
import threading

router = APIRouter(tags=["管理后端API"])

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'video_user',
    'password': 'VideoSystem2024!@#',
    'database': 'video_system',
    'charset': 'utf8mb4'
}

# 数据库连接池
_db_lock = threading.Lock()

@contextmanager
def get_db_connection():
    """获取MySQL数据库连接的上下文管理器"""
    conn = None
    try:
        with _db_lock:
            conn = pymysql.connect(**MYSQL_CONFIG)
            yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"数据库连接失败: {str(e)}")
    finally:
        if conn:
            conn.close()

def init_database():
    """初始化数据库"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 创建用户表 - MySQL兼容语法
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS admin_users (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    username VARCHAR(100) UNIQUE NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    status VARCHAR(20) DEFAULT 'active',
                    role VARCHAR(20) DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建项目表 - MySQL兼容语法
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS admin_projects (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    status VARCHAR(20) DEFAULT 'active',
                    type VARCHAR(50) DEFAULT 'video',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建设置表 - MySQL兼容语法
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS admin_settings (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 插入初始数据（如果不存在）- MySQL兼容语法
            cursor.execute("SELECT COUNT(*) FROM admin_users")
            if cursor.fetchone()[0] == 0:
                initial_users = [
                    ("admin", "<EMAIL>", "active", "admin"),
                    ("zhang123", "<EMAIL>", "active", "user"),
                    ("li456", "<EMAIL>", "suspended", "user"),
                ]
                cursor.executemany(
                    "INSERT INTO admin_users (username, email, status, role) VALUES (%s, %s, %s, %s)",
                    initial_users
                )

            cursor.execute("SELECT COUNT(*) FROM admin_projects")
            if cursor.fetchone()[0] == 0:
                initial_projects = [
                    ("视频创作项目A", "AI视频内容创作", "active", "video"),
                    ("内容分发项目B", "多平台内容分发", "paused", "content"),
                ]
                cursor.executemany(
                    "INSERT INTO admin_projects (name, description, status, type) VALUES (%s, %s, %s, %s)",
                    initial_projects
                )

            conn.commit()
            print("✅ 数据库初始化成功")

    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise

# 初始化数据库
init_database()

# 数据模型
class UserCreate(BaseModel):
    username: str
    email: str
    password: str
    role: str = "user"

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    status: Optional[str] = None

class ProjectCreate(BaseModel):
    name: str
    description: str
    type: str = "video"
    status: str = "active"

class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None

class SystemSettings(BaseModel):
    system_name: Optional[str] = None
    admin_email: Optional[str] = None
    ai_model: Optional[str] = None
    api_key: Optional[str] = None
    timeout: Optional[int] = None
    ip_whitelist: Optional[bool] = None
    rate_limit: Optional[bool] = None
    access_log: Optional[bool] = None
    max_concurrent: Optional[int] = None
    cache_expire: Optional[int] = None
    log_retention: Optional[int] = None

class AIModelTest(BaseModel):
    model: str
    api_key: str

# 用户管理API
@router.get("/users")
async def get_users():
    """获取用户列表"""
    with get_db_connection() as conn:
        cursor = conn.cursor()

        cursor.execute("SELECT id, username, email, status, role, created_at FROM users ORDER BY id")
        rows = cursor.fetchall()

        users = []
        for row in rows:
            users.append({
                "id": row[0],
                "username": row[1],
                "email": row[2],
                "status": row[3],
                "role": row[4],
                "created_at": row[5]
            })

        return {"users": users, "total": len(users)}

@router.post("/users")
async def create_user(user: UserCreate):
    """创建新用户"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute(
                "INSERT INTO users (username, email, status, role) VALUES (?, ?, ?, ?)",
                (user.username, user.email, "active", user.role)
            )
            user_id = cursor.lastrowid
            conn.commit()

            # 获取创建的用户信息
            cursor.execute("SELECT id, username, email, status, role, created_at FROM users WHERE id = ?", (user_id,))
            row = cursor.fetchone()

            new_user = {
                "id": row[0],
                "username": row[1],
                "email": row[2],
                "status": row[3],
                "role": row[4],
                "created_at": row[5]
            }

            return {"message": "用户创建成功", "user": new_user}

    except sqlite3.IntegrityError as e:
        if "username" in str(e):
            raise HTTPException(status_code=400, detail="用户名已存在")
        elif "email" in str(e):
            raise HTTPException(status_code=400, detail="邮箱已存在")
        else:
            raise HTTPException(status_code=400, detail="创建用户失败")

@router.put("/users/{user_id}")
async def update_user(user_id: int, user: UserUpdate):
    """更新用户信息"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 检查用户是否存在
            cursor.execute("SELECT id FROM users WHERE id = ?", (user_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="用户不存在")

            # 安全的更新语句 - 使用预定义的SQL避免注入
            update_data = {}

            # 调试日志
            print(f"DEBUG: 接收到的用户数据: username={user.username}, email={user.email}, status={user.status}")

            # 收集需要更新的字段和值
            if user.username is not None and user.username.strip():
                update_data["username"] = user.username.strip()
            if user.email is not None and user.email.strip():
                update_data["email"] = user.email.strip()
            if user.status is not None:
                update_data["status"] = user.status

            # 按字母顺序排序字段和参数
            update_fields = sorted(update_data.keys())
            params = [update_data[field] for field in update_fields]

            print(f"DEBUG: 排序后的更新字段: {update_fields}")
            print(f"DEBUG: 对应的参数: {params}")

            if update_fields:
                params.append(user_id)

                # 预定义安全的SQL语句 - 键必须按字母顺序排序
                sql_templates = {
                    ("email",): "UPDATE users SET email = ? WHERE id = ?",
                    ("status",): "UPDATE users SET status = ? WHERE id = ?",
                    ("username",): "UPDATE users SET username = ? WHERE id = ?",
                    ("email", "status"): "UPDATE users SET email = ?, status = ? WHERE id = ?",
                    ("email", "username"): "UPDATE users SET email = ?, username = ? WHERE id = ?",
                    ("status", "username"): "UPDATE users SET status = ?, username = ? WHERE id = ?",
                    ("email", "status", "username"): "UPDATE users SET email = ?, status = ?, username = ? WHERE id = ?",
                }

                sql_key = tuple(update_fields)  # 已经排序过了
                print(f"DEBUG: SQL模板键: {sql_key}")
                print(f"DEBUG: 可用的模板键: {list(sql_templates.keys())}")

                if sql_key not in sql_templates:
                    raise HTTPException(status_code=400, detail=f"无效的更新字段组合: {sql_key}")

                cursor.execute(sql_templates[sql_key], params)
                conn.commit()

            # 获取更新后的用户信息
            cursor.execute("SELECT id, username, email, status, role, created_at FROM users WHERE id = ?", (user_id,))
            row = cursor.fetchone()

            updated_user = {
                "id": row[0],
                "username": row[1],
                "email": row[2],
                "status": row[3],
                "role": row[4],
                "created_at": row[5]
            }

            return {"message": "用户信息已更新", "user": updated_user}

    except sqlite3.IntegrityError as e:
        if "username" in str(e):
            raise HTTPException(status_code=400, detail="用户名已存在")
        elif "email" in str(e):
            raise HTTPException(status_code=400, detail="邮箱已存在")
        else:
            raise HTTPException(status_code=400, detail="更新用户失败")

@router.delete("/users/{user_id}")
async def delete_user(user_id: int):
    """删除用户"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 获取要删除的用户信息
            cursor.execute("SELECT id, username, email, status, role FROM users WHERE id = ?", (user_id,))
            row = cursor.fetchone()

            if not row:
                raise HTTPException(status_code=404, detail="用户不存在")

            deleted_user = {
                "id": row[0],
                "username": row[1],
                "email": row[2],
                "status": row[3],
                "role": row[4]
            }

            # 删除用户
            cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))
            conn.commit()

            return {"message": "用户已删除", "user": deleted_user}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")

# 项目管理API
@router.get("/projects")
async def get_projects():
    """获取项目列表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("SELECT id, name, description, status, type, created_at FROM projects ORDER BY id")
    rows = cursor.fetchall()

    projects = []
    for row in rows:
        projects.append({
            "id": row[0],
            "name": row[1],
            "description": row[2],
            "status": row[3],
            "type": row[4],
            "created_at": row[5]
        })

    conn.close()
    return {"projects": projects, "total": len(projects)}

@router.post("/projects")
async def create_project(project: ProjectCreate):
    """创建新项目"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(
            "INSERT INTO projects (name, description, status, type) VALUES (?, ?, ?, ?)",
            (project.name, project.description, project.status, project.type)
        )
        project_id = cursor.lastrowid
        conn.commit()

        # 获取创建的项目信息
        cursor.execute("SELECT id, name, description, status, type, created_at FROM projects WHERE id = ?", (project_id,))
        row = cursor.fetchone()

        new_project = {
            "id": row[0],
            "name": row[1],
            "description": row[2],
            "status": row[3],
            "type": row[4],
            "created_at": row[5]
        }

        conn.close()
        return {"message": "项目创建成功", "project": new_project}

    except sqlite3.Error as e:
        conn.close()
        raise HTTPException(status_code=400, detail="创建项目失败")

@router.put("/projects/{project_id}")
async def update_project(project_id: int, project: ProjectUpdate):
    """更新项目信息"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 检查项目是否存在
            cursor.execute("SELECT id FROM projects WHERE id = ?", (project_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="项目不存在")

            # 安全的更新语句 - 使用预定义的SQL避免注入
            update_fields = []
            params = []

            if project.name:
                update_fields.append("name")
                params.append(project.name)
            if project.description:
                update_fields.append("description")
                params.append(project.description)
            if project.status:
                update_fields.append("status")
                params.append(project.status)

            if update_fields:
                params.append(project_id)

                # 预定义安全的SQL语句
                sql_templates = {
                    ("name",): "UPDATE projects SET name = ? WHERE id = ?",
                    ("description",): "UPDATE projects SET description = ? WHERE id = ?",
                    ("status",): "UPDATE projects SET status = ? WHERE id = ?",
                    ("name", "description"): "UPDATE projects SET name = ?, description = ? WHERE id = ?",
                    ("name", "status"): "UPDATE projects SET name = ?, status = ? WHERE id = ?",
                    ("description", "status"): "UPDATE projects SET description = ?, status = ? WHERE id = ?",
                    ("name", "description", "status"): "UPDATE projects SET name = ?, description = ?, status = ? WHERE id = ?",
                }

                sql_key = tuple(sorted(update_fields))
                if sql_key not in sql_templates:
                    raise HTTPException(status_code=400, detail="无效的更新字段组合")

                cursor.execute(sql_templates[sql_key], params)
                conn.commit()

            # 获取更新后的项目信息
            cursor.execute("SELECT id, name, description, status, type, created_at FROM projects WHERE id = ?", (project_id,))
            row = cursor.fetchone()

            updated_project = {
                "id": row[0],
                "name": row[1],
                "description": row[2],
                "status": row[3],
                "type": row[4],
                "created_at": row[5]
            }

            return {"message": "项目信息已更新", "project": updated_project}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新项目失败: {str(e)}")

@router.delete("/projects/{project_id}")
async def delete_project(project_id: int):
    """删除项目"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 获取要删除的项目信息
    cursor.execute("SELECT id, name, description, status, type FROM projects WHERE id = ?", (project_id,))
    row = cursor.fetchone()

    if not row:
        conn.close()
        raise HTTPException(status_code=404, detail="项目不存在")

    deleted_project = {
        "id": row[0],
        "name": row[1],
        "description": row[2],
        "status": row[3],
        "type": row[4]
    }

    # 删除项目
    cursor.execute("DELETE FROM projects WHERE id = ?", (project_id,))
    conn.commit()
    conn.close()

    return {"message": "项目已删除", "project": deleted_project}

# 系统设置API
@router.post("/admin/settings")
async def save_settings(settings: SystemSettings):
    """保存系统设置"""
    # 这里应该保存到数据库或配置文件
    return {"message": "设置已保存", "settings": settings.dict()}

@router.post("/admin/test-ai-model")
async def test_ai_model(test_data: AIModelTest):
    """测试AI模型连接"""
    # 模拟AI模型测试
    import random
    import time
    
    # 模拟网络延迟
    time.sleep(1)
    
    # 模拟测试结果
    if test_data.api_key and len(test_data.api_key) > 10:
        success_rate = 0.8  # 80%成功率
        if random.random() < success_rate:
            response_time = random.randint(50, 200)
            return {
                "success": True,
                "message": "连接成功",
                "response_time": response_time,
                "model": test_data.model
            }
        else:
            return {
                "success": False,
                "error": "API密钥无效或模型服务暂时不可用",
                "model": test_data.model
            }
    else:
        return {
            "success": False,
            "error": "API密钥格式不正确",
            "model": test_data.model
        }

@router.post("/admin/export-report")
async def export_report():
    """导出数据报告"""
    # 创建示例数据
    data = {
        "日期": ["2024-01-01", "2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05"],
        "用户数": [1200, 1234, 1250, 1280, 1300],
        "项目数": [45, 47, 50, 52, 55],
        "视频处理": [2400, 2567, 2600, 2700, 2800],
        "AI调用": [15000, 15678, 16000, 16500, 17000]
    }
    
    # 创建Excel文件
    df = pd.DataFrame(data)
    output = io.BytesIO()
    
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='数据分析报告', index=False)
    
    output.seek(0)
    
    # 返回文件流
    return StreamingResponse(
        io.BytesIO(output.read()),
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename=analytics_report_{datetime.now().strftime('%Y%m%d')}.xlsx"}
    )

# 额外的API端点
@router.get("/admin/dashboard")
async def get_dashboard_stats():
    """获取仪表盘统计数据"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 获取用户统计
    cursor.execute("SELECT COUNT(*) FROM users")
    total_users = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM users WHERE status = 'active'")
    active_users = cursor.fetchone()[0]

    # 获取项目统计
    cursor.execute("SELECT COUNT(*) FROM projects")
    total_projects = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM projects WHERE status = 'active'")
    active_projects = cursor.fetchone()[0]

    conn.close()

    return {
        "users": {
            "total": total_users,
            "active": active_users,
            "inactive": total_users - active_users
        },
        "projects": {
            "total": total_projects,
            "active": active_projects,
            "inactive": total_projects - active_projects
        },
        "system": {
            "uptime": "99.9%",
            "response_time": "45ms",
            "cpu_usage": "12%",
            "memory_usage": "68%"
        }
    }

@router.get("/admin/logs")
async def get_system_logs():
    """获取系统日志"""
    # 模拟系统日志
    logs = [
        {
            "id": 1,
            "timestamp": "2024-01-19 12:00:00",
            "level": "INFO",
            "message": "用户登录成功",
            "user": "admin"
        },
        {
            "id": 2,
            "timestamp": "2024-01-19 11:58:00",
            "level": "INFO",
            "message": "项目创建成功",
            "user": "zhang123"
        },
        {
            "id": 3,
            "timestamp": "2024-01-19 11:55:00",
            "level": "WARNING",
            "message": "API调用频率过高",
            "user": "li456"
        }
    ]

    return {"logs": logs, "total": len(logs)}

@router.get("/admin/notifications")
async def get_notifications():
    """获取系统通知"""
    notifications = [
        {
            "id": 1,
            "type": "info",
            "title": "系统更新",
            "message": "系统将在今晚进行维护更新",
            "timestamp": "2024-01-19 10:00:00",
            "read": False
        },
        {
            "id": 2,
            "type": "warning",
            "title": "存储空间",
            "message": "存储空间使用率已达到80%",
            "timestamp": "2024-01-19 09:30:00",
            "read": True
        }
    ]

    return {"notifications": notifications, "total": len(notifications)}

@router.post("/admin/backup")
async def create_backup():
    """创建系统备份"""
    # 模拟备份过程
    import time
    time.sleep(1)  # 模拟备份时间

    return {
        "message": "备份创建成功",
        "backup_id": f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "size": "2.5MB",
        "created_at": datetime.now().isoformat()
    }
