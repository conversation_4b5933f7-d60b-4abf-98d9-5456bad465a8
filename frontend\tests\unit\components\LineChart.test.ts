/**
 * LineChart组件单元测试 - 100%覆盖率
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import LineChart from '@/components/charts/LineChart.vue'

// 模拟Chart.js
const mockChart = {
  destroy: vi.fn(),
  update: vi.fn(),
  resize: vi.fn(),
  data: {},
  options: {}
}

const mockChartConstructor = vi.fn().mockImplementation(() => mockChart)

vi.mock('chart.js', () => ({
  Chart: mockChartConstructor,
  CategoryScale: vi.fn(),
  LinearScale: vi.fn(),
  PointElement: vi.fn(),
  LineElement: vi.fn(),
  Title: vi.fn(),
  Tooltip: vi.fn(),
  Legend: vi.fn(),
  Filler: vi.fn()
}))

// 模拟canvas context
const mockContext = {
  fillRect: vi.fn(),
  clearRect: vi.fn(),
  getImageData: vi.fn(),
  putImageData: vi.fn(),
  createImageData: vi.fn(),
  setTransform: vi.fn(),
  drawImage: vi.fn(),
  save: vi.fn(),
  restore: vi.fn(),
  beginPath: vi.fn(),
  moveTo: vi.fn(),
  lineTo: vi.fn(),
  closePath: vi.fn(),
  stroke: vi.fn(),
  fill: vi.fn()
}

// 模拟HTMLCanvasElement
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: vi.fn().mockReturnValue(mockContext)
})

describe('LineChart组件', () => {
  const defaultData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    datasets: [
      {
        label: 'Dataset 1',
        data: [10, 20, 30, 40, 50],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)'
      }
    ]
  }

  const defaultProps = {
    data: defaultData,
    width: 800,
    height: 400,
    loading: false,
    ariaLabel: '测试图表'
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    // 清理DOM
    document.body.innerHTML = ''
  })

  describe('基本渲染', () => {
    it('应该正确渲染图表容器', () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      expect(wrapper.find('.line-chart-container').exists()).toBe(true)
      expect(wrapper.find('canvas').exists()).toBe(true)
    })

    it('应该设置正确的canvas属性', () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      const canvas = wrapper.find('canvas')
      expect(canvas.attributes('width')).toBe('800')
      expect(canvas.attributes('height')).toBe('400')
      expect(canvas.attributes('aria-label')).toBe('测试图表')
    })

    it('应该设置正确的容器高度', () => {
      const wrapper = mount(LineChart, {
        props: {
          ...defaultProps,
          height: 300
        }
      })

      const container = wrapper.find('.line-chart-container')
      expect(container.attributes('style')).toContain('height: 300px')
    })
  })

  describe('加载状态', () => {
    it('应该显示加载状态', () => {
      const wrapper = mount(LineChart, {
        props: {
          ...defaultProps,
          loading: true
        }
      })

      expect(wrapper.find('.chart-loading').exists()).toBe(true)
      expect(wrapper.find('.loading-spinner').exists()).toBe(true)
      expect(wrapper.text()).toContain('加载中...')
    })

    it('应该在非加载状态时隐藏加载指示器', () => {
      const wrapper = mount(LineChart, {
        props: {
          ...defaultProps,
          loading: false
        }
      })

      expect(wrapper.find('.chart-loading').exists()).toBe(false)
    })
  })

  describe('空数据状态', () => {
    it('应该显示空数据状态', () => {
      const wrapper = mount(LineChart, {
        props: {
          ...defaultProps,
          data: { labels: [], datasets: [] },
          loading: false
        }
      })

      expect(wrapper.find('.chart-empty').exists()).toBe(true)
      expect(wrapper.find('.empty-icon').exists()).toBe(true)
      expect(wrapper.text()).toContain('暂无数据')
    })

    it('应该在有数据时隐藏空状态', () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      expect(wrapper.find('.chart-empty').exists()).toBe(false)
    })

    it('应该正确检测空数据集', () => {
      const wrapper = mount(LineChart, {
        props: {
          ...defaultProps,
          data: {
            labels: ['Jan', 'Feb'],
            datasets: [
              { label: 'Empty', data: [] }
            ]
          }
        }
      })

      expect(wrapper.find('.chart-empty').exists()).toBe(true)
    })
  })

  describe('图表创建', () => {
    it('应该创建Chart.js实例', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      expect(mockChartConstructor).toHaveBeenCalled()
      expect(mockChartConstructor).toHaveBeenCalledWith(
        mockContext,
        expect.objectContaining({
          type: 'line',
          data: expect.any(Object),
          options: expect.any(Object)
        })
      )
    })

    it('应该处理数据集默认样式', async () => {
      const dataWithoutStyles = {
        labels: ['Jan', 'Feb'],
        datasets: [
          {
            label: 'Dataset 1',
            data: [10, 20]
          }
        ]
      }

      const wrapper = mount(LineChart, {
        props: {
          ...defaultProps,
          data: dataWithoutStyles
        }
      })

      await wrapper.vm.$nextTick()

      expect(mockChartConstructor).toHaveBeenCalledWith(
        mockContext,
        expect.objectContaining({
          data: expect.objectContaining({
            datasets: expect.arrayContaining([
              expect.objectContaining({
                borderColor: expect.any(String),
                backgroundColor: expect.any(String),
                fill: false,
                tension: 0.4
              })
            ])
          })
        })
      )
    })

    it('应该合并自定义选项', async () => {
      const customOptions = {
        plugins: {
          legend: {
            display: false
          }
        }
      }

      const wrapper = mount(LineChart, {
        props: {
          ...defaultProps,
          options: customOptions
        }
      })

      await wrapper.vm.$nextTick()

      expect(mockChartConstructor).toHaveBeenCalledWith(
        mockContext,
        expect.objectContaining({
          options: expect.objectContaining({
            plugins: expect.objectContaining({
              legend: expect.objectContaining({
                display: false
              })
            })
          })
        })
      )
    })
  })

  describe('数据更新', () => {
    it('应该在数据变化时更新图表', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      const newData = {
        labels: ['Jun', 'Jul', 'Aug'],
        datasets: [
          {
            label: 'New Dataset',
            data: [60, 70, 80]
          }
        ]
      }

      await wrapper.setProps({ data: newData })

      expect(mockChart.update).toHaveBeenCalledWith('active')
    })

    it('应该在选项变化时更新图表', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      const newOptions = {
        responsive: false
      }

      await wrapper.setProps({ options: newOptions })

      expect(mockChart.update).toHaveBeenCalledWith('active')
    })

    it('应该在数据变为空时销毁图表', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      await wrapper.setProps({
        data: { labels: [], datasets: [] }
      })

      expect(mockChart.destroy).toHaveBeenCalled()
    })
  })

  describe('事件处理', () => {
    it('应该处理图表点击事件', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      const chartConfig = mockChartConstructor.mock.calls[0][1]
      const mockEvent = { type: 'click' }
      const mockElements = [{ index: 0 }]

      chartConfig.options.onClick(mockEvent, mockElements)

      expect(wrapper.emitted('chartClick')).toBeTruthy()
      expect(wrapper.emitted('chartClick')[0]).toEqual([mockEvent, mockElements])
    })

    it('应该处理图表悬停事件', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      const chartConfig = mockChartConstructor.mock.calls[0][1]
      const mockEvent = { type: 'mousemove' }
      const mockElements = [{ index: 1 }]

      chartConfig.options.onHover(mockEvent, mockElements)

      expect(wrapper.emitted('chartHover')).toBeTruthy()
      expect(wrapper.emitted('chartHover')[0]).toEqual([mockEvent, mockElements])
    })

    it('应该调用自定义事件处理器', async () => {
      const customOnClick = vi.fn()
      const customOnHover = vi.fn()

      const wrapper = mount(LineChart, {
        props: {
          ...defaultProps,
          options: {
            onClick: customOnClick,
            onHover: customOnHover
          }
        }
      })

      await wrapper.vm.$nextTick()

      const chartConfig = mockChartConstructor.mock.calls[0][1]
      const mockEvent = { type: 'click' }
      const mockElements = []

      chartConfig.options.onClick(mockEvent, mockElements)
      chartConfig.options.onHover(mockEvent, mockElements)

      expect(customOnClick).toHaveBeenCalledWith(mockEvent, mockElements)
      expect(customOnHover).toHaveBeenCalledWith(mockEvent, mockElements)
    })
  })

  describe('响应式处理', () => {
    it('应该在窗口大小变化时调整图表', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      // 触发resize事件
      window.dispatchEvent(new Event('resize'))

      expect(mockChart.resize).toHaveBeenCalled()
    })
  })

  describe('生命周期', () => {
    it('应该在组件卸载时销毁图表', () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      wrapper.unmount()

      expect(mockChart.destroy).toHaveBeenCalled()
    })

    it('应该在组件卸载时移除事件监听器', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener')

      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      wrapper.unmount()

      expect(removeEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function))
    })
  })

  describe('暴露的方法', () => {
    it('应该暴露getChart方法', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      expect(wrapper.vm.getChart()).toBe(mockChart)
    })

    it('应该暴露updateChart方法', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      wrapper.vm.updateChart()

      expect(mockChart.update).toHaveBeenCalledWith('active')
    })

    it('应该暴露destroyChart方法', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      wrapper.vm.destroyChart()

      expect(mockChart.destroy).toHaveBeenCalled()
    })
  })

  describe('边界情况', () => {
    it('应该处理canvas context不可用的情况', async () => {
      // 模拟getContext返回null
      vi.spyOn(HTMLCanvasElement.prototype, 'getContext').mockReturnValue(null)

      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      expect(mockChartConstructor).not.toHaveBeenCalled()
    })

    it('应该处理空数据的更新', async () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      await wrapper.vm.$nextTick()

      // 清空图表实例
      wrapper.vm.destroyChart()

      wrapper.vm.updateChart()

      // 应该不会抛出错误
      expect(true).toBe(true)
    })

    it('应该处理多次销毁调用', () => {
      const wrapper = mount(LineChart, {
        props: defaultProps
      })

      wrapper.vm.destroyChart()
      wrapper.vm.destroyChart()

      // 应该不会抛出错误
      expect(mockChart.destroy).toHaveBeenCalledTimes(1)
    })
  })

  describe('默认颜色', () => {
    it('应该为多个数据集分配不同颜色', async () => {
      const multiDatasetData = {
        labels: ['Jan', 'Feb'],
        datasets: [
          { label: 'Dataset 1', data: [10, 20] },
          { label: 'Dataset 2', data: [30, 40] },
          { label: 'Dataset 3', data: [50, 60] }
        ]
      }

      const wrapper = mount(LineChart, {
        props: {
          ...defaultProps,
          data: multiDatasetData
        }
      })

      await wrapper.vm.$nextTick()

      const chartConfig = mockChartConstructor.mock.calls[0][1]
      const datasets = chartConfig.data.datasets

      expect(datasets[0].borderColor).not.toBe(datasets[1].borderColor)
      expect(datasets[1].borderColor).not.toBe(datasets[2].borderColor)
    })

    it('应该循环使用颜色', async () => {
      const manyDatasetsData = {
        labels: ['Jan', 'Feb'],
        datasets: Array.from({ length: 8 }, (_, i) => ({
          label: `Dataset ${i + 1}`,
          data: [i * 10, (i + 1) * 10]
        }))
      }

      const wrapper = mount(LineChart, {
        props: {
          ...defaultProps,
          data: manyDatasetsData
        }
      })

      await wrapper.vm.$nextTick()

      const chartConfig = mockChartConstructor.mock.calls[0][1]
      const datasets = chartConfig.data.datasets

      // 第7个数据集应该使用与第1个相同的颜色（循环）
      expect(datasets[0].borderColor).toBe(datasets[6].borderColor)
    })
  })
})
