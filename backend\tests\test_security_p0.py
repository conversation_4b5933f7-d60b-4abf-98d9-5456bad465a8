"""
P0级安全测试 - SQL注入检测器验证
确保所有SQL查询通过参数化查询检测器验证
"""

import pytest
from app.core.database_security import (
    SQLInjectionDetector,
    SecurityError,
    ConnectionPool,
    SafeQueryBuilder,
)


class TestSQLInjectionDetector:
    """SQL注入检测器测试"""

    def setup_method(self):
        """测试前设置"""
        self.detector = SQLInjectionDetector()

    def test_safe_queries_pass(self):
        """测试安全查询通过检测"""
        safe_queries = [
            ("SELECT * FROM projects WHERE id = ?", (1,)),
            (
                "INSERT INTO projects (name, status) VALUES (?, ?)",
                ("test", "active"),
            ),
            ("UPDATE projects SET status = ? WHERE id = ?", ("completed", 1)),
            ("DELETE FROM projects WHERE id = ?", (1,)),
            ("SELECT COUNT(*) FROM projects WHERE status = ?", ("active",)),
        ]

        for query, params in safe_queries:
            # 应该不抛出异常
            assert self.detector.validate_query(query, params) is True

    def test_sql_injection_attacks_blocked(self):
        """测试SQL注入攻击被阻止"""
        malicious_queries = [
            # 注释注入
            ("SELECT * FROM projects WHERE id = 1'; DROP TABLE projects; --", ()),

            # UNION注入
            (
                "SELECT * FROM projects WHERE id = 1 UNION SELECT * FROM users",
                (),
            ),

            # OR注入
            ("SELECT * FROM projects WHERE id = ? OR 1=1", (1,)),
            # 删除表
            ("DROP TABLE projects", ()),
            # 脚本注入
            ("SELECT '<script>alert(1)</script>' FROM projects", ()),
        ]

        for query, params in malicious_queries:
            with pytest.raises(SecurityError):
                self.detector.validate_query(query, params)

    def test_parameter_mismatch_blocked(self):
        """测试参数数量不匹配被阻止"""
        with pytest.raises(SecurityError):
            # 查询需要2个参数，但只提供1个
            self.detector.validate_query(
                "SELECT * FROM projects WHERE id = ? AND status = ?", (1,)
            )

        with pytest.raises(SecurityError):
            # 查询需要1个参数，但提供2个
            self.detector.validate_query(
                "SELECT * FROM projects WHERE id = ?", (1, "active")
            )

    def test_dynamic_string_injection_blocked(self):
        """测试动态字符串拼接被阻止"""
        dangerous_queries = [
            "SELECT * FROM projects WHERE name = '{}'",
            "SELECT * FROM projects WHERE id = %s",
            "SELECT * FROM projects WHERE status = %d",
        ]

        for query in dangerous_queries:
            with pytest.raises(SecurityError):
                self.detector.validate_query(query, ())


class TestConnectionPool:
    """连接池测试"""

    def setup_method(self):
        """测试前设置"""
        self.pool = ConnectionPool(":memory:", max_connections=2)

    def test_connection_pool_basic(self):
        """测试连接池基本功能"""
        with self.pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY)")
            cursor.execute("INSERT INTO test (id) VALUES (?)", (1,))

            cursor.execute("SELECT * FROM test WHERE id = ?", (1,))
            result = cursor.fetchone()
            assert result[0] == 1

    def test_connection_pool_limit(self):
        """测试连接池限制"""
        connections = []

        # 获取最大数量的连接
        for _ in range(2):
            conn_context = self.pool.get_connection()
            conn = conn_context.__enter__()
            connections.append((conn_context, conn))

        # 尝试获取超出限制的连接应该失败
        with pytest.raises(SecurityError):
            with self.pool.get_connection() as conn:
                pass

        # 释放连接
        for conn_context, conn in connections:
            conn_context.__exit__(None, None, None)

    def test_connection_rollback_on_error(self):
        """测试错误时自动回滚"""
        with self.pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY)")

        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("INSERT INTO test (id) VALUES (?)", (1,))
                # 模拟错误
                raise Exception("测试错误")
        except Exception:
            pass

        # 验证事务已回滚
        with self.pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM test")
            count = cursor.fetchone()[0]
            assert count == 0


class TestSafeQueryBuilder:
    """安全查询构建器测试"""

    def setup_method(self):
        """测试前设置"""
        self.pool = ConnectionPool(":memory:")
        self.builder = SafeQueryBuilder(self.pool)

        # 创建测试表
        with self.pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                CREATE TABLE projects (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    status TEXT DEFAULT 'draft',
                    priority TEXT DEFAULT 'medium',
                    created_at TEXT
                )
            """
            )

            # 插入测试数据
            test_data = [
                (1, "项目1", "active", "high", "2024-01-01"),
                (2, "项目2", "completed", "medium", "2024-01-02"),
                (3, "项目3", "draft", "low", "2024-01-03"),
            ]
            cursor.executemany(
                "INSERT INTO projects (id, name, status, priority, created_at) "
                "VALUES (?, ?, ?, ?, ?)",
                test_data,
            )
            conn.commit()

    def test_safe_where_clause_building(self):
        """测试安全WHERE子句构建"""
        conditions = {"status": "active", "priority": "high"}

        where_clause, params = self.builder.build_where_clause(conditions)

        assert "status = ?" in where_clause
        assert "priority = ?" in where_clause
        assert "active" in params
        assert "high" in params

    def test_safe_query_execution(self):
        """测试安全查询执行"""
        query = "SELECT * FROM projects WHERE status = ?"
        params = ("active",)

        result = self.builder.execute_safe_query(query, params)

        assert len(result.rows) == 1
        assert result.rows[0]["name"] == "项目1"
        assert result.execution_time > 0

    def test_safe_transaction_execution(self):
        """测试安全事务执行"""
        operations = [
            ("UPDATE projects SET status = ? WHERE id = ?", ("completed", 1)),
            ("UPDATE projects SET priority = ? WHERE id = ?", ("urgent", 1)),
        ]

        success = self.builder.execute_safe_transaction(operations)
        assert success is True

        # 验证更新结果
        result = self.builder.execute_safe_query(
            "SELECT status, priority FROM projects WHERE id = ?", (1,)
        )
        row = result.rows[0]
        assert row["status"] == "completed"
        assert row["priority"] == "urgent"

    def test_sort_field_validation(self):
        """测试排序字段验证"""
        allowed_fields = ["id", "name", "status", "created_at"]

        # 合法字段应该通过
        assert self.builder._validate_sort_field("name", allowed_fields) == "name"

        # 非法字段应该抛出异常
        with pytest.raises(SecurityError):
            self.builder._validate_sort_field("malicious_field", allowed_fields)

        with pytest.raises(SecurityError):
            self.builder._validate_sort_field(
                "id; DROP TABLE projects", allowed_fields
            )

    def test_sort_order_validation(self):
        """测试排序方向验证"""
        assert self.builder._validate_sort_order("asc") == "ASC"
        assert self.builder._validate_sort_order("DESC") == "DESC"

        with pytest.raises(SecurityError):
            self.builder._validate_sort_order("malicious_order")

    def test_malicious_field_names_blocked(self):
        """测试恶意字段名被阻止"""
        malicious_conditions = {
            "id; DROP TABLE projects": 1,
            "name' OR 1=1 --": "test",
            "status) UNION SELECT * FROM users --": "active",
        }

        for field, value in malicious_conditions.items():
            with pytest.raises(SecurityError):
                self.builder.build_where_clause({field: value})


class TestSecurityIntegration:
    """安全集成测试"""

    def test_end_to_end_security(self):
        """端到端安全测试"""
        pool = ConnectionPool(":memory:")
        builder = SafeQueryBuilder(pool)

        # 创建表
        with pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                CREATE TABLE projects (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    status TEXT DEFAULT 'draft'
                )
            """
            )
            conn.commit()

        # 测试安全的CRUD操作
        # 1. 创建
        create_ops = [
            (
                "INSERT INTO projects (name, description, status) "
                "VALUES (?, ?, ?)",
                ("安全项目", "这是一个安全的项目", "active"),
            )
        ]
        assert builder.execute_safe_transaction(create_ops) is True

        # 2. 读取
        result = builder.execute_safe_query(
            "SELECT * FROM projects WHERE name = ?", ("安全项目",)
        )
        assert len(result.rows) == 1
        project_id = result.rows[0]["id"]

        # 3. 更新
        update_ops = [
            (
                "UPDATE projects SET status = ? WHERE id = ?",
                ("completed", project_id),
            )
        ]
        assert builder.execute_safe_transaction(update_ops) is True

        # 4. 验证更新
        result = builder.execute_safe_query(
            "SELECT status FROM projects WHERE id = ?", (project_id,)
        )
        assert result.rows[0]["status"] == "completed"

        # 5. 删除
        delete_ops = [("DELETE FROM projects WHERE id = ?", (project_id,))]
        assert builder.execute_safe_transaction(delete_ops) is True

        # 6. 验证删除
        result = builder.execute_safe_query(
            "SELECT COUNT(*) FROM projects WHERE id = ?", (project_id,)
        )
        assert result.rows[0][0] == 0


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
