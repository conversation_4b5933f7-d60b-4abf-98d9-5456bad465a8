"""
安全数据库迁移脚本 - P1级安全修复
解决迁移过程中的事务安全、数据丢失风险
"""

import sqlite3
import shutil
from datetime import datetime
from pathlib import Path
import logging
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库路径
DB_PATH = Path(__file__).parent.parent.parent / "ai_video_system.db"


class SafeMigrationError(Exception):
    """安全迁移异常"""


class SafeDatabaseMigrator:
    """安全数据库迁移器"""

    def __init__(self, db_path: Path):
        self.db_path = db_path
        self.backup_path = None
        self.migration_log = []

    def create_backup(self) -> Path:
        """创建数据库备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.db_path.with_suffix(f".migration_backup_{timestamp}.db")

        try:
            shutil.copy2(self.db_path, backup_path)
            self.backup_path = backup_path
            logger.info(f"✅ 数据库已备份到: {backup_path}")
            self.migration_log.append(f"备份创建: {backup_path}")
            return backup_path
        except Exception as e:
            raise SafeMigrationError(f"备份创建失败: {e}")

    def verify_backup(self) -> bool:
        """验证备份完整性"""
        if not self.backup_path or not self.backup_path.exists():
            return False

        try:
            # 验证备份文件可以正常打开
            conn = sqlite3.connect(self.backup_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM projects")
            count = cursor.fetchone()[0]
            conn.close()

            logger.info(f"✅ 备份验证成功，包含 {count} 个项目")
            self.migration_log.append(f"备份验证: {count} 个项目")
            return True
        except Exception as e:
            logger.error(f"❌ 备份验证失败: {e}")
            return False

    def check_migration_prerequisites(self) -> bool:
        """检查迁移前置条件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='projects'"
            )
            if not cursor.fetchone():
                raise SafeMigrationError("projects表不存在")

            # 检查数据完整性
            cursor.execute(
                "SELECT COUNT(*) FROM projects WHERE name IS NULL OR name = ''"
            )
            null_names = cursor.fetchone()[0]
            if null_names > 0:
                logger.warning(f"⚠️ 发现 {null_names} 个项目名称为空")

            # 检查是否有正在进行的事务
            cursor.execute("PRAGMA journal_mode")
            journal_mode = cursor.fetchone()[0]
            logger.info(f"📋 当前日志模式: {journal_mode}")

            conn.close()
            self.migration_log.append("前置条件检查: 通过")
            return True

        except Exception as e:
            raise SafeMigrationError(f"前置条件检查失败: {e}")

    def execute_safe_migration(self) -> bool:
        """执行安全迁移"""
        logger.info("🔄 开始安全迁移...")

        try:
            conn = sqlite3.connect(self.db_path)

            # 启用WAL模式以支持并发读取
            conn.execute("PRAGMA journal_mode = WAL")
            conn.execute("PRAGMA synchronous = FULL")  # 最高安全级别
            conn.execute("PRAGMA foreign_keys = ON")

            cursor = conn.cursor()

            # 开始事务
            cursor.execute("BEGIN IMMEDIATE TRANSACTION")

            try:
                # 1. 检查现有表结构
                cursor.execute("PRAGMA table_info(projects)")
                existing_columns = {row[1]: row[2] for row in cursor.fetchall()}
                logger.info(f"📋 现有列: {list(existing_columns.keys())}")

                # 2. 创建临时表（安全的结构）
                cursor.execute(
                    """
                    CREATE TABLE projects_migration_temp (
                        id INTEGER PRIMARY KEY,
                        name VARCHAR(200) NOT NULL,
                        description TEXT,
                        project_type VARCHAR(50) DEFAULT 'video_creation' NOT NULL,
                        owner_id INTEGER NOT NULL,
                        created_by INTEGER NOT NULL,
                        status VARCHAR(20) DEFAULT 'draft' NOT NULL,
                        priority VARCHAR(10) DEFAULT 'medium' NOT NULL,
                        progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
                        estimated_hours INTEGER CHECK (estimated_hours > 0),
                        actual_hours INTEGER DEFAULT 0 CHECK (actual_hours >= 0),
                        start_date DATETIME,
                        due_date DATETIME,
                        completed_at DATETIME,
                        config JSON,
                        tags JSON,
                        task_count INTEGER DEFAULT 0 CHECK (task_count >= 0),
                        completed_task_count INTEGER DEFAULT 0 CHECK (completed_task_count >= 0),
                        member_count INTEGER DEFAULT 1 CHECK (member_count > 0),
                        is_active BOOLEAN DEFAULT 1,
                        is_template BOOLEAN DEFAULT 0,
                        is_public BOOLEAN DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                        updated_at DATETIME,
                        notes TEXT,
                        last_activity TEXT,
                        
                        -- 约束检查
                        CHECK (completed_task_count <= task_count),
                        CHECK (status IN ('draft', 'planning', 'in_progress', 'review', 'completed', 'archived', 'cancelled')),
                        CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
                        CHECK (project_type IN ('video_creation', 'content_distribution', 'ai_analysis', 'automation', 'integration', 'research'))
                    )
                """
                )

                # 3. 安全地迁移数据
                migration_query = """
                    INSERT INTO projects_migration_temp (
                        id, name, description, owner_id, created_by, status, 
                        config, created_at, updated_at, project_type, priority,
                        progress_percentage, is_active
                    )
                    SELECT 
                        id, 
                        COALESCE(name, 'Unnamed Project') as name,
                        description, 
                        COALESCE(owner_id, 1) as owner_id,
                        COALESCE(owner_id, 1) as created_by,
                        CASE 
                            WHEN status = 'active' THEN 'in_progress'
                            WHEN status = 'paused' THEN 'draft'
                            WHEN status IN ('draft', 'planning', 'in_progress', 'review', 'completed', 'archived', 'cancelled') THEN status
                            ELSE 'draft'
                        END as status,
                        config,
                        COALESCE(created_at, datetime('now')) as created_at,
                        updated_at,
                        CASE 
                            WHEN type = 'video' THEN 'video_creation'
                            WHEN type = 'content' THEN 'content_distribution'
                            ELSE 'video_creation'
                        END as project_type,
                        'medium' as priority,
                        0 as progress_percentage,
                        CASE WHEN status = 'active' THEN 1 ELSE 0 END as is_active
                    FROM projects
                """

                cursor.execute(migration_query)
                migrated_count = cursor.rowcount
                logger.info(f"✅ 迁移了 {migrated_count} 个项目")

                # 4. 验证迁移数据
                cursor.execute("SELECT COUNT(*) FROM projects")
                original_count = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM projects_migration_temp")
                temp_count = cursor.fetchone()[0]

                if original_count != temp_count:
                    raise SafeMigrationError(
                        f"数据迁移不完整: 原始{original_count}, 迁移{temp_count}"
                    )

                # 5. 原子性替换表
                cursor.execute("DROP TABLE projects")
                cursor.execute("ALTER TABLE projects_migration_temp RENAME TO projects")

                # 6. 重建索引（优化版）
                optimized_indexes = [
                    "CREATE INDEX idx_projects_status_type ON projects(status, project_type)",
                    "CREATE INDEX idx_projects_owner_status ON projects(owner_id, status)",
                    "CREATE INDEX idx_projects_created_desc ON projects(created_at DESC)",
                    "CREATE INDEX idx_projects_active ON projects(is_active, status) WHERE is_active = 1",
                    "CREATE INDEX idx_projects_name_search ON projects(LOWER(name))",
                ]

                for index_sql in optimized_indexes:
                    cursor.execute(index_sql)

                # 7. 创建关联表（如果不存在）
                self._create_related_tables(cursor)

                # 8. 添加数据完整性触发器
                self._create_integrity_triggers(cursor)

                # 提交事务
                conn.commit()

                logger.info("✅ 安全迁移完成")
                self.migration_log.append("迁移执行: 成功")
                return True

            except Exception as e:
                # 回滚事务
                conn.rollback()
                raise SafeMigrationError(f"迁移执行失败: {e}")

        except Exception as e:
            logger.error(f"❌ 安全迁移失败: {e}")
            self.migration_log.append(f"迁移执行: 失败 - {e}")
            return False
        finally:
            if "conn" in locals():
                conn.close()

    def _create_related_tables(self, cursor):
        """创建关联表"""
        # 项目成员表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS project_members (
                id INTEGER PRIMARY KEY,
                project_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                role VARCHAR(50) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member', 'viewer')),
                joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
                UNIQUE(project_id, user_id)
            )
        """
        )

        # 项目历史表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS project_history (
                id INTEGER PRIMARY KEY,
                project_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                action VARCHAR(50) NOT NULL,
                description TEXT,
                old_value JSON,
                new_value JSON,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
            )
        """
        )

        # 项目模板表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS project_templates (
                id INTEGER PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                description TEXT,
                project_type VARCHAR(50) NOT NULL,
                template_config JSON,
                is_active BOOLEAN DEFAULT 1,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME
            )
        """
        )

        # 创建关联表索引
        cursor.execute(
            "CREATE INDEX IF NOT EXISTS idx_project_members_composite ON project_members(project_id, user_id, is_active)"
        )
        cursor.execute(
            "CREATE INDEX IF NOT EXISTS idx_project_history_composite ON project_history(project_id, created_at DESC)"
        )

    def _create_integrity_triggers(self, cursor):
        """创建数据完整性触发器"""
        # 进度一致性触发器
        cursor.execute(
            """
            CREATE TRIGGER IF NOT EXISTS ensure_progress_consistency
            BEFORE UPDATE OF progress_percentage ON projects
            BEGIN
                SELECT CASE
                    WHEN NEW.status = 'completed' AND NEW.progress_percentage < 100 THEN
                        RAISE(ABORT, 'Completed projects must have 100% progress')
                    WHEN NEW.status = 'draft' AND NEW.progress_percentage > 0 THEN
                        RAISE(ABORT, 'Draft projects should have 0% progress')
                END;
            END
        """
        )

        # 任务计数一致性触发器
        cursor.execute(
            """
            CREATE TRIGGER IF NOT EXISTS ensure_task_consistency
            BEFORE UPDATE OF completed_task_count ON projects
            BEGIN
                SELECT CASE
                    WHEN NEW.completed_task_count > NEW.task_count THEN
                        RAISE(ABORT, 'Completed tasks cannot exceed total tasks')
                END;
            END
        """
        )

        # 状态变更历史记录触发器
        cursor.execute(
            """
            CREATE TRIGGER IF NOT EXISTS log_status_changes
            AFTER UPDATE OF status ON projects
            WHEN OLD.status != NEW.status
            BEGIN
                INSERT INTO project_history (project_id, user_id, action, description, old_value, new_value)
                VALUES (NEW.id, NEW.owner_id, 'status_change', 
                       'Project status changed', 
                       json_object('status', OLD.status),
                       json_object('status', NEW.status));
            END
        """
        )

    def verify_migration(self) -> bool:
        """验证迁移结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查表结构
            cursor.execute("PRAGMA table_info(projects)")
            columns = [row[1] for row in cursor.fetchall()]

            required_columns = [
                "id",
                "name",
                "project_type",
                "status",
                "priority",
                "progress_percentage",
            ]
            missing_columns = [col for col in required_columns if col not in columns]

            if missing_columns:
                raise SafeMigrationError(f"缺少必需列: {missing_columns}")

            # 检查数据完整性
            cursor.execute(
                "SELECT COUNT(*) FROM projects WHERE name IS NULL OR name = ''"
            )
            invalid_names = cursor.fetchone()[0]

            cursor.execute(
                "SELECT COUNT(*) FROM projects WHERE progress_percentage < 0 OR progress_percentage > 100"
            )
            invalid_progress = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM projects")
            total_projects = cursor.fetchone()[0]

            conn.close()

            if invalid_names > 0 or invalid_progress > 0:
                raise SafeMigrationError(
                    f"数据完整性问题: {invalid_names} 个无效名称, {invalid_progress} 个无效进度"
                )

            logger.info(f"✅ 迁移验证成功: {total_projects} 个项目")
            self.migration_log.append(f"迁移验证: 成功 - {total_projects} 个项目")
            return True

        except Exception as e:
            logger.error(f"❌ 迁移验证失败: {e}")
            self.migration_log.append(f"迁移验证: 失败 - {e}")
            return False

    def rollback_migration(self) -> bool:
        """回滚迁移"""
        if not self.backup_path or not self.backup_path.exists():
            logger.error("❌ 无法回滚：备份文件不存在")
            return False

        try:
            shutil.copy2(self.backup_path, self.db_path)
            logger.info("🔄 迁移已回滚到备份状态")
            self.migration_log.append("迁移回滚: 成功")
            return True
        except Exception as e:
            logger.error(f"❌ 回滚失败: {e}")
            self.migration_log.append(f"迁移回滚: 失败 - {e}")
            return False

    def save_migration_log(self):
        """保存迁移日志"""
        log_path = self.db_path.with_suffix(".migration_log.json")
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "backup_path": str(self.backup_path) if self.backup_path else None,
            "migration_log": self.migration_log,
        }

        with open(log_path, "w", encoding="utf-8") as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)

        logger.info(f"📝 迁移日志已保存: {log_path}")


def main():
    """主迁移函数"""
    logger.info("🚀 开始安全数据库迁移...")

    migrator = SafeDatabaseMigrator(DB_PATH)

    try:
        # 1. 创建备份
        migrator.create_backup()

        # 2. 验证备份
        if not migrator.verify_backup():
            raise SafeMigrationError("备份验证失败")

        # 3. 检查前置条件
        migrator.check_migration_prerequisites()

        # 4. 执行迁移
        if not migrator.execute_safe_migration():
            raise SafeMigrationError("迁移执行失败")

        # 5. 验证迁移结果
        if not migrator.verify_migration():
            logger.warning("⚠️ 迁移验证失败，尝试回滚...")
            migrator.rollback_migration()
            raise SafeMigrationError("迁移验证失败，已回滚")

        # 6. 保存迁移日志
        migrator.save_migration_log()

        logger.info("🎉 安全迁移完成！")
        logger.info("主要改进:")
        logger.info("  ✅ 事务安全保护")
        logger.info("  ✅ 数据完整性约束")
        logger.info("  ✅ 自动备份和回滚")
        logger.info("  ✅ 完整性验证")

        return True

    except Exception as e:
        logger.error(f"❌ 安全迁移失败: {e}")
        migrator.save_migration_log()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
