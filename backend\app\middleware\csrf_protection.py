"""CSRF保护中间件
防止跨站请求伪造攻击
"""

import secrets
import hmac
import hashlib
import time
import logging
from typing import Optional, Set, Dict, Any
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from app.core.config import settings

logger = logging.getLogger(__name__)


class CSRFError(HTTPException):
    """CSRF验证错误"""
    def __init__(self, detail: str = "CSRF token validation failed"):
        super().__init__(status_code=403, detail=detail)


class CSRFProtection:
    """CSRF保护器"""
    
    def __init__(self, secret_key: str = None):
        self.secret_key = secret_key or settings.SECRET_KEY
        self.token_lifetime = 3600  # 1小时
        self.header_name = "X-CSRF-Token"
        self.cookie_name = "csrf_token"
        
        # 需要CSRF保护的HTTP方法
        self.protected_methods = {"POST", "PUT", "PATCH", "DELETE"}
        
        # 白名单路径（不需要CSRF保护）
        self.whitelist_paths = {
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/auth/refresh",
            "/health",
            "/metrics",
            "/docs",
            "/openapi.json"
        }
        
        # 白名单路径模式
        self.whitelist_patterns = {
            "/static/",
            "/assets/",
            "/favicon.ico"
        }
    
    def generate_token(self, session_id: str = None) -> str:
        """生成CSRF令牌
        
        Args:
            session_id: 会话ID（可选）
        
        Returns:
            str: CSRF令牌
        """
        # 生成随机数据
        random_data = secrets.token_bytes(32)
        
        # 当前时间戳
        timestamp = int(time.time())
        
        # 构建数据
        data = f"{timestamp}:{session_id or 'anonymous'}:{random_data.hex()}"
        
        # 生成HMAC签名
        signature = hmac.new(
            self.secret_key.encode(),
            data.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # 返回令牌
        return f"{data}:{signature}"
    
    def validate_token(self, token: str, session_id: str = None) -> bool:
        """验证CSRF令牌
        
        Args:
            token: CSRF令牌
            session_id: 会话ID（可选）
        
        Returns:
            bool: 验证是否成功
        """
        try:
            # 解析令牌
            parts = token.split(':')
            if len(parts) != 4:
                return False
            
            timestamp_str, token_session_id, random_hex, signature = parts
            
            # 验证时间戳
            timestamp = int(timestamp_str)
            current_time = int(time.time())
            
            if current_time - timestamp > self.token_lifetime:
                logger.warning("CSRF token expired")
                return False
            
            # 验证会话ID
            expected_session_id = session_id or 'anonymous'
            if token_session_id != expected_session_id:
                logger.warning("CSRF token session mismatch")
                return False
            
            # 重新计算签名
            data = f"{timestamp_str}:{token_session_id}:{random_hex}"
            expected_signature = hmac.new(
                self.secret_key.encode(),
                data.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # 验证签名
            if not hmac.compare_digest(signature, expected_signature):
                logger.warning("CSRF token signature invalid")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"CSRF token validation error: {e}")
            return False
    
    def is_path_whitelisted(self, path: str) -> bool:
        """检查路径是否在白名单中"""
        # 检查精确匹配
        if path in self.whitelist_paths:
            return True
        
        # 检查模式匹配
        for pattern in self.whitelist_patterns:
            if path.startswith(pattern):
                return True
        
        return False
    
    def needs_protection(self, request: Request) -> bool:
        """检查请求是否需要CSRF保护"""
        # 检查HTTP方法
        if request.method not in self.protected_methods:
            return False
        
        # 检查路径白名单
        if self.is_path_whitelisted(request.url.path):
            return False
        
        # 检查Content-Type（API请求通常使用JSON）
        content_type = request.headers.get("content-type", "")
        if content_type.startswith("application/json"):
            # JSON API请求需要保护
            return True
        
        # 表单提交需要保护
        if content_type.startswith("application/x-www-form-urlencoded") or \
           content_type.startswith("multipart/form-data"):
            return True
        
        return False
    
    def get_token_from_request(self, request: Request) -> Optional[str]:
        """从请求中获取CSRF令牌"""
        # 优先从请求头获取
        token = request.headers.get(self.header_name)
        if token:
            return token
        
        # 从Cookie获取
        token = request.cookies.get(self.cookie_name)
        if token:
            return token
        
        return None
    
    def get_session_id(self, request: Request) -> Optional[str]:
        """获取会话ID"""
        # 从请求状态获取用户ID
        if hasattr(request.state, 'user_id') and request.state.user_id:
            return str(request.state.user_id)
        
        # 从会话Cookie获取
        session_id = request.cookies.get('session_id')
        if session_id:
            return session_id
        
        return None


class CSRFMiddleware(BaseHTTPMiddleware):
    """CSRF保护中间件"""
    
    def __init__(self, app, csrf_protection: CSRFProtection = None):
        super().__init__(app)
        self.csrf = csrf_protection or CSRFProtection()
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """处理请求"""
        try:
            # 检查是否需要CSRF保护
            if not self.csrf.needs_protection(request):
                response = await call_next(request)
                
                # 为GET请求添加CSRF令牌到响应头
                if request.method == "GET" and not self.csrf.is_path_whitelisted(request.url.path):
                    session_id = self.csrf.get_session_id(request)
                    csrf_token = self.csrf.generate_token(session_id)
                    response.headers["X-CSRF-Token"] = csrf_token
                
                return response
            
            # 获取CSRF令牌
            csrf_token = self.csrf.get_token_from_request(request)
            
            if not csrf_token:
                logger.warning(f"Missing CSRF token for {request.method} {request.url.path}")
                return JSONResponse(
                    status_code=403,
                    content={
                        "error": {
                            "code": "CSRF_TOKEN_MISSING",
                            "message": "CSRF token is required",
                            "help": f"Include CSRF token in '{self.csrf.header_name}' header or '{self.csrf.cookie_name}' cookie"
                        }
                    }
                )
            
            # 验证CSRF令牌
            session_id = self.csrf.get_session_id(request)
            
            if not self.csrf.validate_token(csrf_token, session_id):
                logger.warning(f"Invalid CSRF token for {request.method} {request.url.path}")
                return JSONResponse(
                    status_code=403,
                    content={
                        "error": {
                            "code": "CSRF_TOKEN_INVALID",
                            "message": "CSRF token validation failed",
                            "help": "Please refresh the page and try again"
                        }
                    }
                )
            
            # 处理请求
            response = await call_next(request)
            
            # 为响应添加新的CSRF令牌
            new_csrf_token = self.csrf.generate_token(session_id)
            response.headers["X-CSRF-Token"] = new_csrf_token
            
            return response
            
        except Exception as e:
            logger.error(f"CSRF middleware error: {e}")
            # 发生错误时拒绝请求
            return JSONResponse(
                status_code=500,
                content={
                    "error": {
                        "code": "CSRF_MIDDLEWARE_ERROR",
                        "message": "CSRF protection error"
                    }
                }
            )


class CSRFConfig:
    """CSRF配置类"""
    
    def __init__(
        self,
        secret_key: str = None,
        token_lifetime: int = 3600,
        header_name: str = "X-CSRF-Token",
        cookie_name: str = "csrf_token",
        whitelist_paths: Set[str] = None,
        whitelist_patterns: Set[str] = None
    ):
        self.secret_key = secret_key or settings.SECRET_KEY
        self.token_lifetime = token_lifetime
        self.header_name = header_name
        self.cookie_name = cookie_name
        self.whitelist_paths = whitelist_paths or set()
        self.whitelist_patterns = whitelist_patterns or set()


# 创建全局CSRF保护实例
csrf_protection = CSRFProtection()


def create_csrf_middleware(config: CSRFConfig = None) -> CSRFMiddleware:
    """创建CSRF中间件
    
    Args:
        config: CSRF配置
    
    Returns:
        CSRFMiddleware: CSRF中间件实例
    """
    if config:
        csrf = CSRFProtection(config.secret_key)
        csrf.token_lifetime = config.token_lifetime
        csrf.header_name = config.header_name
        csrf.cookie_name = config.cookie_name
        csrf.whitelist_paths.update(config.whitelist_paths)
        csrf.whitelist_patterns.update(config.whitelist_patterns)
        return CSRFMiddleware(None, csrf)
    
    return CSRFMiddleware


# 便捷函数
def generate_csrf_token(session_id: str = None) -> str:
    """生成CSRF令牌"""
    return csrf_protection.generate_token(session_id)


def validate_csrf_token(token: str, session_id: str = None) -> bool:
    """验证CSRF令牌"""
    return csrf_protection.validate_token(token, session_id)


# 装饰器
def csrf_exempt(func):
    """CSRF豁免装饰器"""
    func._csrf_exempt = True
    return func


def require_csrf(func):
    """强制CSRF保护装饰器"""
    func._require_csrf = True
    return func