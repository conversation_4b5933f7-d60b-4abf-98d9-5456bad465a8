import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { setupStore } from './stores'
import VueLazyload from 'vue-lazyload'
import './style.css'
import * as Sentry from "@sentry/vue";
import { BrowserTracing } from "@sentry/tracing";

// 创建应用实例
const app = createApp(App)

// 配置Sentry
Sentry.init({
  app,
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  integrations: [
    new BrowserTracing({
      routingInstrumentation: Sentry.vueRouterInstrumentation(router),
      tracingOrigins: ["localhost", new RegExp("^/")]
    })
  ],
  tracesSampleRate: 1.0,
});

// 先配置状态管理
setupStore(app)

// 配置懒加载
app.use(VueLazyload, {
  preLoad: 1.3,
  error: 'error.png',
  loading: 'loading.png',
  attempt: 1
})

// 再配置路由
app.use(router)

// 挂载应用
app.mount('#app')