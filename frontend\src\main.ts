import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { setupStore } from './stores'
import './style.css'

// 创建应用实例
const app = createApp(App)

// 错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue应用错误:', err, info)
}

// 全局异常处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason)
})

try {
  // 先配置状态管理
  setupStore(app)

  // 再配置路由
  app.use(router)

  // 挂载应用
  app.mount('#app')

  console.log('Vue应用启动成功')
} catch (error) {
  console.error('Vue应用启动失败:', error)

  // 显示错误信息给用户
  document.body.innerHTML = `
    <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
      <h1 style="color: #e74c3c;">应用启动失败</h1>
      <p>请检查控制台错误信息或刷新页面重试</p>
      <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; text-align: left; overflow: auto;">
        ${error.message}
      </pre>
    </div>
  `
}