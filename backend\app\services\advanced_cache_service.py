#!/usr/bin/env python3
"""
高级Redis缓存服务
提供分布式缓存、缓存策略、失效机制等功能
"""

import json
import logging
import pickle
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional

from pydantic import BaseModel
from redis.asyncio import Redis

from .redis_manager import redis_manager


class CacheStrategy(str, Enum):
    """缓存策略"""

    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    TTL = "ttl"  # 基于时间
    WRITE_THROUGH = "write_through"  # 写透
    WRITE_BACK = "write_back"  # 写回


class SerializationMethod(str, Enum):
    """序列化方法"""

    JSON = "json"
    PICKLE = "pickle"
    STRING = "string"


class CacheMetrics(BaseModel):
    """缓存指标"""

    hit_count: int = 0
    miss_count: int = 0
    set_count: int = 0
    delete_count: int = 0
    expire_count: int = 0

    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0

    @property
    def total_operations(self) -> int:
        """总操作数"""
        return self.hit_count + self.miss_count + self.set_count + self.delete_count


class AdvancedRedisCache:
    """高级Redis缓存服务"""

    def __init__(self, namespace: str = "default"):
        self.namespace = namespace
        self.logger = logging.getLogger(__name__)
        self.metrics = CacheMetrics()
        self._redis: Optional[Redis] = None

    async def _get_redis(self) -> Redis:
        """获取Redis客户端"""
        if self._redis is None:
            self._redis = await redis_manager.get_redis()
        return self._redis

    def _get_key(self, key: str) -> str:
        """生成带命名空间的键"""
        return redis_manager.get_cache_key(f"{self.namespace}:{key}")

    def _get_metrics_key(self) -> str:
        """生成指标键"""
        return redis_manager.get_cache_key(f"metrics:{self.namespace}")

    def _serialize(
        self,
        value: Any,
        method: SerializationMethod = SerializationMethod.JSON,
    ) -> str:
        """序列化数据"""
        try:
            if method == SerializationMethod.JSON:
                return json.dumps(value, ensure_ascii=False, default=str)
            elif method == SerializationMethod.PICKLE:
                return pickle.dumps(value).hex()
            elif method == SerializationMethod.STRING:
                return str(value)
            else:
                raise ValueError(f"不支持的序列化方法: {method}")
        except Exception as e:
            self.logger.error(f"序列化失败: {e}")
            raise

    def _deserialize(
        self, data: str, method: SerializationMethod = SerializationMethod.JSON
    ) -> Any:
        """反序列化数据"""
        try:
            if method == SerializationMethod.JSON:
                return json.loads(data)
            elif method == SerializationMethod.PICKLE:
                return pickle.loads(bytes.fromhex(data))
            elif method == SerializationMethod.STRING:
                return data
            else:
                raise ValueError(f"不支持的反序列化方法: {method}")
        except Exception as e:
            self.logger.error(f"反序列化失败: {e}")
            raise

    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        method: SerializationMethod = SerializationMethod.JSON,
    ) -> bool:
        """设置缓存"""
        try:
            redis_client = await self._get_redis()
            cache_key = self._get_key(key)

            # 序列化数据
            serialized_value = self._serialize(value, method)

            # 存储序列化方法信息
            cache_data = {
                "data": serialized_value,
                "method": method.value,
                "timestamp": datetime.now().isoformat(),
            }

            # 设置缓存
            if ttl:
                await redis_client.setex(cache_key, ttl, json.dumps(cache_data))
            else:
                await redis_client.set(cache_key, json.dumps(cache_data))

            # 更新指标
            self.metrics.set_count += 1
            await self._update_metrics()

            self.logger.debug(f"缓存设置成功: {key}")
            return True

        except Exception as e:
            self.logger.error(f"设置缓存失败: {e}")
            return False

    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            redis_client = await self._get_redis()
            cache_key = self._get_key(key)

            # 获取缓存数据
            cached_data = await redis_client.get(cache_key)

            if cached_data is None:
                self.metrics.miss_count += 1
                await self._update_metrics()
                return None

            # 解析缓存数据
            cache_info = json.loads(cached_data)
            method = SerializationMethod(cache_info["method"])

            # 反序列化
            value = self._deserialize(cache_info["data"], method)

            # 更新指标
            self.metrics.hit_count += 1
            await self._update_metrics()

            self.logger.debug(f"缓存命中: {key}")
            return value

        except Exception as e:
            self.logger.error(f"获取缓存失败: {e}")
            self.metrics.miss_count += 1
            await self._update_metrics()
            return None

    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            redis_client = await self._get_redis()
            cache_key = self._get_key(key)

            result = await redis_client.delete(cache_key)

            if result:
                self.metrics.delete_count += 1
                await self._update_metrics()
                self.logger.debug(f"缓存删除成功: {key}")

            return bool(result)

        except Exception as e:
            self.logger.error(f"删除缓存失败: {e}")
            return False

    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            redis_client = await self._get_redis()
            cache_key = self._get_key(key)
            return bool(await redis_client.exists(cache_key))
        except Exception as e:
            self.logger.error(f"检查缓存存在性失败: {e}")
            return False

    async def expire(self, key: str, ttl: int) -> bool:
        """设置缓存过期时间"""
        try:
            redis_client = await self._get_redis()
            cache_key = self._get_key(key)

            result = await redis_client.expire(cache_key, ttl)

            if result:
                self.metrics.expire_count += 1
                await self._update_metrics()

            return bool(result)

        except Exception as e:
            self.logger.error(f"设置缓存过期时间失败: {e}")
            return False

    async def ttl(self, key: str) -> int:
        """获取缓存剩余时间"""
        try:
            redis_client = await self._get_redis()
            cache_key = self._get_key(key)
            return await redis_client.ttl(cache_key)
        except Exception as e:
            self.logger.error(f"获取缓存TTL失败: {e}")
            return -1

    async def clear_namespace(self) -> int:
        """清空命名空间下的所有缓存"""
        try:
            redis_client = await self._get_redis()
            pattern = self._get_key("*")

            keys = await redis_client.keys(pattern)
            if keys:
                deleted = await redis_client.delete(*keys)
                self.logger.info(f"清空命名空间 {self.namespace}: {deleted} 个键")
                return deleted

            return 0

        except Exception as e:
            self.logger.error(f"清空命名空间失败: {e}")
            return 0

    async def _update_metrics(self):
        """更新缓存指标"""
        try:
            redis_client = await self._get_redis()
            metrics_key = self._get_metrics_key()

            await redis_client.setex(
                metrics_key, 3600, self.metrics.json()  # 指标缓存1小时
            )

        except Exception as e:
            self.logger.error(f"更新缓存指标失败: {e}")

    async def get_metrics(self) -> CacheMetrics:
        """获取缓存指标"""
        try:
            redis_client = await self._get_redis()
            metrics_key = self._get_metrics_key()

            cached_metrics = await redis_client.get(metrics_key)
            if cached_metrics:
                return CacheMetrics.parse_raw(cached_metrics)

            return self.metrics

        except Exception as e:
            self.logger.error(f"获取缓存指标失败: {e}")
            return self.metrics

    async def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        try:
            redis_client = await self._get_redis()
            pattern = self._get_key("*")

            keys = await redis_client.keys(pattern)
            metrics = await self.get_metrics()

            return {
                "namespace": self.namespace,
                "total_keys": len(keys),
                "metrics": metrics.dict(),
                "hit_rate": f"{metrics.hit_rate:.2%}",
                "redis_info": await redis_manager.health_check(),
            }

        except Exception as e:
            self.logger.error(f"获取缓存信息失败: {e}")
            return {"error": str(e)}


# 预定义的缓存实例
user_cache = AdvancedRedisCache("users")
content_cache = AdvancedRedisCache("content")
video_cache = AdvancedRedisCache("videos")
task_cache = AdvancedRedisCache("tasks")


async def get_cache(namespace: str) -> AdvancedRedisCache:
    """获取指定命名空间的缓存实例"""
    return AdvancedRedisCache(namespace)
