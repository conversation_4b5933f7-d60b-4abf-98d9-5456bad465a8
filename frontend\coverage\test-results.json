{"numTotalTestSuites": 51, "numPassedTestSuites": 50, "numFailedTestSuites": 1, "numPendingTestSuites": 0, "numTotalTests": 111, "numPassedTests": 80, "numFailedTests": 31, "numPendingTests": 0, "numTodoTests": 0, "startTime": 1753329282595, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["", "API服务测试", "认证API"], "fullName": " API服务测试 认证API 应该成功登录用户", "status": "passed", "title": "应该成功登录用户", "duration": 3, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "认证API"], "fullName": " API服务测试 认证API 应该处理登录失败", "status": "passed", "title": "应该处理登录失败", "duration": 3, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "认证API"], "fullName": " API服务测试 认证API 应该成功注册用户", "status": "passed", "title": "应该成功注册用户", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "认证API"], "fullName": " API服务测试 认证API 应该验证令牌格式", "status": "passed", "title": "应该验证令牌格式", "duration": 4, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "项目管理API"], "fullName": " API服务测试 项目管理API 应该获取项目列表", "status": "passed", "title": "应该获取项目列表", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "项目管理API"], "fullName": " API服务测试 项目管理API 应该创建新项目", "status": "passed", "title": "应该创建新项目", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "项目管理API"], "fullName": " API服务测试 项目管理API 应该删除项目", "status": "passed", "title": "应该删除项目", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "内容管理API"], "fullName": " API服务测试 内容管理API 应该获取内容列表", "status": "passed", "title": "应该获取内容列表", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "内容管理API"], "fullName": " API服务测试 内容管理API 应该执行合规检测", "status": "passed", "title": "应该执行合规检测", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "错误处理和安全性"], "fullName": " API服务测试 错误处理和安全性 应该处理401错误并清理存储", "status": "passed", "title": "应该处理401错误并清理存储", "duration": 3, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "错误处理和安全性"], "fullName": " API服务测试 错误处理和安全性 应该安全处理敏感信息", "status": "passed", "title": "应该安全处理敏感信息", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "错误处理和安全性"], "fullName": " API服务测试 错误处理和安全性 应该验证输入参数", "status": "passed", "title": "应该验证输入参数", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "错误处理和安全性"], "fullName": " API服务测试 错误处理和安全性 应该限制请求频率", "status": "passed", "title": "应该限制请求频率", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "网络错误处理"], "fullName": " API服务测试 网络错误处理 应该处理网络连接失败", "status": "passed", "title": "应该处理网络连接失败", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "API服务测试", "网络错误处理"], "fullName": " API服务测试 网络错误处理 应该处理超时错误", "status": "passed", "title": "应该处理超时错误", "duration": 1, "failureMessages": []}], "startTime": 1753329284653, "endTime": 1753329284679, "status": "passed", "message": "", "name": "D:/二创/二创短视频分发/frontend/src/tests/api.test.ts"}, {"assertionResults": [{"ancestorTitles": ["", "计算引擎测试", "引擎初始化"], "fullName": " 计算引擎测试 引擎初始化 应该成功初始化引擎", "status": "pending", "title": "应该成功初始化引擎", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "引擎初始化"], "fullName": " 计算引擎测试 引擎初始化 应该处理初始化失败", "status": "pending", "title": "应该处理初始化失败", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "AI推理功能"], "fullName": " 计算引擎测试 AI推理功能 应该成功执行图像分类", "status": "pending", "title": "应该成功执行图像分类", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "AI推理功能"], "fullName": " 计算引擎测试 AI推理功能 应该处理无效输入", "status": "pending", "title": "应该处理无效输入", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "AI推理功能"], "fullName": " 计算引擎测试 AI推理功能 应该支持对象检测", "status": "pending", "title": "应该支持对象检测", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "视频处理功能"], "fullName": " 计算引擎测试 视频处理功能 应该成功处理视频文件", "status": "pending", "title": "应该成功处理视频文件", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "视频处理功能"], "fullName": " 计算引擎测试 视频处理功能 应该验证文件类型", "status": "pending", "title": "应该验证文件类型", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "视频处理功能"], "fullName": " 计算引擎测试 视频处理功能 应该处理大文件限制", "status": "pending", "title": "应该处理大文件限制", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "错误处理"], "fullName": " 计算引擎测试 错误处理 应该正确处理网络错误", "status": "pending", "title": "应该正确处理网络错误", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "错误处理"], "fullName": " 计算引擎测试 错误处理 应该清理内存资源", "status": "pending", "title": "应该清理内存资源", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "性能监控"], "fullName": " 计算引擎测试 性能监控 应该记录处理时间", "status": "pending", "title": "应该记录处理时间", "failureMessages": []}, {"ancestorTitles": ["", "计算引擎测试", "性能监控"], "fullName": " 计算引擎测试 性能监控 应该监控内存使用", "status": "pending", "title": "应该监控内存使用", "failureMessages": []}], "startTime": 1753329282595, "endTime": 1753329282595, "status": "passed", "message": "", "name": "D:/二创/二创短视频分发/frontend/src/tests/compute.test.ts"}, {"assertionResults": [{"ancestorTitles": ["", "集成测试 - 完整工作流程", "计算引擎集成"], "fullName": " 集成测试 - 完整工作流程 计算引擎集成 应该完成完整的引擎初始化流程", "status": "passed", "title": "应该完成完整的引擎初始化流程", "duration": 6, "failureMessages": []}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "计算引擎集成"], "fullName": " 集成测试 - 完整工作流程 计算引擎集成 应该处理引擎初始化失败的情况", "status": "passed", "title": "应该处理引擎初始化失败的情况", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "视频处理工作流程"], "fullName": " 集成测试 - 完整工作流程 视频处理工作流程 应该完成完整的视频处理流程", "status": "passed", "title": "应该完成完整的视频处理流程", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "视频处理工作流程"], "fullName": " 集成测试 - 完整工作流程 视频处理工作流程 应该处理无效视频文件", "status": "failed", "title": "应该处理无效视频文件", "duration": 7, "failureMessages": ["不支持的文件格式"], "location": {"line": 212, "column": 17}}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "视频处理工作流程"], "fullName": " 集成测试 - 完整工作流程 视频处理工作流程 应该支持进度监控", "status": "passed", "title": "应该支持进度监控", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "AI推理工作流程"], "fullName": " 集成测试 - 完整工作流程 AI推理工作流程 应该完成图像分类流程", "status": "failed", "title": "应该完成图像分类流程", "duration": 2, "failureMessages": ["AI engine not available"], "location": {"line": 250, "column": 17}}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "AI推理工作流程"], "fullName": " 集成测试 - 完整工作流程 AI推理工作流程 应该支持对象检测", "status": "failed", "title": "应该支持对象检测", "duration": 1, "failureMessages": ["AI engine not available"], "location": {"line": 250, "column": 17}}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "AI推理工作流程"], "fullName": " 集成测试 - 完整工作流程 AI推理工作流程 应该处理AI推理错误", "status": "failed", "title": "应该处理AI推理错误", "duration": 1, "failureMessages": ["AI engine not available"], "location": {"line": 250, "column": 17}}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "性能监控和内存管理"], "fullName": " 集成测试 - 完整工作流程 性能监控和内存管理 应该监控内存使用情况", "status": "failed", "title": "应该监控内存使用情况", "duration": 1, "failureMessages": ["AI engine not available"], "location": {"line": 250, "column": 17}}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "性能监控和内存管理"], "fullName": " 集成测试 - 完整工作流程 性能监控和内存管理 应该正确计算成功率", "status": "failed", "title": "应该正确计算成功率", "duration": 1, "failureMessages": ["不支持的文件格式"], "location": {"line": 212, "column": 17}}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "错误恢复和清理"], "fullName": " 集成测试 - 完整工作流程 错误恢复和清理 应该正确清理资源", "status": "passed", "title": "应该正确清理资源", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "错误恢复和清理"], "fullName": " 集成测试 - 完整工作流程 错误恢复和清理 应该处理并发任务", "status": "passed", "title": "应该处理并发任务", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "用户界面集成"], "fullName": " 集成测试 - 完整工作流程 用户界面集成 应该正确渲染计算测试页面", "status": "failed", "title": "应该正确渲染计算测试页面", "duration": 29, "failureMessages": ["expected false to be true // Object.is equality"], "location": {"line": 433, "column": 71}}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "用户界面集成"], "fullName": " 集成测试 - 完整工作流程 用户界面集成 应该响应文件选择", "status": "passed", "title": "应该响应文件选择", "duration": 10, "failureMessages": []}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "端到端工作流程"], "fullName": " 集成测试 - 完整工作流程 端到端工作流程 应该完成完整的视频处理工作流程", "status": "passed", "title": "应该完成完整的视频处理工作流程", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "集成测试 - 完整工作流程", "端到端工作流程"], "fullName": " 集成测试 - 完整工作流程 端到端工作流程 应该完成完整的AI推理工作流程", "status": "failed", "title": "应该完成完整的AI推理工作流程", "duration": 1, "failureMessages": ["AI engine not available"], "location": {"line": 250, "column": 17}}], "startTime": 1753329283986, "endTime": 1753329284054, "status": "failed", "message": "", "name": "D:/二创/二创短视频分发/frontend/src/tests/integration.test.ts"}, {"assertionResults": [{"ancestorTitles": ["", "性能测试", "内存管理性能"], "fullName": " 性能测试 内存管理性能 应该在大量任务处理后保持内存稳定", "status": "failed", "title": "应该在大量任务处理后保持内存稳定", "duration": 152, "failureMessages": ["expected 2 to be 5 // Object.is equality"], "location": {"line": 208, "column": 41}}, {"ancestorTitles": ["", "性能测试", "内存管理性能"], "fullName": " 性能测试 内存管理性能 应该正确清理已完成任务的内存", "status": "failed", "title": "应该正确清理已完成任务的内存", "duration": 105, "failureMessages": ["expected 3 to be +0 // Object.is equality"], "location": {"line": 238, "column": 62}}, {"ancestorTitles": ["", "性能测试", "内存管理性能"], "fullName": " 性能测试 内存管理性能 应该监控TensorFlow.js内存使用", "status": "failed", "title": "应该监控TensorFlow.js内存使用", "duration": 3, "failureMessages": ["AI engine not available"], "location": {"line": 250, "column": 17}}, {"ancestorTitles": ["", "性能测试", "并发处理性能"], "fullName": " 性能测试 并发处理性能 应该高效处理并发视频任务", "status": "passed", "title": "应该高效处理并发视频任务", "duration": 100, "failureMessages": []}, {"ancestorTitles": ["", "性能测试", "并发处理性能"], "fullName": " 性能测试 并发处理性能 应该限制并发任务数量防止资源耗尽", "status": "passed", "title": "应该限制并发任务数量防止资源耗尽", "duration": 100, "failureMessages": []}, {"ancestorTitles": ["", "性能测试", "并发处理性能"], "fullName": " 性能测试 并发处理性能 应该处理混合任务类型的并发", "status": "failed", "title": "应该处理混合任务类型的并发", "duration": 2, "failureMessages": ["AI engine not available"], "location": {"line": 250, "column": 17}}, {"ancestorTitles": ["", "性能测试", "错误恢复性能"], "fullName": " 性能测试 错误恢复性能 应该快速从任务失败中恢复", "status": "failed", "title": "应该快速从任务失败中恢复", "duration": 2, "failureMessages": ["不支持的文件格式"], "location": {"line": 212, "column": 17}}, {"ancestorTitles": ["", "性能测试", "错误恢复性能"], "fullName": " 性能测试 错误恢复性能 应该处理内存不足的情况", "status": "passed", "title": "应该处理内存不足的情况", "duration": 91, "failureMessages": []}, {"ancestorTitles": ["", "性能测试", "长时间运行性能"], "fullName": " 性能测试 长时间运行性能 应该在长时间运行后保持性能稳定", "status": "failed", "title": "应该在长时间运行后保持性能稳定", "duration": 252, "failureMessages": ["expected 0 to be less than 0"], "location": {"line": 452, "column": 33}}, {"ancestorTitles": ["", "性能测试", "长时间运行性能"], "fullName": " 性能测试 长时间运行性能 应该正确管理任务队列大小", "status": "passed", "title": "应该正确管理任务队列大小", "duration": 474, "failureMessages": []}, {"ancestorTitles": ["", "性能测试", "资源使用优化"], "fullName": " 性能测试 资源使用优化 应该优化Worker使用", "status": "failed", "title": "应该优化Worker使用", "duration": 31, "failureMessages": ["expected \"spy\" to be called 1 times, but got 0 times"], "location": {"line": 510, "column": 33}}, {"ancestorTitles": ["", "性能测试", "资源使用优化"], "fullName": " 性能测试 资源使用优化 应该优化内存分配", "status": "passed", "title": "应该优化内存分配", "duration": 127, "failureMessages": []}], "startTime": 1753329283770, "endTime": 1753329285209, "status": "failed", "message": "", "name": "D:/二创/二创短视频分发/frontend/src/tests/performance.test.ts"}, {"assertionResults": [{"ancestorTitles": ["", "useResponsiveLayout", "设备类型检测"], "fullName": " useResponsiveLayout 设备类型检测 应该正确识别桌面设备", "status": "passed", "title": "应该正确识别桌面设备", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "设备类型检测"], "fullName": " useResponsiveLayout 设备类型检测 应该正确识别平板设备", "status": "failed", "title": "应该正确识别平板设备", "duration": 2, "failureMessages": ["expected 'desktop' to be 'tablet' // Object.is equality"], "location": {"line": 71, "column": 32}}, {"ancestorTitles": ["", "useResponsiveLayout", "设备类型检测"], "fullName": " useResponsiveLayout 设备类型检测 应该正确识别移动设备", "status": "passed", "title": "应该正确识别移动设备", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "断点检测"], "fullName": " useResponsiveLayout 断点检测 应该正确识别xs断点", "status": "passed", "title": "应该正确识别xs断点", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "断点检测"], "fullName": " useResponsiveLayout 断点检测 应该正确识别sm断点", "status": "passed", "title": "应该正确识别sm断点", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "断点检测"], "fullName": " useResponsiveLayout 断点检测 应该正确识别md断点", "status": "passed", "title": "应该正确识别md断点", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "断点检测"], "fullName": " useResponsiveLayout 断点检测 应该正确识别lg断点", "status": "failed", "title": "应该正确识别lg断点", "duration": 0, "failureMessages": ["expected 'xl' to be 'lg' // Object.is equality"], "location": {"line": 123, "column": 39}}, {"ancestorTitles": ["", "useResponsiveLayout", "断点检测"], "fullName": " useResponsiveLayout 断点检测 应该正确识别xl断点", "status": "failed", "title": "应该正确识别xl断点", "duration": 1, "failureMessages": ["expected 'xxl' to be 'xl' // Object.is equality"], "location": {"line": 133, "column": 39}}, {"ancestorTitles": ["", "useResponsiveLayout", "断点检测"], "fullName": " useResponsiveLayout 断点检测 应该正确识别xxl断点", "status": "passed", "title": "应该正确识别xxl断点", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "布局模式"], "fullName": " useResponsiveLayout 布局模式 移动设备应该使用mobile布局模式", "status": "passed", "title": "移动设备应该使用mobile布局模式", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "布局模式"], "fullName": " useResponsiveLayout 布局模式 平板设备应该使用tablet布局模式", "status": "failed", "title": "平板设备应该使用tablet布局模式", "duration": 1, "failureMessages": ["expected 'desktop' to be 'tablet' // Object.is equality"], "location": {"line": 165, "column": 32}}, {"ancestorTitles": ["", "useResponsiveLayout", "布局模式"], "fullName": " useResponsiveLayout 布局模式 桌面设备应该使用desktop布局模式", "status": "passed", "title": "桌面设备应该使用desktop布局模式", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "UI元素显示控制"], "fullName": " useResponsiveLayout UI元素显示控制 移动设备应该显示汉堡菜单", "status": "passed", "title": "移动设备应该显示汉堡菜单", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "UI元素显示控制"], "fullName": " useResponsiveLayout UI元素显示控制 桌面设备不应该显示汉堡菜单", "status": "passed", "title": "桌面设备不应该显示汉堡菜单", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "UI元素显示控制"], "fullName": " useResponsiveLayout UI元素显示控制 移动设备应该显示底部导航", "status": "passed", "title": "移动设备应该显示底部导航", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "UI元素显示控制"], "fullName": " useResponsiveLayout UI元素显示控制 桌面设备不应该显示底部导航", "status": "passed", "title": "桌面设备不应该显示底部导航", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "UI元素显示控制"], "fullName": " useResponsiveLayout UI元素显示控制 桌面设备应该显示侧边栏", "status": "passed", "title": "桌面设备应该显示侧边栏", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "UI元素显示控制"], "fullName": " useResponsiveLayout UI元素显示控制 移动设备默认不应该显示侧边栏", "status": "passed", "title": "移动设备默认不应该显示侧边栏", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "移动端菜单控制"], "fullName": " useResponsiveLayout 移动端菜单控制 应该能够切换移动端菜单", "status": "passed", "title": "应该能够切换移动端菜单", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "移动端菜单控制"], "fullName": " useResponsiveLayout 移动端菜单控制 打开移动端菜单时应该阻止背景滚动", "status": "passed", "title": "打开移动端菜单时应该阻止背景滚动", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "移动端菜单控制"], "fullName": " useResponsiveLayout 移动端菜单控制 关闭移动端菜单时应该恢复背景滚动", "status": "passed", "title": "关闭移动端菜单时应该恢复背景滚动", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "移动端菜单控制"], "fullName": " useResponsiveLayout 移动端菜单控制 切换到桌面端时应该自动关闭移动端菜单", "status": "passed", "title": "切换到桌面端时应该自动关闭移动端菜单", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "尺寸计算"], "fullName": " useResponsiveLayout 尺寸计算 应该为不同设备类型返回正确的导航栏高度", "status": "failed", "title": "应该为不同设备类型返回正确的导航栏高度", "duration": 1, "failureMessages": ["expected '72px' to be '64px' // Object.is equality"], "location": {"line": 314, "column": 34}}, {"ancestorTitles": ["", "useResponsiveLayout", "尺寸计算"], "fullName": " useResponsiveLayout 尺寸计算 应该为不同设备类型返回正确的侧边栏宽度", "status": "passed", "title": "应该为不同设备类型返回正确的侧边栏宽度", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "尺寸计算"], "fullName": " useResponsiveLayout 尺寸计算 移动设备打开菜单时应该返回正确的侧边栏宽度", "status": "passed", "title": "移动设备打开菜单时应该返回正确的侧边栏宽度", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "断点匹配工具"], "fullName": " useResponsiveLayout 断点匹配工具 matchesBreakpoint应该正确匹配向上断点", "status": "failed", "title": "matchesBreakpoint应该正确匹配向上断点", "duration": 1, "failureMessages": ["expected true to be false // Object.is equality"], "location": {"line": 360, "column": 45}}, {"ancestorTitles": ["", "useResponsiveLayout", "断点匹配工具"], "fullName": " useResponsiveLayout 断点匹配工具 matchesBreakpoint应该正确匹配向下断点", "status": "passed", "title": "matchesBreakpoint应该正确匹配向下断点", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "断点匹配工具"], "fullName": " useResponsiveLayout 断点匹配工具 getMediaQuery应该返回正确的媒体查询字符串", "status": "passed", "title": "getMediaQuery应该返回正确的媒体查询字符串", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "事件处理"], "fullName": " useResponsiveLayout 事件处理 应该监听window resize事件", "status": "failed", "title": "应该监听window resize事件", "duration": 3, "failureMessages": ["expected +0 to be 800 // Object.is equality"], "location": {"line": 392, "column": 33}}, {"ancestorTitles": ["", "useResponsiveLayout", "事件处理"], "fullName": " useResponsiveLayout 事件处理 应该监听orientationchange事件", "status": "failed", "title": "应该监听orientationchange事件", "duration": 155, "failureMessages": ["expected +0 to be 667 // Object.is equality"], "location": {"line": 405, "column": 33}}, {"ancestorTitles": ["", "useResponsiveLayout", "BREAKPOINTS常量"], "fullName": " useResponsiveLayout BREAKPOINTS常量 应该包含所有必要的断点", "status": "passed", "title": "应该包含所有必要的断点", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "useResponsiveLayout", "BREAKPOINTS常量"], "fullName": " useResponsiveLayout BREAKPOINTS常量 断点值应该按升序排列", "status": "passed", "title": "断点值应该按升序排列", "duration": 0, "failureMessages": []}], "startTime": 1753329284164, "endTime": 1753329284335, "status": "failed", "message": "", "name": "D:/二创/二创短视频分发/frontend/tests/unit/composables/useResponsiveLayout.test.ts"}, {"assertionResults": [{"ancestorTitles": ["", "测试辅助工具", "isTestEnvironment"], "fullName": " 测试辅助工具 isTestEnvironment 应该检测localStorage中的测试模式", "status": "passed", "title": "应该检测localStorage中的测试模式", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "isTestEnvironment"], "fullName": " 测试辅助工具 isTestEnvironment 应该检测环境变量中的测试模式", "status": "passed", "title": "应该检测环境变量中的测试模式", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "isTestEnvironment"], "fullName": " 测试辅助工具 isTestEnvironment 应该检测URL参数中的测试模式", "status": "passed", "title": "应该检测URL参数中的测试模式", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "isTestEnvironment"], "fullName": " 测试辅助工具 isTestEnvironment 非测试环境应该返回false", "status": "failed", "title": "非测试环境应该返回false", "duration": 1, "failureMessages": ["expected true to be false // Object.is equality"], "location": {"line": 48, "column": 35}}, {"ancestorTitles": ["", "测试辅助工具", "setupTestEnvironment"], "fullName": " 测试辅助工具 setupTestEnvironment 应该设置管理员测试环境", "status": "failed", "title": "应该设置管理员测试环境", "duration": 1, "failureMessages": ["expected undefined to be 'true' // Object.is equality"], "location": {"line": 56, "column": 49}}, {"ancestorTitles": ["", "测试辅助工具", "setupTestEnvironment"], "fullName": " 测试辅助工具 setupTestEnvironment 应该设置普通用户测试环境", "status": "failed", "title": "应该设置普通用户测试环境", "duration": 1, "failureMessages": ["expected undefined to be 'user' // Object.is equality"], "location": {"line": 69, "column": 29}}, {"ancestorTitles": ["", "测试辅助工具", "setupTestEnvironment"], "fullName": " 测试辅助工具 setupTestEnvironment 应该设置开发者测试环境", "status": "failed", "title": "应该设置开发者测试环境", "duration": 0, "failureMessages": ["expected undefined to be 'developer' // Object.is equality"], "location": {"line": 77, "column": 29}}, {"ancestorTitles": ["", "测试辅助工具", "cleanupTestEnvironment"], "fullName": " 测试辅助工具 cleanupTestEnvironment 应该清理所有测试相关的localStorage项", "status": "failed", "title": "应该清理所有测试相关的localStorage项", "duration": 1, "failureMessages": ["expected undefined to be truthy"], "location": {"line": 88, "column": 49}}, {"ancestorTitles": ["", "测试辅助工具", "hasTestPermission"], "fullName": " 测试辅助工具 hasTestPermission 管理员应该拥有所有权限", "status": "passed", "title": "管理员应该拥有所有权限", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "hasTestPermission"], "fullName": " 测试辅助工具 hasTestPermission 普通用户应该只有指定权限", "status": "passed", "title": "普通用户应该只有指定权限", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "hasTestPermission"], "fullName": " 测试辅助工具 hasTestPermission 开发者应该有开发权限", "status": "passed", "title": "开发者应该有开发权限", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "hasTestPermission"], "fullName": " 测试辅助工具 hasTestPermission 非测试环境应该返回false", "status": "failed", "title": "非测试环境应该返回false", "duration": 0, "failureMessages": ["expected true to be false // Object.is equality"], "location": {"line": 125, "column": 60}}, {"ancestorTitles": ["", "测试辅助工具", "shouldBypassRouteGuard"], "fullName": " 测试辅助工具 shouldBypassRouteGuard 管理员应该绕过所有路由守卫", "status": "passed", "title": "管理员应该绕过所有路由守卫", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "shouldBypassRouteGuard"], "fullName": " 测试辅助工具 shouldBypassRouteGuard 有权限的用户应该通过路由守卫", "status": "passed", "title": "有权限的用户应该通过路由守卫", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "shouldBypassRouteGuard"], "fullName": " 测试辅助工具 shouldBypassRouteGuard 无权限的用户应该被路由守卫拦截", "status": "failed", "title": "无权限的用户应该被路由守卫拦截", "duration": 0, "failureMessages": ["expected true to be false // Object.is equality"], "location": {"line": 161, "column": 55}}, {"ancestorTitles": ["", "测试辅助工具", "shouldBypassRouteGuard"], "fullName": " 测试辅助工具 shouldBypassRouteGuard 非测试环境应该返回false", "status": "failed", "title": "非测试环境应该返回false", "duration": 0, "failureMessages": ["expected true to be false // Object.is equality"], "location": {"line": 166, "column": 49}}, {"ancestorTitles": ["", "测试辅助工具", "TEST_USERS"], "fullName": " 测试辅助工具 TEST_USERS 应该包含所有必要的用户类型", "status": "passed", "title": "应该包含所有必要的用户类型", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "TEST_USERS"], "fullName": " 测试辅助工具 TEST_USERS 管理员用户应该有正确的属性", "status": "passed", "title": "管理员用户应该有正确的属性", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "TEST_USERS"], "fullName": " 测试辅助工具 TEST_USERS 普通用户应该有正确的属性", "status": "passed", "title": "普通用户应该有正确的属性", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "TEST_USERS"], "fullName": " 测试辅助工具 TEST_USERS 开发者用户应该有正确的属性", "status": "passed", "title": "开发者用户应该有正确的属性", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "TEST_USERS"], "fullName": " 测试辅助工具 TEST_USERS 所有用户都应该有必要的基础属性", "status": "passed", "title": "所有用户都应该有必要的基础属性", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "边界情况测试"], "fullName": " 测试辅助工具 边界情况测试 应该处理无效的用户类型", "status": "passed", "title": "应该处理无效的用户类型", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "边界情况测试"], "fullName": " 测试辅助工具 边界情况测试 应该处理空的路由meta", "status": "passed", "title": "应该处理空的路由meta", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "测试辅助工具", "边界情况测试"], "fullName": " 测试辅助工具 边界情况测试 应该处理undefined的路由meta", "status": "passed", "title": "应该处理undefined的路由meta", "duration": 0, "failureMessages": []}], "startTime": 1753329284781, "endTime": 1753329284789, "status": "failed", "message": "", "name": "D:/二创/二创短视频分发/frontend/tests/unit/utils/test-helpers.test.ts"}, {"assertionResults": [], "startTime": 1753329282595, "endTime": 1753329282595, "status": "failed", "message": "Failed to resolve import \"/logo.svg\" from \"src/components/layout/MobileNavigation.vue\". Does the file exist?", "name": "D:/二创/二创短视频分发/frontend/tests/integration/layout-system.test.ts"}]}