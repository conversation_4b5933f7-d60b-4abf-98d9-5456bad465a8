/**
 * 生产API服务
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios'

// API配置 (使用代理)
const API_BASE_URL = '/api'

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // 清除认证信息
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user_info')
      
      // 重定向到登录页面
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// API接口类型定义
export interface User {
  id: number
  username: string
  email: string
  role: string
  subscription_type: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  success: boolean
  user: User
}

export interface VideoProcessingRequest {
  step: number
  input_type: string
  input_data: string
  ai_model?: string
  user_id?: number
}

export interface VideoProcessingResponse {
  success: boolean
  processing_id: number
  output: {
    step: number
    processed_at: string
    ai_model: string
    result: string
    success: boolean
  }
}

export interface SystemStats {
  total_users: number
  total_projects: number
  total_processing: number
}

// API服务类
export class ProductionApiService {
  // 健康检查
  static async healthCheck(): Promise<any> {
    try {
      const response = await apiClient.get('/health')
      return response.data
    } catch (error) {
      console.error('Health check failed:', error)
      throw error
    }
  }

  // 用户认证
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post('/auth/login', credentials)
      
      // 保存用户信息到localStorage
      if (response.data.success) {
        localStorage.setItem('user_info', JSON.stringify(response.data.user))
        localStorage.setItem('access_token', 'simple-token') // 简化版本
      }
      
      return response.data
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  // 获取所有用户
  static async getAllUsers(): Promise<User[]> {
    try {
      const response = await apiClient.get('/users')
      return response.data.users
    } catch (error) {
      console.error('Get users failed:', error)
      throw error
    }
  }

  // 视频处理工作流
  static async processVideoStep(request: VideoProcessingRequest): Promise<VideoProcessingResponse> {
    try {
      const response = await apiClient.post('/workflow/process', null, {
        params: {
          step: request.step,
          input_type: request.input_type,
          input_data: request.input_data,
          ai_model: request.ai_model || 'default',
          user_id: request.user_id || 1
        }
      })
      return response.data
    } catch (error) {
      console.error('Video processing failed:', error)
      throw error
    }
  }

  // 获取系统统计
  static async getSystemStats(): Promise<SystemStats> {
    try {
      const response = await apiClient.get('/stats')
      return response.data
    } catch (error) {
      console.error('Get stats failed:', error)
      throw error
    }
  }

  // 测试API连接
  static async testConnection(): Promise<boolean> {
    try {
      const response = await apiClient.get('/')
      return response.status === 200
    } catch (error) {
      console.error('API connection test failed:', error)
      return false
    }
  }
}

// 导出默认实例
export default ProductionApiService
