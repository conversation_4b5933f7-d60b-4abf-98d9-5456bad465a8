# Page snapshot

```yaml
- banner:
  - link "🎬 二创短视频分发系统":
    - /url: /
  - navigation:
    - link "首页":
      - /url: /
    - link "视频创作":
      - /url: /video-creation
    - link "计算引擎":
      - /url: /compute-test
    - link "个人中心":
      - /url: /profile
- complementary:
  - heading "功能导航" [level=3]
  - navigation:
    - link "首页概览":
      - /url: /
      - img
      - text: 首页概览
    - link "智能视频生成":
      - /url: /video-creation
      - img
      - text: 智能视频生成
    - link "计算引擎测试":
      - /url: /compute-test
      - img
      - text: 计算引擎测试
    - link "内容分析":
      - /url: /content-analysis
      - img
      - text: 内容分析
    - link "批量处理":
      - /url: /batch-processing
      - img
      - text: 批量处理
    - link "个人中心":
      - /url: /profile
      - img
      - text: 个人中心
- main
- text: "[plugin:vite:vue] [vue/compiler-sfc] Identifier 'togglePasswordVisibility' has already been declared. (64:6) D:/二创/二创短视频分发/frontend/src/pages/Login.vue 232| 233| // 🔒 证据链: 密码可见性切换 234| const togglePasswordVisibility = () => { 235| showPassword.value = !showPassword.value 236| } D:/二创/二创短视频分发/frontend/src/pages/Login.vue:64:6 51 | <div class=\"input-wrapper\"> 52 | <input 53 | id=\"username\" | ^ 54 | v-model=\"formData.username\" 55 | type=\"text\" at constructor (D:\\二创\\二创短视频分发\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:367:19) at TypeScriptParserMixin.raise (D:\\二创\\二创短视频分发\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:6627:19) at TypeScriptScopeHandler.checkRedeclarationInScope (D:\\二创\\二创短视频分发\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:1644:19) at TypeScriptScopeHandler.declareName (D:\\二创\\二创短视频分发\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:1610:12) at TypeScriptScopeHandler.declareName (D:\\二创\\二创短视频分发\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:4910:11) at TypeScriptParserMixin.declareNameFromIdentifier (D:\\二创\\二创短视频分发\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:7591:16) at TypeScriptParserMixin.checkIdentifier (D:\\二创\\二创短视频分发\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:7587:12) at TypeScriptParserMixin.checkLVal (D:\\二创\\二创短视频分发\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:7526:12) at TypeScriptParserMixin.parseVarId (D:\\二创\\二创短视频分发\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:13412:10) at TypeScriptParserMixin.parseVarId (D:\\二创\\二创短视频分发\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:9767:11 Click outside, press Esc key, or fix the code to dismiss. You can also disable this overlay by setting"
- code: server.hmr.overlay
- text: to
- code: "false"
- text: in
- code: vite.config.ts
- text: .
```