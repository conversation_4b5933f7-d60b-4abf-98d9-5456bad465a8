/**
 * AI驱动优化系统 - 2025年最佳实践
 * 基于用户行为的智能预加载和性能优化
 */

import { getRUM } from './rum-monitoring'

// AI优化配置
interface AIOptimizerConfig {
  enableBehaviorTracking: boolean
  enablePredictivePreloading: boolean
  enableAdaptiveOptimization: boolean
  learningRate: number
  predictionThreshold: number
  maxPredictions: number
  dataRetentionDays: number
}

// 用户行为数据
interface UserBehavior {
  sessionId: string
  userId?: string
  timestamp: number
  currentPage: string
  nextPage?: string
  timeOnPage: number
  scrollDepth: number
  interactions: number
  deviceType: 'mobile' | 'tablet' | 'desktop'
  connectionSpeed: 'slow' | 'fast' | 'unknown'
  exitIntent?: boolean
}

// 预测结果
interface PredictionResult {
  nextPage: string
  confidence: number
  priority: 'high' | 'medium' | 'low'
  estimatedTime: number
}

// 优化建议
interface OptimizationSuggestion {
  type: 'preload' | 'prefetch' | 'lazy-load' | 'critical-css'
  resource: string
  priority: number
  reason: string
  impact: 'high' | 'medium' | 'low'
}

class AIOptimizer {
  private config: AIOptimizerConfig
  private behaviorData: UserBehavior[] = []
  private predictionModel: Map<string, Map<string, number>> = new Map()
  private optimizationHistory: Map<string, number> = new Map()
  private sessionStartTime: number = Date.now()
  private currentPageStartTime: number = Date.now()
  private interactionCount: number = 0
  private maxScrollDepth: number = 0

  constructor(config: Partial<AIOptimizerConfig> = {}) {
    this.config = {
      enableBehaviorTracking: true,
      enablePredictivePreloading: true,
      enableAdaptiveOptimization: true,
      learningRate: 0.1,
      predictionThreshold: 0.3,
      maxPredictions: 3,
      dataRetentionDays: 30,
      ...config
    }

    this.init()
  }

  private init(): void {
    if (typeof window === 'undefined') return

    this.loadStoredData()
    this.setupBehaviorTracking()
    this.setupPredictivePreloading()
    this.setupAdaptiveOptimization()
    this.startCleanupTimer()

    console.log('🤖 AI优化系统已启动')
  }

  // 加载存储的数据
  private loadStoredData(): void {
    try {
      const storedBehavior = localStorage.getItem('ai_behavior_data')
      if (storedBehavior) {
        this.behaviorData = JSON.parse(storedBehavior)
        this.buildPredictionModel()
      }

      const storedModel = localStorage.getItem('ai_prediction_model')
      if (storedModel) {
        const modelData = JSON.parse(storedModel)
        this.predictionModel = new Map(modelData)
      }
    } catch (error) {
      console.warn('AI优化器数据加载失败:', error)
    }
  }

  // 保存数据到本地存储
  private saveData(): void {
    try {
      // 只保留最近的数据
      const cutoffTime = Date.now() - (this.config.dataRetentionDays * 24 * 60 * 60 * 1000)
      this.behaviorData = this.behaviorData.filter(data => data.timestamp > cutoffTime)

      localStorage.setItem('ai_behavior_data', JSON.stringify(this.behaviorData))
      localStorage.setItem('ai_prediction_model', JSON.stringify(Array.from(this.predictionModel.entries())))
    } catch (error) {
      console.warn('AI优化器数据保存失败:', error)
    }
  }

  // 设置行为跟踪
  private setupBehaviorTracking(): void {
    if (!this.config.enableBehaviorTracking) return

    // 跟踪页面访问
    this.trackPageView(window.location.pathname)

    // 跟踪用户交互
    document.addEventListener('click', () => {
      this.interactionCount++
    })

    document.addEventListener('scroll', () => {
      const scrollDepth = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      this.maxScrollDepth = Math.max(this.maxScrollDepth, scrollDepth)
    })

    // 跟踪页面离开
    window.addEventListener('beforeunload', () => {
      this.trackPageExit()
    })

    // 跟踪路由变化
    window.addEventListener('popstate', () => {
      this.trackPageView(window.location.pathname)
    })
  }

  // 设置预测性预加载
  private setupPredictivePreloading(): void {
    if (!this.config.enablePredictivePreloading) return

    // 在页面空闲时进行预测和预加载
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        this.performPredictivePreloading()
      })
    } else {
      setTimeout(() => {
        this.performPredictivePreloading()
      }, 1000)
    }
  }

  // 设置自适应优化
  private setupAdaptiveOptimization(): void {
    if (!this.config.enableAdaptiveOptimization) return

    // 监控性能指标
    const rum = getRUM()
    if (rum) {
      // 基于性能数据调整优化策略
      this.adaptOptimizationStrategy()
    }
  }

  // 跟踪页面访问
  public trackPageView(page: string): void {
    const previousPage = this.getCurrentPage()
    
    if (previousPage && previousPage !== page) {
      // 记录页面转换
      const behavior: UserBehavior = {
        sessionId: this.getSessionId(),
        userId: this.getUserId(),
        timestamp: Date.now(),
        currentPage: previousPage,
        nextPage: page,
        timeOnPage: Date.now() - this.currentPageStartTime,
        scrollDepth: this.maxScrollDepth,
        interactions: this.interactionCount,
        deviceType: this.getDeviceType(),
        connectionSpeed: this.getConnectionSpeed()
      }

      this.behaviorData.push(behavior)
      this.updatePredictionModel(behavior)
      this.saveData()
    }

    this.currentPageStartTime = Date.now()
    this.interactionCount = 0
    this.maxScrollDepth = 0
  }

  // 跟踪页面退出
  private trackPageExit(): void {
    const currentPage = this.getCurrentPage()
    if (!currentPage) return

    const behavior: UserBehavior = {
      sessionId: this.getSessionId(),
      userId: this.getUserId(),
      timestamp: Date.now(),
      currentPage,
      timeOnPage: Date.now() - this.currentPageStartTime,
      scrollDepth: this.maxScrollDepth,
      interactions: this.interactionCount,
      deviceType: this.getDeviceType(),
      connectionSpeed: this.getConnectionSpeed(),
      exitIntent: true
    }

    this.behaviorData.push(behavior)
    this.saveData()
  }

  // 构建预测模型
  private buildPredictionModel(): void {
    this.predictionModel.clear()

    // 使用简单的马尔可夫链模型
    for (const behavior of this.behaviorData) {
      if (!behavior.nextPage) continue

      const currentPage = behavior.currentPage
      const nextPage = behavior.nextPage

      if (!this.predictionModel.has(currentPage)) {
        this.predictionModel.set(currentPage, new Map())
      }

      const transitions = this.predictionModel.get(currentPage)!
      const currentCount = transitions.get(nextPage) || 0
      transitions.set(nextPage, currentCount + 1)
    }

    // 转换为概率
    for (const [currentPage, transitions] of this.predictionModel.entries()) {
      const total = Array.from(transitions.values()).reduce((sum, count) => sum + count, 0)
      for (const [nextPage, count] of transitions.entries()) {
        transitions.set(nextPage, count / total)
      }
    }
  }

  // 更新预测模型
  private updatePredictionModel(behavior: UserBehavior): void {
    if (!behavior.nextPage) return

    const currentPage = behavior.currentPage
    const nextPage = behavior.nextPage

    if (!this.predictionModel.has(currentPage)) {
      this.predictionModel.set(currentPage, new Map())
    }

    const transitions = this.predictionModel.get(currentPage)!
    const currentProb = transitions.get(nextPage) || 0
    
    // 使用学习率更新概率
    const newProb = currentProb + this.config.learningRate * (1 - currentProb)
    transitions.set(nextPage, newProb)

    // 重新归一化
    const total = Array.from(transitions.values()).reduce((sum, prob) => sum + prob, 0)
    for (const [page, prob] of transitions.entries()) {
      transitions.set(page, prob / total)
    }
  }

  // 执行预测性预加载
  private performPredictivePreloading(): void {
    const currentPage = this.getCurrentPage()
    if (!currentPage) return

    const predictions = this.getPredictions(currentPage)
    
    for (const prediction of predictions) {
      if (prediction.confidence > this.config.predictionThreshold) {
        this.preloadPage(prediction.nextPage, prediction.priority)
      }
    }
  }

  // 获取预测结果
  public getPredictions(currentPage: string): PredictionResult[] {
    const transitions = this.predictionModel.get(currentPage)
    if (!transitions) return []

    const predictions: PredictionResult[] = []
    
    for (const [nextPage, probability] of transitions.entries()) {
      if (probability > this.config.predictionThreshold) {
        predictions.push({
          nextPage,
          confidence: probability,
          priority: this.getPriority(probability),
          estimatedTime: this.estimateLoadTime(nextPage)
        })
      }
    }

    return predictions
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, this.config.maxPredictions)
  }

  // 预加载页面
  private preloadPage(page: string, priority: 'high' | 'medium' | 'low'): void {
    // 检查是否已经预加载
    if (this.optimizationHistory.has(page)) {
      const lastPreload = this.optimizationHistory.get(page)!
      if (Date.now() - lastPreload < 5 * 60 * 1000) { // 5分钟内不重复预加载
        return
      }
    }

    // 根据优先级选择预加载策略
    const link = document.createElement('link')
    
    if (priority === 'high') {
      link.rel = 'preload'
      link.as = 'document'
    } else {
      link.rel = 'prefetch'
    }
    
    link.href = page
    document.head.appendChild(link)

    this.optimizationHistory.set(page, Date.now())
    
    console.log(`🚀 AI预加载: ${page} (优先级: ${priority})`)
  }

  // 自适应优化策略
  private adaptOptimizationStrategy(): void {
    const deviceType = this.getDeviceType()
    const connectionSpeed = this.getConnectionSpeed()

    // 根据设备和网络条件调整策略
    if (deviceType === 'mobile' || connectionSpeed === 'slow') {
      this.config.predictionThreshold = 0.5 // 提高阈值，减少预加载
      this.config.maxPredictions = 1
    } else {
      this.config.predictionThreshold = 0.3 // 降低阈值，增加预加载
      this.config.maxPredictions = 3
    }
  }

  // 获取优化建议
  public getOptimizationSuggestions(): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = []
    const currentPage = this.getCurrentPage()
    
    if (!currentPage) return suggestions

    const predictions = this.getPredictions(currentPage)
    
    for (const prediction of predictions) {
      if (prediction.confidence > 0.7) {
        suggestions.push({
          type: 'preload',
          resource: prediction.nextPage,
          priority: prediction.confidence,
          reason: `用户有${(prediction.confidence * 100).toFixed(1)}%的概率访问此页面`,
          impact: 'high'
        })
      } else if (prediction.confidence > 0.4) {
        suggestions.push({
          type: 'prefetch',
          resource: prediction.nextPage,
          priority: prediction.confidence,
          reason: `用户可能访问此页面`,
          impact: 'medium'
        })
      }
    }

    return suggestions.sort((a, b) => b.priority - a.priority)
  }

  // 辅助方法
  private getCurrentPage(): string {
    return window.location.pathname
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('ai_session_id')
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('ai_session_id', sessionId)
    }
    return sessionId
  }

  private getUserId(): string | undefined {
    return localStorage.getItem('user_id') || undefined
  }

  private getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    const width = window.innerWidth
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet'
    return 'desktop'
  }

  private getConnectionSpeed(): 'slow' | 'fast' | 'unknown' {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      if (connection.effectiveType === '4g') return 'fast'
      if (connection.effectiveType === '3g') return 'fast'
      if (connection.effectiveType === '2g' || connection.effectiveType === 'slow-2g') return 'slow'
    }
    return 'unknown'
  }

  private getPriority(confidence: number): 'high' | 'medium' | 'low' {
    if (confidence > 0.7) return 'high'
    if (confidence > 0.4) return 'medium'
    return 'low'
  }

  private estimateLoadTime(page: string): number {
    // 基于历史数据估算加载时间
    const avgLoadTime = this.behaviorData
      .filter(b => b.currentPage === page)
      .reduce((sum, b, _, arr) => sum + (b.timeOnPage / arr.length), 0)
    
    return avgLoadTime || 1000 // 默认1秒
  }

  private startCleanupTimer(): void {
    // 每小时清理一次过期数据
    setInterval(() => {
      const cutoffTime = Date.now() - (this.config.dataRetentionDays * 24 * 60 * 60 * 1000)
      this.behaviorData = this.behaviorData.filter(data => data.timestamp > cutoffTime)
      this.saveData()
    }, 60 * 60 * 1000)
  }

  // 公共方法
  public getStats() {
    return {
      behaviorDataCount: this.behaviorData.length,
      modelSize: this.predictionModel.size,
      optimizationCount: this.optimizationHistory.size,
      config: this.config
    }
  }

  public clearData(): void {
    this.behaviorData = []
    this.predictionModel.clear()
    this.optimizationHistory.clear()
    localStorage.removeItem('ai_behavior_data')
    localStorage.removeItem('ai_prediction_model')
  }
}

// 全局AI优化器实例
let aiOptimizer: AIOptimizer | null = null

// 初始化AI优化器
export function initAIOptimizer(config?: Partial<AIOptimizerConfig>): AIOptimizer {
  if (aiOptimizer) {
    return aiOptimizer
  }
  
  aiOptimizer = new AIOptimizer(config)
  return aiOptimizer
}

// 获取AI优化器实例
export function getAIOptimizer(): AIOptimizer | null {
  return aiOptimizer
}

// 导出类型和类
export type { AIOptimizerConfig, UserBehavior, PredictionResult, OptimizationSuggestion }
export { AIOptimizer }

console.log('🤖 AI优化器模块加载完成 - 2025年最佳实践')
