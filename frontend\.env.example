# 🔒 证据链: 环境变量配置示例文件
# 复制此文件为 .env.local 并填入实际值

# 应用基础配置
NEXT_PUBLIC_APP_NAME="二创短视频分发系统"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_APP_ENV="development"

# API端点配置 - 避免硬编码
NEXT_PUBLIC_API_BASE_URL="http://localhost:8000"
NEXT_PUBLIC_ANALYTICS_ENDPOINT="/api/analytics"
NEXT_PUBLIC_HEALTH_CHECK_ENDPOINT="/api/health"

# AI服务配置 - ❗待PM确认: 具体的AI服务提供商
NEXT_PUBLIC_AI_SERVICE_ENDPOINT="https://api.openai.com"
OPENAI_API_KEY="sk-your-openai-api-key-here"
ANTHROPIC_API_KEY="your-anthropic-api-key-here"

# 文件上传配置
NEXT_PUBLIC_MAX_FILE_SIZE="104857600" # 100MB in bytes
NEXT_PUBLIC_ALLOWED_FILE_TYPES="video/mp4,video/avi,video/mov,video/wmv,video/flv,video/webm"

# 安全配置
NEXT_PUBLIC_ENABLE_CSP="true"
NEXT_PUBLIC_ENABLE_HTTPS_ONLY="false" # 生产环境应设为true

# 监控和分析配置
NEXT_PUBLIC_ENABLE_ANALYTICS="true"
NEXT_PUBLIC_ANALYTICS_BATCH_SIZE="10"
NEXT_PUBLIC_ANALYTICS_FLUSH_INTERVAL="30000" # 30秒

# 缓存配置
NEXT_PUBLIC_CACHE_TTL="300000" # 5分钟
NEXT_PUBLIC_CACHE_MAX_SIZE="100"

# 错误处理配置
NEXT_PUBLIC_ERROR_REPORTING_ENABLED="true"
NEXT_PUBLIC_ERROR_REPORTING_ENDPOINT="/api/errors"

# 性能监控配置
NEXT_PUBLIC_PERFORMANCE_MONITORING="true"
NEXT_PUBLIC_MEMORY_MONITORING_INTERVAL="60000" # 1分钟

# 开发模式配置
NEXT_PUBLIC_DEBUG_MODE="false"
NEXT_PUBLIC_VERBOSE_LOGGING="false"

# 数据库配置 (服务端使用，不要添加NEXT_PUBLIC_前缀)
DATABASE_URL="postgresql://username:password@localhost:5432/database"
REDIS_URL="redis://localhost:6379"

# 第三方服务配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# JWT配置 (服务端使用)
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="7d"

# 文件存储配置
UPLOAD_DIR="./uploads"
STATIC_FILES_URL="/static"

# 限流配置
RATE_LIMIT_WINDOW="60000" # 1分钟
RATE_LIMIT_MAX_REQUESTS="100"

# 日志配置
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"

# 备份和恢复配置
BACKUP_ENABLED="true"
BACKUP_INTERVAL="86400000" # 24小时
BACKUP_RETENTION_DAYS="30"
