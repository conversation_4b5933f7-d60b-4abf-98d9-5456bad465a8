/**
 * 性能优化检查测试 - 2025年最佳实践
 * 检查布局切换的性能影响和Core Web Vitals
 */

import { test, expect } from '@playwright/test';

// 测试配置
const BASE_URL = 'http://localhost:3000';

// 性能阈值 - 基于2025年Web性能标准
const PERFORMANCE_THRESHOLDS = {
  // Core Web Vitals
  LCP: 2500, // Largest Contentful Paint (ms)
  FID: 100,  // First Input Delay (ms)
  CLS: 0.1,  // Cumulative Layout Shift
  
  // 其他重要指标
  FCP: 1800, // First Contentful Paint (ms)
  TTI: 3800, // Time to Interactive (ms)
  TBT: 300,  // Total Blocking Time (ms)
  
  // 布局切换性能
  routeTransition: 500, // 路由切换时间 (ms)
  layoutReflow: 16,     // 布局重排时间 (ms) - 60fps标准
};

test.describe('性能优化检查', () => {
  test.beforeEach(async ({ page }) => {
    test.setTimeout(60000);
    
    // 设置测试环境
    await page.addInitScript(() => {
      window.localStorage.setItem('test_mode', 'true');
      window.localStorage.setItem('auth_token', 'test_token_123');
    });
  });

  test('Core Web Vitals 测试', async ({ page }) => {
    // 启用性能监控
    await page.goto(BASE_URL);
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // 获取Core Web Vitals指标
    const webVitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = {};
        
        // 使用Performance Observer API获取指标
        if ('PerformanceObserver' in window) {
          const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (entry.entryType === 'largest-contentful-paint') {
                vitals.LCP = entry.startTime;
              }
              if (entry.entryType === 'first-input') {
                vitals.FID = entry.processingStart - entry.startTime;
              }
              if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
                vitals.CLS = (vitals.CLS || 0) + entry.value;
              }
            }
          });
          
          observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
          
          // 获取导航时间指标
          const navigation = performance.getEntriesByType('navigation')[0];
          if (navigation) {
            vitals.FCP = navigation.responseStart - navigation.fetchStart;
            vitals.TTI = navigation.loadEventEnd - navigation.fetchStart;
          }
          
          // 等待一段时间收集指标
          setTimeout(() => {
            observer.disconnect();
            resolve(vitals);
          }, 2000);
        } else {
          resolve({});
        }
      });
    });
    
    console.log('Core Web Vitals:', webVitals);
    
    // 验证性能指标
    if (webVitals.LCP) {
      expect(webVitals.LCP).toBeLessThan(PERFORMANCE_THRESHOLDS.LCP);
    }
    if (webVitals.FID) {
      expect(webVitals.FID).toBeLessThan(PERFORMANCE_THRESHOLDS.FID);
    }
    if (webVitals.CLS) {
      expect(webVitals.CLS).toBeLessThan(PERFORMANCE_THRESHOLDS.CLS);
    }
  });

  test('路由切换性能测试', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    const routes = [
      { path: '/video-creation', name: '视频创作' },
      { path: '/compute-test', name: '计算引擎测试' },
      { path: '/profile', name: '个人中心' },
      { path: '/', name: '首页' }
    ];
    
    const routePerformance = [];
    
    for (const route of routes) {
      // 记录开始时间
      const startTime = Date.now();
      
      // 导航到新路由
      await page.goto(`${BASE_URL}${route.path}`);
      await page.waitForLoadState('networkidle');
      
      // 等待布局稳定
      await page.waitForTimeout(500);
      
      // 记录结束时间
      const endTime = Date.now();
      const transitionTime = endTime - startTime;
      
      routePerformance.push({
        route: route.name,
        time: transitionTime
      });
      
      console.log(`${route.name} 路由切换时间: ${transitionTime}ms`);
      
      // 验证切换时间在合理范围内
      expect(transitionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.routeTransition * 4); // 允许一定的网络延迟
    }
    
    // 计算平均切换时间
    const avgTransitionTime = routePerformance.reduce((sum, item) => sum + item.time, 0) / routePerformance.length;
    console.log(`平均路由切换时间: ${avgTransitionTime}ms`);
    
    // 验证平均切换时间
    expect(avgTransitionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.routeTransition * 2);
  });

  test('布局重排性能测试', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    // 监控布局重排
    const layoutShifts = await page.evaluate(() => {
      return new Promise((resolve) => {
        const shifts = [];
        
        if ('PerformanceObserver' in window) {
          const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
                shifts.push({
                  value: entry.value,
                  startTime: entry.startTime,
                  sources: entry.sources?.map(source => ({
                    node: source.node?.tagName,
                    previousRect: source.previousRect,
                    currentRect: source.currentRect
                  }))
                });
              }
            }
          });
          
          observer.observe({ entryTypes: ['layout-shift'] });
          
          // 模拟一些可能导致布局重排的操作
          setTimeout(() => {
            // 切换侧边栏
            const sidebar = document.querySelector('.app-sidebar');
            if (sidebar) {
              sidebar.style.width = sidebar.style.width === '200px' ? '250px' : '200px';
            }
          }, 1000);
          
          setTimeout(() => {
            observer.disconnect();
            resolve(shifts);
          }, 3000);
        } else {
          resolve([]);
        }
      });
    });
    
    console.log('布局重排事件:', layoutShifts);
    
    // 计算总的布局偏移
    const totalShift = layoutShifts.reduce((sum, shift) => sum + shift.value, 0);
    console.log(`总布局偏移: ${totalShift}`);
    
    // 验证布局稳定性
    expect(totalShift).toBeLessThan(PERFORMANCE_THRESHOLDS.CLS);
  });

  test('内存使用情况测试', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    // 获取初始内存使用情况
    const initialMemory = await page.evaluate(() => {
      if ('memory' in performance) {
        return {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
        };
      }
      return null;
    });
    
    if (initialMemory) {
      console.log('初始内存使用:', initialMemory);
      
      // 执行一些操作后检查内存
      await page.click('text=视频创作');
      await page.waitForLoadState('networkidle');
      await page.click('text=计算引擎');
      await page.waitForLoadState('networkidle');
      await page.click('text=首页');
      await page.waitForLoadState('networkidle');
      
      const finalMemory = await page.evaluate(() => {
        if ('memory' in performance) {
          return {
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize,
            jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
          };
        }
        return null;
      });
      
      if (finalMemory) {
        console.log('最终内存使用:', finalMemory);
        
        const memoryIncrease = finalMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
        console.log(`内存增长: ${memoryIncrease} bytes`);
        
        // 验证内存增长在合理范围内（不超过10MB）
        expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
      }
    }
  });

  test('资源加载性能测试', async ({ page }) => {
    // 监控网络请求
    const requests = [];
    page.on('request', request => {
      requests.push({
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType(),
        startTime: Date.now()
      });
    });
    
    const responses = [];
    page.on('response', response => {
      responses.push({
        url: response.url(),
        status: response.status(),
        size: response.headers()['content-length'],
        endTime: Date.now()
      });
    });
    
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    // 分析资源加载情况
    console.log(`总请求数: ${requests.length}`);
    console.log(`总响应数: ${responses.length}`);
    
    // 检查是否有失败的请求
    const failedResponses = responses.filter(r => r.status >= 400);
    expect(failedResponses.length).toBe(0);
    
    // 检查大文件（超过1MB的资源）
    const largeFiles = responses.filter(r => {
      const size = parseInt(r.size || '0');
      return size > 1024 * 1024;
    });
    
    if (largeFiles.length > 0) {
      console.log('大文件资源:', largeFiles);
      // 可以添加警告或建议优化
    }
  });

  test('渲染性能测试', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    // 测试滚动性能
    const scrollPerformance = await page.evaluate(() => {
      return new Promise((resolve) => {
        const startTime = performance.now();
        let frameCount = 0;
        
        const measureFrame = () => {
          frameCount++;
          if (frameCount < 60) { // 测试1秒的滚动
            requestAnimationFrame(measureFrame);
          } else {
            const endTime = performance.now();
            const duration = endTime - startTime;
            const fps = (frameCount / duration) * 1000;
            resolve({ fps, duration, frameCount });
          }
        };
        
        // 开始滚动测试
        window.scrollTo(0, 100);
        requestAnimationFrame(measureFrame);
      });
    });
    
    console.log('滚动性能:', scrollPerformance);
    
    // 验证帧率（应该接近60fps）
    expect(scrollPerformance.fps).toBeGreaterThan(30); // 最低要求
    expect(scrollPerformance.fps).toBeGreaterThan(50); // 理想目标
  });
});
