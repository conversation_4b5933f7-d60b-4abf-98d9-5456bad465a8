[tool:pytest]
# pytest配置文件

[pytest]
# 测试发现模式
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 测试目录
testpaths = tests

# 异步测试配置
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试
    api: API测试
    database: 数据库测试
    middleware: 中间件测试
    auth: 认证测试
    cache: 缓存测试
    security: 安全测试

# 最小版本要求
minversion = 6.0

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::pytest.PytestDeprecationWarning