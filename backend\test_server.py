"""简单的测试服务器
用于端到端测试
"""

from fastapi import FastAPI
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pathlib import Path

# 创建FastAPI应用
app = FastAPI(
    title="二创短视频分发系统",
    description="AI视频内容创作系统后端API",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
static_dir = Path(__file__).parent / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")


def get_unified_layout_html(page_title: str, content: str) -> str:
    """生成统一布局的HTML"""
    return f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{page_title} - 二创短视频分发系统</title>
        <link rel="stylesheet" href="/shared-ui-design.css">
        <style>
            /* 页面特定样式 */
            .welcome-section {{
                background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
                color: white;
                padding: var(--space-12) var(--space-6);
                margin: calc(-1 * var(--space-6));
                margin-bottom: var(--space-8);
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-radius: var(--radius-xl);
            }}

            .welcome-content {{ flex: 1; }}

            .welcome-title {{
                font-size: var(--text-3xl);
                font-weight: 700;
                margin-bottom: var(--space-4);
                line-height: 1.2;
            }}

            .welcome-subtitle {{
                font-size: var(--text-lg);
                opacity: 0.9;
                max-width: 600px;
            }}

            .welcome-stats {{
                display: flex;
                gap: var(--space-8);
            }}

            .stat-item {{ text-align: center; }}

            .stat-number {{
                font-size: var(--text-2xl);
                font-weight: 700;
                margin-bottom: var(--space-1);
            }}

            .stat-label {{
                font-size: var(--text-sm);
                opacity: 0.8;
            }}

            .features-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
                gap: var(--space-6);
                margin-bottom: var(--space-8);
            }}

            .feature-card {{
                background: var(--bg-primary);
                border: 1px solid var(--border-primary);
                border-radius: var(--radius-xl);
                padding: var(--space-6);
                transition: all var(--transition-normal);
                box-shadow: var(--shadow-sm);
            }}

            .feature-card:hover {{
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
                border-color: var(--primary-200);
            }}

            .feature-card__header {{
                display: flex;
                align-items: center;
                gap: var(--space-4);
                margin-bottom: var(--space-4);
            }}

            .feature-card__icon {{
                width: 3rem;
                height: 3rem;
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                font-size: 1.5rem;
            }}

            .feature-card__icon--primary {{
                background: var(--primary-50);
                color: var(--primary-600);
            }}

            .feature-card__icon--success {{
                background: var(--success-500);
                color: white;
            }}

            .feature-card__icon--warning {{
                background: var(--warning-500);
                color: white;
            }}

            .feature-card__title {{
                font-size: var(--text-xl);
                font-weight: 600;
                color: var(--text-primary);
                margin: 0;
            }}

            .feature-card__description {{
                color: var(--text-secondary);
                line-height: 1.6;
                margin-bottom: var(--space-6);
            }}

            .feature-card__actions {{
                display: flex;
                gap: var(--space-3);
            }}
        </style>
    </head>
    <body>
        <div class="app-layout">
            <!-- 顶部导航栏 -->
            <header class="app-header">
                <a href="/" class="app-header__brand">
                    <div class="app-header__brand-icon">🎬</div>
                    <span>二创短视频分发系统</span>
                </a>

                <nav class="app-header__nav">
                    <a href="/" class="app-header__nav-item">系统概览</a>
                    <a href="/admin" class="app-header__nav-item">管理界面</a>
                    <a href="/docs" class="app-header__nav-item">API文档</a>
                    <a href="/health" class="app-header__nav-item">健康检查</a>
                </nav>
            </header>

            <!-- 主体内容区 -->
            <div class="app-body">
                <!-- 左侧功能区 -->
                <aside class="app-sidebar">
                    <div class="app-sidebar__header">
                        <h3 class="app-sidebar__title">管理功能</h3>
                    </div>

                    <nav class="app-sidebar__nav">
                        <a href="/" class="app-sidebar__nav-item">
                            <svg class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                            </svg>
                            <span>系统概览</span>
                        </a>

                        <a href="/admin" class="app-sidebar__nav-item">
                            <svg class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                            </svg>
                            <span>系统管理</span>
                        </a>

                        <a href="/docs" class="app-sidebar__nav-item">
                            <svg class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                            </svg>
                            <span>API文档</span>
                        </a>

                        <a href="/health" class="app-sidebar__nav-item">
                            <svg class="app-sidebar__nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                            <span>健康检查</span>
                        </a>
                    </nav>
                </aside>

                <!-- 中央工作区 -->
                <main class="app-main">
                    <div class="app-main__header">
                        <h1 class="app-main__title">{page_title}</h1>
                    </div>

                    <div class="app-main__content">
                        {content}
                    </div>
                </main>
            </div>
        </div>
    </body>
    </html>
    """

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """主页面"""
    return get_unified_layout_html("系统概览", """
        <!-- 欢迎区域 -->
        <div class="welcome-section">
            <div class="welcome-content">
                <h1 class="welcome-title">欢迎使用后端管理系统</h1>
                <p class="welcome-subtitle">二创短视频分发系统后端管理控制台</p>
            </div>
            <div class="welcome-stats">
                <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">系统可用性</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">在线服务</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">1.2s</div>
                    <div class="stat-label">平均响应</div>
                </div>
            </div>
        </div>

        <!-- 功能卡片 -->
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-card__header">
                    <div class="feature-card__icon feature-card__icon--primary">🤖</div>
                    <h3 class="feature-card__title">AI内容创作</h3>
                </div>
                <p class="feature-card__description">智能视频生成、文案创作、内容优化</p>
                <div class="feature-card__actions">
                    <a href="/ai-services" class="btn btn-primary">管理AI服务</a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-card__header">
                    <div class="feature-card__icon feature-card__icon--success">📊</div>
                    <h3 class="feature-card__title">数据分析</h3>
                </div>
                <p class="feature-card__description">内容表现分析、用户行为洞察</p>
                <div class="feature-card__actions">
                    <a href="/analytics" class="btn btn-primary">查看分析</a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-card__header">
                    <div class="feature-card__icon feature-card__icon--warning">🚀</div>
                    <h3 class="feature-card__title">多平台分发</h3>
                </div>
                <p class="feature-card__description">一键发布到多个短视频平台</p>
                <div class="feature-card__actions">
                    <a href="/distribution" class="btn btn-primary">分发管理</a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-card__header">
                    <div class="feature-card__icon feature-card__icon--primary">⚙️</div>
                    <h3 class="feature-card__title">系统管理</h3>
                </div>
                <p class="feature-card__description">用户管理、权限控制、系统监控</p>
                <div class="feature-card__actions">
                    <a href="/admin" class="btn btn-primary">系统设置</a>
                </div>
            </div>
        </div>
    """)

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "二创短视频分发系统",
        "version": "2.0.0",
        "timestamp": "2025-01-24T12:00:00Z"
    }

@app.get("/admin", response_class=HTMLResponse)
async def admin_page():
    """管理界面"""
    return get_unified_layout_html("系统管理", """
        <div class="admin-dashboard">
            <div class="admin-grid">
                <div class="feature-card">
                    <div class="feature-card__header">
                        <div class="feature-card__icon feature-card__icon--primary">👥</div>
                        <h3 class="feature-card__title">用户管理</h3>
                    </div>
                    <p class="feature-card__description">管理系统用户、权限分配、角色设置</p>
                    <div class="feature-card__actions">
                        <a href="/users" class="btn btn-primary">管理用户</a>
                        <a href="/roles" class="btn btn-secondary">角色权限</a>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-card__header">
                        <div class="feature-card__icon feature-card__icon--success">📈</div>
                        <h3 class="feature-card__title">系统监控</h3>
                    </div>
                    <p class="feature-card__description">服务器状态、性能指标、运行监控</p>
                    <div class="feature-card__actions">
                        <a href="/monitoring" class="btn btn-primary">查看监控</a>
                        <a href="/logs" class="btn btn-secondary">系统日志</a>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-card__header">
                        <div class="feature-card__icon feature-card__icon--warning">🎥</div>
                        <h3 class="feature-card__title">内容管理</h3>
                    </div>
                    <p class="feature-card__description">视频内容、审核管理、内容分析</p>
                    <div class="feature-card__actions">
                        <a href="/content" class="btn btn-primary">内容审核</a>
                        <a href="/analytics" class="btn btn-secondary">数据分析</a>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-card__header">
                        <div class="feature-card__icon feature-card__icon--primary">⚙️</div>
                        <h3 class="feature-card__title">系统配置</h3>
                    </div>
                    <p class="feature-card__description">参数设置、功能开关、系统配置</p>
                    <div class="feature-card__actions">
                        <a href="/settings" class="btn btn-primary">系统设置</a>
                        <a href="/config" class="btn btn-secondary">配置管理</a>
                    </div>
                </div>
            </div>
        </div>
    """)

@app.get("/api/v1/status")
async def api_status():
    """API状态接口"""
    return {
        "api_version": "v1",
        "status": "running",
        "endpoints": [
            "/",
            "/health", 
            "/admin",
            "/docs",
            "/api/v1/status"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
