"""简单的测试服务器
用于端到端测试
"""

from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import os
from pathlib import Path

# 创建FastAPI应用
app = FastAPI(
    title="二创短视频分发系统",
    description="AI视频内容创作系统后端API",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
static_dir = Path(__file__).parent / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """主页面"""
    return """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>二创短视频分发系统 - 后端管理</title>
        <style>
            body {
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 40px;
                box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            }
            h1 {
                text-align: center;
                font-size: 2.5em;
                margin-bottom: 30px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .features {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 40px;
            }
            .feature-card {
                background: rgba(255, 255, 255, 0.2);
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                transition: transform 0.3s ease;
            }
            .feature-card:hover {
                transform: translateY(-5px);
            }
            .feature-card h3 {
                font-size: 1.5em;
                margin-bottom: 15px;
            }
            .api-links {
                margin-top: 30px;
                text-align: center;
            }
            .api-links a {
                color: #fff;
                text-decoration: none;
                margin: 0 15px;
                padding: 10px 20px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 25px;
                transition: background 0.3s ease;
            }
            .api-links a:hover {
                background: rgba(255, 255, 255, 0.3);
            }
            .status {
                text-align: center;
                margin-top: 20px;
                font-size: 1.2em;
            }
            .status.online {
                color: #4CAF50;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎬 二创短视频分发系统</h1>
            <div class="status online">
                ✅ 后端服务运行正常
            </div>
            
            <div class="features">
                <div class="feature-card">
                    <h3>🤖 AI内容创作</h3>
                    <p>智能视频生成、文案创作、内容优化</p>
                </div>
                <div class="feature-card">
                    <h3>📊 数据分析</h3>
                    <p>内容表现分析、用户行为洞察</p>
                </div>
                <div class="feature-card">
                    <h3>🚀 多平台分发</h3>
                    <p>一键发布到多个短视频平台</p>
                </div>
                <div class="feature-card">
                    <h3>⚙️ 系统管理</h3>
                    <p>用户管理、权限控制、系统监控</p>
                </div>
            </div>
            
            <div class="api-links">
                <a href="/docs" target="_blank">📚 API文档</a>
                <a href="/redoc" target="_blank">📖 ReDoc文档</a>
                <a href="/health" target="_blank">💚 健康检查</a>
                <a href="/admin" target="_blank">🔧 管理界面</a>
            </div>
        </div>
    </body>
    </html>
    """

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "二创短视频分发系统",
        "version": "2.0.0",
        "timestamp": "2025-01-24T12:00:00Z"
    }

@app.get("/admin", response_class=HTMLResponse)
async def admin_page():
    """管理界面"""
    return """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>系统管理 - 二创短视频分发系统</title>
        <style>
            body {
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 0;
                padding: 20px;
                background: #f5f5f5;
            }
            .admin-container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 10px;
                padding: 30px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                border-bottom: 2px solid #667eea;
                padding-bottom: 10px;
            }
            .admin-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-top: 30px;
            }
            .admin-card {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                transition: box-shadow 0.3s ease;
            }
            .admin-card:hover {
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            .admin-card h3 {
                color: #667eea;
                margin-bottom: 15px;
            }
        </style>
    </head>
    <body>
        <div class="admin-container">
            <h1>🔧 系统管理界面</h1>
            
            <div class="admin-grid">
                <div class="admin-card">
                    <h3>👥 用户管理</h3>
                    <p>管理系统用户、权限分配</p>
                </div>
                <div class="admin-card">
                    <h3>📈 系统监控</h3>
                    <p>服务器状态、性能指标</p>
                </div>
                <div class="admin-card">
                    <h3>🎥 内容管理</h3>
                    <p>视频内容、审核管理</p>
                </div>
                <div class="admin-card">
                    <h3>⚙️ 系统配置</h3>
                    <p>参数设置、功能开关</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

@app.get("/api/v1/status")
async def api_status():
    """API状态接口"""
    return {
        "api_version": "v1",
        "status": "running",
        "endpoints": [
            "/",
            "/health", 
            "/admin",
            "/docs",
            "/api/v1/status"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
