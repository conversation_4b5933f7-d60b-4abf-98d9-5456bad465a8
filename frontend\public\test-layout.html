<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎬 前端布局测试工具</h1>
        <p>这个工具可以帮助测试Vue应用的布局渲染情况</p>
        
        <div class="test-item info">
            <h3>测试说明</h3>
            <p>点击下面的按钮来测试不同页面的布局元素是否正确渲染</p>
        </div>
        
        <div class="test-item">
            <h3>页面导航测试</h3>
            <button onclick="testPage('/')">测试首页</button>
            <button onclick="testPage('/video-creation')">测试视频创作</button>
            <button onclick="testPage('/compute-test')">测试计算引擎</button>
            <button onclick="testPage('/profile')">测试个人中心</button>
            <button onclick="testPage('/login')">测试登录页面</button>
        </div>
        
        <div class="test-item">
            <h3>当前页面布局检查</h3>
            <button onclick="checkCurrentLayout()">检查当前页面布局</button>
            <div id="layout-result"></div>
        </div>
        
        <div class="test-item">
            <h3>测试结果</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        function testPage(path) {
            // 在新窗口中打开页面
            const newWindow = window.open(`http://localhost:3000${path}`, '_blank');
            
            // 等待页面加载后检查布局
            setTimeout(() => {
                try {
                    const doc = newWindow.document;
                    const result = checkLayoutElements(doc);
                    displayResult(path, result);
                } catch (error) {
                    displayResult(path, { error: '无法访问页面内容（可能是跨域限制）' });
                }
            }, 2000);
        }
        
        function checkCurrentLayout() {
            const result = checkLayoutElements(document);
            document.getElementById('layout-result').innerHTML = formatResult('当前页面', result);
        }
        
        function checkLayoutElements(doc) {
            const elements = {
                'app-header': !!doc.querySelector('.app-header'),
                'app-sidebar': !!doc.querySelector('.app-sidebar'),
                'app-main': !!doc.querySelector('.app-main'),
                'app-header__brand': !!doc.querySelector('.app-header__brand'),
                'app-header__nav': !!doc.querySelector('.app-header__nav'),
                'app-sidebar__nav': !!doc.querySelector('.app-sidebar__nav'),
                'login-container': !!doc.querySelector('.login-container'),
                'user-type-selector': !!doc.querySelector('.user-type-selector'),
                'login-form': !!doc.querySelector('.login-form')
            };
            
            return elements;
        }
        
        function formatResult(pageName, result) {
            if (result.error) {
                return `<div class="error">❌ ${pageName}: ${result.error}</div>`;
            }
            
            let html = `<div class="info"><strong>${pageName} 布局检查结果:</strong><br>`;
            
            // 统一布局元素
            const layoutElements = ['app-header', 'app-sidebar', 'app-main', 'app-header__brand', 'app-header__nav', 'app-sidebar__nav'];
            const loginElements = ['login-container', 'user-type-selector', 'login-form'];
            
            html += '<strong>统一布局元素:</strong><br>';
            layoutElements.forEach(element => {
                const status = result[element] ? '✅' : '❌';
                html += `${status} ${element}<br>`;
            });
            
            html += '<strong>登录页面元素:</strong><br>';
            loginElements.forEach(element => {
                const status = result[element] ? '✅' : '❌';
                html += `${status} ${element}<br>`;
            });
            
            html += '</div>';
            return html;
        }
        
        function displayResult(path, result) {
            const resultsDiv = document.getElementById('test-results');
            const resultHtml = formatResult(path, result);
            resultsDiv.innerHTML += resultHtml;
        }
        
        // 页面加载时自动检查当前页面
        window.onload = function() {
            checkCurrentLayout();
        };
    </script>
</body>
</html>
