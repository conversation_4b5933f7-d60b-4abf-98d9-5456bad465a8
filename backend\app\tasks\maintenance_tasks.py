"""
维护任务
"""

from datetime import datetime, timedelta
from typing import Any, Dict

from celery import current_task
from sqlalchemy import and_

from app.core.celery_app import celery_app
from app.core.database import get_db
from app.models import Content, Distribution


@celery_app.task(bind=True)
def cleanup_old_tasks(self) -> Dict[str, Any]:
    """
    清理旧的任务和数据
    """
    try:
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "message": "开始清理任务"},
        )

        # 获取数据库会话
        db = next(get_db())

        # 清理7天前的失败分发任务
        cutoff_date = datetime.utcnow() - timedelta(days=7)

        current_task.update_state(
            state="PROGRESS",
            meta={
                "current": 25,
                "total": 100,
                "message": "清理失败的分发任务",
            },
        )

        failed_distributions = (
            db.query(Distribution)
            .filter(
                and_(
                    Distribution.status == "failed",
                    Distribution.created_at < cutoff_date,
                )
            )
            .count()
        )

        db.query(Distribution).filter(
            and_(
                Distribution.status == "failed",
                Distribution.created_at < cutoff_date,
            )
        ).delete()

        current_task.update_state(
            state="PROGRESS",
            meta={"current": 50, "total": 100, "message": "清理过期内容"},
        )

        # 清理30天前的草稿内容
        draft_cutoff = datetime.utcnow() - timedelta(days=30)
        old_drafts = (
            db.query(Content)
            .filter(
                and_(
                    Content.status == "draft",
                    Content.created_at < draft_cutoff,
                )
            )
            .count()
        )

        db.query(Content).filter(
            and_(Content.status == "draft", Content.created_at < draft_cutoff)
        ).delete()

        current_task.update_state(
            state="PROGRESS",
            meta={"current": 75, "total": 100, "message": "更新统计信息"},
        )

        # 统计清理结果
        db.commit()
        db.close()

        current_task.update_state(
            state="PROGRESS",
            meta={"current": 100, "total": 100, "message": "清理完成"},
        )

        return {
            "failed_distributions_cleaned": failed_distributions,
            "old_drafts_cleaned": old_drafts,
            "cutoff_date": cutoff_date.isoformat(),
            "message": "清理任务完成",
        }

    except Exception as e:
        current_task.update_state(state="FAILURE", meta={"error": str(e)})
        raise


@celery_app.task(bind=True)
def generate_daily_report(self) -> Dict[str, Any]:
    """
    生成每日报告
    """
    try:
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "message": "生成每日报告"},
        )

        # 获取数据库会话
        db = next(get_db())

        # 计算昨天的日期范围
        yesterday = datetime.utcnow().date() - timedelta(days=1)
        start_date = datetime.combine(yesterday, datetime.min.time())
        end_date = datetime.combine(yesterday, datetime.max.time())

        current_task.update_state(
            state="PROGRESS",
            meta={"current": 25, "total": 100, "message": "统计内容数据"},
        )

        # 统计内容数据
        total_content = (
            db.query(Content)
            .filter(Content.created_at.between(start_date, end_date))
            .count()
        )

        approved_content = (
            db.query(Content)
            .filter(
                and_(
                    Content.created_at.between(start_date, end_date),
                    Content.status == "approved",
                )
            )
            .count()
        )

        current_task.update_state(
            state="PROGRESS",
            meta={"current": 50, "total": 100, "message": "统计分发数据"},
        )

        # 统计分发数据
        total_distributions = (
            db.query(Distribution)
            .filter(Distribution.created_at.between(start_date, end_date))
            .count()
        )

        successful_distributions = (
            db.query(Distribution)
            .filter(
                and_(
                    Distribution.created_at.between(start_date, end_date),
                    Distribution.status == "completed",
                )
            )
            .count()
        )

        current_task.update_state(
            state="PROGRESS",
            meta={"current": 75, "total": 100, "message": "生成报告"},
        )

        # 按平台统计分发数据
        platform_stats = {}
        platforms = ["douyin", "xiaohongshu", "bilibili", "weibo"]

        for platform in platforms:
            platform_count = (
                db.query(Distribution)
                .filter(
                    and_(
                        Distribution.created_at.between(start_date, end_date),
                        Distribution.platform == platform,
                        Distribution.status == "completed",
                    )
                )
                .count()
            )
            platform_stats[platform] = platform_count

        db.close()

        report = {
            "date": yesterday.isoformat(),
            "content_stats": {
                "total_created": total_content,
                "approved": approved_content,
                "approval_rate": (
                    (approved_content / total_content * 100) if total_content > 0 else 0
                ),
            },
            "distribution_stats": {
                "total_distributions": total_distributions,
                "successful": successful_distributions,
                "success_rate": (
                    (successful_distributions / total_distributions * 100)
                    if total_distributions > 0
                    else 0
                ),
                "platform_breakdown": platform_stats,
            },
            "message": "每日报告生成完成",
        }

        return report

    except Exception as e:
        current_task.update_state(state="FAILURE", meta={"error": str(e)})
        raise
