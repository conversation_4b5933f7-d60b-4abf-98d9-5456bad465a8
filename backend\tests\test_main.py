"""
主应用测试
"""

import pytest
from fastapi.testclient import TestClient

from main import app


class TestMainApp:
    """主应用测试类"""

    @pytest.fixture
    def client(self):
        """测试客户端"""
        return TestClient(app)

    def test_root_endpoint(self, client):
        """测试根路径"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "AI内容合规服务" in data["message"]
        assert "version" in data
        assert "status" in data
        assert data["status"] == "运行中"

    def test_health_endpoint(self, client):
        """测试健康检查端点"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "service" in data
        assert "version" in data
        assert "database" in data

    def test_status_endpoint(self, client):
        """测试系统状态端点"""
        response = client.get("/status")
        assert response.status_code == 200
        data = response.json()
        assert "service" in data
        assert "status" in data
        assert "timestamp" in data
        assert "database" in data
        assert "services" in data
        assert "endpoints" in data

    def test_api_info_endpoint(self, client):
        """测试API信息端点"""
        response = client.get("/api/v1/info")
        assert response.status_code == 200
        data = response.json()
        assert "name" in data
        assert "version" in data
        assert "endpoints" in data
        assert "services" in data
        assert "description" in data

    def test_404_handler(self, client):
        """测试404错误处理"""
        response = client.get("/nonexistent")
        assert response.status_code == 404
        data = response.json()
        assert "error" in data
        assert "接口不存在" in data["error"]
        assert "available_endpoints" in data

    def test_docs_endpoint(self, client):
        """测试API文档端点"""
        response = client.get("/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]

    def test_redoc_endpoint(self, client):
        """测试ReDoc文档端点"""
        response = client.get("/redoc")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
