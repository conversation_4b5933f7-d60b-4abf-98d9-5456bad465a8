"""
数据库迁移脚本 - 添加用户角色字段
🔒 证据链: 为RBAC权限系统添加必要的数据库字段
"""

import sqlite3
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.config import settings


def migrate_add_user_role():
    """添加用户角色字段的迁移"""

    # 连接数据库
    db_path = settings.DATABASE_URL.replace("sqlite:///", "")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 检查role字段是否已存在
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]

        if "role" not in columns:
            print("添加role字段到users表...")

            # 添加role字段
            cursor.execute(
                """
                ALTER TABLE users 
                ADD COLUMN role VARCHAR(20) DEFAULT 'user' NOT NULL
            """
            )

            # 创建索引
            cursor.execute(
                """
                CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)
            """
            )

            # 更新现有用户的角色
            # 将is_superuser为True的用户设置为admin
            cursor.execute(
                """
                UPDATE users 
                SET role = 'admin' 
                WHERE is_superuser = 1
            """
            )

            print("✅ role字段添加成功")
        else:
            print("ℹ️ role字段已存在，跳过迁移")

        # 提交更改
        conn.commit()
        print("✅ 数据库迁移完成")

    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()


def rollback_user_role():
    """回滚用户角色字段的迁移"""

    db_path = settings.DATABASE_URL.replace("sqlite:///", "")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # SQLite不支持DROP COLUMN，需要重建表
        print("开始回滚role字段...")

        # 创建临时表
        cursor.execute(
            """
            CREATE TABLE users_temp AS 
            SELECT id, username, email, hashed_password, full_name, 
                   phone_number, is_active, is_superuser, avatar_url, bio,
                   created_at, updated_at, last_login
            FROM users
        """
        )

        # 删除原表
        cursor.execute("DROP TABLE users")

        # 重命名临时表
        cursor.execute("ALTER TABLE users_temp RENAME TO users")

        # 重建索引
        cursor.execute("CREATE UNIQUE INDEX idx_users_username ON users(username)")
        cursor.execute("CREATE UNIQUE INDEX idx_users_email ON users(email)")
        cursor.execute("CREATE INDEX idx_users_phone_number ON users(phone_number)")

        conn.commit()
        print("✅ 回滚完成")

    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="用户角色字段迁移")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")

    args = parser.parse_args()

    if args.rollback:
        rollback_user_role()
    else:
        migrate_add_user_role()
