/**
 * 测试环境辅助工具 - 2025年最佳实践
 * 提供完善的测试环境权限绕过和状态模拟
 */

import { useAuthStore } from '../stores/auth'
import type { User } from '../types/auth'

// 测试用户数据
export const TEST_USERS = {
  admin: {
    id: 'test_admin_001',
    username: 'test_admin',
    email: '<EMAIL>',
    role: 'admin',
    permissions: ['*'] as string[], // 管理员拥有所有权限
    avatar: '/test-avatars/admin.png',
    createdAt: new Date('2025-01-01').toISOString(),
    lastLoginAt: new Date().toISOString()
  },
  user: {
    id: 'test_user_001', 
    username: 'test_user',
    email: '<EMAIL>',
    role: 'user',
    permissions: ['video:create', 'video:view', 'profile:edit'] as string[],
    avatar: '/test-avatars/user.png',
    createdAt: new Date('2025-01-01').toISOString(),
    lastLoginAt: new Date().toISOString()
  },
  developer: {
    id: 'test_dev_001',
    username: 'test_developer', 
    email: '<EMAIL>',
    role: 'developer',
    permissions: ['*', 'system:test', 'system:debug'] as string[],
    avatar: '/test-avatars/developer.png',
    createdAt: new Date('2025-01-01').toISOString(),
    lastLoginAt: new Date().toISOString()
  }
} as const

// 测试环境检测
export function isTestEnvironment(): boolean {
  return (
    // Playwright测试环境
    typeof window !== 'undefined' && window.localStorage?.getItem('test_mode') === 'true' ||
    // Vitest测试环境
    import.meta.env.MODE === 'test' ||
    // 环境变量配置
    import.meta.env.VITE_BYPASS_AUTH === 'true' ||
    // URL参数检测
    typeof window !== 'undefined' && window.location?.search.includes('test=true') ||
    // Node.js测试环境
    typeof process !== 'undefined' && process.env.NODE_ENV === 'test'
  )
}

// 设置测试环境
export function setupTestEnvironment(userType: keyof typeof TEST_USERS = 'admin'): void {
  if (typeof window === 'undefined') return

  const testUser = TEST_USERS[userType]
  
  // 设置localStorage标识
  window.localStorage.setItem('test_mode', 'true')
  window.localStorage.setItem('test_user_type', userType)
  
  // 设置认证信息
  window.localStorage.setItem('auth_token', `test_token_${testUser.id}`)
  window.localStorage.setItem('refresh_token', `refresh_token_${testUser.id}`)
  window.localStorage.setItem('user_info', JSON.stringify(testUser))
  
  // 设置token过期时间（1小时后）
  const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString()
  window.localStorage.setItem('token_expires_at', expiresAt)
  
  console.log(`🧪 测试环境已设置 - 用户类型: ${userType}`, testUser)
}

// 清理测试环境
export function cleanupTestEnvironment(): void {
  if (typeof window === 'undefined') return

  const testKeys = [
    'test_mode',
    'test_user_type', 
    'auth_token',
    'refresh_token',
    'user_info',
    'token_expires_at'
  ]
  
  testKeys.forEach(key => {
    window.localStorage.removeItem(key)
  })
  
  console.log('🧹 测试环境已清理')
}

// 模拟认证状态
export function mockAuthState(userType: keyof typeof TEST_USERS = 'admin'): void {
  if (!isTestEnvironment()) {
    console.warn('⚠️ 非测试环境，跳过认证状态模拟')
    return
  }

  const authStore = useAuthStore()
  const testUser = TEST_USERS[userType]
  
  // 直接设置store状态
  authStore.$patch({
    user: testUser as User,
    token: `test_token_${testUser.id}`,
    refreshToken: `refresh_token_${testUser.id}`
  })
  
  console.log(`🎭 认证状态已模拟 - 用户: ${testUser.username}`)
}

// 检查用户权限（测试环境专用）
export function hasTestPermission(permission: string, userType: keyof typeof TEST_USERS = 'admin'): boolean {
  if (!isTestEnvironment()) return false
  
  const testUser = TEST_USERS[userType]
  return testUser.permissions.includes('*') || testUser.permissions.includes(permission)
}

// 模拟API响应
export function mockApiResponse<T>(data: T, delay: number = 100): Promise<T> {
  return new Promise((resolve) => {
    setTimeout(() => resolve(data), delay)
  })
}

// 测试环境路由守卫绕过
export function shouldBypassRouteGuard(to: any): boolean {
  if (!isTestEnvironment()) return false
  
  // 获取当前测试用户类型
  const userType = (typeof window !== 'undefined' ? 
    window.localStorage.getItem('test_user_type') : 'admin') as keyof typeof TEST_USERS
  
  const testUser = TEST_USERS[userType] || TEST_USERS.admin
  
  // 检查路由权限要求
  const requiredPermissions = to.meta?.permissions || []
  const requiredRoles = to.meta?.roles || []
  
  // 管理员绕过所有检查
  if (testUser.permissions && testUser.permissions.includes('*')) {
    return true
  }
  
  // 检查权限
  if (requiredPermissions.length > 0 && testUser.permissions) {
    const hasPermission = requiredPermissions.some((perm: string) =>
      testUser.permissions!.includes(perm)
    )
    if (!hasPermission) return false
  }
  
  // 检查角色
  if (requiredRoles.length > 0) {
    const hasRole = requiredRoles.includes(testUser.role)
    if (!hasRole) return false
  }
  
  return true
}

// 测试环境初始化脚本
export function initTestEnvironment(): void {
  if (!isTestEnvironment()) return
  
  // 自动设置测试环境
  const urlParams = new URLSearchParams(window.location.search)
  const testUserType = urlParams.get('testUser') as keyof typeof TEST_USERS || 'admin'
  
  setupTestEnvironment(testUserType)
  
  // 延迟模拟认证状态，确保store已初始化
  setTimeout(() => {
    mockAuthState(testUserType)
  }, 100)
  
  // 添加全局测试标识
  if (typeof window !== 'undefined') {
    (window as any).__TEST_ENV__ = true;
    (window as any).__TEST_HELPERS__ = {
      setupTestEnvironment,
      cleanupTestEnvironment,
      mockAuthState,
      hasTestPermission,
      TEST_USERS
    };
  }
  
  console.log('🚀 测试环境初始化完成')
}

// 页面加载时自动初始化测试环境
if (typeof window !== 'undefined' && isTestEnvironment()) {
  // 使用 requestIdleCallback 确保在空闲时初始化
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      initTestEnvironment()
    })
  } else {
    setTimeout(initTestEnvironment, 0)
  }
}

// 导出类型
export type TestUserType = keyof typeof TEST_USERS
export type TestUser = typeof TEST_USERS[TestUserType]
