"""
增强的API速率限制模块
🔒 证据链: 基于SlowAPI和Redis的高级速率限制实现
集成最新的2024年技术文档建议
"""

import hashlib
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from enum import Enum

import redis
from fastapi import HTTPException, Request, Response, status
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import BaseModel

from app.core.config import settings


class RateLimitType(str, Enum):
    """速率限制类型"""
    FIXED_WINDOW = "fixed_window"
    SLIDING_WINDOW = "sliding_window"
    TOKEN_BUCKET = "token_bucket"
    LEAKY_BUCKET = "leaky_bucket"


class RateLimitRule(BaseModel):
    """速率限制规则"""
    max_requests: int
    window_seconds: int
    limit_type: RateLimitType = RateLimitType.FIXED_WINDOW
    burst_allowance: Optional[int] = None  # 突发流量允许
    description: str = ""


class EnhancedRateLimiter:
    """增强的速率限制器"""

    def __init__(self):
        # 🔒 证据链: 使用Redis实现分布式速率限制
        try:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD,
                db=settings.REDIS_DB + 2,  # 使用专用DB避免冲突
                decode_responses=True,
                socket_timeout=5.0,
                socket_connect_timeout=5.0,
                retry_on_timeout=True,
                max_connections=20,
            )
            # 测试连接
            self.redis_client.ping()
            self.redis_available = True
            print("✅ Enhanced Rate Limiter Redis连接成功")
        except Exception as e:
            print(f"⚠️ Enhanced Rate Limiter Redis连接失败: {e}")
            self.redis_client = None
            self.redis_available = False
            # 使用内存存储作为降级方案
            self._memory_store = {}
            self._cleanup_time = time.time()

        # 🔒 证据链: 2024年最新的速率限制策略配置
        self.rate_limit_rules = {
            # 认证端点 - 极严格限制（防暴力破解）
            "POST:/api/v1/auth/login": RateLimitRule(
                max_requests=3,
                window_seconds=300,  # 5分钟
                limit_type=RateLimitType.SLIDING_WINDOW,
                description="登录防暴力破解"
            ),
            "POST:/api/v1/auth/register": RateLimitRule(
                max_requests=2,
                window_seconds=600,  # 10分钟
                limit_type=RateLimitType.FIXED_WINDOW,
                description="注册防滥用"
            ),
            "POST:/api/v1/auth/send-sms": RateLimitRule(
                max_requests=2,
                window_seconds=60,  # 1分钟
                limit_type=RateLimitType.SLIDING_WINDOW,
                description="短信验证码防刷"
            ),
            
            # 内容操作 - 中等限制
            "POST:/api/v1/content": RateLimitRule(
                max_requests=15,
                window_seconds=300,  # 5分钟
                limit_type=RateLimitType.TOKEN_BUCKET,
                burst_allowance=5,
                description="内容创建限制"
            ),
            "PUT:/api/v1/content": RateLimitRule(
                max_requests=25,
                window_seconds=300,
                limit_type=RateLimitType.SLIDING_WINDOW,
                description="内容更新限制"
            ),
            "DELETE:/api/v1/content": RateLimitRule(
                max_requests=8,
                window_seconds=300,
                limit_type=RateLimitType.FIXED_WINDOW,
                description="内容删除限制"
            ),
            
            # 视频处理 - 资源密集型严格限制
            "POST:/api/v1/video/download": RateLimitRule(
                max_requests=3,
                window_seconds=300,
                limit_type=RateLimitType.LEAKY_BUCKET,
                description="视频下载限制"
            ),
            "POST:/api/v1/video/process": RateLimitRule(
                max_requests=5,
                window_seconds=600,
                limit_type=RateLimitType.TOKEN_BUCKET,
                burst_allowance=2,
                description="视频处理限制"
            ),
            
            # AI服务 - 计算资源保护
            "POST:/api/v1/ai": RateLimitRule(
                max_requests=10,
                window_seconds=300,
                limit_type=RateLimitType.SLIDING_WINDOW,
                description="AI服务调用限制"
            ),
            
            # 查询操作 - 相对宽松
            "GET": RateLimitRule(
                max_requests=80,
                window_seconds=300,
                limit_type=RateLimitType.SLIDING_WINDOW,
                burst_allowance=20,
                description="查询操作限制"
            ),
            
            # 默认限制
            "default": RateLimitRule(
                max_requests=40,
                window_seconds=300,
                limit_type=RateLimitType.SLIDING_WINDOW,
                description="默认限制"
            ),
        }

    def _get_client_identifier(self, request: Request) -> str:
        """获取客户端标识符 - 增强版"""
        # 🔒 证据链: 多层客户端识别，防止绕过

        # 优先使用认证用户ID
        user_id = getattr(request.state, "user_id", None)
        if user_id:
            return f"user:{user_id}"

        # 获取真实IP地址
        client_ip = self._get_real_ip(request)

        # 结合多个请求头增强识别
        user_agent = request.headers.get("user-agent", "")
        accept_language = request.headers.get("accept-language", "")
        accept_encoding = request.headers.get("accept-encoding", "")
        
        # 创建指纹
        fingerprint_data = f"{user_agent}:{accept_language}:{accept_encoding}"
        fingerprint_hash = hashlib.sha256(fingerprint_data.encode()).hexdigest()[:12]

        return f"ip:{client_ip}:fp:{fingerprint_hash}"

    def _get_real_ip(self, request: Request) -> str:
        """获取真实IP地址 - 增强版"""
        # 🔒 证据链: 正确处理各种代理和CDN的IP头

        # 检查常见的代理头（按优先级排序）
        ip_headers = [
            "cf-connecting-ip",  # Cloudflare
            "x-real-ip",  # Nginx
            "x-forwarded-for",  # 标准代理头
            "x-client-ip",  # Apache
            "x-cluster-client-ip",  # 集群
            "forwarded-for",
            "forwarded",
        ]

        for header in ip_headers:
            ip_value = request.headers.get(header)
            if ip_value:
                # 处理多个IP的情况（取第一个）
                if "," in ip_value:
                    ip_value = ip_value.split(",")[0].strip()
                # 验证IP格式
                if self._is_valid_ip(ip_value):
                    return ip_value

        # 默认使用连接IP
        return request.client.host if request.client else "unknown"

    def _is_valid_ip(self, ip: str) -> bool:
        """验证IP地址格式"""
        try:
            import ipaddress
            ipaddress.ip_address(ip)
            return True
        except ValueError:
            return False

    def _get_rate_limit_key(self, identifier: str, endpoint: str, window: str, limit_type: str) -> str:
        """生成速率限制键"""
        return f"enhanced_rate_limit:{limit_type}:{endpoint}:{identifier}:{window}"

    def check_rate_limit(
        self,
        request: Request,
        rule: RateLimitRule,
        endpoint: str = None,
    ) -> Tuple[bool, Dict]:
        """检查速率限制 - 支持多种算法"""

        if not endpoint:
            endpoint = f"{request.method}:{request.url.path}"

        identifier = self._get_client_identifier(request)
        current_time = int(time.time())

        # 根据限制类型选择算法
        if rule.limit_type == RateLimitType.SLIDING_WINDOW:
            return self._check_sliding_window_limit(
                identifier, endpoint, rule, current_time
            )
        elif rule.limit_type == RateLimitType.TOKEN_BUCKET:
            return self._check_token_bucket_limit(
                identifier, endpoint, rule, current_time
            )
        elif rule.limit_type == RateLimitType.LEAKY_BUCKET:
            return self._check_leaky_bucket_limit(
                identifier, endpoint, rule, current_time
            )
        else:  # FIXED_WINDOW
            return self._check_fixed_window_limit(
                identifier, endpoint, rule, current_time
            )

    def _check_sliding_window_limit(
        self, identifier: str, endpoint: str, rule: RateLimitRule, current_time: int
    ) -> Tuple[bool, Dict]:
        """滑动窗口算法"""
        if not self.redis_available:
            return self._check_memory_fallback(identifier, endpoint, rule, current_time)

        try:
            key = f"sliding:{identifier}:{endpoint}"
            window_start = current_time - rule.window_seconds

            # 使用Redis有序集合实现滑动窗口
            pipe = self.redis_client.pipeline()
            # 移除过期的请求记录
            pipe.zremrangebyscore(key, 0, window_start)
            # 添加当前请求
            pipe.zadd(key, {str(current_time): current_time})
            # 获取窗口内的请求数
            pipe.zcard(key)
            # 设置过期时间
            pipe.expire(key, rule.window_seconds + 60)
            
            results = pipe.execute()
            current_requests = results[2]

            remaining = max(0, rule.max_requests - current_requests)
            reset_time = current_time + rule.window_seconds

            rate_limit_info = {
                "limit": rule.max_requests,
                "remaining": remaining,
                "reset": reset_time,
                "reset_time": datetime.fromtimestamp(reset_time).isoformat(),
                "algorithm": "sliding_window",
                "window_seconds": rule.window_seconds,
            }

            if current_requests > rule.max_requests:
                return False, rate_limit_info

            return True, rate_limit_info

        except Exception as e:
            print(f"Sliding window rate limit error: {e}")
            return True, {"error": "rate_limit_unavailable"}

    def _check_token_bucket_limit(
        self, identifier: str, endpoint: str, rule: RateLimitRule, current_time: int
    ) -> Tuple[bool, Dict]:
        """令牌桶算法"""
        if not self.redis_available:
            return self._check_memory_fallback(identifier, endpoint, rule, current_time)

        try:
            key = f"token_bucket:{identifier}:{endpoint}"
            
            # 获取当前令牌桶状态
            bucket_data = self.redis_client.hgetall(key)
            
            if not bucket_data:
                # 初始化令牌桶
                tokens = rule.max_requests
                last_refill = current_time
            else:
                tokens = float(bucket_data.get("tokens", rule.max_requests))
                last_refill = int(bucket_data.get("last_refill", current_time))

            # 计算需要添加的令牌数
            time_passed = current_time - last_refill
            refill_rate = rule.max_requests / rule.window_seconds  # 每秒添加的令牌数
            tokens_to_add = time_passed * refill_rate
            
            # 更新令牌数（不超过桶容量）
            max_tokens = rule.max_requests + (rule.burst_allowance or 0)
            tokens = min(max_tokens, tokens + tokens_to_add)

            # 检查是否有足够的令牌
            if tokens >= 1:
                tokens -= 1
                allowed = True
            else:
                allowed = False

            # 更新Redis中的令牌桶状态
            pipe = self.redis_client.pipeline()
            pipe.hset(key, mapping={
                "tokens": str(tokens),
                "last_refill": str(current_time)
            })
            pipe.expire(key, rule.window_seconds * 2)
            pipe.execute()

            rate_limit_info = {
                "limit": rule.max_requests,
                "remaining": int(tokens),
                "reset": current_time + int((1 - tokens % 1) / refill_rate) if tokens < 1 else current_time,
                "algorithm": "token_bucket",
                "tokens": tokens,
                "refill_rate": refill_rate,
            }

            return allowed, rate_limit_info

        except Exception as e:
            print(f"Token bucket rate limit error: {e}")
            return True, {"error": "rate_limit_unavailable"}

    def _check_leaky_bucket_limit(
        self, identifier: str, endpoint: str, rule: RateLimitRule, current_time: int
    ) -> Tuple[bool, Dict]:
        """漏桶算法"""
        if not self.redis_available:
            return self._check_memory_fallback(identifier, endpoint, rule, current_time)

        try:
            key = f"leaky_bucket:{identifier}:{endpoint}"
            
            # 获取当前漏桶状态
            bucket_data = self.redis_client.hgetall(key)
            
            if not bucket_data:
                # 初始化漏桶
                volume = 0
                last_leak = current_time
            else:
                volume = float(bucket_data.get("volume", 0))
                last_leak = int(bucket_data.get("last_leak", current_time))

            # 计算漏出的水量
            time_passed = current_time - last_leak
            leak_rate = rule.max_requests / rule.window_seconds  # 每秒漏出的请求数
            leaked_volume = time_passed * leak_rate
            
            # 更新桶中的水量
            volume = max(0, volume - leaked_volume)

            # 检查是否可以添加新请求
            if volume < rule.max_requests:
                volume += 1
                allowed = True
            else:
                allowed = False

            # 更新Redis中的漏桶状态
            pipe = self.redis_client.pipeline()
            pipe.hset(key, mapping={
                "volume": str(volume),
                "last_leak": str(current_time)
            })
            pipe.expire(key, rule.window_seconds * 2)
            pipe.execute()

            rate_limit_info = {
                "limit": rule.max_requests,
                "remaining": int(rule.max_requests - volume),
                "reset": current_time + int(volume / leak_rate) if volume > 0 else current_time,
                "algorithm": "leaky_bucket",
                "volume": volume,
                "leak_rate": leak_rate,
            }

            return allowed, rate_limit_info

        except Exception as e:
            print(f"Leaky bucket rate limit error: {e}")
            return True, {"error": "rate_limit_unavailable"}

    def _check_fixed_window_limit(
        self, identifier: str, endpoint: str, rule: RateLimitRule, current_time: int
    ) -> Tuple[bool, Dict]:
        """固定窗口算法"""
        if not self.redis_available:
            return self._check_memory_fallback(identifier, endpoint, rule, current_time)

        try:
            window_start = current_time - (current_time % rule.window_seconds)
            key = self._get_rate_limit_key(identifier, endpoint, str(window_start), "fixed")

            # 使用Redis原子操作
            pipe = self.redis_client.pipeline()
            pipe.incr(key)
            pipe.expire(key, rule.window_seconds)
            results = pipe.execute()

            current_requests = results[0]
            remaining = max(0, rule.max_requests - current_requests)
            reset_time = window_start + rule.window_seconds

            rate_limit_info = {
                "limit": rule.max_requests,
                "remaining": remaining,
                "reset": reset_time,
                "reset_time": datetime.fromtimestamp(reset_time).isoformat(),
                "algorithm": "fixed_window",
                "window_seconds": rule.window_seconds,
            }

            if current_requests > rule.max_requests:
                return False, rate_limit_info

            return True, rate_limit_info

        except Exception as e:
            print(f"Fixed window rate limit error: {e}")
            return True, {"error": "rate_limit_unavailable"}

    def _check_memory_fallback(
        self, identifier: str, endpoint: str, rule: RateLimitRule, current_time: int
    ) -> Tuple[bool, Dict]:
        """内存降级方案"""
        # 简化的内存实现（仅支持固定窗口）
        window_start = current_time - (current_time % rule.window_seconds)
        key = f"{identifier}:{endpoint}:{window_start}"

        if key not in self._memory_store:
            self._memory_store[key] = {
                "count": 0,
                "expires": window_start + rule.window_seconds,
            }

        self._memory_store[key]["count"] += 1
        current_requests = self._memory_store[key]["count"]

        remaining = max(0, rule.max_requests - current_requests)
        reset_time = window_start + rule.window_seconds

        rate_limit_info = {
            "limit": rule.max_requests,
            "remaining": remaining,
            "reset": reset_time,
            "reset_time": datetime.fromtimestamp(reset_time).isoformat(),
            "algorithm": "fixed_window_fallback",
            "fallback": True,
        }

        # 清理过期数据
        if current_time - self._cleanup_time > 300:
            self._cleanup_memory_store()
            self._cleanup_time = current_time

        return current_requests <= rule.max_requests, rate_limit_info

    def _cleanup_memory_store(self):
        """清理过期的内存存储"""
        current_time = time.time()
        expired_keys = [
            key
            for key, data in self._memory_store.items()
            if data["expires"] < current_time
        ]
        for key in expired_keys:
            del self._memory_store[key]

    def get_rate_limit_rule(self, request: Request) -> RateLimitRule:
        """获取适用的速率限制规则"""
        endpoint_key = f"{request.method}:{request.url.path}"

        # 精确匹配
        if endpoint_key in self.rate_limit_rules:
            return self.rate_limit_rules[endpoint_key]

        # 方法匹配
        if request.method in self.rate_limit_rules:
            return self.rate_limit_rules[request.method]

        # 默认规则
        return self.rate_limit_rules["default"]

    def get_rate_limit_stats(self) -> Dict:
        """获取速率限制统计信息"""
        stats = {
            "redis_available": self.redis_available,
            "total_rules": len(self.rate_limit_rules),
            "algorithms_supported": [e.value for e in RateLimitType],
            "memory_fallback_entries": len(self._memory_store) if not self.redis_available else 0,
            "rules": {
                endpoint: {
                    "max_requests": rule.max_requests,
                    "window_seconds": rule.window_seconds,
                    "algorithm": rule.limit_type.value,
                    "description": rule.description,
                }
                for endpoint, rule in self.rate_limit_rules.items()
            },
        }
        return stats


class EnhancedRateLimitMiddleware(BaseHTTPMiddleware):
    """增强速率限制中间件"""

    def __init__(self, app, rate_limiter: EnhancedRateLimiter = None):
        super().__init__(app)
        self.rate_limiter = rate_limiter or EnhancedRateLimiter()
        
        # 白名单路径（不进行速率限制）
        self.whitelist_paths = {
            "/health",
            "/metrics",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/favicon.ico",
        }

    async def dispatch(self, request: Request, call_next):
        """处理请求的速率限制"""
        
        # 检查是否在白名单中
        if request.url.path in self.whitelist_paths:
            return await call_next(request)

        # 获取适用的速率限制规则
        rule = self.rate_limiter.get_rate_limit_rule(request)
        
        # 检查速率限制
        allowed, rate_limit_info = self.rate_limiter.check_rate_limit(request, rule)
        
        if not allowed:
            # 速率限制触发，返回429错误
            response = Response(
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Limit: {rate_limit_info.get('limit')} requests per {rule.window_seconds} seconds",
                    "rate_limit_info": rate_limit_info,
                    "retry_after": rate_limit_info.get('reset', 0) - int(time.time()),
                },
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                media_type="application/json"
            )
            
            # 添加速率限制头
            self._add_rate_limit_headers(response, rate_limit_info)
            return response

        # 继续处理请求
        response = await call_next(request)
        
        # 添加速率限制头到响应
        self._add_rate_limit_headers(response, rate_limit_info)
        
        return response

    def _add_rate_limit_headers(self, response: Response, rate_limit_info: Dict):
        """添加速率限制头到响应"""
        response.headers["X-RateLimit-Limit"] = str(rate_limit_info.get("limit", 0))
        response.headers["X-RateLimit-Remaining"] = str(rate_limit_info.get("remaining", 0))
        response.headers["X-RateLimit-Reset"] = str(rate_limit_info.get("reset", 0))
        response.headers["X-RateLimit-Algorithm"] = rate_limit_info.get("algorithm", "unknown")
        
        if "reset_time" in rate_limit_info:
            response.headers["X-RateLimit-Reset-Time"] = rate_limit_info["reset_time"]


# 全局速率限制器实例
enhanced_rate_limiter = EnhancedRateLimiter()