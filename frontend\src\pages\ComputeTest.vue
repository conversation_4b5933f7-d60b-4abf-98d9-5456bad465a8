<template>
  <div class="compute-test-page">
    <div class="compute-test-container">
      
      <!-- FFmpeg 测试区域 -->
      <div class="card mb-8">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
            <VideoIcon class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h2 class="text-xl font-semibold text-gray-900">FFmpeg WASM 测试</h2>
            <p class="text-gray-600">测试浏览器内视频处理能力</p>
          </div>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">选择视频文件</label>
            <input 
              ref="videoFileInput"
              type="file" 
              accept="video/*" 
              @change="handleVideoFileSelect"
              class="input-field"
            />
          </div>
          
          <div class="flex space-x-4">
            <button 
              @click="testFFmpegLoad" 
              :disabled="ffmpegLoading"
              class="btn-primary"
            >
              <CpuIcon class="w-4 h-4 mr-2 inline" />
              {{ ffmpegLoading ? '加载中...' : '加载 FFmpeg' }}
            </button>
            
            <button 
              @click="testVideoConversion" 
              :disabled="!ffmpegLoaded || !selectedVideoFile || converting"
              class="btn-secondary"
            >
              <RefreshCwIcon class="w-4 h-4 mr-2 inline" />
              {{ converting ? '转换中...' : '转换视频' }}
            </button>
          </div>
          
          <div v-if="ffmpegStatus" class="p-4 bg-gray-50 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">FFmpeg 状态</h4>
            <p class="text-sm text-gray-600">{{ ffmpegStatus }}</p>
          </div>
          
          <div v-if="conversionProgress > 0" class="space-y-2">
            <div class="flex justify-between text-sm">
              <span>转换进度</span>
              <span>{{ conversionProgress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div 
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: conversionProgress + '%' }"
              ></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- TensorFlow.js 测试区域 -->
      <div class="card mb-8">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
            <BrainIcon class="w-6 h-6 text-green-600" />
          </div>
          <div>
            <h2 class="text-xl font-semibold text-gray-900">TensorFlow.js 测试</h2>
            <p class="text-gray-600">测试机器学习推理能力</p>
          </div>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">选择图片文件</label>
            <input 
              ref="imageFileInput"
              type="file" 
              accept="image/*" 
              @change="handleImageFileSelect"
              class="input-field"
            />
          </div>
          
          <div class="flex space-x-4">
            <button 
              @click="testTensorFlowLoad" 
              :disabled="tensorflowLoading"
              class="btn-primary"
            >
              <BrainIcon class="w-4 h-4 mr-2 inline" />
              {{ tensorflowLoading ? '加载中...' : '加载 TensorFlow' }}
            </button>
            
            <button 
              @click="testImageClassification" 
              :disabled="!tensorflowLoaded || !selectedImageFile || classifying"
              class="btn-secondary"
            >
              <ScanIcon class="w-4 h-4 mr-2 inline" />
              {{ classifying ? '分析中...' : '图像分类' }}
            </button>
          </div>
          
          <div v-if="tensorflowStatus" class="p-4 bg-gray-50 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">TensorFlow 状态</h4>
            <p class="text-sm text-gray-600">{{ tensorflowStatus }}</p>
          </div>
          
          <div v-if="classificationResults.length > 0" class="space-y-2">
            <h4 class="font-medium text-gray-900">分类结果</h4>
            <div class="space-y-1">
              <div 
                v-for="(result, index) in classificationResults" 
                :key="index"
                class="flex justify-between items-center p-2 bg-gray-50 rounded"
              >
                <span class="text-sm text-gray-700">{{ result.className }}</span>
                <span class="text-sm font-medium text-gray-900">{{ (result.probability * 100).toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Web Workers 测试区域 -->
      <div class="card mb-8">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
            <ZapIcon class="w-6 h-6 text-purple-600" />
          </div>
          <div>
            <h2 class="text-xl font-semibold text-gray-900">Web Workers 测试</h2>
            <p class="text-gray-600">测试多线程计算能力</p>
          </div>
        </div>
        
        <div class="space-y-4">
          <div class="flex space-x-4">
            <button 
              @click="testWebWorker" 
              :disabled="workerRunning"
              class="btn-primary"
            >
              <CpuIcon class="w-4 h-4 mr-2 inline" />
              {{ workerRunning ? '计算中...' : '启动 Worker 计算' }}
            </button>
          </div>
          
          <div v-if="workerResult" class="p-4 bg-gray-50 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">计算结果</h4>
            <p class="text-sm text-gray-600">{{ workerResult }}</p>
          </div>
        </div>
      </div>
      
      <!-- 系统信息 -->
      <div class="card">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
            <InfoIcon class="w-6 h-6 text-orange-600" />
          </div>
          <div>
            <h2 class="text-xl font-semibold text-gray-900">系统信息</h2>
            <p class="text-gray-600">浏览器和硬件支持情况</p>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">WebAssembly</span>
              <span class="text-sm font-medium" :class="systemInfo.webAssembly ? 'text-green-600' : 'text-red-600'">
                {{ systemInfo.webAssembly ? '支持' : '不支持' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Web Workers</span>
              <span class="text-sm font-medium" :class="systemInfo.webWorkers ? 'text-green-600' : 'text-red-600'">
                {{ systemInfo.webWorkers ? '支持' : '不支持' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">WebGPU</span>
              <span class="text-sm font-medium" :class="systemInfo.webGPU ? 'text-green-600' : 'text-red-600'">
                {{ systemInfo.webGPU ? '支持' : '不支持' }}
              </span>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">CPU 核心数</span>
              <span class="text-sm font-medium text-gray-900">{{ systemInfo.cpuCores }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">内存</span>
              <span class="text-sm font-medium text-gray-900">{{ systemInfo.memory }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">用户代理</span>
              <span class="text-sm font-medium text-gray-900 truncate">{{ systemInfo.userAgent }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useComputeStore } from '../stores/compute'
import {
  Video as VideoIcon,
  Brain as BrainIcon,
  Cpu as CpuIcon,
  RefreshCw as RefreshCwIcon,
  Scan as ScanIcon,
  Zap as ZapIcon,
  Info as InfoIcon,
} from 'lucide-vue-next'

// 使用计算引擎store
const computeStore = useComputeStore()

// 文件选择相关
const selectedVideoFile = ref<File | null>(null)
const selectedImageFile = ref<File | null>(null)
const videoFileInput = ref<HTMLInputElement>()
const imageFileInput = ref<HTMLInputElement>()

// 计算属性
const ffmpegLoading = computed(() => computeStore.isProcessing && computeStore.currentTask?.type === 'video')
const ffmpegLoaded = computed(() => computeStore.isInitialized && computeStore.videoEngineReady)
const ffmpegStatus = computed(() => computeStore.hasErrors ? '引擎错误' : '引擎就绪')
const converting = computed(() => computeStore.isProcessing && computeStore.currentTask?.type === 'video')
const conversionProgress = computed(() => computeStore.globalProgress)

const tensorflowLoading = computed(() => computeStore.isProcessing && computeStore.currentTask?.type === 'ai')
const tensorflowLoaded = computed(() => computeStore.isInitialized && computeStore.aiEngineReady)
const tensorflowStatus = computed(() => computeStore.hasErrors ? '引擎错误' : '引擎就绪')
const classifying = computed(() => computeStore.isProcessing && computeStore.currentTask?.type === 'ai')
const classificationResults = ref<Array<{ className: string; probability: number }>>([])

// Web Workers 相关状态
const workerRunning = ref(false)
const workerResult = ref('')

// 系统信息
const systemInfo = ref({
  webAssembly: false,
  webWorkers: false,
  webGPU: false,
  cpuCores: 0,
  memory: '未知',
  userAgent: ''
})

/**
 * 处理视频文件选择
 */
const handleVideoFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    selectedVideoFile.value = target.files[0]
  }
}

/**
 * 处理图片文件选择
 */
const handleImageFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    selectedImageFile.value = target.files[0]
  }
}

/**
 * 测试 FFmpeg 加载
 */
const testFFmpegLoad = async () => {
  try {
    await computeStore.initEngine()
  } catch (error) {
    console.error('FFmpeg 加载失败:', error)
  }
}

/**
 * 测试视频转换
 */
const testVideoConversion = async () => {
  if (!selectedVideoFile.value) return
  
  try {
    const options = {
      input: selectedVideoFile.value,
      output: {
        format: 'mp4',
        quality: 'medium' as const
      }
    }
    await computeStore.processVideo(options)
  } catch (error) {
    console.error('视频转换失败:', error)
  }
}

/**
 * 测试 TensorFlow 加载
 */
const testTensorFlowLoad = async () => {
  try {
    await computeStore.initEngine()
  } catch (error) {
    console.error('TensorFlow 加载失败:', error)
  }
}

/**
 * 测试图像分类
 */
const testImageClassification = async () => {
  if (!selectedImageFile.value) return
  
  try {
    const imageUrl = URL.createObjectURL(selectedImageFile.value)
    const options = {
      model: 'image-classification',
      input: imageUrl
    }
    await computeStore.runInference(options)
    
    // 模拟分类结果
    classificationResults.value = [
      { className: '猫', probability: 0.85 },
      { className: '狗', probability: 0.12 },
      { className: '鸟', probability: 0.03 }
    ]
  } catch (error) {
    console.error('图像分类失败:', error)
  }
}

/**
 * 测试 Web Worker
 */
const testWebWorker = async () => {
  workerRunning.value = true
  workerResult.value = ''
  
  try {
    // 模拟 Web Worker 计算
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const result = Math.PI * Math.random() * 1000000
    workerResult.value = `计算完成: ${result.toFixed(6)}`
  } catch (error) {
    workerResult.value = `Worker 计算失败: ${error}`
  } finally {
    workerRunning.value = false
  }
}

/**
 * 检测系统信息
 */
const detectSystemInfo = () => {
  // 检测 WebAssembly 支持
  systemInfo.value.webAssembly = typeof WebAssembly === 'object'
  
  // 检测 Web Workers 支持
  systemInfo.value.webWorkers = typeof Worker !== 'undefined'
  
  // 检测 WebGPU 支持
  systemInfo.value.webGPU = 'gpu' in navigator
  
  // 获取 CPU 核心数
  systemInfo.value.cpuCores = navigator.hardwareConcurrency || 0
  
  // 获取内存信息
  if ('memory' in performance) {
    const memory = (performance as any).memory
    systemInfo.value.memory = `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)} MB`
  }
  
  // 获取用户代理
  systemInfo.value.userAgent = navigator.userAgent.substring(0, 50) + '...'
}

// 组件挂载时检测系统信息
onMounted(() => {
  detectSystemInfo()
})
</script>

<style scoped>
.compute-test-page {
  padding: 2rem 0;
}

.compute-test-container {
  max-width: 1024px;
  margin: 0 auto;
  padding: 0 1.5rem;
}
</style>