/**
 * 测试登录功能
 */

const axios = require('axios');

async function testLogin() {
  console.log('🔐 测试登录功能...\n');
  
  const testUsers = [
    { username: 'admin_user', password: 'Admin123!', role: '管理员' },
    { username: 'content_creator', password: 'Creator123!', role: '创作者' },
    { username: 'demo_user', password: 'Demo123!', role: '普通用户' }
  ];
  
  for (const testUser of testUsers) {
    try {
      console.log(`测试用户: ${testUser.username} (${testUser.role})`);
      
      const response = await axios.post('http://localhost:8001/auth/login', {
        username: testUser.username,
        password: testUser.password
      });
      
      if (response.data.success) {
        console.log('  ✅ 登录成功');
        console.log('  👤 用户信息:', response.data.user.username);
        console.log('  📧 邮箱:', response.data.user.email);
        console.log('  🎭 角色:', response.data.user.role);
      } else {
        console.log('  ❌ 登录失败');
      }
      
    } catch (error) {
      console.log('  ❌ 登录错误:', error.response?.data?.detail || error.message);
    }
    
    console.log('');
  }
  
  // 测试错误登录
  console.log('测试错误登录:');
  try {
    await axios.post('http://localhost:8001/auth/login', {
      username: 'wrong_user',
      password: 'wrong_password'
    });
  } catch (error) {
    console.log('  ✅ 正确拒绝了错误凭据:', error.response?.data?.detail);
  }
}

testLogin().catch(console.error);
