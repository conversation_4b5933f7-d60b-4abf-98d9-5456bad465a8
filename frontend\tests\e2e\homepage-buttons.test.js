/**
 * 前端主页面功能按钮测试脚本
 * 使用Playwright进行端到端测试
 */

const { test, expect } = require('@playwright/test');

test.describe('前端主页面功能按钮测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问主页面
    await page.goto('http://localhost:3001/');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
  });

  test('页面基本加载测试', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle('二创短视频分发系统');
    
    // 检查主要容器是否存在
    await expect(page.locator('#app')).toBeVisible();
  });

  test('导航栏按钮测试', async ({ page }) => {
    // 等待导航栏加载
    await page.waitForSelector('.app-header', { timeout: 10000 });
    
    // 检查logo是否存在
    const logo = page.locator('.app-header .logo');
    if (await logo.count() > 0) {
      await expect(logo).toBeVisible();
    }
    
    // 检查主导航菜单
    const navItems = page.locator('.app-header .nav-item');
    const navCount = await navItems.count();
    console.log(`找到 ${navCount} 个导航项`);
    
    // 测试每个导航项
    for (let i = 0; i < navCount; i++) {
      const navItem = navItems.nth(i);
      await expect(navItem).toBeVisible();
      
      // 获取导航项文本
      const text = await navItem.textContent();
      console.log(`导航项 ${i + 1}: ${text}`);
      
      // 检查是否可点击
      await expect(navItem).toBeEnabled();
    }
  });

  test('侧边栏按钮测试', async ({ page }) => {
    // 检查侧边栏是否存在
    const sidebar = page.locator('.app-sidebar');
    if (await sidebar.count() > 0) {
      await expect(sidebar).toBeVisible();
      
      // 检查侧边栏导航项
      const sidebarItems = page.locator('.app-sidebar .nav-item');
      const sidebarCount = await sidebarItems.count();
      console.log(`找到 ${sidebarCount} 个侧边栏项`);
      
      // 测试每个侧边栏项
      for (let i = 0; i < sidebarCount; i++) {
        const item = sidebarItems.nth(i);
        await expect(item).toBeVisible();
        
        const text = await item.textContent();
        console.log(`侧边栏项 ${i + 1}: ${text}`);
      }
    }
  });

  test('主要功能按钮测试', async ({ page }) => {
    // 等待主内容区域加载
    await page.waitForSelector('.app-main', { timeout: 10000 });
    
    // 查找所有按钮
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    console.log(`找到 ${buttonCount} 个按钮`);
    
    // 测试每个按钮
    for (let i = 0; i < buttonCount; i++) {
      const button = buttons.nth(i);
      
      // 检查按钮是否可见
      if (await button.isVisible()) {
        const text = await button.textContent();
        console.log(`按钮 ${i + 1}: ${text}`);
        
        // 检查按钮是否可点击
        const isEnabled = await button.isEnabled();
        console.log(`  - 可点击: ${isEnabled}`);
        
        // 检查按钮类名
        const className = await button.getAttribute('class');
        console.log(`  - 样式类: ${className}`);
      }
    }
  });

  test('链接按钮测试', async ({ page }) => {
    // 查找所有链接
    const links = page.locator('a');
    const linkCount = await links.count();
    console.log(`找到 ${linkCount} 个链接`);
    
    // 测试每个链接
    for (let i = 0; i < linkCount; i++) {
      const link = links.nth(i);
      
      if (await link.isVisible()) {
        const text = await link.textContent();
        const href = await link.getAttribute('href');
        console.log(`链接 ${i + 1}: ${text} -> ${href}`);
        
        // 检查链接是否有效
        if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
          console.log(`  - 链接有效: ${href}`);
        }
      }
    }
  });

  test('表单控件测试', async ({ page }) => {
    // 查找输入框
    const inputs = page.locator('input');
    const inputCount = await inputs.count();
    console.log(`找到 ${inputCount} 个输入框`);
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      
      if (await input.isVisible()) {
        const type = await input.getAttribute('type');
        const placeholder = await input.getAttribute('placeholder');
        console.log(`输入框 ${i + 1}: type=${type}, placeholder=${placeholder}`);
      }
    }
    
    // 查找选择框
    const selects = page.locator('select');
    const selectCount = await selects.count();
    console.log(`找到 ${selectCount} 个选择框`);
    
    // 查找文本域
    const textareas = page.locator('textarea');
    const textareaCount = await textareas.count();
    console.log(`找到 ${textareaCount} 个文本域`);
  });

  test('响应式布局测试', async ({ page }) => {
    // 测试桌面端布局
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(1000);
    
    const desktopLayout = page.locator('.app-layout');
    if (await desktopLayout.count() > 0) {
      await expect(desktopLayout).toBeVisible();
      console.log('桌面端布局正常显示');
    }
    
    // 测试移动端布局
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    const mobileLayout = page.locator('.mobile-layout');
    if (await mobileLayout.count() > 0) {
      await expect(mobileLayout).toBeVisible();
      console.log('移动端布局正常显示');
    }
    
    // 检查汉堡菜单按钮
    const hamburgerMenu = page.locator('.hamburger-menu, .mobile-menu-toggle');
    if (await hamburgerMenu.count() > 0) {
      await expect(hamburgerMenu).toBeVisible();
      console.log('移动端汉堡菜单按钮存在');
    }
  });

  test('交互功能测试', async ({ page }) => {
    // 测试点击事件
    const clickableElements = page.locator('button, a, .clickable');
    const clickableCount = await clickableElements.count();
    
    console.log(`找到 ${clickableCount} 个可点击元素`);
    
    // 测试前几个可点击元素
    const testCount = Math.min(5, clickableCount);
    for (let i = 0; i < testCount; i++) {
      const element = clickableElements.nth(i);
      
      if (await element.isVisible() && await element.isEnabled()) {
        const text = await element.textContent();
        console.log(`测试点击: ${text}`);
        
        try {
          // 模拟点击
          await element.click({ timeout: 3000 });
          await page.waitForTimeout(500);
          console.log(`  - 点击成功`);
        } catch (error) {
          console.log(`  - 点击失败: ${error.message}`);
        }
      }
    }
  });

  test('页面性能测试', async ({ page }) => {
    // 开始性能监控
    const startTime = Date.now();
    
    // 重新加载页面
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    console.log(`页面加载时间: ${loadTime}ms`);
    
    // 检查页面是否有JavaScript错误
    const errors = [];
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.waitForTimeout(2000);
    
    if (errors.length > 0) {
      console.log('发现JavaScript错误:');
      errors.forEach(error => console.log(`  - ${error}`));
    } else {
      console.log('未发现JavaScript错误');
    }
  });
});
