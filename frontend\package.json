{"name": "video-system-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --fix", "test": "vitest --config vitest.config.minimal.ts", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "check": "vue-tsc --noEmit && npm run lint && npm run test"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.1", "@sentry/tracing": "^7.120.3", "@sentry/vue": "^9.40.0", "@tensorflow-models/coco-ssd": "^2.2.3", "@tensorflow-models/mobilenet": "^2.1.1", "@tensorflow/tfjs": "^4.22.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "i18next": "^25.3.2", "lucide-vue-next": "^0.525.0", "pinia": "^2.3.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "vue": "^3.5.0", "vue-i18n": "^10.0.0", "vue-lazyload": "^3.0.0", "vue-router": "^4.5.0"}, "devDependencies": {"@playwright/test": "^1.50.0", "@types/node": "^20.0.0", "@vitejs/plugin-vue": "^5.1.0", "@vitest/ui": "^1.6.0", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.0.0", "happy-dom": "^15.11.6", "jsdom": "^26.1.0", "postcss": "^8.5.6", "prettier": "^3.4.0", "puppeteer": "^24.15.0", "tailwindcss-animate": "^1.0.7", "terser": "^5.43.1", "typescript": "^5.8.0", "vite": "^5.4.0", "vite-plugin-compression": "^0.5.1", "vitest": "^1.6.0", "vue-tsc": "^2.0.0"}}