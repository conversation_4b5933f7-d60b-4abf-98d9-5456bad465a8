"""
API端点测试
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.models import Content, Project, User
from app.utils.auth_deps import create_access_token, get_password_hash
from main import app


class TestAuthAPI:
    """认证API测试"""

    @pytest.fixture
    def client(self):
        return TestClient(app)

    def test_login_success(self, client: TestClient, db: Session):
        """测试登录成功"""
        # 创建测试用户
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password=get_password_hash("testpass123"),
            is_active=True,
        )
        db.add(user)
        db.commit()

        response = client.post(
            "/api/v1/auth/login",
            data={"username": "testuser", "password": "testpass123"},
        )

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"

    def test_login_invalid_credentials(self, client: TestClient):
        """测试登录失败 - 无效凭据"""
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "nonexistent", "password": "wrongpass"},
        )

        assert response.status_code == 401

    def test_register_success(self, client: TestClient):
        """测试注册成功"""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "newpass123",
            "full_name": "New User",
        }

        response = client.post("/api/v1/auth/register", json=user_data)

        assert response.status_code == 201
        data = response.json()
        assert data["username"] == "newuser"
        assert data["email"] == "<EMAIL>"

    def test_register_duplicate_username(self, client: TestClient, db: Session):
        """测试注册失败 - 用户名重复"""
        # 先创建一个用户
        user = User(
            username="existinguser",
            email="<EMAIL>",
            hashed_password=get_password_hash("pass123"),
        )
        db.add(user)
        db.commit()

        # 尝试注册相同用户名
        user_data = {
            "username": "existinguser",
            "email": "<EMAIL>",
            "password": "newpass123",
        }

        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 400


class TestProjectsAPI:
    """项目API测试"""

    @pytest.fixture
    def client(self):
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self, db: Session):
        """认证头部"""
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password=get_password_hash("testpass123"),
            is_active=True,
        )
        db.add(user)
        db.commit()
        db.refresh(user)

        token = create_access_token(data={"sub": user.username})
        return {"Authorization": f"Bearer {token}"}

    def test_create_project(self, client: TestClient, auth_headers: dict):
        """测试创建项目"""
        project_data = {
            "name": "Test Project",
            "description": "A test project for API testing",
        }

        response = client.post(
            "/api/v1/projects/", json=project_data, headers=auth_headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Project"
        assert data["description"] == "A test project for API testing"

    def test_get_projects(self, client: TestClient, auth_headers: dict, db: Session):
        """测试获取项目列表"""
        # 创建测试项目
        project = Project(
            name="Test Project", description="Test description", owner_id=1
        )
        db.add(project)
        db.commit()

        response = client.get("/api/v1/projects/", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_get_project_by_id(
        self, client: TestClient, auth_headers: dict, db: Session
    ):
        """测试根据ID获取项目"""
        project = Project(
            name="Test Project", description="Test description", owner_id=1
        )
        db.add(project)
        db.commit()
        db.refresh(project)

        response = client.get(f"/api/v1/projects/{project.id}", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Test Project"

    def test_update_project(self, client: TestClient, auth_headers: dict, db: Session):
        """测试更新项目"""
        project = Project(
            name="Original Project", description="Original description", owner_id=1
        )
        db.add(project)
        db.commit()
        db.refresh(project)

        update_data = {"name": "Updated Project", "description": "Updated description"}

        response = client.put(
            f"/api/v1/projects/{project.id}", json=update_data, headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Project"

    def test_delete_project(self, client: TestClient, auth_headers: dict, db: Session):
        """测试删除项目"""
        project = Project(
            name="Project to Delete",
            description="This project will be deleted",
            owner_id=1,
        )
        db.add(project)
        db.commit()
        db.refresh(project)

        response = client.delete(f"/api/v1/projects/{project.id}", headers=auth_headers)

        assert response.status_code == 204


class TestContentAPI:
    """内容API测试"""

    @pytest.fixture
    def client(self):
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self, db: Session):
        """认证头部"""
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password=get_password_hash("testpass123"),
            is_active=True,
        )
        db.add(user)
        db.commit()
        db.refresh(user)

        token = create_access_token(data={"sub": user.username})
        return {"Authorization": f"Bearer {token}"}

    def test_create_content(self, client: TestClient, auth_headers: dict):
        """测试创建内容"""
        content_data = {
            "title": "Test Content",
            "content_type": "text",
            "original_text": "This is test content for API testing",
        }

        response = client.post(
            "/api/v1/content/", json=content_data, headers=auth_headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "Test Content"
        assert data["content_type"] == "text"

    def test_get_content_list(
        self, client: TestClient, auth_headers: dict, db: Session
    ):
        """测试获取内容列表"""
        content = Content(
            title="Test Content",
            content_type="text",
            original_text="Test content",
            creator_id=1,
        )
        db.add(content)
        db.commit()

        response = client.get("/api/v1/content/", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_content_compliance_check(self, client: TestClient, auth_headers: dict):
        """测试内容合规检查"""
        content_data = {"text": "This is a test content for compliance checking"}

        response = client.post(
            "/api/v1/content/compliance-check", json=content_data, headers=auth_headers
        )

        # 根据实际实现调整状态码
        assert response.status_code in [200, 202]


class TestSystemAPI:
    """系统API测试"""

    @pytest.fixture
    def client(self):
        return TestClient(app)

    def test_system_health(self, client: TestClient):
        """测试系统健康检查"""
        response = client.get("/api/v1/system/health")

        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "timestamp" in data

    def test_system_metrics(self, client: TestClient):
        """测试系统指标"""
        response = client.get("/api/v1/system/metrics")

        # 根据实际实现调整状态码
        assert response.status_code in [200, 404]

    def test_system_info(self, client: TestClient):
        """测试系统信息"""
        response = client.get("/api/v1/system/info")

        # 根据实际实现调整状态码
        assert response.status_code in [200, 404]
