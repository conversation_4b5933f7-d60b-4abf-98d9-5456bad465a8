"""
真实的语音识别服务集成 - 基于librosa和transformers
集成开源项目：Whisper模型用于语音识别
"""

import asyncio
import os
import subprocess
import tempfile
from datetime import datetime
from typing import Any, Dict, List

import librosa
import numpy as np
import soundfile as sf
import torch
from transformers import pipeline


class RealSpeechRecognitionService:
    """真实的语音识别服务 - 基于Whisper和librosa"""

    def __init__(self):
        """初始化语音识别服务"""
        self.model = None
        self.processor = None
        self.pipe = None
        self.model_loaded = False

        self.supported_formats = [
            ".wav",
            ".mp3",
            ".flac",
            ".m4a",
            ".ogg",
            ".wma",
            ".aac",
        ]
        self.supported_languages = [
            "zh",
            "en",
            "ja",
            "ko",
            "fr",
            "de",
            "es",
            "auto",
        ]

    async def initialize_models(self, model_name: str = "openai/whisper-base"):
        """
        初始化Whisper模型

        Args:
            model_name: 模型名称，默认使用whisper-base
        """
        try:
            print(f"正在加载语音识别模型: {model_name}")

            # 方法1: 使用transformers pipeline (推荐)
            use_cuda = torch.cuda.is_available()
            dtype = torch.float16 if use_cuda else torch.float32
            device = 0 if use_cuda else -1

            self.pipe = pipeline(
                "automatic-speech-recognition",
                model=model_name,
                torch_dtype=dtype,
                device=device,
            )

            self.model_loaded = True
            print(f"✅ 语音识别模型加载成功: {model_name}")

            return {
                "success": True,
                "model": model_name,
                "device": "cuda" if torch.cuda.is_available() else "cpu",
                "message": "模型初始化完成",
            }

        except Exception as e:
            print(f"❌ 模型加载失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "模型初始化失败",
            }

    async def preprocess_audio(
        self, input_path: str, target_sr: int = 16000, normalize: bool = True
    ) -> Dict[str, Any]:
        """
        音频预处理

        Args:
            input_path: 输入音频文件路径
            target_sr: 目标采样率
            normalize: 是否标准化音量

        Returns:
            Dict[str, Any]: 预处理结果
        """
        try:
            # 使用librosa加载音频
            audio, sr = librosa.load(input_path, sr=target_sr)

            # 音频增强处理
            if normalize:
                # 音量标准化
                audio = librosa.util.normalize(audio)

            # 降噪处理 (简单的)
            audio = self._simple_denoise(audio)

            # 创建临时处理文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                output_path = temp_file.name
                sf.write(output_path, audio, target_sr)

            return {
                "success": True,
                "processed_path": output_path,
                "original_sr": sr,
                "target_sr": target_sr,
                "duration": len(audio) / target_sr,
                "audio_shape": audio.shape,
                "message": "音频预处理完成",
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "音频预处理失败",
            }

    def _simple_denoise(
        self, audio: np.ndarray, noise_factor: float = 0.1
    ) -> np.ndarray:
        """
        简单的降噪处理

        Args:
            audio: 音频数据
            noise_factor: 噪声衰减因子

        Returns:
            np.ndarray: 降噪后的音频
        """
        # 计算音频的RMS能量
        rms = librosa.feature.rms(y=audio)[0]

        # 简单的噪声门限制
        threshold = np.percentile(rms, 20)  # 取20%分位数作为噪声阈值

        # 对低于阈值的部分应用衰减
        for i, frame_rms in enumerate(rms):
            if frame_rms < threshold:
                start_sample = i * 512  # hop_length默认512
                end_sample = min((i + 1) * 512, len(audio))
                audio[start_sample:end_sample] *= noise_factor

        return audio

    async def transcribe_audio(
        self,
        audio_path: str,
        language: str = "auto",
        return_timestamps: bool = True,
    ) -> Dict[str, Any]:
        """
        语音转文字

        Args:
            audio_path: 音频文件路径
            language: 语言代码 (zh/en/auto等)
            return_timestamps: 是否返回时间戳

        Returns:
            Dict[str, Any]: 识别结果
        """
        try:
            if not self.model_loaded:
                # 如果模型未加载，先初始化
                init_result = await self.initialize_models()
                if not init_result["success"]:
                    return {
                        "success": False,
                        "error": "模型未初始化",
                        "message": "请先初始化语音识别模型",
                    }

            # 音频预处理
            preprocess_result = await self.preprocess_audio(audio_path)
            if not preprocess_result["success"]:
                return {
                    "success": False,
                    "error": preprocess_result["error"],
                    "message": "音频预处理失败",
                }

            processed_audio_path = preprocess_result["processed_path"]

            # 使用Whisper进行识别
            if language == "auto":
                # 自动检测语言
                result = self.pipe(
                    processed_audio_path, return_timestamps=return_timestamps
                )
            else:
                # 指定语言
                result = self.pipe(
                    processed_audio_path,
                    return_timestamps=return_timestamps,
                    generate_kwargs={"language": language},
                )

            # 清理临时文件
            try:
                os.unlink(processed_audio_path)
            except OSError:
                pass

            # 处理结果
            text = result.get("text", "")
            chunks = result.get("chunks", [])

            # 计算置信度 (Whisper原生不提供，这里用简单估算)
            confidence = self._estimate_confidence(text, chunks)

            return {
                "success": True,
                "text": text.strip(),
                "language": language,
                "confidence": confidence,
                "duration": preprocess_result["duration"],
                "chunks": chunks if return_timestamps else [],
                "word_count": len(text.split()),
                "char_count": len(text),
                "model_used": "whisper-base",
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "语音识别失败",
            }

    def _estimate_confidence(self, text: str, chunks: List[Dict]) -> float:
        """
        估算识别置信度

        Args:
            text: 识别的文本
            chunks: 时间戳块

        Returns:
            float: 置信度 (0-1)
        """
        if not text.strip():
            return 0.0

        # 基于文本长度和完整性的简单估算
        base_confidence = 0.8

        # 长度因子
        length_factor = min(len(text) / 100, 1.0) * 0.1

        # 标点符号因子
        punctuation_count = sum(1 for c in text if c in "。！？.,!?")
        punctuation_factor = min(punctuation_count / 10, 1.0) * 0.1

        confidence = base_confidence + length_factor + punctuation_factor
        return min(confidence, 1.0)

    async def batch_transcribe(
        self, audio_files: List[str], language: str = "auto"
    ) -> Dict[str, Any]:
        """
        批量语音识别

        Args:
            audio_files: 音频文件路径列表
            language: 语言代码

        Returns:
            Dict[str, Any]: 批量识别结果
        """
        results = []
        successful = 0
        failed = 0

        for i, audio_file in enumerate(audio_files):
            print(f"正在处理第{i+1}/{len(audio_files)}个文件: {audio_file}")

            result = await self.transcribe_audio(audio_file, language)
            results.append({"file_path": audio_file, "index": i, **result})

            if result["success"]:
                successful += 1
            else:
                failed += 1

        success_rate = successful / len(audio_files) if audio_files else 0

        return {
            "success": True,
            "results": results,
            "total_files": len(audio_files),
            "successful": successful,
            "failed": failed,
            "success_rate": success_rate,
            "timestamp": datetime.now().isoformat(),
        }

    async def extract_audio_from_video(
        self, video_path: str, output_format: str = "wav"
    ) -> Dict[str, Any]:
        """
        从视频中提取音频

        Args:
            video_path: 视频文件路径
            output_format: 输出音频格式

        Returns:
            Dict[str, Any]: 提取结果
        """
        try:
            # 创建临时音频文件
            suffix = f".{output_format}"
            with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as temp_file:
                audio_path = temp_file.name

            # 使用FFmpeg提取音频
            cmd = [
                "ffmpeg",
                "-i",
                video_path,
                "-vn",  # 不要视频
                "-acodec",
                "pcm_s16le",  # 音频编码
                "-ar",
                "16000",  # 采样率
                "-ac",
                "1",  # 单声道
                "-y",  # 覆盖输出文件
                audio_path,
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                return {
                    "success": True,
                    "audio_path": audio_path,
                    "video_path": video_path,
                    "format": output_format,
                    "message": "音频提取成功",
                }
            else:
                return {
                    "success": False,
                    "error": result.stderr,
                    "message": "音频提取失败",
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "音频提取异常",
            }

    async def check_system_requirements(self) -> Dict[str, Any]:
        """检查系统依赖和要求"""
        requirements = {}

        # 检查Python包
        packages = {
            "librosa": "音频处理",
            "soundfile": "音频文件读写",
            "transformers": "AI模型",
            "torch": "深度学习框架",
            "numpy": "数值计算",
        }

        for package, description in packages.items():
            try:
                __import__(package)
                requirements[package] = {
                    "available": True,
                    "description": description,
                }
            except ImportError:
                requirements[package] = {
                    "available": False,
                    "description": description,
                    "error": f"Package {package} not installed",
                }

        # 检查FFmpeg
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"],
                capture_output=True,
                text=True,
                timeout=10,
            )
            if result.returncode == 0:
                version = result.stdout.split("\n")[0]
            else:
                version = None

            requirements["ffmpeg"] = {
                "available": result.returncode == 0,
                "description": "音视频处理工具",
                "version": version,
            }
        except Exception:
            requirements["ffmpeg"] = {
                "available": False,
                "description": "音视频处理工具",
                "error": "FFmpeg not found",
            }

        # 检查GPU可用性
        torch_available = "torch" in globals()
        if torch_available:
            cuda_available = torch.cuda.is_available()
            device_count = torch.cuda.device_count() if cuda_available else 0
        else:
            cuda_available = False
            device_count = 0

        requirements["gpu"] = {
            "available": cuda_available,
            "description": "GPU加速",
            "device_count": device_count,
        }

        all_available = all(
            req.get("available", False) for req in requirements.values()
        )

        return {
            "requirements": requirements,
            "all_available": all_available,
            "timestamp": datetime.now().isoformat(),
        }


# 示例使用
async def test_speech_recognition():
    """测试语音识别服务"""
    service = RealSpeechRecognitionService()

    # 检查系统要求
    requirements = await service.check_system_requirements()
    print("系统依赖检查:", requirements)

    # 初始化模型
    init_result = await service.initialize_models()
    print("模型初始化:", init_result)

    if init_result["success"]:
        # 测试音频文件识别 (需要实际音频文件)
        # result = await service.transcribe_audio("test_audio.wav")
        # print("识别结果:", result)
        pass


if __name__ == "__main__":
    asyncio.run(test_speech_recognition())
