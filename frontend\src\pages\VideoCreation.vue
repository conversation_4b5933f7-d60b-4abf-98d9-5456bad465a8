<template>
  <div class="video-creation-page">
    <!-- 三列布局主体 -->
    <div class="video-workspace">
      <!-- 左侧面板 - 素材库和工具 -->
      <VideoAssetPanel />
      <!-- 中间面板 - 预览区域 -->
      <VideoPreviewPanel />
      <!-- 右侧面板 - 属性和效果 -->
      <VideoPropertyPanel />
    </div>
  </div>
</template>

<script setup lang="ts">
import VideoAssetPanel from '../components/video/VideoAssetPanel.vue'
import VideoPreviewPanel from '../components/video/VideoPreviewPanel.vue'
import VideoPropertyPanel from '../components/video/VideoPropertyPanel.vue'
// 预留composable集成点，如 useFfmpeg/useWorker
</script>

<style scoped>
.video-creation-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-workspace {
  flex: 1;
  display: flex;
  overflow: hidden;
  min-height: 600px;
}
</style>