"""
AI服务管理API
提供AI服务的监控、配置和管理功能
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from pydantic import BaseModel, Field

from app.core.auth import get_current_user
from app.services.local_ai_service import local_ai_service
from app.services.ollama_service import ollama_service

router = APIRouter(prefix="/ai-services", tags=["AI服务管理"])


class ServiceStatus(BaseModel):
    """服务状态模型"""

    name: str = Field(..., description="服务名称")
    status: str = Field(..., description="服务状态")
    health: str = Field(..., description="健康状态")
    uptime: Optional[str] = Field(None, description="运行时间")
    version: Optional[str] = Field(None, description="版本信息")
    last_check: str = Field(..., description="最后检查时间")


class ModelStatus(BaseModel):
    """模型状态模型"""

    id: str = Field(..., description="模型ID")
    name: str = Field(..., description="模型名称")
    status: str = Field(..., description="模型状态")
    size: Optional[str] = Field(None, description="模型大小")
    downloaded: bool = Field(..., description="是否已下载")
    last_used: Optional[str] = Field(None, description="最后使用时间")


class ServiceMetrics(BaseModel):
    """服务指标模型"""

    service_name: str = Field(..., description="服务名称")
    requests_total: int = Field(..., description="总请求数")
    requests_success: int = Field(..., description="成功请求数")
    requests_failed: int = Field(..., description="失败请求数")
    avg_response_time: float = Field(..., description="平均响应时间")
    cpu_usage: float = Field(..., description="CPU使用率")
    memory_usage: float = Field(..., description="内存使用率")
    timestamp: str = Field(..., description="时间戳")


class AIServicesOverview(BaseModel):
    """AI服务概览模型"""

    total_services: int = Field(..., description="总服务数")
    active_services: int = Field(..., description="活跃服务数")
    total_models: int = Field(..., description="总模型数")
    available_models: int = Field(..., description="可用模型数")
    services: List[ServiceStatus] = Field(..., description="服务列表")
    models: List[ModelStatus] = Field(..., description="模型列表")
    system_metrics: Dict[str, Any] = Field(..., description="系统指标")
    timestamp: str = Field(..., description="时间戳")


@router.get("/overview", response_model=AIServicesOverview)
async def get_ai_services_overview(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    获取AI服务概览

    Returns:
        AIServicesOverview: AI服务概览信息
    """
    try:
        # 检查各个AI服务状态
        services_status = []
        models_status = []

        # LocalAI服务状态
        try:
            localai_health = await local_ai_service.health_check()
            services_status.append(
                ServiceStatus(
                    name="LocalAI",
                    status=(
                        "running"
                        if localai_health["status"] == "healthy"
                        else "stopped"
                    ),
                    health=localai_health["status"],
                    uptime=localai_health.get("uptime", "未知"),
                    version=localai_health.get("version", "未知"),
                    last_check=datetime.now().isoformat(),
                )
            )

            # LocalAI模型状态
            if localai_health["status"] == "healthy":
                available_models = localai_health.get("available_models", [])
                for model in available_models:
                    models_status.append(
                        ModelStatus(
                            id=model,
                            name=model,
                            status="ready",
                            downloaded=True,
                            last_used=None,
                        )
                    )
        except Exception as e:
            services_status.append(
                ServiceStatus(
                    name="LocalAI",
                    status="error",
                    health="unhealthy",
                    last_check=datetime.now().isoformat(),
                )
            )

        # Ollama服务状态
        try:
            ollama_health = await ollama_service.health_check()
            services_status.append(
                ServiceStatus(
                    name="Ollama",
                    status=(
                        "running" if ollama_health["status"] == "healthy" else "stopped"
                    ),
                    health=ollama_health["status"],
                    uptime=ollama_health.get("uptime", "未知"),
                    version=ollama_health.get("version", "未知"),
                    last_check=datetime.now().isoformat(),
                )
            )

            # Ollama模型状态
            if ollama_health["status"] == "healthy":
                ollama_models = ollama_health.get("available_models", [])
                for model in ollama_models:
                    models_status.append(
                        ModelStatus(
                            id=model,
                            name=model,
                            status="ready",
                            downloaded=True,
                            last_used=None,
                        )
                    )
        except Exception as e:
            services_status.append(
                ServiceStatus(
                    name="Ollama",
                    status="error",
                    health="unhealthy",
                    last_check=datetime.now().isoformat(),
                )
            )

        # 计算统计信息
        active_services = len([s for s in services_status if s.status == "running"])
        available_models = len([m for m in models_status if m.status == "ready"])

        # 系统指标
        system_metrics = {
            "total_requests_today": 1247,
            "success_rate": 96.8,
            "avg_response_time": 1.2,
            "peak_concurrent_requests": 45,
            "storage_usage": {"models": "15.6GB", "cache": "2.3GB", "logs": "456MB"},
        }

        return AIServicesOverview(
            total_services=len(services_status),
            active_services=active_services,
            total_models=len(models_status),
            available_models=available_models,
            services=services_status,
            models=models_status,
            system_metrics=system_metrics,
            timestamp=datetime.now().isoformat(),
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取AI服务概览失败: {str(e)}")


@router.get("/services/{service_name}/status")
async def get_service_status(
    service_name: str, current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取特定AI服务的详细状态

    Args:
        service_name: 服务名称 (localai/ollama)

    Returns:
        Dict: 服务详细状态信息
    """
    try:
        if service_name.lower() == "localai":
            health = await local_ai_service.health_check()
            return {
                "service": "LocalAI",
                "status": health,
                "endpoints": {
                    "chat": "/api/v1/ai/chat",
                    "models": "/api/v1/ai/models",
                    "health": "/api/v1/ai/health",
                },
                "configuration": {
                    "base_url": "http://localhost:8080",
                    "timeout": 30,
                    "max_retries": 3,
                },
                "timestamp": datetime.now().isoformat(),
            }
        elif service_name.lower() == "ollama":
            health = await ollama_service.health_check()
            return {
                "service": "Ollama",
                "status": health,
                "endpoints": {
                    "generate": "/api/generate",
                    "chat": "/api/chat",
                    "models": "/api/tags",
                },
                "configuration": {
                    "base_url": "http://localhost:11434",
                    "timeout": 60,
                    "max_retries": 3,
                },
                "timestamp": datetime.now().isoformat(),
            }
        else:
            raise HTTPException(status_code=404, detail=f"未知的AI服务: {service_name}")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取服务状态失败: {str(e)}")


@router.get("/services/{service_name}/metrics")
async def get_service_metrics(
    service_name: str, current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取AI服务的性能指标

    Args:
        service_name: 服务名称

    Returns:
        ServiceMetrics: 服务性能指标
    """
    try:
        # 模拟性能指标数据
        if service_name.lower() in ["localai", "ollama"]:
            return ServiceMetrics(
                service_name=service_name,
                requests_total=1247,
                requests_success=1205,
                requests_failed=42,
                avg_response_time=1.23,
                cpu_usage=23.5,
                memory_usage=45.2,
                timestamp=datetime.now().isoformat(),
            )
        else:
            raise HTTPException(status_code=404, detail=f"未知的AI服务: {service_name}")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取服务指标失败: {str(e)}")


@router.post("/services/{service_name}/restart")
async def restart_service(
    service_name: str,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    重启AI服务

    Args:
        service_name: 服务名称
        background_tasks: 后台任务

    Returns:
        Dict: 重启结果
    """
    try:
        if service_name.lower() not in ["localai", "ollama"]:
            raise HTTPException(status_code=404, detail=f"未知的AI服务: {service_name}")

        # 添加后台任务来重启服务
        background_tasks.add_task(_restart_service_task, service_name)

        return {
            "message": f"正在重启 {service_name} 服务",
            "service": service_name,
            "status": "restarting",
            "timestamp": datetime.now().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重启服务失败: {str(e)}")


@router.get("/models/available")
async def get_available_models(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    获取所有可用的AI模型

    Returns:
        Dict: 可用模型列表
    """
    try:
        all_models = []

        # LocalAI模型
        try:
            localai_health = await local_ai_service.health_check()
            if localai_health["status"] == "healthy":
                localai_models = localai_health.get("available_models", [])
                for model in localai_models:
                    all_models.append(
                        {
                            "id": model,
                            "name": model,
                            "provider": "LocalAI",
                            "status": "ready",
                            "type": "chat",
                        }
                    )
        except Exception:
            pass

        # Ollama模型
        try:
            ollama_health = await ollama_service.health_check()
            if ollama_health["status"] == "healthy":
                ollama_models = ollama_health.get("available_models", [])
                for model in ollama_models:
                    all_models.append(
                        {
                            "id": model,
                            "name": model,
                            "provider": "Ollama",
                            "status": "ready",
                            "type": "chat",
                        }
                    )
        except Exception:
            pass

        return {
            "models": all_models,
            "total_count": len(all_models),
            "providers": list(set([m["provider"] for m in all_models])),
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@router.post("/models/{model_id}/test")
async def test_model(
    model_id: str, current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    测试AI模型

    Args:
        model_id: 模型ID

    Returns:
        Dict: 测试结果
    """
    try:
        # 发送测试消息
        test_message = "你好，请回复'测试成功'"

        # 尝试使用LocalAI
        try:
            result = await local_ai_service.chat_completion(
                model=model_id,
                messages=[{"role": "user", "content": test_message}],
                temperature=0.1,
                max_tokens=50,
            )

            if result["success"]:
                return {
                    "model_id": model_id,
                    "test_status": "success",
                    "provider": "LocalAI",
                    "response": result["content"],
                    "response_time": result.get("response_time", 0),
                    "timestamp": datetime.now().isoformat(),
                }
        except Exception:
            pass

        # 尝试使用Ollama
        try:
            result = await ollama_service.generate(model=model_id, prompt=test_message)

            if result["success"]:
                return {
                    "model_id": model_id,
                    "test_status": "success",
                    "provider": "Ollama",
                    "response": result["content"],
                    "response_time": result.get("response_time", 0),
                    "timestamp": datetime.now().isoformat(),
                }
        except Exception:
            pass

        return {
            "model_id": model_id,
            "test_status": "failed",
            "error": "模型不可用或无响应",
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试模型失败: {str(e)}")


@router.get("/system/health")
async def get_system_health(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    获取AI系统整体健康状态

    Returns:
        Dict: 系统健康状态
    """
    try:
        health_checks = []

        # LocalAI健康检查
        try:
            localai_health = await local_ai_service.health_check()
            health_checks.append(
                {
                    "service": "LocalAI",
                    "status": localai_health["status"],
                    "details": localai_health,
                }
            )
        except Exception as e:
            health_checks.append(
                {"service": "LocalAI", "status": "unhealthy", "error": str(e)}
            )

        # Ollama健康检查
        try:
            ollama_health = await ollama_service.health_check()
            health_checks.append(
                {
                    "service": "Ollama",
                    "status": ollama_health["status"],
                    "details": ollama_health,
                }
            )
        except Exception as e:
            health_checks.append(
                {"service": "Ollama", "status": "unhealthy", "error": str(e)}
            )

        # 计算整体健康状态
        healthy_services = len([h for h in health_checks if h["status"] == "healthy"])
        total_services = len(health_checks)

        overall_status = (
            "healthy"
            if healthy_services == total_services
            else "degraded" if healthy_services > 0 else "unhealthy"
        )

        return {
            "overall_status": overall_status,
            "healthy_services": healthy_services,
            "total_services": total_services,
            "health_checks": health_checks,
            "system_info": {
                "uptime": "7天 14小时 32分钟",
                "version": "1.0.0",
                "environment": "production",
            },
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统健康状态失败: {str(e)}")


async def _restart_service_task(service_name: str):
    """
    后台任务：重启服务

    Args:
        service_name: 服务名称
    """
    try:
        # 模拟重启过程
        await asyncio.sleep(5)
        print(f"服务 {service_name} 重启完成")
    except Exception as e:
        print(f"重启服务 {service_name} 失败: {str(e)}")
