"""文件上传安全验证中间件
提供文件类型、大小、内容安全检查
"""

import os
import magic
import hashlib
import tempfile
from typing import List, Dict, Optional, Set
from fastapi import UploadFile, HTTPException, status
from pathlib import Path
import logging
from PIL import Image
import cv2
import numpy as np

logger = logging.getLogger(__name__)


class FileSecurityValidator:
    """文件安全验证器"""
    
    def __init__(self):
        """
        初始化文件安全验证器
        """
        # 允许的文件类型配置
        self.allowed_mime_types = {
            'image': {
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp',
                'image/bmp'
            },
            'video': {
                'video/mp4',
                'video/avi',
                'video/mov',
                'video/wmv',
                'video/flv',
                'video/webm',
                'video/mkv'
            },
            'audio': {
                'audio/mp3',
                'audio/wav',
                'audio/aac',
                'audio/ogg',
                'audio/flac'
            },
            'document': {
                'application/pdf',
                'text/plain',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            }
        }
        
        # 文件扩展名映射
        self.extension_mapping = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp',
            '.mp4': 'video/mp4',
            '.avi': 'video/avi',
            '.mov': 'video/mov',
            '.wmv': 'video/wmv',
            '.flv': 'video/flv',
            '.webm': 'video/webm',
            '.mkv': 'video/mkv',
            '.mp3': 'audio/mp3',
            '.wav': 'audio/wav',
            '.aac': 'audio/aac',
            '.ogg': 'audio/ogg',
            '.flac': 'audio/flac',
            '.pdf': 'application/pdf',
            '.txt': 'text/plain',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
        
        # 文件大小限制（字节）
        self.size_limits = {
            'image': 10 * 1024 * 1024,      # 10MB
            'video': 500 * 1024 * 1024,     # 500MB
            'audio': 50 * 1024 * 1024,      # 50MB
            'document': 20 * 1024 * 1024    # 20MB
        }
        
        # 危险文件扩展名黑名单
        self.dangerous_extensions = {
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
            '.jar', '.php', '.asp', '.aspx', '.jsp', '.py', '.pl', '.sh',
            '.ps1', '.msi', '.deb', '.rpm', '.dmg', '.pkg'
        }
        
        # 恶意文件签名（魔数）
        self.malicious_signatures = {
            b'\x4D\x5A',  # PE executable
            b'\x7F\x45\x4C\x46',  # ELF executable
            b'\xCA\xFE\xBA\xBE',  # Java class file
            b'\xFE\xED\xFA\xCE',  # Mach-O executable
        }
    
    async def validate_file(self, file: UploadFile, allowed_types: Optional[List[str]] = None) -> Dict[str, any]:
        """
        验证上传文件的安全性
        
        Args:
            file: 上传的文件对象
            allowed_types: 允许的文件类型列表，如果为None则允许所有配置的类型
            
        Returns:
            Dict[str, any]: 验证结果
            
        Raises:
            HTTPException: 文件验证失败时抛出异常
        """
        try:
            # 基础验证
            await self._validate_basic_info(file)
            
            # 文件名安全检查
            await self._validate_filename(file.filename)
            
            # 读取文件内容进行深度验证
            content = await file.read()
            await file.seek(0)  # 重置文件指针
            
            # 文件类型验证
            file_type, mime_type = await self._detect_file_type(content, file.filename)
            
            # 检查是否为允许的类型
            if allowed_types and file_type not in allowed_types:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File type '{file_type}' is not allowed. Allowed types: {allowed_types}"
                )
            
            # 文件大小验证
            await self._validate_file_size(len(content), file_type)
            
            # 恶意内容检测
            await self._scan_malicious_content(content)
            
            # 文件完整性验证
            await self._validate_file_integrity(content, file_type, file.filename)
            
            # 计算文件哈希
            file_hash = hashlib.sha256(content).hexdigest()
            
            return {
                'valid': True,
                'file_type': file_type,
                'mime_type': mime_type,
                'size': len(content),
                'hash': file_hash,
                'filename': file.filename
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"File validation error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="File validation failed due to internal error"
            )
    
    async def _validate_basic_info(self, file: UploadFile) -> None:
        """
        验证文件基础信息
        
        Args:
            file: 上传的文件对象
            
        Raises:
            HTTPException: 验证失败时抛出异常
        """
        if not file:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file provided"
            )
        
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Filename is required"
            )
        
        if file.size == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Empty file is not allowed"
            )
    
    async def _validate_filename(self, filename: str) -> None:
        """
        验证文件名安全性
        
        Args:
            filename: 文件名
            
        Raises:
            HTTPException: 验证失败时抛出异常
        """
        # 检查文件名长度
        if len(filename) > 255:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Filename is too long (max 255 characters)"
            )
        
        # 检查危险字符
        dangerous_chars = {'<', '>', ':', '"', '|', '?', '*', '\0'}
        if any(char in filename for char in dangerous_chars):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Filename contains dangerous characters"
            )
        
        # 检查路径遍历攻击
        if '..' in filename or filename.startswith('/') or '\\' in filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Filename contains path traversal patterns"
            )
        
        # 检查危险扩展名
        file_ext = Path(filename).suffix.lower()
        if file_ext in self.dangerous_extensions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File extension '{file_ext}' is not allowed"
            )
    
    async def _detect_file_type(self, content: bytes, filename: str) -> tuple[str, str]:
        """
        检测文件真实类型
        
        Args:
            content: 文件内容
            filename: 文件名
            
        Returns:
            tuple[str, str]: (文件类型, MIME类型)
            
        Raises:
            HTTPException: 检测失败时抛出异常
        """
        try:
            # 使用python-magic检测MIME类型
            mime_type = magic.from_buffer(content, mime=True)
            
            # 确定文件类型分类
            file_type = None
            for category, mime_types in self.allowed_mime_types.items():
                if mime_type in mime_types:
                    file_type = category
                    break
            
            if not file_type:
                # 尝试通过扩展名判断
                file_ext = Path(filename).suffix.lower()
                expected_mime = self.extension_mapping.get(file_ext)
                
                if expected_mime:
                    # 检查MIME类型是否匹配扩展名
                    if mime_type != expected_mime:
                        logger.warning(
                            f"MIME type mismatch: detected '{mime_type}', expected '{expected_mime}' for {filename}"
                        )
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="File type does not match file extension"
                        )
                
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unsupported file type: {mime_type}"
                )
            
            return file_type, mime_type
            
        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            logger.error(f"File type detection error: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unable to determine file type"
            )
    
    async def _validate_file_size(self, size: int, file_type: str) -> None:
        """
        验证文件大小
        
        Args:
            size: 文件大小（字节）
            file_type: 文件类型
            
        Raises:
            HTTPException: 验证失败时抛出异常
        """
        max_size = self.size_limits.get(file_type, 10 * 1024 * 1024)  # 默认10MB
        
        if size > max_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size ({size} bytes) exceeds maximum allowed size ({max_size} bytes) for {file_type} files"
            )
    
    async def _scan_malicious_content(self, content: bytes) -> None:
        """
        扫描恶意内容
        
        Args:
            content: 文件内容
            
        Raises:
            HTTPException: 发现恶意内容时抛出异常
        """
        # 检查恶意文件签名
        for signature in self.malicious_signatures:
            if content.startswith(signature):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="File contains malicious signature"
                )
        
        # 检查可疑字符串
        suspicious_strings = [
            b'<script',
            b'javascript:',
            b'vbscript:',
            b'onload=',
            b'onerror=',
            b'eval(',
            b'exec(',
            b'system(',
            b'shell_exec('
        ]
        
        content_lower = content.lower()
        for suspicious in suspicious_strings:
            if suspicious in content_lower:
                logger.warning(f"Suspicious content detected: {suspicious}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="File contains suspicious content"
                )
    
    async def _validate_file_integrity(self, content: bytes, file_type: str, filename: str) -> None:
        """
        验证文件完整性
        
        Args:
            content: 文件内容
            file_type: 文件类型
            filename: 文件名
            
        Raises:
            HTTPException: 验证失败时抛出异常
        """
        try:
            if file_type == 'image':
                await self._validate_image_integrity(content)
            elif file_type == 'video':
                await self._validate_video_integrity(content, filename)
            # 其他类型的完整性验证可以在这里添加
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"File integrity validation error: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File integrity validation failed"
            )
    
    async def _validate_image_integrity(self, content: bytes) -> None:
        """
        验证图片文件完整性
        
        Args:
            content: 图片文件内容
            
        Raises:
            HTTPException: 验证失败时抛出异常
        """
        try:
            # 使用PIL验证图片
            with tempfile.NamedTemporaryFile() as temp_file:
                temp_file.write(content)
                temp_file.flush()
                
                with Image.open(temp_file.name) as img:
                    # 验证图片可以正常打开
                    img.verify()
                    
                    # 检查图片尺寸是否合理
                    if img.size[0] > 10000 or img.size[1] > 10000:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Image dimensions are too large"
                        )
                        
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Image validation error: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or corrupted image file"
            )
    
    async def _validate_video_integrity(self, content: bytes, filename: str) -> None:
        """
        验证视频文件完整性
        
        Args:
            content: 视频文件内容
            filename: 文件名
            
        Raises:
            HTTPException: 验证失败时抛出异常
        """
        try:
            # 使用OpenCV验证视频
            with tempfile.NamedTemporaryFile(suffix=Path(filename).suffix) as temp_file:
                temp_file.write(content)
                temp_file.flush()
                
                cap = cv2.VideoCapture(temp_file.name)
                
                if not cap.isOpened():
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Invalid or corrupted video file"
                    )
                
                # 检查视频基本信息
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                cap.release()
                
                # 验证视频参数是否合理
                if frame_count <= 0 or fps <= 0:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Invalid video parameters"
                    )
                
                if width > 4096 or height > 4096:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Video resolution is too high"
                    )
                    
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Video validation error: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or corrupted video file"
            )


# 全局文件安全验证器实例
file_security_validator = FileSecurityValidator()


async def validate_upload_file(file: UploadFile, allowed_types: Optional[List[str]] = None) -> Dict[str, any]:
    """
    验证上传文件的便捷函数
    
    Args:
        file: 上传的文件对象
        allowed_types: 允许的文件类型列表
        
    Returns:
        Dict[str, any]: 验证结果
    """
    return await file_security_validator.validate_file(file, allowed_types)