import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  },
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/tests/setup.ts'],
    include: [
      'src/tests/api.test.ts',
      'tests/unit/**/*.test.ts',
      'tests/integration/**/*.test.ts'
    ],
    exclude: [
      'src/tests/performance.test.ts',
      'src/tests/integration.test.ts',
      'src/tests/compute.test.ts',
      'node_modules/**',
      'dist/**'
    ],
    // 增加内存限制和超时设置
    testTimeout: 10000,
    hookTimeout: 10000,
    teardownTimeout: 5000,
    // 禁用并发以减少内存使用
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true
      }
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage'
    }
  }
})