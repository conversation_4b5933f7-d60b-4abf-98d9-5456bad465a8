#!/usr/bin/env python3
"""
API版本管理演示端点
展示不同版本的API实现和兼容性处理
🔒 证据链: 基于2024年最佳实践的API版本控制策略
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Request, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from app.core.api_version_manager import (
    api_version_manager,
    version_required,
    deprecated_endpoint
)
from app.core.auth import get_current_user
from app.models.user import User

router = APIRouter(prefix="/version-demo", tags=["版本管理演示"])


# 响应模型定义
class UserResponseV1(BaseModel):
    """用户响应模型 - v1版本"""
    user_id: int
    user_name: str
    created_at: str
    is_active: bool


class UserResponseV2(BaseModel):
    """用户响应模型 - v2版本（驼峰命名）"""
    userId: int
    userName: str
    createdAt: str
    isActive: bool
    email: Optional[str] = None
    profile: Optional[Dict[str, Any]] = None


class UserResponseV3(BaseModel):
    """用户响应模型 - v3版本（增强字段）"""
    userId: int
    userName: str
    createdAt: str
    isActive: bool
    email: Optional[str] = None
    profile: Optional[Dict[str, Any]] = None
    permissions: List[str] = Field(default_factory=list)
    lastLoginAt: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class PaginationV1(BaseModel):
    """分页模型 - v1版本"""
    page: int
    limit: int
    total: int


class PaginationV2(BaseModel):
    """分页模型 - v2版本"""
    page: int
    limit: int
    total: int
    has_next: bool
    has_prev: bool


class PaginationV3(BaseModel):
    """分页模型 - v3版本（增强分页）"""
    number: int
    size: int
    total: int
    totalPages: int
    hasNext: bool
    hasPrevious: bool
    links: Dict[str, Optional[str]] = Field(default_factory=dict)


@router.get("/version-info")
async def get_version_info(request: Request):
    """获取当前请求的版本信息"""
    version = getattr(request.state, "api_version", "unknown")
    version_source = getattr(request.state, "api_version_source", "unknown")
    
    version_info = api_version_manager.get_version_info(version)
    warnings = api_version_manager.get_deprecation_warnings(version)
    
    return {
        "current_version": version,
        "version_source": version_source,
        "version_info": {
            "status": version_info.status.value if version_info else "unknown",
            "release_date": version_info.release_date.isoformat() if version_info else None,
            "description": version_info.description if version_info else None,
            "is_deprecated": version_info.is_deprecated if version_info else False,
            "days_until_sunset": version_info.days_until_sunset if version_info else None,
        },
        "warnings": warnings,
        "supported_versions": api_version_manager.get_supported_versions(),
        "latest_version": api_version_manager.get_latest_version(),
        "timestamp": datetime.now().isoformat(),
    }


@router.get("/users")
async def get_users(
    request: Request,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user)
):
    """获取用户列表 - 支持多版本响应格式"""
    version = getattr(request.state, "api_version", api_version_manager.default_version)
    
    # 模拟用户数据
    mock_users = [
        {
            "user_id": 1,
            "user_name": "张三",
            "created_at": "2024-01-15T10:30:00Z",
            "is_active": True,
            "email": "<EMAIL>",
            "profile": {"avatar": "https://example.com/avatar1.jpg", "bio": "开发者"},
            "permissions": ["read", "write"],
            "last_login_at": "2024-12-30T15:45:00Z",
            "metadata": {"source": "web", "verified": True}
        },
        {
            "user_id": 2,
            "user_name": "李四",
            "created_at": "2024-02-20T14:20:00Z",
            "is_active": True,
            "email": "<EMAIL>",
            "profile": {"avatar": "https://example.com/avatar2.jpg", "bio": "设计师"},
            "permissions": ["read"],
            "last_login_at": "2024-12-29T09:15:00Z",
            "metadata": {"source": "mobile", "verified": False}
        }
    ]
    
    total = len(mock_users)
    start = (page - 1) * limit
    end = start + limit
    users_page = mock_users[start:end]
    
    # 根据版本返回不同格式的响应
    if version == "v1":
        # v1版本：下划线命名，简单分页
        formatted_users = [
            UserResponseV1(
                user_id=user["user_id"],
                user_name=user["user_name"],
                created_at=user["created_at"],
                is_active=user["is_active"]
            ).dict()
            for user in users_page
        ]
        
        pagination = PaginationV1(
            page=page,
            limit=limit,
            total=total
        ).dict()
        
        return {
            "users": formatted_users,
            "pagination": pagination
        }
    
    elif version == "v2":
        # v2版本：驼峰命名，增强分页
        formatted_users = [
            UserResponseV2(
                userId=user["user_id"],
                userName=user["user_name"],
                createdAt=user["created_at"],
                isActive=user["is_active"],
                email=user["email"],
                profile=user["profile"]
            ).dict()
            for user in users_page
        ]
        
        pagination = PaginationV2(
            page=page,
            limit=limit,
            total=total,
            has_next=end < total,
            has_prev=page > 1
        ).dict()
        
        return {
            "users": formatted_users,
            "pagination": pagination
        }
    
    elif version == "v3":
        # v3版本：完整字段，高级分页
        formatted_users = [
            UserResponseV3(
                userId=user["user_id"],
                userName=user["user_name"],
                createdAt=user["created_at"],
                isActive=user["is_active"],
                email=user["email"],
                profile=user["profile"],
                permissions=user["permissions"],
                lastLoginAt=user["last_login_at"],
                metadata=user["metadata"]
            ).dict()
            for user in users_page
        ]
        
        total_pages = (total + limit - 1) // limit
        
        pagination = PaginationV3(
            number=page,
            size=limit,
            total=total,
            totalPages=total_pages,
            hasNext=page < total_pages,
            hasPrevious=page > 1,
            links={
                "first": f"/api/v3/version-demo/users?page=1&limit={limit}",
                "last": f"/api/v3/version-demo/users?page={total_pages}&limit={limit}",
                "next": f"/api/v3/version-demo/users?page={page + 1}&limit={limit}" if page < total_pages else None,
                "prev": f"/api/v3/version-demo/users?page={page - 1}&limit={limit}" if page > 1 else None,
            }
        ).dict()
        
        return {
            "data": formatted_users,
            "pagination": pagination,
            "meta": {
                "version": version,
                "timestamp": datetime.now().isoformat(),
                "request_id": f"req_{datetime.now().timestamp()}"
            }
        }
    
    else:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported API version: {version}"
        )


@router.get("/users/{user_id}")
@version_required(min_version="v2")
async def get_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """获取单个用户 - 需要v2或更高版本"""
    version = getattr(request.state, "api_version", api_version_manager.default_version)
    
    # 模拟用户数据
    mock_user = {
        "user_id": user_id,
        "user_name": f"用户{user_id}",
        "created_at": "2024-01-15T10:30:00Z",
        "is_active": True,
        "email": f"user{user_id}@example.com",
        "profile": {"avatar": f"https://example.com/avatar{user_id}.jpg"},
        "permissions": ["read", "write"],
        "last_login_at": "2024-12-30T15:45:00Z",
        "metadata": {"source": "web", "verified": True}
    }
    
    if version == "v2":
        return UserResponseV2(
            userId=mock_user["user_id"],
            userName=mock_user["user_name"],
            createdAt=mock_user["created_at"],
            isActive=mock_user["is_active"],
            email=mock_user["email"],
            profile=mock_user["profile"]
        ).dict()
    
    elif version == "v3":
        return UserResponseV3(
            userId=mock_user["user_id"],
            userName=mock_user["user_name"],
            createdAt=mock_user["created_at"],
            isActive=mock_user["is_active"],
            email=mock_user["email"],
            profile=mock_user["profile"],
            permissions=mock_user["permissions"],
            lastLoginAt=mock_user["last_login_at"],
            metadata=mock_user["metadata"]
        ).dict()


@router.post("/legacy-endpoint")
@deprecated_endpoint(
    sunset_date="2024-12-31",
    migration_guide="/docs/migration/legacy-to-v2"
)
async def legacy_endpoint(
    request: Request,
    data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """遗留端点示例 - 已弃用"""
    return {
        "message": "This endpoint is deprecated",
        "data": data,
        "migration_guide": "/docs/migration/legacy-to-v2",
        "sunset_date": "2024-12-31"
    }


@router.get("/version-statistics")
async def get_version_statistics():
    """获取版本统计信息"""
    return api_version_manager.get_version_statistics()


@router.post("/migrate-data")
async def migrate_data(
    request: Request,
    data: Dict[str, Any],
    from_version: str = Query(..., description="源版本"),
    to_version: str = Query(..., description="目标版本")
):
    """数据迁移演示"""
    try:
        migrated_data = api_version_manager.migrate_request_data(
            data, from_version, to_version
        )
        
        return {
            "success": True,
            "original_data": data,
            "migrated_data": migrated_data,
            "migration": {
                "from_version": from_version,
                "to_version": to_version,
                "timestamp": datetime.now().isoformat()
            }
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail={
                "error": "Migration failed",
                "message": str(e),
                "from_version": from_version,
                "to_version": to_version
            }
        )


@router.get("/compatibility-check")
async def check_compatibility(
    requested_version: str = Query(..., description="请求的版本"),
    endpoint_version: str = Query(..., description="端点支持的版本")
):
    """版本兼容性检查"""
    is_compatible = api_version_manager.check_version_compatibility(
        requested_version, endpoint_version
    )
    
    return {
        "compatible": is_compatible,
        "requested_version": requested_version,
        "endpoint_version": endpoint_version,
        "compatibility_matrix": api_version_manager.compatibility_matrix,
        "timestamp": datetime.now().isoformat()
    }