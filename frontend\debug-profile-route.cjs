/**
 * 调试个人中心路由跳转问题
 */

const puppeteer = require('puppeteer');

async function debugProfileRoute() {
  console.log('🔍 调试个人中心路由跳转...');
  
  const browser = await puppeteer.launch({
    headless: false, // 显示浏览器以便观察
    devtools: true,  // 打开开发者工具
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    console.log(`📝 [${type.toUpperCase()}] ${text}`);
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.error('❌ 页面错误:', error.message);
  });
  
  try {
    // 访问主页
    console.log('🌐 访问主页...');
    await page.goto('http://localhost:3001/', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('✅ 主页加载完成');
    
    // 检查当前路径
    let currentPath = await page.evaluate(() => window.location.pathname);
    console.log(`📍 当前路径: ${currentPath}`);
    
    // 点击个人中心按钮
    console.log('🖱️ 点击个人中心按钮...');
    
    const profileButton = await page.$('a[href="/profile"]');
    if (profileButton) {
      console.log('✅ 找到个人中心按钮');
      
      // 监听路由变化
      await page.evaluateOnNewDocument(() => {
        let originalPushState = history.pushState;
        let originalReplaceState = history.replaceState;
        
        history.pushState = function(state, title, url) {
          console.log('🔄 pushState:', url);
          return originalPushState.apply(history, arguments);
        };
        
        history.replaceState = function(state, title, url) {
          console.log('🔄 replaceState:', url);
          return originalReplaceState.apply(history, arguments);
        };
        
        window.addEventListener('popstate', (event) => {
          console.log('🔄 popstate:', window.location.pathname);
        });
      });
      
      // 点击按钮
      await profileButton.click();
      
      // 等待路由跳转
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 检查跳转后的路径
      currentPath = await page.evaluate(() => window.location.pathname);
      console.log(`📍 跳转后路径: ${currentPath}`);
      
      // 检查页面内容
      const pageTitle = await page.title();
      console.log(`📄 页面标题: ${pageTitle}`);
      
      // 检查是否在登录页面
      const isLoginPage = await page.$('.login-container') !== null;
      console.log(`🔐 是否在登录页面: ${isLoginPage}`);
      
      // 检查是否在个人中心页面
      const isProfilePage = await page.$('.profile-page') !== null;
      console.log(`👤 是否在个人中心页面: ${isProfilePage}`);
      
      // 检查URL参数
      const urlParams = await page.evaluate(() => {
        const params = new URLSearchParams(window.location.search);
        const result = {};
        for (let [key, value] of params) {
          result[key] = value;
        }
        return result;
      });
      console.log(`🔗 URL参数:`, urlParams);
      
      // 检查认证状态
      const authState = await page.evaluate(() => {
        return {
          localStorage: {
            access_token: localStorage.getItem('access_token'),
            user_info: localStorage.getItem('user_info'),
            refresh_token: localStorage.getItem('refresh_token')
          },
          sessionStorage: {
            temp_auth: sessionStorage.getItem('temp_auth')
          }
        };
      });
      console.log(`🔑 认证状态:`, authState);
      
      // 检查Vue Router状态
      const routerState = await page.evaluate(() => {
        return {
          currentRoute: window.$router ? window.$router.currentRoute.value : 'Router not found',
          hasRouter: typeof window.$router !== 'undefined'
        };
      });
      console.log(`🛣️ 路由状态:`, routerState);
      
    } else {
      console.log('❌ 未找到个人中心按钮');
    }
    
    // 保持浏览器打开以便手动检查
    console.log('🔍 浏览器将保持打开状态，请手动检查...');
    console.log('按 Ctrl+C 退出');
    
    // 等待用户手动关闭
    await new Promise(() => {});
    
  } catch (error) {
    console.error('💥 调试过程中出现错误:', error.message);
  } finally {
    // await browser.close();
  }
}

// 运行调试
debugProfileRoute().catch(console.error);
