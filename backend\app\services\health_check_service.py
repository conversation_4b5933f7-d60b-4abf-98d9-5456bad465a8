#!/usr/bin/env python3
"""
系统健康检查服务
提供系统状态监控和自动恢复机制
"""

import logging
import subprocess
import time
from datetime import datetime
from typing import Any, Dict, List

import psutil
from sqlalchemy import text
from sqlalchemy.orm import Session

from app.services.system_monitoring_service import SystemMonitoringService


class HealthStatus:
    """健康状态枚举"""

    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    DOWN = "down"


class HealthCheckService:
    """系统健康检查服务"""

    def __init__(self, db: Session = None):
        self.db = db
        self.logger = logging.getLogger(__name__)
        self.monitoring_service = SystemMonitoringService()

        # 健康检查阈值配置
        self.thresholds = {
            "cpu_usage_warning": 80.0,
            "cpu_usage_critical": 95.0,
            "memory_usage_warning": 85.0,
            "memory_usage_critical": 95.0,
            "disk_usage_warning": 80.0,
            "disk_usage_critical": 90.0,
            "response_time_warning": 5.0,  # 秒
            "response_time_critical": 10.0,  # 秒
        }

        # 历史检查结果
        self.check_history = []
        self.max_history_size = 100

    def perform_full_health_check(self) -> Dict[str, Any]:
        """执行完整的系统健康检查"""

        check_start_time = time.time()
        health_report = {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": HealthStatus.HEALTHY,
            "checks": {},
            "summary": {},
            "recommendations": [],
        }

        try:
            # 系统资源检查
            system_health = self._check_system_resources()
            health_report["checks"]["system_resources"] = system_health

            # 数据库连接检查
            if self.db:
                db_health = self._check_database_connection()
                health_report["checks"]["database"] = db_health

            # 服务可用性检查
            services_health = self._check_services_availability()
            health_report["checks"]["services"] = services_health

            # 磁盘空间检查
            disk_health = self._check_disk_space()
            health_report["checks"]["disk_space"] = disk_health

            # 网络连接检查
            network_health = self._check_network_connectivity()
            health_report["checks"]["network"] = network_health

            # 计算总体状态
            overall_status = self._calculate_overall_status(health_report["checks"])
            health_report["overall_status"] = overall_status

            # 生成摘要和建议
            health_report["summary"] = self._generate_summary(health_report["checks"])
            health_report["recommendations"] = self._generate_recommendations(
                health_report["checks"]
            )

            # 记录检查时间
            check_duration = time.time() - check_start_time
            health_report["check_duration_seconds"] = round(check_duration, 2)

            # 保存到历史记录
            self._save_to_history(health_report)

            return health_report

        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "overall_status": HealthStatus.CRITICAL,
                "error": f"健康检查失败: {str(e)}",
                "check_duration_seconds": time.time() - check_start_time,
            }

    def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源使用情况"""

        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()

            # 确定CPU状态
            if cpu_percent >= self.thresholds["cpu_usage_critical"]:
                cpu_status = HealthStatus.CRITICAL
            elif cpu_percent >= self.thresholds["cpu_usage_warning"]:
                cpu_status = HealthStatus.WARNING
            else:
                cpu_status = HealthStatus.HEALTHY

            # 确定内存状态
            if memory.percent >= self.thresholds["memory_usage_critical"]:
                memory_status = HealthStatus.CRITICAL
            elif memory.percent >= self.thresholds["memory_usage_warning"]:
                memory_status = HealthStatus.WARNING
            else:
                memory_status = HealthStatus.HEALTHY

            return {
                "status": max(
                    cpu_status,
                    memory_status,
                    key=lambda x: [
                        HealthStatus.HEALTHY,
                        HealthStatus.WARNING,
                        HealthStatus.CRITICAL,
                    ].index(x),
                ),
                "cpu": {
                    "usage_percent": cpu_percent,
                    "status": cpu_status,
                    "cores": psutil.cpu_count(),
                },
                "memory": {
                    "usage_percent": memory.percent,
                    "status": memory_status,
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                },
            }

        except Exception as e:
            self.logger.error(f"系统资源检查失败: {e}")
            return {"status": HealthStatus.CRITICAL, "error": str(e)}

    def _check_database_connection(self) -> Dict[str, Any]:
        """检查数据库连接"""

        try:
            start_time = time.time()

            # 执行简单查询测试连接
            result = self.db.execute(text("SELECT 1"))
            result.fetchone()

            response_time = time.time() - start_time

            # 确定状态
            if response_time >= self.thresholds["response_time_critical"]:
                status = HealthStatus.CRITICAL
            elif response_time >= self.thresholds["response_time_warning"]:
                status = HealthStatus.WARNING
            else:
                status = HealthStatus.HEALTHY

            return {
                "status": status,
                "response_time_seconds": round(response_time, 3),
                "connection_active": True,
            }

        except Exception as e:
            self.logger.error(f"数据库连接检查失败: {e}")
            return {
                "status": HealthStatus.CRITICAL,
                "connection_active": False,
                "error": str(e),
            }

    def _check_services_availability(self) -> Dict[str, Any]:
        """检查关键服务可用性"""

        services_to_check = [
            {"name": "ffmpeg", "command": ["ffmpeg", "-version"]},
            {"name": "python", "command": ["python", "--version"]},
        ]

        service_results = {}
        overall_status = HealthStatus.HEALTHY

        for service in services_to_check:
            try:
                start_time = time.time()
                result = subprocess.run(
                    service["command"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                )
                response_time = time.time() - start_time

                if result.returncode == 0:
                    status = HealthStatus.HEALTHY
                else:
                    status = HealthStatus.CRITICAL
                    overall_status = HealthStatus.CRITICAL

                service_results[service["name"]] = {
                    "status": status,
                    "available": result.returncode == 0,
                    "response_time_seconds": round(response_time, 3),
                }

            except subprocess.TimeoutExpired:
                service_results[service["name"]] = {
                    "status": HealthStatus.CRITICAL,
                    "available": False,
                    "error": "响应超时",
                }
                overall_status = HealthStatus.CRITICAL

            except Exception as e:
                service_results[service["name"]] = {
                    "status": HealthStatus.CRITICAL,
                    "available": False,
                    "error": str(e),
                }
                overall_status = HealthStatus.CRITICAL

        return {"status": overall_status, "services": service_results}

    def _check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""

        try:
            disk_usage = psutil.disk_usage("/")
            usage_percent = (disk_usage.used / disk_usage.total) * 100

            # 确定状态
            if usage_percent >= self.thresholds["disk_usage_critical"]:
                status = HealthStatus.CRITICAL
            elif usage_percent >= self.thresholds["disk_usage_warning"]:
                status = HealthStatus.WARNING
            else:
                status = HealthStatus.HEALTHY

            return {
                "status": status,
                "usage_percent": round(usage_percent, 2),
                "total_gb": round(disk_usage.total / (1024**3), 2),
                "used_gb": round(disk_usage.used / (1024**3), 2),
                "free_gb": round(disk_usage.free / (1024**3), 2),
            }

        except Exception as e:
            self.logger.error(f"磁盘空间检查失败: {e}")
            return {"status": HealthStatus.CRITICAL, "error": str(e)}

    def _check_network_connectivity(self) -> Dict[str, Any]:
        """检查网络连接"""

        test_hosts = [
            "*******",  # Google DNS
            "***************",  # 114 DNS
        ]

        connectivity_results = {}
        successful_pings = 0

        for host in test_hosts:
            try:
                start_time = time.time()
                result = subprocess.run(
                    ["ping", "-c", "1", "-W", "3", host],
                    capture_output=True,
                    timeout=5,
                )
                response_time = time.time() - start_time

                success = result.returncode == 0
                if success:
                    successful_pings += 1

                connectivity_results[host] = {
                    "reachable": success,
                    "response_time_seconds": round(response_time, 3),
                }

            except Exception as e:
                connectivity_results[host] = {
                    "reachable": False,
                    "error": str(e),
                }

        # 确定总体网络状态
        if successful_pings == len(test_hosts):
            status = HealthStatus.HEALTHY
        elif successful_pings > 0:
            status = HealthStatus.WARNING
        else:
            status = HealthStatus.CRITICAL

        return {
            "status": status,
            "hosts_tested": len(test_hosts),
            "successful_connections": successful_pings,
            "results": connectivity_results,
        }

    def _calculate_overall_status(self, checks: Dict[str, Any]) -> str:
        """计算总体健康状态"""

        statuses = []
        for check_name, check_result in checks.items():
            if isinstance(check_result, dict) and "status" in check_result:
                statuses.append(check_result["status"])

        # 如果有任何critical状态，整体为critical
        if HealthStatus.CRITICAL in statuses:
            return HealthStatus.CRITICAL
        # 如果有warning状态，整体为warning
        elif HealthStatus.WARNING in statuses:
            return HealthStatus.WARNING
        # 否则为healthy
        else:
            return HealthStatus.HEALTHY

    def _generate_summary(self, checks: Dict[str, Any]) -> Dict[str, Any]:
        """生成健康检查摘要"""

        summary = {
            "total_checks": len(checks),
            "healthy_checks": 0,
            "warning_checks": 0,
            "critical_checks": 0,
        }

        for check_result in checks.values():
            if isinstance(check_result, dict) and "status" in check_result:
                status = check_result["status"]
                if status == HealthStatus.HEALTHY:
                    summary["healthy_checks"] += 1
                elif status == HealthStatus.WARNING:
                    summary["warning_checks"] += 1
                elif status == HealthStatus.CRITICAL:
                    summary["critical_checks"] += 1

        return summary

    def _generate_recommendations(self, checks: Dict[str, Any]) -> List[str]:
        """生成优化建议"""

        recommendations = []

        # 系统资源建议
        if "system_resources" in checks:
            sys_check = checks["system_resources"]
            if sys_check.get("cpu", {}).get("status") == HealthStatus.CRITICAL:
                recommendations.append("CPU使用率过高，建议检查并优化高耗CPU进程")
            if sys_check.get("memory", {}).get("status") == HealthStatus.CRITICAL:
                recommendations.append("内存使用率过高，建议释放内存或增加RAM")

        # 磁盘空间建议
        if "disk_space" in checks:
            disk_check = checks["disk_space"]
            if disk_check.get("status") == HealthStatus.CRITICAL:
                recommendations.append("磁盘空间不足，建议清理临时文件或扩展存储")

        # 数据库建议
        if "database" in checks:
            db_check = checks["database"]
            if db_check.get("status") == HealthStatus.CRITICAL:
                recommendations.append("数据库连接异常，请检查数据库服务状态")

        # 网络建议
        if "network" in checks:
            net_check = checks["network"]
            if net_check.get("status") == HealthStatus.CRITICAL:
                recommendations.append("网络连接异常，请检查网络配置和防火墙设置")

        return recommendations

    def _save_to_history(self, health_report: Dict[str, Any]):
        """保存健康检查历史"""

        self.check_history.append(
            {
                "timestamp": health_report["timestamp"],
                "overall_status": health_report["overall_status"],
                "check_duration": health_report.get("check_duration_seconds", 0),
                "summary": health_report.get("summary", {}),
            }
        )

        # 限制历史记录大小
        if len(self.check_history) > self.max_history_size:
            self.check_history = self.check_history[-self.max_history_size :]

    def get_health_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取健康检查历史"""

        return self.check_history[-limit:] if self.check_history else []

    def get_health_trends(self) -> Dict[str, Any]:
        """分析健康趋势"""

        if not self.check_history:
            return {"trend": "no_data", "message": "暂无历史数据"}

        recent_checks = self.check_history[-10:]  # 最近10次检查

        # 计算状态分布
        status_counts = {}
        for check in recent_checks:
            status = check["overall_status"]
            status_counts[status] = status_counts.get(status, 0) + 1

        # 分析趋势
        if len(recent_checks) >= 3:
            last_three = [check["overall_status"] for check in recent_checks[-3:]]

            if all(status == HealthStatus.HEALTHY for status in last_three):
                trend = "improving"
            elif all(status == HealthStatus.CRITICAL for status in last_three):
                trend = "degrading"
            elif (
                last_three[0] == HealthStatus.CRITICAL
                and last_three[-1] == HealthStatus.HEALTHY
            ):
                trend = "recovering"
            else:
                trend = "stable"
        else:
            trend = "insufficient_data"

        return {
            "trend": trend,
            "recent_status_distribution": status_counts,
            "total_checks_analyzed": len(recent_checks),
        }
