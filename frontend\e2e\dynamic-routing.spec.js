/**
 * 动态路由测试 - 2025年最佳实践
 * 使用Playwright进行全面的路由和布局测试
 */

import { test, expect } from '@playwright/test';

// 测试配置
const BASE_URL = 'http://localhost:3000';
const ROUTES = [
  { path: '/', name: '首页', shouldHaveLayout: true, expectedTitle: '二创短视频分发系统' },
  { path: '/video-creation', name: '视频创作', shouldHaveLayout: true, expectedTitle: '智能视频生成 - 二创短视频分发系统' },
  { path: '/compute-test', name: '计算引擎测试', shouldHaveLayout: true, expectedTitle: '计算引擎测试 - 二创短视频分发系统' },
  { path: '/profile', name: '个人中心', shouldHaveLayout: true, expectedTitle: '个人中心 - 二创短视频分发系统' },
  { path: '/login', name: '登录页面', shouldHaveLayout: false, expectedTitle: '用户登录 - 二创短视频分发系统' }
];

// 布局元素选择器
const LAYOUT_SELECTORS = {
  header: '.app-header',
  sidebar: '.app-sidebar',
  main: '.app-main',
  brand: '.app-header__brand',
  nav: '.app-header__nav',
  sidebarNav: '.app-sidebar__nav'
};

// 登录页面元素选择器
const LOGIN_SELECTORS = {
  container: '.login-container',
  userTypeSelector: '.user-type-selector',
  form: '.login-form'
};

test.describe('动态路由测试', () => {
  test.beforeEach(async ({ page }) => {
    // 设置更长的超时时间以适应Vue应用加载
    test.setTimeout(30000);

    // 监听控制台错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`Console error: ${msg.text()}`);
      }
    });

    // 监听页面错误
    page.on('pageerror', error => {
      console.log(`Page error: ${error.message}`);
    });

    // 设置完善的测试环境
    await page.addInitScript(() => {
      // 设置测试环境标识
      window.localStorage.setItem('test_mode', 'true');
      window.localStorage.setItem('test_user_type', 'admin');

      // 设置完整的认证信息
      const testUser = {
        id: 'test_admin_001',
        username: 'test_admin',
        email: '<EMAIL>',
        role: 'admin',
        permissions: ['*'],
        avatar: '/test-avatars/admin.png',
        createdAt: new Date('2025-01-01').toISOString(),
        lastLoginAt: new Date().toISOString()
      };

      window.localStorage.setItem('auth_token', `test_token_${testUser.id}`);
      window.localStorage.setItem('refresh_token', `refresh_token_${testUser.id}`);
      window.localStorage.setItem('user_info', JSON.stringify(testUser));

      // 设置token过期时间（1小时后）
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString();
      window.localStorage.setItem('token_expires_at', expiresAt);

      // 添加全局测试标识
      window.__TEST_ENV__ = true;

      console.log('🧪 测试环境初始化完成 - 管理员权限');
    });
  });

  // 测试每个路由的基本功能
  for (const route of ROUTES) {
    test(`${route.name} (${route.path}) - 基本渲染测试`, async ({ page }) => {
      // 导航到页面
      await page.goto(`${BASE_URL}${route.path}`);
      
      // 等待Vue应用完全加载
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // 检查页面标题
      await expect(page).toHaveTitle(route.expectedTitle);
      
      // 检查页面是否正确渲染
      if (route.shouldHaveLayout) {
        // 检查统一布局元素
        for (const [name, selector] of Object.entries(LAYOUT_SELECTORS)) {
          await expect(page.locator(selector)).toBeVisible({
            timeout: 10000
          });
        }
        
        // 检查导航链接数量
        const navLinks = page.locator('.app-header__nav-item');
        await expect(navLinks).toHaveCount(4, { timeout: 5000 });
        
        const sidebarLinks = page.locator('.app-sidebar__nav-item');
        await expect(sidebarLinks).toHaveCount(6, { timeout: 5000 });
        
      } else {
        // 登录页面检查
        for (const [name, selector] of Object.entries(LOGIN_SELECTORS)) {
          await expect(page.locator(selector)).toBeVisible({
            timeout: 10000
          });
        }
        
        // 确保登录页面没有统一布局元素
        for (const [name, selector] of Object.entries(LAYOUT_SELECTORS)) {
          await expect(page.locator(selector)).not.toBeVisible();
        }
      }
    });
  }

  // 测试路由导航
  test('路由导航测试', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    // 测试顶部导航
    const topNavLinks = [
      { text: '首页', expectedPath: '/' },
      { text: '视频创作', expectedPath: '/video-creation' },
      { text: '计算引擎', expectedPath: '/compute-test' },
      { text: '个人中心', expectedPath: '/profile' }
    ];
    
    for (const link of topNavLinks) {
      await page.click(`text=${link.text}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
      
      // 检查URL是否正确
      expect(page.url()).toBe(`${BASE_URL}${link.expectedPath}`);
      
      // 检查布局元素仍然存在
      await expect(page.locator('.app-header')).toBeVisible();
      await expect(page.locator('.app-sidebar')).toBeVisible();
    }
  });

  // 测试侧边栏导航
  test('侧边栏导航测试', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    const sidebarLinks = [
      { text: '首页概览', expectedPath: '/' },
      { text: '智能视频生成', expectedPath: '/video-creation' },
      { text: '计算引擎测试', expectedPath: '/compute-test' },
      { text: '个人中心', expectedPath: '/profile' }
    ];
    
    for (const link of sidebarLinks) {
      await page.click(`.app-sidebar__nav-item:has-text("${link.text}")`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
      
      // 检查URL是否正确
      expect(page.url()).toBe(`${BASE_URL}${link.expectedPath}`);
      
      // 检查活跃状态
      await expect(page.locator(`.app-sidebar__nav-item:has-text("${link.text}")`))
        .toHaveClass(/active/);
    }
  });

  // 测试浏览器前进后退
  test('浏览器历史导航测试', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    // 导航到不同页面
    await page.click('text=视频创作');
    await page.waitForLoadState('networkidle');
    expect(page.url()).toBe(`${BASE_URL}/video-creation`);
    
    await page.click('text=计算引擎');
    await page.waitForLoadState('networkidle');
    expect(page.url()).toBe(`${BASE_URL}/compute-test`);
    
    // 测试后退
    await page.goBack();
    await page.waitForLoadState('networkidle');
    expect(page.url()).toBe(`${BASE_URL}/video-creation`);
    
    // 测试前进
    await page.goForward();
    await page.waitForLoadState('networkidle');
    expect(page.url()).toBe(`${BASE_URL}/compute-test`);
    
    // 确保布局在历史导航后仍然正确
    await expect(page.locator('.app-header')).toBeVisible();
    await expect(page.locator('.app-sidebar')).toBeVisible();
  });

  // 测试直接URL访问
  test('直接URL访问测试', async ({ page }) => {
    const testRoutes = ['/video-creation', '/compute-test', '/profile'];
    
    for (const route of testRoutes) {
      await page.goto(`${BASE_URL}${route}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // 检查布局是否正确加载
      await expect(page.locator('.app-header')).toBeVisible();
      await expect(page.locator('.app-sidebar')).toBeVisible();
      await expect(page.locator('.app-main')).toBeVisible();
      
      // 检查URL是否正确
      expect(page.url()).toBe(`${BASE_URL}${route}`);
    }
  });

  // 测试页面刷新
  test('页面刷新测试', async ({ page }) => {
    await page.goto(`${BASE_URL}/video-creation`);
    await page.waitForLoadState('networkidle');
    
    // 确保页面正确加载
    await expect(page.locator('.app-header')).toBeVisible();
    
    // 刷新页面
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // 检查布局是否仍然正确
    await expect(page.locator('.app-header')).toBeVisible();
    await expect(page.locator('.app-sidebar')).toBeVisible();
    await expect(page.locator('.app-main')).toBeVisible();
    
    // 检查URL保持不变
    expect(page.url()).toBe(`${BASE_URL}/video-creation`);
  });

  // 测试权限控制
  test('权限控制测试', async ({ page }) => {
    // 设置普通用户权限
    await page.addInitScript(() => {
      window.localStorage.setItem('test_user_type', 'user');
      const testUser = {
        id: 'test_user_001',
        username: 'test_user',
        email: '<EMAIL>',
        role: 'user',
        permissions: ['video:create', 'video:view', 'profile:edit'],
        avatar: '/test-avatars/user.png'
      };
      window.localStorage.setItem('user_info', JSON.stringify(testUser));
    });

    // 尝试访问需要管理员权限的页面
    await page.goto(`${BASE_URL}/compute-test`);
    await page.waitForLoadState('networkidle');

    // 应该被重定向到首页并显示权限错误
    expect(page.url()).toContain('error=TEST_INSUFFICIENT_PERMISSIONS');
  });

  // 测试404页面处理
  test('404页面处理测试', async ({ page }) => {
    await page.goto(`${BASE_URL}/non-existent-page`);
    await page.waitForLoadState('networkidle');

    // 检查是否有适当的错误处理
    // 这里可能需要根据实际的404处理逻辑进行调整
    const pageContent = await page.textContent('body');
    expect(pageContent).toBeTruthy();
  });
});
