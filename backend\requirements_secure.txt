# 安全依赖清单 - 已更新到最新安全版本
# 生成时间: 2025-01-23
# 基于安全审查结果创建的清理版本

# Web框架和API
fastapi==0.115.14
starlette==0.46.2
uvicorn==0.24.0
gunicorn==21.2.0

# 数据库
SQLAlchemy==2.0.23
alembic==1.16.2
psycopg2-binary==2.9.9
aiosqlite==0.19.0
asyncpg==0.29.0

# 认证和安全
PyJWT==2.10.1
bcrypt==4.1.2
cryptography==41.0.7
passlib==1.7.4
python-jose==3.5.0

# 缓存和消息队列
redis==6.2.0
aioredis==2.0.1
celery==5.3.4
kombu==5.5.4
billard==4.2.1
vine==5.1.0

# HTTP客户端
requests==2.32.4
httpx==0.28.1
aiohttp==3.12.13
httpcore==1.0.9

# 数据处理
pandas==2.2.2
numpy==2.3.1
scipy==1.13.1
scikit-learn==1.7.0

# AI和机器学习
tensorflow==2.19.0
torch==2.7.1
transformers==4.53.1
openai==1.93.0
ollama==0.5.1
huggingface-hub==0.33.2

# 图像和视频处理
opencv-python==*********
opencv-python-headless==*********
pillow==11.3.0
moviepy==2.2.1
ffmpeg-python==0.2.0

# 音频处理
librosa==0.11.0
soundfile==0.13.1
audioread==3.0.1

# 数据验证
pydantic==2.11.7
pydantic-core==2.33.2
pydantic-settings==2.10.1
marshmallow==3.20.1

# 配置管理
python-dotenv==1.1.1
omegaconf==2.3.0
hydra-core==1.3.2

# 日志和监控
loguru==0.7.3
structlog==23.2.0
prometheus-client==0.19.0
psutil==7.0.0

# 任务调度
APScheduler==3.11.0

# 文档和API
apispec==6.8.0
apispec-webframeworks==1.0.0

# 开发工具
black==25.1.0
flake8==7.3.0
isort==6.0.1
mypy-extensions==1.1.0
autoflake==2.3.1
autopep8==2.3.2

# 测试
pytest==8.3.2
pytest-asyncio==1.0.0

# 其他核心依赖
click==8.2.1
rich==14.0.0
typer==0.16.0
tenacity==8.5.0
joblib==1.4.2
tqdm==4.67.1
PyYAML==6.0.2
orjson==3.9.10

# 网络和协议
websockets==12.0
websocket-client==1.8.0
h11==0.16.0
httptools==0.6.4

# 加密和哈希
pycryptodome==3.23.0

# 时间和日期
python-dateutil==2.9.0.post0
pytz==2025.2
tzdata==2025.2
tzlocal==5.3.1

# 文件处理
fsspec==2025.5.1
cloudpathlib==0.21.1
pathlib2==2.3.7.post1

# 数据格式
msgpack==1.1.1
protobuf==5.29.5

# 系统工具
psutil==7.0.0
platformdirs==4.3.8
packaging==25.0

# 异步支持
anyio==3.7.1
async-timeout==5.0.1
nest-asyncio==1.6.0

# 类型检查
typing-extensions==4.14.0
typing-inspection==0.4.1
annotated-types==0.7.0

# 工具库
more-itertools==10.7.0
attrs==25.3.0
frozenlist==1.7.0
multidict==6.6.0
yarl==1.20.1

# 字符串处理
charset-normalizer==3.4.2
idna==3.10

# 模板引擎
Jinja2==3.1.6
MarkupSafe==3.0.2
Mako==1.3.10

# 序列化
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
referencing==0.36.2
rpds-py==0.26.0

# 缓存
cachetools==6.1.0
cachelib==0.13.0
diskcache==5.6.3

# 网络工具
urllib3==2.4.0
certifi==2025.6.15

# 进程管理
multiprocess==0.70.18

# 数学和科学计算
sympy==1.14.0
mpmath==1.3.0

# 其他实用工具
six==1.17.0
wrapt==1.17.2
decorator==5.2.1
humanfriendly==10.0
colorama==0.4.6
wcwidth==0.2.13

# 压缩和归档
zipp==3.23.0

# 正则表达式
regex==2024.11.6

# 路径处理
pathspec==0.12.1

# 配置解析
iniconfig==2.1.0
toml==0.10.2

# 进度条
tqdm==4.67.1

# 颜色输出
termcolor==3.1.0
coloredlogs==15.0.1

# 数据结构
sortedcontainers==2.4.0

# 文本处理
jieba==0.42.1

# 编码检测
chardet==5.2.0

# 系统信息
distro==1.9.0

# 文件监控
watchfiles==1.1.0

# 网络协议
websocket-client==1.8.0
wsproto==1.2.0

# 数据库连接池
SQLAlchemy==2.0.23

# 异步数据库
aiosqlite==0.19.0
asyncpg==0.29.0

# 线程池
threadpoolctl==3.6.0

# Flask相关（如果需要）
Flask==2.3.3
Flask-Caching==2.3.1
Flask-Cors==4.0.1
Flask-JWT-Extended==4.6.0
Flask-SQLAlchemy==3.0.5

# AI相关
anthropic==0.55.0
openai-whisper==20250625
detoxify==0.5.2
nudenet==3.4.2

# 视频下载
yt-dlp==2025.6.30
gallery-dl==1.29.7

# 其他工具
selenium==4.34.0
qrcode==7.3.1
email-validator==2.2.0

# 中文处理
jieba==0.42.1

# 云服务SDK
aliyun-python-sdk-core==2.16.0
aliyun-python-sdk-kms==2.16.5
bce-python-sdk==0.9.35
oss2==2.19.1

# 语音识别
funasr==1.2.6
kaldiio==2.18.1

# 自然语言处理
spacy==3.8.7
spacy-legacy==3.0.12
spacy-loggers==1.0.5

# 机器学习工具
numba==0.61.2
onnxruntime==1.22.0
safetensors==0.5.3

# 数据可视化
matplotlib==3.8.4
seaborn==0.13.2

# 地理信息
geoip2==4.7.0
maxminddb==2.2.0

# 其他专用库
airportsdata==20250622
iso3166==2.1.1
langcodes==3.5.0
language-data==1.3.0

# 安全相关
safety>=3.0.0
pip-audit>=2.6.0
bandit>=1.7.5