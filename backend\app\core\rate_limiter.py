"""
API速率限制模块
🔒 证据链: 防止暴力破解攻击，实现分布式速率限制
"""

import hashlib
import time
from datetime import datetime
from typing import Dict, Tuple

import redis
from fastapi import HTTPException, Request, status
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings


class RateLimitExceeded(Exception):
    """速率限制超出异常"""

    def __init__(self, message: str, retry_after: int):
        self.message = message
        self.retry_after = retry_after
        super().__init__(message)


class RateLimiter:
    """速率限制器"""

    def __init__(self):
        # 🔒 证据链: 使用Redis实现分布式速率限制
        try:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD,
                db=settings.REDIS_DB + 1,  # 使用不同的DB避免冲突
                decode_responses=True,
                socket_timeout=5.0,
                socket_connect_timeout=5.0,
                retry_on_timeout=True,
            )
            # 测试连接
            self.redis_client.ping()
            self.redis_available = True
        except Exception:
            # ❗待PM确认: Redis不可用时的降级策略
            self.redis_client = None
            self.redis_available = False
            # 使用内存存储作为降级方案
            self._memory_store = {}
            self._cleanup_time = time.time()

    def _get_client_identifier(self, request: Request) -> str:
        """获取客户端标识符"""
        # 🔒 证据链: 多层客户端识别，防止绕过

        # 优先使用认证用户ID
        user_id = getattr(request.state, "user_id", None)
        if user_id:
            return f"user:{user_id}"

        # 获取真实IP地址
        client_ip = self._get_real_ip(request)

        # 结合User-Agent增强识别
        user_agent = request.headers.get("user-agent", "")
        user_agent_hash = hashlib.md5(user_agent.encode()).hexdigest()[:8]

        return f"ip:{client_ip}:ua:{user_agent_hash}"

    def _get_real_ip(self, request: Request) -> str:
        """获取真实IP地址"""
        # 🔒 证据链: 正确处理代理和负载均衡器的IP头

        # 检查常见的代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # 取第一个IP（客户端真实IP）
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip.strip()

        # Cloudflare
        cf_ip = request.headers.get("cf-connecting-ip")
        if cf_ip:
            return cf_ip.strip()

        # 默认使用连接IP
        return request.client.host if request.client else "unknown"

    def _get_rate_limit_key(self, identifier: str, endpoint: str, window: str) -> str:
        """生成速率限制键"""
        return f"rate_limit:{endpoint}:{identifier}:{window}"

    def check_rate_limit(
        self,
        request: Request,
        max_requests: int,
        window_seconds: int,
        endpoint: str = None,
    ) -> Tuple[bool, Dict]:
        """检查速率限制"""

        if not endpoint:
            endpoint = f"{request.method}:{request.url.path}"

        identifier = self._get_client_identifier(request)
        current_time = int(time.time())
        window_start = current_time - (current_time % window_seconds)

        if self.redis_available:
            return self._check_redis_rate_limit(
                identifier, endpoint, window_start, window_seconds, max_requests
            )
        else:
            return self._check_memory_rate_limit(
                identifier, endpoint, window_start, window_seconds, max_requests
            )

    def _check_redis_rate_limit(
        self,
        identifier: str,
        endpoint: str,
        window_start: int,
        window_seconds: int,
        max_requests: int,
    ) -> Tuple[bool, Dict]:
        """Redis速率限制检查"""

        key = self._get_rate_limit_key(identifier, endpoint, str(window_start))

        try:
            # 🔒 证据链: 使用Redis原子操作确保并发安全
            pipe = self.redis_client.pipeline()
            pipe.incr(key)
            pipe.expire(key, window_seconds)
            results = pipe.execute()

            current_requests = results[0]

            # 计算剩余请求数和重置时间
            remaining = max(0, max_requests - current_requests)
            reset_time = window_start + window_seconds

            rate_limit_info = {
                "limit": max_requests,
                "remaining": remaining,
                "reset": reset_time,
                "reset_time": datetime.fromtimestamp(reset_time).isoformat(),
            }

            if current_requests > max_requests:
                return False, rate_limit_info

            return True, rate_limit_info

        except Exception as e:
            # Redis错误时允许请求通过，但记录错误
            print(f"Redis rate limit error: {e}")
            return True, {"error": "rate_limit_unavailable"}

    def _check_memory_rate_limit(
        self,
        identifier: str,
        endpoint: str,
        window_start: int,
        window_seconds: int,
        max_requests: int,
    ) -> Tuple[bool, Dict]:
        """内存速率限制检查（降级方案）"""

        # 🔒 证据链: 内存存储的清理机制
        current_time = time.time()
        if current_time - self._cleanup_time > 300:  # 每5分钟清理一次
            self._cleanup_memory_store()
            self._cleanup_time = current_time

        key = f"{identifier}:{endpoint}:{window_start}"

        if key not in self._memory_store:
            self._memory_store[key] = {
                "count": 0,
                "expires": window_start + window_seconds,
            }

        self._memory_store[key]["count"] += 1
        current_requests = self._memory_store[key]["count"]

        remaining = max(0, max_requests - current_requests)
        reset_time = window_start + window_seconds

        rate_limit_info = {
            "limit": max_requests,
            "remaining": remaining,
            "reset": reset_time,
            "reset_time": datetime.fromtimestamp(reset_time).isoformat(),
            "fallback": True,
        }

        if current_requests > max_requests:
            return False, rate_limit_info

        return True, rate_limit_info

    def _cleanup_memory_store(self):
        """清理过期的内存存储"""
        current_time = time.time()
        expired_keys = [
            key
            for key, data in self._memory_store.items()
            if data["expires"] < current_time
        ]
        for key in expired_keys:
            del self._memory_store[key]


class RateLimitMiddleware(BaseHTTPMiddleware):
    """速率限制中间件"""

    def __init__(self, app, rate_limiter: RateLimiter = None):
        super().__init__(app)
        self.rate_limiter = rate_limiter or RateLimiter()

        # 🔒 证据链: 不同端点的速率限制配置
        self.rate_limits = {
            # 认证相关端点 - 严格限制
            "POST:/api/v1/auth/login": (5, 300),  # 5次/5分钟
            "POST:/api/v1/auth/register": (3, 600),  # 3次/10分钟
            "POST:/api/v1/auth/send-sms": (3, 60),  # 3次/1分钟
            "POST:/api/v1/auth/send_sms": (3, 60),  # 3次/1分钟
            # 内容操作 - 中等限制
            "POST:/api/v1/content": (20, 300),  # 20次/5分钟
            "PUT:/api/v1/content": (30, 300),  # 30次/5分钟
            "DELETE:/api/v1/content": (10, 300),  # 10次/5分钟
            # 视频处理 - 资源密集型操作
            "POST:/api/v1/video/download": (5, 300),  # 5次/5分钟
            "POST:/api/v1/video/process": (10, 600),  # 10次/10分钟
            # 查询操作 - 宽松限制
            "GET": (100, 300),  # 100次/5分钟（通用GET请求）
            # 默认限制
            "default": (60, 300),  # 60次/5分钟
        }

    async def dispatch(self, request: Request, call_next):
        """处理请求的速率限制"""

        # 🔒 证据链: 跳过静态资源和健康检查
        if self._should_skip_rate_limit(request):
            return await call_next(request)

        # 获取速率限制配置
        endpoint_key = f"{request.method}:{request.url.path}"
        max_requests, window_seconds = self._get_rate_limit_config(request)

        # 检查速率限制
        allowed, rate_info = self.rate_limiter.check_rate_limit(
            request, max_requests, window_seconds, endpoint_key
        )

        if not allowed:
            # 🔒 证据链: 返回标准的429状态码和重试信息
            retry_after = rate_info.get("reset", 0) - int(time.time())

            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Try again in {retry_after} seconds.",
                    "retry_after": retry_after,
                    "limit": rate_info.get("limit"),
                    "reset_time": rate_info.get("reset_time"),
                },
                headers={
                    "Retry-After": str(retry_after),
                    "X-RateLimit-Limit": str(rate_info.get("limit", 0)),
                    "X-RateLimit-Remaining": str(rate_info.get("remaining", 0)),
                    "X-RateLimit-Reset": str(rate_info.get("reset", 0)),
                },
            )

        # 添加速率限制头到响应
        response = await call_next(request)

        # 🔒 证据链: 添加标准的速率限制响应头
        response.headers["X-RateLimit-Limit"] = str(rate_info.get("limit", 0))
        response.headers["X-RateLimit-Remaining"] = str(rate_info.get("remaining", 0))
        response.headers["X-RateLimit-Reset"] = str(rate_info.get("reset", 0))

        return response

    def _should_skip_rate_limit(self, request: Request) -> bool:
        """判断是否跳过速率限制"""
        # 🔒 证据链: 跳过不需要限制的请求
        skip_paths = [
            "/docs",
            "/redoc",
            "/openapi.json",  # API文档
            "/health",
            "/ping",
            "/status",  # 健康检查
            "/static/",
            "/favicon.ico",  # 静态资源
        ]

        path = request.url.path
        return any(path.startswith(skip_path) for skip_path in skip_paths)

    def _get_rate_limit_config(self, request: Request) -> Tuple[int, int]:
        """获取速率限制配置"""
        endpoint_key = f"{request.method}:{request.url.path}"

        # 精确匹配
        if endpoint_key in self.rate_limits:
            return self.rate_limits[endpoint_key]

        # 方法匹配
        if request.method in self.rate_limits:
            return self.rate_limits[request.method]

        # 默认限制
        return self.rate_limits["default"]


# 全局速率限制器实例
rate_limiter = RateLimiter()


# 装饰器函数
def rate_limit(max_requests: int, window_seconds: int):
    """速率限制装饰器"""

    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            identifier = rate_limiter._get_client_identifier(request)
            endpoint = f"{request.method}:{request.url.path}"

            allowed, rate_info = rate_limiter.check_rate_limit(
                request, max_requests, window_seconds, endpoint
            )

            if not allowed:
                retry_after = rate_info.get("reset", 0) - int(time.time())
                raise RateLimitExceeded(
                    f"Rate limit exceeded. Try again in {retry_after} seconds.",
                    retry_after,
                )

            return await func(request, *args, **kwargs)

        return wrapper

    return decorator


# 常用速率限制装饰器
def auth_rate_limit(func):
    """认证端点速率限制"""
    return rate_limit(5, 300)(func)


def content_rate_limit(func):
    """内容操作速率限制"""
    return rate_limit(20, 300)(func)


def video_rate_limit(func):
    """视频处理速率限制"""
    return rate_limit(5, 300)(func)
