<template>
  <div class="simple-login-page">
    <div class="login-container">
      <h1>🎬 二创短视频分发系统</h1>
      <h2>简单登录测试</h2>
      
      <!-- API状态 -->
      <div class="api-status" :class="{ connected: apiConnected, disconnected: !apiConnected }">
        API状态: {{ apiConnected ? '🟢 已连接' : '🔴 未连接' }}
      </div>
      
      <!-- 登录表单 -->
      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label>选择测试用户:</label>
          <select v-model="selectedUser" @change="updatePassword">
            <option value="admin_user">admin_user (管理员)</option>
            <option value="content_creator">content_creator (创作者)</option>
            <option value="business_user">business_user (商业用户)</option>
            <option value="demo_user">demo_user (普通用户)</option>
            <option value="test_developer">test_developer (开发者)</option>
            <option value="marketing_team">marketing_team (营销团队)</option>
          </select>
        </div>
        
        <div class="form-group">
          <label>用户名:</label>
          <input v-model="username" type="text" required />
        </div>
        
        <div class="form-group">
          <label>密码:</label>
          <input v-model="password" type="password" required />
        </div>
        
        <button type="submit" :disabled="isLoading">
          {{ isLoading ? '登录中...' : '登录' }}
        </button>
        
        <button type="button" @click="testAPI">测试API</button>
      </form>
      
      <!-- 结果显示 -->
      <div v-if="result" class="result" :class="resultType">
        <pre>{{ result }}</pre>
      </div>
      
      <!-- 用户信息 -->
      <div v-if="userInfo" class="user-info">
        <h3>登录成功！用户信息:</h3>
        <p><strong>ID:</strong> {{ userInfo.id }}</p>
        <p><strong>用户名:</strong> {{ userInfo.username }}</p>
        <p><strong>邮箱:</strong> {{ userInfo.email }}</p>
        <p><strong>角色:</strong> {{ userInfo.role }}</p>
        <button @click="goToHome">进入主页</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const apiConnected = ref(false)
const isLoading = ref(false)
const selectedUser = ref('admin_user')
const username = ref('admin_user')
const password = ref('Admin123!')
const result = ref('')
const resultType = ref('info')
const userInfo = ref(null)

// 用户密码映射
const userPasswords = {
  'admin_user': 'Admin123!',
  'content_creator': 'Creator123!',
  'business_user': 'Business123!',
  'demo_user': 'Demo123!',
  'test_developer': 'Dev123!',
  'marketing_team': 'Marketing123!'
}

// 更新密码
const updatePassword = () => {
  username.value = selectedUser.value
  password.value = userPasswords[selectedUser.value] || ''
}

// 显示结果
const showResult = (message: string, type: string = 'info') => {
  result.value = message
  resultType.value = type
}

// 测试API连接
const testAPI = async () => {
  showResult('测试API连接...', 'info')
  
  try {
    const response = await fetch('http://localhost:8001/health')
    const data = await response.json()
    
    apiConnected.value = true
    showResult(`API连接成功！
状态: ${data.status}
数据库: ${data.database}
用户数: ${data.users}`, 'success')
    
  } catch (error) {
    apiConnected.value = false
    showResult(`API连接失败: ${error.message}
请确保后端服务器正在运行`, 'error')
  }
}

// 处理登录
const handleLogin = async () => {
  if (!username.value || !password.value) {
    showResult('请输入用户名和密码', 'error')
    return
  }
  
  isLoading.value = true
  showResult(`正在登录: ${username.value}...`, 'info')
  
  try {
    const response = await fetch('http://localhost:8001/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: username.value,
        password: password.value
      })
    })
    
    const data = await response.json()
    
    if (response.ok && data.success) {
      userInfo.value = data.user
      showResult(`登录成功！`, 'success')
      
      // 保存到localStorage
      localStorage.setItem('user_info', JSON.stringify(data.user))
      localStorage.setItem('access_token', 'simple-token')
      
    } else {
      showResult(`登录失败: ${data.detail || '未知错误'}`, 'error')
    }
    
  } catch (error) {
    showResult(`登录请求失败: ${error.message}`, 'error')
  } finally {
    isLoading.value = false
  }
}

// 进入主页
const goToHome = () => {
  router.push('/')
}

// 组件挂载时测试API
onMounted(() => {
  testAPI()
})
</script>

<style scoped>
.simple-login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-container {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  max-width: 500px;
  width: 100%;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
}

h2 {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-size: 18px;
}

.api-status {
  text-align: center;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  font-weight: bold;
}

.api-status.connected {
  background: #d4edda;
  color: #155724;
}

.api-status.disconnected {
  background: #f8d7da;
  color: #721c24;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

input, select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  box-sizing: border-box;
}

button {
  background: #007bff;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  margin-right: 10px;
  margin-bottom: 10px;
}

button:hover:not(:disabled) {
  background: #0056b3;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.result {
  margin-top: 20px;
  padding: 15px;
  border-radius: 5px;
  white-space: pre-wrap;
  font-family: monospace;
}

.result.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.result.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.result.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.user-info {
  margin-top: 20px;
  padding: 20px;
  background: #d4edda;
  border-radius: 5px;
  border: 1px solid #c3e6cb;
}

.user-info h3 {
  color: #155724;
  margin-bottom: 15px;
}

.user-info p {
  margin: 5px 0;
  color: #155724;
}
</style>
