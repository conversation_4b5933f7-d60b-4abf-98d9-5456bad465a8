# Page snapshot

```yaml
- banner:
  - link "🎬 二创短视频分发系统":
    - /url: /
  - navigation:
    - link "首页":
      - /url: /
    - link "视频创作":
      - /url: /video-creation
    - link "计算引擎":
      - /url: /compute-test
    - link "个人中心":
      - /url: /profile
- complementary:
  - heading "功能导航" [level=3]
  - navigation:
    - link "首页概览":
      - /url: /
      - img
      - text: 首页概览
    - link "智能视频生成":
      - /url: /video-creation
      - img
      - text: 智能视频生成
    - link "计算引擎测试":
      - /url: /compute-test
      - img
      - text: 计算引擎测试
    - link "内容分析":
      - /url: /content-analysis
      - img
      - text: 内容分析
    - link "批量处理":
      - /url: /batch-processing
      - img
      - text: 批量处理
    - link "个人中心":
      - /url: /profile
      - img
      - text: 个人中心
- main:
  - heading "欢迎使用二创短视频分发系统" [level=1]
  - paragraph: 基于先进的AI技术，为您提供专业的视频内容创作解决方案
  - text: 1,234 视频已生成 98.5% 成功率 24/7 在线服务
  - img
  - heading "智能视频生成" [level=3]
  - paragraph: 利用AI技术自动生成高质量视频内容，支持多种风格和主题
  - link "开始创作":
    - /url: /video-creation
  - img
  - heading "计算引擎" [level=3]
  - paragraph: 强大的本地计算能力，支持FFmpeg、TensorFlow.js等核心技术
  - link "测试引擎":
    - /url: /compute-test
  - img
  - heading "AI增强" [level=3]
  - paragraph: 集成多种AI模型，提供智能内容分析和优化建议
  - button "即将推出" [disabled]
  - heading "核心技术特性" [level=2]
  - img
  - heading "FFmpeg WASM" [level=3]
  - paragraph: 浏览器内视频处理
  - img
  - heading "TensorFlow.js" [level=3]
  - paragraph: 机器学习推理
  - img
  - heading "WebGPU" [level=3]
  - paragraph: GPU加速计算
  - img
  - heading "Web Speech" [level=3]
  - paragraph: 语音识别合成
```