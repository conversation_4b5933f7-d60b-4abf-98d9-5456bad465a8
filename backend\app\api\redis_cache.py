#!/usr/bin/env python3
"""
Redis缓存管理API
提供Redis缓存的REST API接口
"""

from typing import Any, Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from ..core.redis_manager import redis_manager
from ..services.redis_cache_service import get_cache
from ..services.redis_integrated_service import redis_integrated_service

router = APIRouter(prefix="/api/v1/redis", tags=["Redis缓存"])


class CacheSetRequest(BaseModel):
    """缓存设置请求"""

    key: str
    value: Any
    ttl: Optional[int] = None
    namespace: str = "default"


class CacheGetRequest(BaseModel):
    """缓存获取请求"""

    key: str
    namespace: str = "default"


class CacheDeleteRequest(BaseModel):
    """缓存删除请求"""

    key: str
    namespace: str = "default"


@router.post("/cache/set")
async def set_cache(request: CacheSetRequest):
    """设置缓存"""
    try:
        cache = await get_cache(request.namespace)
        success = await cache.set(request.key, request.value, request.ttl)

        if success:
            return {
                "success": True,
                "message": f"缓存设置成功: {request.key}",
                "namespace": request.namespace,
                "ttl": request.ttl,
            }
        else:
            raise HTTPException(status_code=500, detail="缓存设置失败")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"缓存操作失败: {str(e)}")


@router.post("/cache/get")
async def get_cache_value(request: CacheGetRequest):
    """获取缓存"""
    try:
        cache = await get_cache(request.namespace)
        value = await cache.get(request.key)

        return {
            "success": True,
            "key": request.key,
            "value": value,
            "namespace": request.namespace,
            "found": value is not None,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"缓存操作失败: {str(e)}")


@router.post("/cache/delete")
async def delete_cache(request: CacheDeleteRequest):
    """删除缓存"""
    try:
        cache = await get_cache(request.namespace)
        success = await cache.delete(request.key)

        return {
            "success": success,
            "message": f"缓存删除{'成功' if success else '失败'}: {request.key}",
            "namespace": request.namespace,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"缓存操作失败: {str(e)}")


@router.get("/cache/info/{namespace}")
async def get_cache_info(namespace: str):
    """获取缓存信息"""
    try:
        cache = await get_cache(namespace)
        info = await cache.get_cache_info()

        return {"success": True, "cache_info": info}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存信息失败: {str(e)}")


@router.get("/cache/metrics/{namespace}")
async def get_cache_metrics(namespace: str):
    """获取缓存指标"""
    try:
        cache = await get_cache(namespace)
        metrics = await cache.get_metrics()

        return {
            "success": True,
            "namespace": namespace,
            "metrics": metrics.dict(),
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存指标失败: {str(e)}")


@router.delete("/cache/clear/{namespace}")
async def clear_cache_namespace(namespace: str):
    """清空指定命名空间的缓存"""
    try:
        cache = await get_cache(namespace)
        deleted_count = await cache.clear_namespace()

        return {
            "success": True,
            "message": f"清空命名空间 {namespace}",
            "deleted_count": deleted_count,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")


@router.get("/health")
async def redis_health_check():
    """Redis健康检查"""
    try:
        health = await redis_manager.health_check()

        return {"success": True, "redis_health": health}

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "redis_health": {"status": "unhealthy"},
        }


@router.get("/service/status")
async def get_service_status():
    """获取Redis集成服务状态"""
    try:
        health = await redis_integrated_service.get_service_health()

        return {"success": True, "service_health": health}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取服务状态失败: {str(e)}")


@router.get("/projects/status")
async def get_projects_status():
    """获取所有项目状态"""
    try:
        status = await redis_integrated_service.get_all_project_status()

        return {"success": True, "projects_status": status}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目状态失败: {str(e)}")


@router.get("/projects/{project_name}/status")
async def get_project_status(project_name: str):
    """获取指定项目状态"""
    try:
        status = await redis_integrated_service.check_project_status(project_name)

        return {
            "success": True,
            "project_name": project_name,
            "status": status.value,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目状态失败: {str(e)}")
