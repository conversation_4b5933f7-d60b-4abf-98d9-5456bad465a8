"""
API版本管理策略
🔒 证据链: 实现灵活的API版本控制和向后兼容性
基于2024年最佳实践
"""

import re
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass

from fastapi import Request, HTTPException, status
from pydantic import BaseModel


class VersioningStrategy(str, Enum):
    """版本控制策略"""
    URL_PATH = "url_path"  # /api/v1/users
    QUERY_PARAM = "query_param"  # /api/users?version=1
    HEADER = "header"  # X-API-Version: 1
    ACCEPT_HEADER = "accept_header"  # Accept: application/vnd.api+json;version=1
    SUBDOMAIN = "subdomain"  # v1.api.example.com


class VersionStatus(str, Enum):
    """版本状态"""
    DEVELOPMENT = "development"  # 开发中
    BETA = "beta"  # 测试版
    STABLE = "stable"  # 稳定版
    DEPRECATED = "deprecated"  # 已弃用
    SUNSET = "sunset"  # 即将下线
    RETIRED = "retired"  # 已下线


@dataclass
class APIVersion:
    """API版本信息"""
    version: str
    status: VersionStatus
    release_date: datetime
    deprecation_date: Optional[datetime] = None
    sunset_date: Optional[datetime] = None
    description: str = ""
    breaking_changes: List[str] = None
    migration_guide: str = ""
    supported_until: Optional[datetime] = None

    def __post_init__(self):
        if self.breaking_changes is None:
            self.breaking_changes = []

    @property
    def is_active(self) -> bool:
        """版本是否仍然活跃"""
        return self.status not in [VersionStatus.RETIRED]

    @property
    def is_deprecated(self) -> bool:
        """版本是否已弃用"""
        return self.status in [VersionStatus.DEPRECATED, VersionStatus.SUNSET]

    @property
    def days_until_sunset(self) -> Optional[int]:
        """距离下线还有多少天"""
        if self.sunset_date:
            delta = self.sunset_date - datetime.now()
            return max(0, delta.days)
        return None


class APIVersionManager:
    """API版本管理器"""

    def __init__(self):
        # 🔒 证据链: 使用配置文件定义支持的API版本
        from app.core.version_config import VersionConfig
        
        self.versions = VersionConfig.get_version_definitions()
        self.compatibility_matrix = VersionConfig.get_compatibility_matrix()
        self.migration_paths = VersionConfig.get_migration_paths()
        self.deprecation_timeline = VersionConfig.get_deprecation_timeline()
        self.feature_matrix = VersionConfig.get_feature_matrix()
        self.performance_benchmarks = VersionConfig.get_performance_benchmarks()
        self.security_features = VersionConfig.get_security_features()

        # 默认版本和策略
        self.default_version = "v2"
        self.versioning_strategy = VersioningStrategy.URL_PATH

    def extract_version_from_request(self, request: Request) -> Tuple[str, str]:
        """从请求中提取版本信息"""
        version = None
        source = "default"

        # 1. URL路径版本 (优先级最高)
        path_version = self._extract_from_url_path(request.url.path)
        if path_version:
            version = path_version
            source = "url_path"

        # 2. 查询参数版本
        elif "version" in request.query_params:
            version = request.query_params["version"]
            source = "query_param"

        # 3. 请求头版本
        elif "X-API-Version" in request.headers:
            version = request.headers["X-API-Version"]
            source = "header"

        # 4. Accept头版本
        elif "Accept" in request.headers:
            accept_version = self._extract_from_accept_header(request.headers["Accept"])
            if accept_version:
                version = accept_version
                source = "accept_header"

        # 5. 子域名版本
        elif request.url.hostname:
            subdomain_version = self._extract_from_subdomain(request.url.hostname)
            if subdomain_version:
                version = subdomain_version
                source = "subdomain"

        # 使用默认版本
        if not version:
            version = self.default_version
            source = "default"

        # 标准化版本格式
        version = self._normalize_version(version)

        return version, source

    def _extract_from_url_path(self, path: str) -> Optional[str]:
        """从URL路径提取版本"""
        # 匹配 /api/v1/, /api/v2/ 等格式
        match = re.match(r"/api/(v\d+)/", path)
        if match:
            return match.group(1)
        return None

    def _extract_from_accept_header(self, accept_header: str) -> Optional[str]:
        """从Accept头提取版本"""
        # 匹配 application/vnd.api+json;version=1 格式
        match = re.search(r"version=([\d\.]+)", accept_header)
        if match:
            return f"v{match.group(1)}"
        return None

    def _extract_from_subdomain(self, hostname: str) -> Optional[str]:
        """从子域名提取版本"""
        # 匹配 v1.api.example.com 格式
        if hostname.startswith("v"):
            parts = hostname.split(".")
            if len(parts) > 0 and re.match(r"v\d+", parts[0]):
                return parts[0]
        return None

    def _normalize_version(self, version: str) -> str:
        """标准化版本格式"""
        # 移除前缀和后缀空格
        version = version.strip()
        
        # 确保以v开头
        if not version.startswith("v"):
            version = f"v{version}"
        
        # 移除小版本号（如v1.0 -> v1）
        version = re.sub(r"(v\d+)\..*", r"\1", version)
        
        return version

    def validate_version(self, version: str) -> bool:
        """验证版本是否有效"""
        return version in self.versions and self.versions[version].is_active

    def get_version_info(self, version: str) -> Optional[APIVersion]:
        """获取版本信息"""
        return self.versions.get(version)

    def check_version_compatibility(self, requested_version: str, endpoint_version: str) -> bool:
        """检查版本兼容性"""
        if requested_version not in self.compatibility_matrix:
            return False
        return endpoint_version in self.compatibility_matrix[requested_version]

    def get_deprecation_warnings(self, version: str) -> List[str]:
        """获取弃用警告"""
        warnings = []
        version_info = self.get_version_info(version)
        
        if not version_info:
            return warnings

        if version_info.is_deprecated:
            warnings.append(f"API version {version} is deprecated")
            
            if version_info.sunset_date:
                days_left = version_info.days_until_sunset
                if days_left is not None:
                    if days_left == 0:
                        warnings.append(f"API version {version} will be retired today")
                    elif days_left <= 30:
                        warnings.append(f"API version {version} will be retired in {days_left} days")
                    else:
                        warnings.append(f"API version {version} will be retired on {version_info.sunset_date.strftime('%Y-%m-%d')}")
            
            if version_info.migration_guide:
                warnings.append(f"Migration guide available at: {version_info.migration_guide}")

        return warnings

    def get_supported_versions(self) -> List[str]:
        """获取支持的版本列表"""
        return [v for v, info in self.versions.items() if info.is_active]

    def get_latest_version(self) -> str:
        """获取最新稳定版本"""
        stable_versions = [
            v for v, info in self.versions.items()
            if info.status == VersionStatus.STABLE and info.is_active
        ]
        if stable_versions:
            # 按版本号排序，返回最新的
            return sorted(stable_versions, key=lambda x: int(x[1:]))[-1]
        return self.default_version

    def create_version_response_headers(self, version: str) -> Dict[str, str]:
        """创建版本相关的响应头"""
        headers = {
            "X-API-Version": version,
            "X-API-Supported-Versions": ",".join(self.get_supported_versions()),
            "X-API-Latest-Version": self.get_latest_version(),
        }

        # 添加弃用警告
        warnings = self.get_deprecation_warnings(version)
        if warnings:
            headers["X-API-Deprecation-Warning"] = "; ".join(warnings)

        version_info = self.get_version_info(version)
        if version_info and version_info.sunset_date:
            headers["X-API-Sunset"] = version_info.sunset_date.strftime("%Y-%m-%d")

        return headers

    def get_version_statistics(self) -> Dict:
        """获取版本统计信息"""
        stats = {
            "total_versions": len(self.versions),
            "active_versions": len([v for v in self.versions.values() if v.is_active]),
            "deprecated_versions": len([v for v in self.versions.values() if v.is_deprecated]),
            "default_version": self.default_version,
            "latest_version": self.get_latest_version(),
            "versioning_strategy": self.versioning_strategy.value,
        }

        # 按状态分组
        by_status = {}
        for version, info in self.versions.items():
            status = info.status.value
            if status not in by_status:
                by_status[status] = []
            by_status[status].append(version)
        
        stats["versions_by_status"] = by_status
        return stats

    def get_enhanced_version_statistics(self) -> Dict:
        """获取增强版本统计信息"""
        base_stats = self.get_version_statistics()
        
        # 添加新的统计信息
        enhanced_stats = {
            **base_stats,
            "migration_paths": list(self.migration_paths.keys()),
            "feature_matrix": self.feature_matrix,
            "performance_benchmarks": self.performance_benchmarks,
            "security_features": self.security_features,
            "deprecation_timeline": {
                version: {
                    "release_date": timeline["release_date"].isoformat(),
                    "deprecation_date": timeline["deprecation_date"].isoformat(),
                    "sunset_date": timeline["sunset_date"].isoformat(),
                    "end_of_life": timeline["end_of_life"].isoformat(),
                }
                for version, timeline in self.deprecation_timeline.items()
            },
        }
        
        return enhanced_stats

    def get_migration_path(self, from_version: str, to_version: str) -> Optional[Dict]:
        """获取特定的迁移路径"""
        path_key = f"{from_version}_to_{to_version}"
        return self.migration_paths.get(path_key)

    def get_version_features(self, version: str) -> Dict[str, bool]:
        """获取特定版本的功能列表"""
        features = {}
        for feature, version_support in self.feature_matrix.items():
            features[feature] = version_support.get(version, False)
        return features

    def compare_versions(self, version1: str, version2: str) -> Dict:
        """比较两个版本的差异"""
        v1_features = self.get_version_features(version1)
        v2_features = self.get_version_features(version2)
        
        added_features = [f for f, supported in v2_features.items() if supported and not v1_features.get(f, False)]
        removed_features = [f for f, supported in v1_features.items() if supported and not v2_features.get(f, False)]
        common_features = [f for f in v1_features if v1_features[f] and v2_features.get(f, False)]
        
        return {
            "version1": version1,
            "version2": version2,
            "added_features": added_features,
            "removed_features": removed_features,
            "common_features": common_features,
            "compatibility": self.check_version_compatibility(version1, version2),
        }

    def migrate_request_data(self, data: Dict, from_version: str, to_version: str) -> Dict:
        """数据迁移（简化示例）"""
        # 这里可以实现具体的数据迁移逻辑
        # 例如字段重命名、格式转换等
        
        if from_version == "v1" and to_version == "v2":
            # v1 到 v2 的迁移示例
            if "user_id" in data:
                data["userId"] = data.pop("user_id")  # 驼峰命名
            if "created_at" in data:
                data["createdAt"] = data.pop("created_at")
        
        elif from_version == "v2" and to_version == "v3":
            # v2 到 v3 的迁移示例
            if "pagination" in data:
                # 分页格式变更
                old_pagination = data.pop("pagination")
                data["page"] = {
                    "number": old_pagination.get("page", 1),
                    "size": old_pagination.get("limit", 20),
                    "total": old_pagination.get("total", 0),
                }

        return data


class APIVersionMiddleware:
    """API版本中间件"""

    def __init__(self, app, version_manager: APIVersionManager = None):
        self.app = app
        self.version_manager = version_manager or APIVersionManager()

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        # 创建请求对象
        request = Request(scope, receive)
        
        # 提取版本信息
        version, source = self.version_manager.extract_version_from_request(request)
        
        # 验证版本
        if not self.version_manager.validate_version(version):
            # 返回版本不支持错误
            response_body = {
                "error": "Unsupported API version",
                "requested_version": version,
                "supported_versions": self.version_manager.get_supported_versions(),
                "latest_version": self.version_manager.get_latest_version(),
            }
            
            await send({
                "type": "http.response.start",
                "status": 400,
                "headers": [
                    [b"content-type", b"application/json"],
                    [b"x-api-version-error", b"unsupported"],
                ],
            })
            
            import json
            await send({
                "type": "http.response.body",
                "body": json.dumps(response_body).encode(),
            })
            return

        # 将版本信息添加到请求状态
        request.state.api_version = version
        request.state.api_version_source = source

        # 修改响应以添加版本头
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                headers = dict(message.get("headers", []))
                version_headers = self.version_manager.create_version_response_headers(version)
                
                for key, value in version_headers.items():
                    headers[key.lower().encode()] = value.encode()
                
                message["headers"] = list(headers.items())
            
            await send(message)

        await self.app(scope, receive, send_wrapper)


# 全局版本管理器实例
api_version_manager = APIVersionManager()


# 装饰器函数
def version_required(min_version: str = None, max_version: str = None):
    """版本要求装饰器"""
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            current_version = getattr(request.state, "api_version", api_version_manager.default_version)
            
            # 检查最小版本要求
            if min_version:
                min_ver_num = int(min_version[1:])
                current_ver_num = int(current_version[1:])
                if current_ver_num < min_ver_num:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail={
                            "error": "API version too old",
                            "current_version": current_version,
                            "minimum_required": min_version,
                            "latest_version": api_version_manager.get_latest_version(),
                        }
                    )
            
            # 检查最大版本要求
            if max_version:
                max_ver_num = int(max_version[1:])
                current_ver_num = int(current_version[1:])
                if current_ver_num > max_ver_num:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail={
                            "error": "API version too new",
                            "current_version": current_version,
                            "maximum_supported": max_version,
                        }
                    )

            return await func(request, *args, **kwargs)
        return wrapper
    return decorator


def deprecated_endpoint(sunset_date: str, migration_guide: str = ""):
    """弃用端点装饰器"""
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            # 添加弃用警告到响应
            response = await func(request, *args, **kwargs)
            
            if hasattr(response, "headers"):
                response.headers["X-API-Deprecated"] = "true"
                response.headers["X-API-Sunset"] = sunset_date
                if migration_guide:
                    response.headers["X-API-Migration-Guide"] = migration_guide
            
            return response
        return wrapper
    return decorator