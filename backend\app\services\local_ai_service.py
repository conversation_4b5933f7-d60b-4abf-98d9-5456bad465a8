"""
LocalAI服务集成 - 基于开源项目LocalAI
GitHub: https://github.com/mudler/LocalAI
用途: 本地部署大语言模型，提供OpenAI兼容API服务
"""

from datetime import datetime
from typing import Any, Dict, List

import httpx


class LocalAIService:
    """LocalAI开源项目集成服务"""

    def __init__(self, base_url: str = "http://localhost:8080"):
        """
        初始化LocalAI服务

        Args:
            base_url: LocalAI服务地址
        """
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=300.0)
        self.available_models = []
        self.use_mock = False  # 标识是否使用模拟服务

    async def health_check(self) -> Dict[str, Any]:
        """检查LocalAI服务健康状态"""
        try:
            response = await self.client.get(f"{self.base_url}/v1/models")
            if response.status_code == 200:
                models = response.json()
                self.available_models = [
                    model["id"] for model in models.get("data", [])
                ]
                return {
                    "status": "healthy",
                    "available_models": self.available_models,
                    "service": "LocalAI",
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": f"HTTP {response.status_code}",
                    "service": "LocalAI",
                }
        except Exception as e:
            return {
                "status": "unreachable",
                "error": str(e),
                "service": "LocalAI",
            }

    async def chat_completion(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 2000,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """
        调用LocalAI的OpenAI兼容聊天完成API

        Args:
            model: 模型名称 (如: "deepseek-chat", "llama2-chat")
            messages: 消息列表
            temperature: 温度参数
            max_tokens: 最大令牌数
            stream: 是否流式输出

        Returns:
            Dict[str, Any]: API响应结果
        """
        # 首先尝试真实的LocalAI服务
        try:
            payload = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": stream,
            }

            response = await self.client.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"},
            )

            if response.status_code == 200:
                result = response.json()
                self.use_mock = False
                return {
                    "success": True,
                    "content": result["choices"][0]["message"]["content"],
                    "model": model,
                    "usage": result.get("usage", {}),
                    "finish_reason": result["choices"][0].get("finish_reason"),
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                # 如果LocalAI返回错误，使用模拟服务
                return await self._use_mock_service(
                    model, messages, temperature, max_tokens
                )

        except Exception:
            # 如果LocalAI不可用，使用模拟服务
            return await self._use_mock_service(
                model, messages, temperature, max_tokens
            )

    async def _use_mock_service(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 2000,
    ) -> Dict[str, Any]:
        """使用模拟服务进行响应"""
        try:
            from app.services.mock_ai_service import mock_ai_service

            self.use_mock = True
            return await mock_ai_service.chat_completion(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
            )
        except Exception as e:
            return {
                "success": False,
                "error": f"模拟服务调用失败: {str(e)}",
                "model": model,
            }

    async def text_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
    ) -> Dict[str, Any]:
        """
        文本补全接口

        Args:
            prompt: 输入提示
            model: 模型名称
            max_tokens: 最大令牌数
            temperature: 温度参数

        Returns:
            Dict[str, Any]: 补全结果
        """
        try:
            payload = {
                "model": model,
                "prompt": prompt,
                "max_tokens": max_tokens,
                "temperature": temperature,
            }

            response = await self.client.post(
                f"{self.base_url}/v1/completions", json=payload
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "text": result["choices"][0]["text"],
                    "model": model,
                    "usage": result.get("usage", {}),
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "model": model,
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"文本补全失败: {str(e)}",
                "model": model,
            }

    async def content_rewrite(
        self,
        original_content: str,
        target_style: str = "专业",
        model: str = "deepseek-chat",
    ) -> Dict[str, Any]:
        """
        内容改写服务

        Args:
            original_content: 原始内容
            target_style: 目标风格
            model: 使用的模型

        Returns:
            Dict[str, Any]: 改写结果
        """
        messages = [
            {
                "role": "system",
                "content": (
                    f"你是一个专业的内容改写助手，能够将内容改写为{target_style}风格，"
                    "保持核心信息不变，提升表达质量。"
                ),
            },
            {
                "role": "user",
                "content": (
                    f"请将以下内容改写为{target_style}风格：\n\n{original_content}"
                ),
            },
        ]

        result = await self.chat_completion(
            model=model, messages=messages, temperature=0.7
        )

        if result["success"]:
            return {
                "success": True,
                "original_content": original_content,
                "rewritten_content": result["content"],
                "target_style": target_style,
                "model": model,
                "timestamp": datetime.now().isoformat(),
            }
        else:
            return {
                "success": False,
                "error": result["error"],
                "original_content": original_content,
                "target_style": target_style,
            }

    async def content_generation(
        self,
        topic: str,
        content_type: str = "文章",
        length: str = "中等",
        style: str = "专业",
        model: str = "deepseek-chat",
    ) -> Dict[str, Any]:
        """
        内容生成服务

        Args:
            topic: 主题
            content_type: 内容类型
            length: 长度要求
            style: 风格要求
            model: 使用的模型

        Returns:
            Dict[str, Any]: 生成结果
        """
        messages = [
            {
                "role": "system",
                "content": f"你是一个专业的内容创作助手，能够根据主题生成高质量的{content_type}内容。",
            },
            {
                "role": "user",
                "content": (
                    f"请以{style}的风格，写一篇关于'{topic}'的{content_type}，"
                    f"长度要求：{length}。"
                ),
            },
        ]

        result = await self.chat_completion(
            model=model, messages=messages, temperature=0.8
        )

        if result["success"]:
            return {
                "success": True,
                "topic": topic,
                "content_type": content_type,
                "generated_content": result["content"],
                "style": style,
                "length": length,
                "model": model,
                "timestamp": datetime.now().isoformat(),
            }
        else:
            return {
                "success": False,
                "error": result["error"],
                "topic": topic,
                "content_type": content_type,
            }

    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()


# 全局LocalAI服务实例
local_ai_service = LocalAIService()


# 快捷函数
async def rewrite_content(content: str, style: str = "专业") -> Dict[str, Any]:
    """快捷内容改写函数"""
    return await local_ai_service.content_rewrite(content, style)


async def generate_content(
    topic: str, content_type: str = "文章", style: str = "专业"
) -> Dict[str, Any]:
    """快捷内容生成函数"""
    return await local_ai_service.content_generation(topic, content_type, style)
