"""Add complete database schema

Revision ID: 836f11f87ea6
Revises: 0345e694f1a8
Create Date: 2025-07-09 09:01:18.441819

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "836f11f87ea6"
down_revision: Union[str, Sequence[str], None] = "0345e694f1a8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "content_analyses",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("analysis_id", sa.String(length=50), nullable=False),
        sa.Column("content_id", sa.String(length=50), nullable=False),
        sa.Column("content_type", sa.String(length=20), nullable=False),
        sa.Column("compliance_level", sa.String(length=20), nullable=False),
        sa.Column("compliance_score", sa.Float(), nullable=False),
        sa.Column("compliance_reasons", sa.JSON(), nullable=True),
        sa.Column("quality_score", sa.Float(), nullable=False),
        sa.Column("engagement_score", sa.Float(), nullable=False),
        sa.Column("recommended_platforms", sa.JSON(), nullable=True),
        sa.Column("platform_scores", sa.JSON(), nullable=True),
        sa.Column("categories", sa.JSON(), nullable=True),
        sa.Column("tags", sa.JSON(), nullable=True),
        sa.Column("analysis_version", sa.String(length=20), nullable=True),
        sa.Column("analyzer_info", sa.JSON(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_analysis_compliance",
        "content_analyses",
        ["compliance_level", "compliance_score"],
        unique=False,
    )
    op.create_index(
        "idx_analysis_content",
        "content_analyses",
        ["content_id", "content_type"],
        unique=False,
    )
    op.create_index(
        "idx_analysis_created_at", "content_analyses", ["created_at"], unique=False
    )
    op.create_index(
        op.f("ix_content_analyses_analysis_id"),
        "content_analyses",
        ["analysis_id"],
        unique=True,
    )
    op.create_index(
        op.f("ix_content_analyses_id"), "content_analyses", ["id"], unique=False
    )
    op.create_table(
        "content_items",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("title", sa.String(length=200), nullable=False),
        sa.Column("content_type", sa.String(length=20), nullable=False),
        sa.Column("original_content", sa.Text(), nullable=True),
        sa.Column("processed_content", sa.Text(), nullable=True),
        sa.Column("status", sa.String(length=20), nullable=True),
        sa.Column("compliance_status", sa.String(length=20), nullable=True),
        sa.Column("compliance_result", sa.Text(), nullable=True),
        sa.Column("file_path", sa.String(length=500), nullable=True),
        sa.Column("thumbnail_path", sa.String(length=500), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_content_items_id"), "content_items", ["id"], unique=False)
    op.create_index(
        op.f("ix_content_items_project_id"),
        "content_items",
        ["project_id"],
        unique=False,
    )
    op.create_table(
        "contents",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("title", sa.String(length=200), nullable=False),
        sa.Column("content_type", sa.String(length=50), nullable=False),
        sa.Column("original_text", sa.Text(), nullable=True),
        sa.Column("rewritten_text", sa.Text(), nullable=True),
        sa.Column("creator_id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=True),
        sa.Column("status", sa.String(length=20), nullable=True),
        sa.Column("compliance_score", sa.Float(), nullable=True),
        sa.Column("compliance_issues", sa.Text(), nullable=True),
        sa.Column("content_metadata", sa.Text(), nullable=True),
        sa.Column("rewrite_metadata", sa.Text(), nullable=True),
        sa.Column("media_files", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("published_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_contents_content_type"), "contents", ["content_type"], unique=False
    )
    op.create_index(
        op.f("ix_contents_creator_id"), "contents", ["creator_id"], unique=False
    )
    op.create_index(op.f("ix_contents_id"), "contents", ["id"], unique=False)
    op.create_index(
        op.f("ix_contents_project_id"), "contents", ["project_id"], unique=False
    )
    op.create_index(op.f("ix_contents_title"), "contents", ["title"], unique=False)
    op.create_table(
        "distribution_tasks",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("content_item_id", sa.Integer(), nullable=False),
        sa.Column("platform", sa.String(length=50), nullable=False),
        sa.Column("status", sa.String(length=20), nullable=True),
        sa.Column("platform_config", sa.Text(), nullable=True),
        sa.Column("platform_id", sa.String(length=100), nullable=True),
        sa.Column("platform_url", sa.String(length=500), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("distributed_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_distribution_tasks_content_item_id"),
        "distribution_tasks",
        ["content_item_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_distribution_tasks_id"), "distribution_tasks", ["id"], unique=False
    )
    op.create_table(
        "distributions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("content_id", sa.Integer(), nullable=False),
        sa.Column("platform", sa.String(length=50), nullable=False),
        sa.Column("creator_id", sa.Integer(), nullable=False),
        sa.Column("title", sa.String(length=200), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("scheduled_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("published_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("status", sa.String(length=20), nullable=True),
        sa.Column("platform_config", sa.Text(), nullable=True),
        sa.Column("result_data", sa.Text(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_distributions_content_id"),
        "distributions",
        ["content_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_distributions_creator_id"),
        "distributions",
        ["creator_id"],
        unique=False,
    )
    op.create_index(op.f("ix_distributions_id"), "distributions", ["id"], unique=False)
    op.create_index(
        op.f("ix_distributions_platform"), "distributions", ["platform"], unique=False
    )
    op.create_table(
        "project_statuses",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_name", sa.String(length=100), nullable=False),
        sa.Column("project_type", sa.String(length=50), nullable=False),
        sa.Column("github_url", sa.String(length=500), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("version", sa.String(length=50), nullable=True),
        sa.Column("last_check_status", sa.String(length=50), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("package_names", sa.JSON(), nullable=True),
        sa.Column("dependencies", sa.JSON(), nullable=True),
        sa.Column("config_data", sa.JSON(), nullable=True),
        sa.Column("check_count", sa.Integer(), nullable=True),
        sa.Column("success_count", sa.Integer(), nullable=True),
        sa.Column("failure_count", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("last_checked_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("project_name"),
    )
    op.create_index(
        "idx_project_name_status",
        "project_statuses",
        ["project_name", "status"],
        unique=False,
    )
    op.create_index(
        "idx_project_type", "project_statuses", ["project_type"], unique=False
    )
    op.create_index(
        "idx_project_updated_at", "project_statuses", ["updated_at"], unique=False
    )
    op.create_index(
        op.f("ix_project_statuses_id"), "project_statuses", ["id"], unique=False
    )
    op.create_table(
        "projects",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=100), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("owner_id", sa.Integer(), nullable=False),
        sa.Column("status", sa.String(length=20), nullable=True),
        sa.Column("config", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_projects_id"), "projects", ["id"], unique=False)
    op.create_index(op.f("ix_projects_name"), "projects", ["name"], unique=False)
    op.create_index(
        op.f("ix_projects_owner_id"), "projects", ["owner_id"], unique=False
    )
    op.create_table(
        "system_metrics",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("metric_name", sa.String(length=100), nullable=False),
        sa.Column("metric_type", sa.String(length=50), nullable=False),
        sa.Column("metric_value", sa.Float(), nullable=False),
        sa.Column("metric_labels", sa.JSON(), nullable=True),
        sa.Column("service_name", sa.String(length=100), nullable=False),
        sa.Column("instance_id", sa.String(length=100), nullable=True),
        sa.Column("timestamp", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_metrics_name_time",
        "system_metrics",
        ["metric_name", "timestamp"],
        unique=False,
    )
    op.create_index(
        "idx_metrics_service",
        "system_metrics",
        ["service_name", "timestamp"],
        unique=False,
    )
    op.create_index(
        op.f("ix_system_metrics_id"), "system_metrics", ["id"], unique=False
    )
    op.create_table(
        "users",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("username", sa.String(length=50), nullable=False),
        sa.Column("email", sa.String(length=100), nullable=False),
        sa.Column("hashed_password", sa.String(length=255), nullable=False),
        sa.Column("full_name", sa.String(length=100), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column("is_superuser", sa.Boolean(), nullable=True),
        sa.Column("avatar_url", sa.String(length=500), nullable=True),
        sa.Column("bio", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("last_login", sa.DateTime(timezone=True), nullable=True),
        sa.Column("user_id", sa.String(length=50), nullable=False),
        sa.Column("password_hash", sa.String(length=255), nullable=False),
        sa.Column("role", sa.String(length=20), nullable=False),
        sa.Column("status", sa.String(length=20), nullable=False),
        sa.Column("display_name", sa.String(length=100), nullable=True),
        sa.Column("video_count", sa.Integer(), nullable=True),
        sa.Column("total_views", sa.Integer(), nullable=True),
        sa.Column("total_likes", sa.Integer(), nullable=True),
        sa.Column("last_login_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("idx_user_email_status", "users", ["email", "status"], unique=False)
    op.create_index("idx_user_role_status", "users", ["role", "status"], unique=False)
    op.create_index(op.f("ix_users_email"), "users", ["email"], unique=True)
    op.create_index(op.f("ix_users_id"), "users", ["id"], unique=False)
    op.create_index(op.f("ix_users_user_id"), "users", ["user_id"], unique=True)
    op.create_index(op.f("ix_users_username"), "users", ["username"], unique=True)
    op.create_table(
        "videos",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("video_id", sa.String(length=50), nullable=False),
        sa.Column("title", sa.String(length=200), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("tags", sa.JSON(), nullable=True),
        sa.Column("original_url", sa.String(length=1000), nullable=True),
        sa.Column("file_path", sa.String(length=500), nullable=True),
        sa.Column("file_size", sa.Integer(), nullable=True),
        sa.Column("duration", sa.Float(), nullable=True),
        sa.Column("format", sa.String(length=20), nullable=True),
        sa.Column("resolution", sa.String(length=20), nullable=True),
        sa.Column("bitrate", sa.Integer(), nullable=True),
        sa.Column("fps", sa.Float(), nullable=True),
        sa.Column("status", sa.String(length=20), nullable=True),
        sa.Column("processing_progress", sa.Float(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("compliance_level", sa.String(length=20), nullable=True),
        sa.Column("compliance_score", sa.Float(), nullable=True),
        sa.Column("quality_score", sa.Float(), nullable=True),
        sa.Column("engagement_score", sa.Float(), nullable=True),
        sa.Column("recommended_platforms", sa.JSON(), nullable=True),
        sa.Column("categories", sa.JSON(), nullable=True),
        sa.Column("view_count", sa.Integer(), nullable=True),
        sa.Column("like_count", sa.Integer(), nullable=True),
        sa.Column("share_count", sa.Integer(), nullable=True),
        sa.Column("download_count", sa.Integer(), nullable=True),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("processed_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_video_compliance",
        "videos",
        ["compliance_level", "compliance_score"],
        unique=False,
    )
    op.create_index("idx_video_created_at", "videos", ["created_at"], unique=False)
    op.create_index(
        "idx_video_user_status", "videos", ["user_id", "status"], unique=False
    )
    op.create_index(op.f("ix_videos_id"), "videos", ["id"], unique=False)
    op.create_index(op.f("ix_videos_video_id"), "videos", ["video_id"], unique=True)
    op.create_table(
        "tasks",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("task_id", sa.String(length=50), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("task_type", sa.String(length=50), nullable=False),
        sa.Column("priority", sa.String(length=20), nullable=True),
        sa.Column("status", sa.String(length=20), nullable=True),
        sa.Column("progress", sa.Float(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("input_data", sa.JSON(), nullable=True),
        sa.Column("output_data", sa.JSON(), nullable=True),
        sa.Column("task_metadata", sa.JSON(), nullable=True),
        sa.Column("worker_id", sa.String(length=100), nullable=True),
        sa.Column("execution_time", sa.Float(), nullable=True),
        sa.Column("retry_count", sa.Integer(), nullable=True),
        sa.Column("max_retries", sa.Integer(), nullable=True),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("video_id", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("started_at", sa.DateTime(), nullable=True),
        sa.Column("completed_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["video_id"],
            ["videos.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("idx_task_created_at", "tasks", ["created_at"], unique=False)
    op.create_index(
        "idx_task_priority_status", "tasks", ["priority", "status"], unique=False
    )
    op.create_index(
        "idx_task_type_status", "tasks", ["task_type", "status"], unique=False
    )
    op.create_index(
        "idx_task_user_status", "tasks", ["user_id", "status"], unique=False
    )
    op.create_index(op.f("ix_tasks_id"), "tasks", ["id"], unique=False)
    op.create_index(op.f("ix_tasks_task_id"), "tasks", ["task_id"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_tasks_task_id"), table_name="tasks")
    op.drop_index(op.f("ix_tasks_id"), table_name="tasks")
    op.drop_index("idx_task_user_status", table_name="tasks")
    op.drop_index("idx_task_type_status", table_name="tasks")
    op.drop_index("idx_task_priority_status", table_name="tasks")
    op.drop_index("idx_task_created_at", table_name="tasks")
    op.drop_table("tasks")
    op.drop_index(op.f("ix_videos_video_id"), table_name="videos")
    op.drop_index(op.f("ix_videos_id"), table_name="videos")
    op.drop_index("idx_video_user_status", table_name="videos")
    op.drop_index("idx_video_created_at", table_name="videos")
    op.drop_index("idx_video_compliance", table_name="videos")
    op.drop_table("videos")
    op.drop_index(op.f("ix_users_username"), table_name="users")
    op.drop_index(op.f("ix_users_user_id"), table_name="users")
    op.drop_index(op.f("ix_users_id"), table_name="users")
    op.drop_index(op.f("ix_users_email"), table_name="users")
    op.drop_index("idx_user_role_status", table_name="users")
    op.drop_index("idx_user_email_status", table_name="users")
    op.drop_table("users")
    op.drop_index(op.f("ix_system_metrics_id"), table_name="system_metrics")
    op.drop_index("idx_metrics_service", table_name="system_metrics")
    op.drop_index("idx_metrics_name_time", table_name="system_metrics")
    op.drop_table("system_metrics")
    op.drop_index(op.f("ix_projects_owner_id"), table_name="projects")
    op.drop_index(op.f("ix_projects_name"), table_name="projects")
    op.drop_index(op.f("ix_projects_id"), table_name="projects")
    op.drop_table("projects")
    op.drop_index(op.f("ix_project_statuses_id"), table_name="project_statuses")
    op.drop_index("idx_project_updated_at", table_name="project_statuses")
    op.drop_index("idx_project_type", table_name="project_statuses")
    op.drop_index("idx_project_name_status", table_name="project_statuses")
    op.drop_table("project_statuses")
    op.drop_index(op.f("ix_distributions_platform"), table_name="distributions")
    op.drop_index(op.f("ix_distributions_id"), table_name="distributions")
    op.drop_index(op.f("ix_distributions_creator_id"), table_name="distributions")
    op.drop_index(op.f("ix_distributions_content_id"), table_name="distributions")
    op.drop_table("distributions")
    op.drop_index(op.f("ix_distribution_tasks_id"), table_name="distribution_tasks")
    op.drop_index(
        op.f("ix_distribution_tasks_content_item_id"), table_name="distribution_tasks"
    )
    op.drop_table("distribution_tasks")
    op.drop_index(op.f("ix_contents_title"), table_name="contents")
    op.drop_index(op.f("ix_contents_project_id"), table_name="contents")
    op.drop_index(op.f("ix_contents_id"), table_name="contents")
    op.drop_index(op.f("ix_contents_creator_id"), table_name="contents")
    op.drop_index(op.f("ix_contents_content_type"), table_name="contents")
    op.drop_table("contents")
    op.drop_index(op.f("ix_content_items_project_id"), table_name="content_items")
    op.drop_index(op.f("ix_content_items_id"), table_name="content_items")
    op.drop_table("content_items")
    op.drop_index(op.f("ix_content_analyses_id"), table_name="content_analyses")
    op.drop_index(
        op.f("ix_content_analyses_analysis_id"), table_name="content_analyses"
    )
    op.drop_index("idx_analysis_created_at", table_name="content_analyses")
    op.drop_index("idx_analysis_content", table_name="content_analyses")
    op.drop_index("idx_analysis_compliance", table_name="content_analyses")
    op.drop_table("content_analyses")
    # ### end Alembic commands ###
