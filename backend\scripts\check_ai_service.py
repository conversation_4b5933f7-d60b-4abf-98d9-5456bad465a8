#!/usr/bin/env python3
"""
AI模型服务状态检查器
快速检查Ollama和DeepSeek模型的安装状态
"""

import json
import subprocess
import sys
from datetime import datetime


def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, shell=True, capture_output=True, text=True, timeout=10
        )
        return (
            result.returncode == 0,
            result.stdout.strip(),
            result.stderr.strip(),
        )
    except subprocess.TimeoutExpired:
        return False, "", "命令超时"
    except Exception as e:
        return False, "", str(e)


def check_ollama_installation():
    """检查Ollama安装状态"""
    print("🔍 检查Ollama安装状态...")

    # 检查ollama命令是否可用
    success, stdout, stderr = run_command("ollama version")
    if success:
        print(f"✅ Ollama已安装: {stdout}")
        return True
    else:
        print(f"❌ Ollama未安装或未在PATH中: {stderr}")
        return False


def check_ollama_service():
    """检查Ollama服务状态"""
    print("🔍 检查Ollama服务状态...")

    # 尝试列出模型来检查服务是否运行
    success, stdout, stderr = run_command("ollama list")
    if success:
        print("✅ Ollama服务正在运行")
        return True, stdout
    else:
        print(f"❌ Ollama服务未运行: {stderr}")
        return False, ""


def check_deepseek_models():
    """检查DeepSeek模型"""
    print("🔍 检查DeepSeek模型...")

    success, models_output = check_ollama_service()
    if not success:
        return []

    deepseek_models = []
    for line in models_output.split("\n"):
        if "deepseek" in line.lower():
            deepseek_models.append(line.strip())

    if deepseek_models:
        print("✅ 发现DeepSeek模型:")
        for model in deepseek_models:
            print(f"   - {model}")
    else:
        print("❌ 未发现DeepSeek模型")

    return deepseek_models


def test_model_inference():
    """测试模型推理功能"""
    print("🔍 测试模型推理...")

    # 获取可用的DeepSeek模型
    models = check_deepseek_models()
    if not models:
        print("❌ 没有可用的DeepSeek模型进行测试")
        return False

    # 使用第一个模型进行简单测试
    model_name = models[0].split()[0]  # 获取模型名称
    test_prompt = "你好，请回复'测试成功'"

    print(f"🧪 使用模型 {model_name} 进行测试...")
    cmd = f'ollama run {model_name} "{test_prompt}"'
    success, stdout, stderr = run_command(cmd)

    if success and "测试成功" in stdout:
        print("✅ 模型推理测试成功")
        return True
    else:
        print(f"❌ 模型推理测试失败: {stderr}")
        return False


def generate_status_report():
    """生成状态报告"""
    print("\n" + "=" * 50)
    print("📊 AI模型服务状态报告")
    print("=" * 50)

    status = {
        "timestamp": datetime.now().isoformat(),
        "ollama_installed": False,
        "ollama_running": False,
        "deepseek_models": [],
        "inference_working": False,
        "overall_status": "❌ 未就绪",
    }

    # 检查各项状态
    status["ollama_installed"] = check_ollama_installation()

    if status["ollama_installed"]:
        running, _ = check_ollama_service()
        status["ollama_running"] = running

        if running:
            status["deepseek_models"] = check_deepseek_models()

            if status["deepseek_models"]:
                status["inference_working"] = test_model_inference()

    # 判断总体状态
    if (
        status["ollama_installed"]
        and status["ollama_running"]
        and status["deepseek_models"]
        and status["inference_working"]
    ):
        status["overall_status"] = "✅ 完全就绪"
    elif status["ollama_installed"] and status["ollama_running"]:
        status["overall_status"] = "⚠️ 部分就绪"
    else:
        status["overall_status"] = "❌ 未就绪"

    # 显示摘要
    print("\n📋 状态摘要:")
    print(f"   Ollama安装: {'✅' if status['ollama_installed'] else '❌'}")
    print(f"   Ollama服务: {'✅' if status['ollama_running'] else '❌'}")
    print(f"   DeepSeek模型: {len(status['deepseek_models'])}个")
    print(f"   推理功能: {'✅' if status['inference_working'] else '❌'}")
    print(f"   总体状态: {status['overall_status']}")

    # 保存报告
    with open("ai_model_service_status.json", "w", encoding="utf-8") as f:
        json.dump(status, f, ensure_ascii=False, indent=2)

    print("\n📄 详细报告已保存: ai_model_service_status.json")

    # 提供建议
    if status["overall_status"] != "✅ 完全就绪":
        print("\n💡 建议操作:")
        if not status["ollama_installed"]:
            print("   1. 安装Ollama: https://ollama.ai/download")
        elif not status["ollama_running"]:
            print("   1. 启动Ollama服务: ollama serve")
        elif not status["deepseek_models"]:
            print("   1. 下载DeepSeek模型: ollama pull deepseek-v3:7b-q4_0")
        else:
            print("   1. 检查网络连接和模型完整性")

    return status


if __name__ == "__main__":
    print("🚀 AI模型服务状态检查器")
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)

    try:
        status = generate_status_report()

        # 返回适当的退出码
        if status["overall_status"] == "✅ 完全就绪":
            sys.exit(0)
        else:
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\n⚠️ 检查被用户中断")
        sys.exit(2)
    except Exception as e:
        print(f"\n❌ 检查过程出错: {e}")
        sys.exit(3)
