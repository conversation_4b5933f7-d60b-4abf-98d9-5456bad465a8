#!/bin/bash

# 统一登录页面功能测试脚本
echo "🎬 二创短视频分发系统 - 统一登录页面功能测试"
echo "============================================================"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0

# 测试函数
test_api() {
    local test_name="$1"
    local url="$2"
    local method="$3"
    local data="$4"
    local expected_status="$5"
    
    echo "📋 测试: $test_name"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" "$url")
        status_code="${response: -3}"
        body="${response%???}"
    else
        response=$(curl -s -w "%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$url")
        status_code="${response: -3}"
        body="${response%???}"
    fi
    
    if [ "$status_code" = "$expected_status" ]; then
        echo "✅ $test_name - 状态码: $status_code"
        if [ "$method" = "POST" ] && [ "$status_code" = "200" ]; then
            echo "   响应: $(echo "$body" | head -c 100)..."
        fi
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo "❌ $test_name - 期望状态码: $expected_status, 实际: $status_code"
        echo "   响应: $body"
    fi
    echo ""
}

# 1. 测试前端登录页面加载
echo "🧪 测试前端登录页面..."
test_api "前端登录页面加载" "http://localhost:3000/login" "GET" "" "200"

# 2. 测试后端登录页面加载
echo "🧪 测试后端登录页面..."
test_api "后端登录页面加载" "http://localhost:8000/login" "GET" "" "200"

# 3. 测试管理员登录
echo "🧪 测试管理员登录..."
admin_data='{"username":"admin","password":"admin123","userType":"admin"}'
test_api "管理员登录" "http://localhost:8000/api/auth/login" "POST" "$admin_data" "200"

# 4. 测试普通用户登录
echo "🧪 测试普通用户登录..."
user_data='{"username":"testuser","password":"password123","userType":"user"}'
test_api "普通用户登录" "http://localhost:8000/api/auth/login" "POST" "$user_data" "200"

# 5. 测试邮箱登录
echo "🧪 测试邮箱登录..."
email_data='{"username":"<EMAIL>","password":"password123","userType":"user"}'
test_api "邮箱登录" "http://localhost:8000/api/auth/login" "POST" "$email_data" "200"

# 6. 测试错误密码
echo "🧪 测试错误密码..."
wrong_data='{"username":"admin","password":"wrongpassword","userType":"admin"}'
test_api "错误密码登录" "http://localhost:8000/api/auth/login" "POST" "$wrong_data" "401"

# 7. 测试权限控制
echo "🧪 测试权限控制..."
permission_data='{"username":"testuser","password":"password123","userType":"admin"}'
test_api "普通用户尝试管理员登录" "http://localhost:8000/api/auth/login" "POST" "$permission_data" "403"

# 8. 测试空数据
echo "🧪 测试空数据..."
empty_data='{"username":"","password":"","userType":"user"}'
test_api "空数据登录" "http://localhost:8000/api/auth/login" "POST" "$empty_data" "400"

# 9. 测试API文档访问
echo "🧪 测试API文档..."
test_api "API文档访问" "http://localhost:8000/docs" "GET" "" "200"

# 10. 测试健康检查
echo "🧪 测试健康检查..."
test_api "健康检查" "http://localhost:8000/health" "GET" "" "200"

# 输出测试结果
echo "============================================================"
echo "📊 测试结果汇总:"
echo "✅ 通过测试: $PASSED_TESTS/$TOTAL_TESTS"
echo "❌ 失败测试: $((TOTAL_TESTS - PASSED_TESTS))/$TOTAL_TESTS"
echo "📈 成功率: $(( (PASSED_TESTS * 100) / TOTAL_TESTS ))%"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo ""
    echo "🎉 所有登录功能测试通过！"
    echo "✨ 统一登录页面功能完整，前后端协同工作正常"
    echo ""
    echo "🔍 功能验证清单:"
    echo "  ✅ 前端登录页面正常加载"
    echo "  ✅ 后端登录页面正常加载"
    echo "  ✅ 管理员登录功能正常"
    echo "  ✅ 普通用户登录功能正常"
    echo "  ✅ 邮箱登录功能正常"
    echo "  ✅ 错误处理机制正常"
    echo "  ✅ 权限控制机制正常"
    echo "  ✅ 数据验证机制正常"
    echo "  ✅ API文档访问正常"
    echo "  ✅ 系统健康检查正常"
    exit 0
else
    echo ""
    echo "⚠️  部分测试失败，请检查相关功能"
    exit 1
fi
