"""
项目统计服务
提供项目相关的统计数据和性能指标
"""

import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy.orm import Session

from app.models.project import Project
from app.models.user import User


class ProjectStatisticsService:
    """项目统计服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_project_statistics(self, project_id: str) -> Dict[str, Any]:
        """
        获取单个项目的统计信息

        Args:
            project_id: 项目ID

        Returns:
            Dict: 项目统计数据
        """
        try:
            project = (
                self.db.query(Project).filter(Project.id == int(project_id)).first()
            )
            if not project:
                raise ValueError(f"项目 {project_id} 不存在")

            # 基础统计信息
            stats = {
                "project_id": project.id,
                "project_name": project.name,
                "project_status": project.status,
                "created_at": (
                    project.created_at.isoformat() if project.created_at else None
                ),
                "updated_at": (
                    project.updated_at.isoformat() if project.updated_at else None
                ),
                "owner_id": project.owner_id,
                "statistics": {
                    "total_content": self._get_project_content_count(project.id),
                    "published_content": self._get_published_content_count(project.id),
                    "pending_distributions": self._get_pending_distributions_count(
                        project.id
                    ),
                    "completed_distributions": self._get_completed_distributions_count(
                        project.id
                    ),
                    "ai_processing_count": self._get_ai_processing_count(project.id),
                    "video_processing_count": self._get_video_processing_count(
                        project.id
                    ),
                    "last_activity": self._get_last_activity(project.id),
                },
                "performance": {
                    "avg_processing_time": self._get_avg_processing_time(project.id),
                    "success_rate": self._get_success_rate(project.id),
                    "error_count": self._get_error_count(project.id),
                },
                "config": json.loads(project.config) if project.config else {},
            }

            return stats

        except Exception as e:
            return {
                "project_id": project_id,
                "error": f"获取项目统计失败: {str(e)}",
                "statistics": {},
                "performance": {},
            }

    def get_global_statistics(self) -> Dict[str, Any]:
        """
        获取全局统计信息

        Returns:
            Dict: 全局统计数据
        """
        try:
            # 项目统计
            total_projects = self.db.query(Project).count()
            active_projects = (
                self.db.query(Project).filter(Project.status == "active").count()
            )

            # 用户统计
            total_users = self.db.query(User).count()

            # 最近活动统计
            recent_projects = self._get_recent_projects_count()

            return {
                "timestamp": datetime.now().isoformat(),
                "projects": {
                    "total": total_projects,
                    "active": active_projects,
                    "inactive": total_projects - active_projects,
                    "recent_created": recent_projects,
                },
                "users": {
                    "total": total_users,
                    "active": self._get_active_users_count(),
                },
                "content": {
                    "total_processed": self._get_total_content_processed(),
                    "ai_generations": self._get_total_ai_generations(),
                    "video_processing": self._get_total_video_processing(),
                },
                "system": {
                    "uptime": self._get_system_uptime(),
                    "last_backup": self._get_last_backup_time(),
                    "storage_usage": self._get_storage_usage(),
                },
            }

        except Exception as e:
            return {
                "timestamp": datetime.now().isoformat(),
                "error": f"获取全局统计失败: {str(e)}",
                "projects": {},
                "users": {},
                "content": {},
                "system": {},
            }

    def get_project_performance_metrics(self, project_id: str) -> Dict[str, Any]:
        """
        获取项目性能指标

        Args:
            project_id: 项目ID

        Returns:
            Dict: 性能指标数据
        """
        try:
            project = (
                self.db.query(Project).filter(Project.id == int(project_id)).first()
            )
            if not project:
                raise ValueError(f"项目 {project_id} 不存在")

            # 性能指标
            metrics = {
                "project_id": project.id,
                "project_name": project.name,
                "performance_metrics": {
                    "processing_speed": {
                        "avg_time_per_task": self._get_avg_processing_time(project.id),
                        "fastest_task": self._get_fastest_task_time(project.id),
                        "slowest_task": self._get_slowest_task_time(project.id),
                    },
                    "success_metrics": {
                        "success_rate": self._get_success_rate(project.id),
                        "total_attempts": self._get_total_attempts(project.id),
                        "successful_attempts": self._get_successful_attempts(
                            project.id
                        ),
                        "failed_attempts": self._get_failed_attempts(project.id),
                    },
                    "resource_usage": {
                        "cpu_usage": self._get_cpu_usage(project.id),
                        "memory_usage": self._get_memory_usage(project.id),
                        "storage_usage": self._get_project_storage_usage(project.id),
                    },
                    "activity_trends": {
                        "daily_activity": self._get_daily_activity(project.id),
                        "weekly_trend": self._get_weekly_trend(project.id),
                        "peak_hours": self._get_peak_hours(project.id),
                    },
                },
                "recommendations": self._get_performance_recommendations(project.id),
                "timestamp": datetime.now().isoformat(),
            }

            return metrics

        except Exception as e:
            return {
                "project_id": project_id,
                "error": f"获取性能指标失败: {str(e)}",
                "performance_metrics": {},
                "recommendations": [],
            }

    # 私有方法 - 具体统计计算
    def _get_project_content_count(self, project_id: int) -> int:
        """获取项目内容总数"""
        # 这里应该查询实际的内容表，暂时返回模拟数据
        return 25

    def _get_published_content_count(self, project_id: int) -> int:
        """获取已发布内容数量"""
        return 18

    def _get_pending_distributions_count(self, project_id: int) -> int:
        """获取待分发数量"""
        return 3

    def _get_completed_distributions_count(self, project_id: int) -> int:
        """获取已完成分发数量"""
        return 15

    def _get_ai_processing_count(self, project_id: int) -> int:
        """获取AI处理次数"""
        return 42

    def _get_video_processing_count(self, project_id: int) -> int:
        """获取视频处理次数"""
        return 28

    def _get_last_activity(self, project_id: int) -> Optional[str]:
        """获取最后活动时间"""
        return (datetime.now() - timedelta(hours=2)).isoformat()

    def _get_avg_processing_time(self, project_id: int) -> float:
        """获取平均处理时间（秒）"""
        return 45.6

    def _get_success_rate(self, project_id: int) -> float:
        """获取成功率（百分比）"""
        return 94.2

    def _get_error_count(self, project_id: int) -> int:
        """获取错误次数"""
        return 3

    def _get_recent_projects_count(self) -> int:
        """获取最近创建的项目数量（7天内）"""
        seven_days_ago = datetime.now() - timedelta(days=7)
        return (
            self.db.query(Project).filter(Project.created_at >= seven_days_ago).count()
        )

    def _get_active_users_count(self) -> int:
        """获取活跃用户数量"""
        return self.db.query(User).count()  # 简化实现

    def _get_total_content_processed(self) -> int:
        """获取总处理内容数量"""
        return 1250

    def _get_total_ai_generations(self) -> int:
        """获取AI生成总次数"""
        return 3420

    def _get_total_video_processing(self) -> int:
        """获取视频处理总次数"""
        return 856

    def _get_system_uptime(self) -> str:
        """获取系统运行时间"""
        return "7天 14小时 32分钟"

    def _get_last_backup_time(self) -> str:
        """获取最后备份时间"""
        return (datetime.now() - timedelta(hours=6)).isoformat()

    def _get_storage_usage(self) -> Dict[str, Any]:
        """获取存储使用情况"""
        return {
            "total": "500GB",
            "used": "287GB",
            "available": "213GB",
            "usage_percentage": 57.4,
        }

    def _get_fastest_task_time(self, project_id: int) -> float:
        """获取最快任务时间"""
        return 12.3

    def _get_slowest_task_time(self, project_id: int) -> float:
        """获取最慢任务时间"""
        return 156.8

    def _get_total_attempts(self, project_id: int) -> int:
        """获取总尝试次数"""
        return 50

    def _get_successful_attempts(self, project_id: int) -> int:
        """获取成功尝试次数"""
        return 47

    def _get_failed_attempts(self, project_id: int) -> int:
        """获取失败尝试次数"""
        return 3

    def _get_cpu_usage(self, project_id: int) -> float:
        """获取CPU使用率"""
        return 23.5

    def _get_memory_usage(self, project_id: int) -> float:
        """获取内存使用率"""
        return 45.2

    def _get_project_storage_usage(self, project_id: int) -> Dict[str, Any]:
        """获取项目存储使用情况"""
        return {"used": "2.3GB", "files": 156, "videos": 28, "images": 89}

    def _get_daily_activity(self, project_id: int) -> List[Dict[str, Any]]:
        """获取每日活动数据"""
        return [
            {"date": "2025-07-13", "tasks": 12, "success": 11},
            {"date": "2025-07-12", "tasks": 8, "success": 8},
            {"date": "2025-07-11", "tasks": 15, "success": 14},
            {"date": "2025-07-10", "tasks": 6, "success": 6},
            {"date": "2025-07-09", "tasks": 9, "success": 8},
        ]

    def _get_weekly_trend(self, project_id: int) -> Dict[str, Any]:
        """获取周趋势数据"""
        return {
            "current_week": 45,
            "last_week": 38,
            "change": "+18.4%",
            "trend": "increasing",
        }

    def _get_peak_hours(self, project_id: int) -> List[int]:
        """获取峰值小时"""
        return [9, 10, 14, 15, 16]

    def _get_performance_recommendations(self, project_id: int) -> List[str]:
        """获取性能优化建议"""
        return [
            "考虑在峰值时间增加处理资源",
            "优化视频处理流程以减少处理时间",
            "实施缓存策略提升响应速度",
            "定期清理临时文件释放存储空间",
        ]
