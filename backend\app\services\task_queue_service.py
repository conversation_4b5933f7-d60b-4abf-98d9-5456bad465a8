#!/usr/bin/env python3
"""
AI视频内容创作系统 - 任务队列服务
提供后台任务处理、任务调度、进度跟踪等功能
"""

import asyncio
import logging
import threading
import uuid
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """任务状态"""

    PENDING = "pending"  # 待处理
    RUNNING = "running"  # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消


class TaskPriority(str, Enum):
    """任务优先级"""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class Task(BaseModel):
    """任务模型"""

    task_id: str = Field(..., description="任务ID")
    name: str = Field(..., description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")

    # 任务配置
    task_type: str = Field(..., description="任务类型")
    priority: TaskPriority = Field(default=TaskPriority.NORMAL)
    status: TaskStatus = Field(default=TaskStatus.PENDING)

    # 任务数据
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Optional[Dict[str, Any]] = Field(None)
    error_message: Optional[str] = Field(None)

    # 进度信息
    progress: float = Field(default=0.0, description="进度百分比 0-100")
    steps_total: int = Field(default=1, description="总步骤数")
    steps_completed: int = Field(default=0, description="已完成步骤数")

    # 时间信息
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = Field(None)
    completed_at: Optional[datetime] = Field(None)
    updated_at: datetime = Field(default_factory=datetime.now)

    # 元数据
    user_id: Optional[str] = Field(None, description="创建用户ID")
    retry_count: int = Field(default=0, description="重试次数")
    max_retries: int = Field(default=3, description="最大重试次数")


class TaskResult(BaseModel):
    """任务结果"""

    task_id: str
    status: TaskStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    duration: Optional[float] = None


class TaskWorker:
    """任务执行器"""

    def __init__(self, worker_id: str):
        self.worker_id = worker_id
        self.logger = logging.getLogger(f"worker.{worker_id}")
        self.is_busy = False
        self.current_task: Optional[Task] = None
        self.stop_event = threading.Event()

    async def execute_task(
        self, task: Task, task_handlers: Dict[str, Callable]
    ) -> TaskResult:
        """执行任务"""
        self.is_busy = True
        self.current_task = task

        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            task.updated_at = datetime.now()

            self.logger.info(f"开始执行任务: {task.task_id} ({task.task_type})")

            # 获取任务处理器
            handler = task_handlers.get(task.task_type)
            if not handler:
                raise ValueError(f"未找到任务处理器: {task.task_type}")

            # 执行任务
            start_time = datetime.now()

            if asyncio.iscoroutinefunction(handler):
                result = await handler(task)
            else:
                result = handler(task)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # 更新任务状态
            task.status = TaskStatus.COMPLETED
            task.completed_at = end_time
            task.output_data = result
            task.progress = 100.0
            task.steps_completed = task.steps_total
            task.updated_at = end_time

            self.logger.info(f"任务执行成功: {task.task_id} ({duration:.2f}s)")

            return TaskResult(
                task_id=task.task_id,
                status=TaskStatus.COMPLETED,
                result=result,
                duration=duration,
            )

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"任务执行失败: {task.task_id} - {error_msg}")

            # 更新任务状态
            task.status = TaskStatus.FAILED
            task.error_message = error_msg
            task.completed_at = datetime.now()
            task.updated_at = datetime.now()

            return TaskResult(
                task_id=task.task_id, status=TaskStatus.FAILED, error=error_msg
            )

        finally:
            self.is_busy = False
            self.current_task = None


class TaskQueue:
    """任务队列"""

    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.queues = {
            TaskPriority.URGENT: [],
            TaskPriority.HIGH: [],
            TaskPriority.NORMAL: [],
            TaskPriority.LOW: [],
        }
        self.lock = threading.Lock()

    def add_task(self, task: Task) -> bool:
        """添加任务到队列"""
        with self.lock:
            total_tasks = sum(len(q) for q in self.queues.values())
            if total_tasks >= self.max_size:
                return False

            self.queues[task.priority].append(task)
            return True

    def get_next_task(self) -> Optional[Task]:
        """获取下一个待执行的任务"""
        with self.lock:
            # 按优先级顺序获取任务
            for priority in [
                TaskPriority.URGENT,
                TaskPriority.HIGH,
                TaskPriority.NORMAL,
                TaskPriority.LOW,
            ]:
                if self.queues[priority]:
                    return self.queues[priority].pop(0)
            return None

    def get_queue_size(self) -> Dict[str, int]:
        """获取队列大小"""
        with self.lock:
            return {
                priority.value: len(self.queues[priority]) for priority in TaskPriority
            }

    def clear(self):
        """清空队列"""
        with self.lock:
            for queue in self.queues.values():
                queue.clear()


class TaskQueueService:
    """任务队列服务"""

    def __init__(self, max_workers: int = 4, max_queue_size: int = 1000):
        self.logger = logging.getLogger(__name__)
        self.max_workers = max_workers

        # 任务存储
        self.tasks: Dict[str, Task] = {}
        self.task_queue = TaskQueue(max_queue_size)

        # 工作器
        self.workers: List[TaskWorker] = []
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

        # 任务处理器注册表
        self.task_handlers: Dict[str, Callable] = {}

        # 服务状态
        self.is_running = False
        self.stop_event = threading.Event()

        # 初始化工作器
        self._init_workers()

        # 注册默认任务处理器
        self._register_default_handlers()

    def _init_workers(self):
        """初始化工作器"""
        for i in range(self.max_workers):
            worker = TaskWorker(f"worker-{i}")
            self.workers.append(worker)

    def _register_default_handlers(self):
        """注册默认任务处理器"""

        async def video_download_handler(task: Task) -> Dict[str, Any]:
            """视频下载任务处理器"""
            url = task.input_data.get("url")
            quality = task.input_data.get("quality", "720p")

            # 模拟下载过程
            for i in range(5):
                if self.stop_event.is_set():
                    raise Exception("任务被取消")

                await asyncio.sleep(1)  # 模拟下载时间
                task.progress = (i + 1) * 20
                task.steps_completed = i + 1
                task.updated_at = datetime.now()

            return {
                "url": url,
                "quality": quality,
                "file_path": f"/downloads/video_{task.task_id}.mp4",
                "file_size": 1024 * 1024 * 50,  # 50MB
            }

        async def content_analysis_handler(task: Task) -> Dict[str, Any]:
            """内容分析任务处理器"""
            content = task.input_data.get("content", "")

            # 模拟分析过程
            for i in range(3):
                if self.stop_event.is_set():
                    raise Exception("任务被取消")

                await asyncio.sleep(0.5)
                task.progress = (i + 1) * 33.33
                task.steps_completed = i + 1
                task.updated_at = datetime.now()

            return {
                "content": content,
                "compliance_score": 0.95,
                "quality_score": 0.8,
                "tags": ["原创", "教育", "有趣"],
                "recommended_platforms": ["抖音", "B站"],
            }

        def simple_task_handler(task: Task) -> Dict[str, Any]:
            """简单任务处理器"""
            import time

            time.sleep(2)  # 模拟处理时间
            return {"message": "任务执行完成", "input": task.input_data}

        # 注册处理器
        self.register_handler("video_download", video_download_handler)
        self.register_handler("content_analysis", content_analysis_handler)
        self.register_handler("simple_task", simple_task_handler)

    def register_handler(self, task_type: str, handler: Callable):
        """注册任务处理器"""
        self.task_handlers[task_type] = handler
        self.logger.info(f"注册任务处理器: {task_type}")

    def create_task(
        self,
        name: str,
        task_type: str,
        input_data: Dict[str, Any] = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        user_id: str = None,
        description: str = None,
    ) -> str:
        """创建任务"""
        task_id = str(uuid.uuid4())

        task = Task(
            task_id=task_id,
            name=name,
            description=description,
            task_type=task_type,
            priority=priority,
            input_data=input_data or {},
            user_id=user_id,
        )

        # 存储任务
        self.tasks[task_id] = task

        # 添加到队列
        if self.task_queue.add_task(task):
            self.logger.info(f"任务创建成功: {task_id} ({task_type})")
            return task_id
        else:
            raise RuntimeError("任务队列已满，无法添加新任务")

    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)

    def list_tasks(self, user_id: str = None, status: TaskStatus = None) -> List[Task]:
        """列出任务"""
        tasks = list(self.tasks.values())

        if user_id:
            tasks = [t for t in tasks if t.user_id == user_id]

        if status:
            tasks = [t for t in tasks if t.status == status]

        return tasks

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.tasks.get(task_id)
        if not task:
            return False

        if task.status in [TaskStatus.PENDING]:
            task.status = TaskStatus.CANCELLED
            task.updated_at = datetime.now()
            self.logger.info(f"任务已取消: {task_id}")
            return True

        return False

    async def process_tasks(self):
        """处理任务队列"""
        while not self.stop_event.is_set():
            try:
                # 查找空闲的工作器
                available_worker = None
                for worker in self.workers:
                    if not worker.is_busy:
                        available_worker = worker
                        break

                if not available_worker:
                    await asyncio.sleep(0.1)
                    continue

                # 获取下一个任务
                task = self.task_queue.get_next_task()
                if not task:
                    await asyncio.sleep(0.1)
                    continue

                # 异步执行任务
                asyncio.create_task(
                    available_worker.execute_task(task, self.task_handlers)
                )

            except Exception as e:
                self.logger.error(f"任务处理循环错误: {e}")
                await asyncio.sleep(1)

    def start(self):
        """启动服务"""
        if self.is_running:
            return

        self.is_running = True
        self.stop_event.clear()

        # 启动任务处理循环
        asyncio.create_task(self.process_tasks())

        self.logger.info("任务队列服务已启动")

    def stop(self):
        """停止服务"""
        if not self.is_running:
            return

        self.is_running = False
        self.stop_event.set()

        # 停止线程池
        self.executor.shutdown(wait=True)

        self.logger.info("任务队列服务已停止")

    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计"""
        tasks = list(self.tasks.values())

        # 状态分布
        status_distribution = {}
        for status in TaskStatus:
            status_distribution[status.value] = len(
                [t for t in tasks if t.status == status]
            )

        # 优先级分布
        priority_distribution = {}
        for priority in TaskPriority:
            priority_distribution[priority.value] = len(
                [t for t in tasks if t.priority == priority]
            )

        # 工作器状态
        worker_stats = {
            "total_workers": len(self.workers),
            "busy_workers": len([w for w in self.workers if w.is_busy]),
            "available_workers": len([w for w in self.workers if not w.is_busy]),
        }

        return {
            "service_running": self.is_running,
            "total_tasks": len(tasks),
            "status_distribution": status_distribution,
            "priority_distribution": priority_distribution,
            "queue_sizes": self.task_queue.get_queue_size(),
            "worker_stats": worker_stats,
            "registered_handlers": list(self.task_handlers.keys()),
        }


# 全局服务实例
task_queue_service = TaskQueueService()
