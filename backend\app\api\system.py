"""
系统监控和缓存管理API端点
"""

from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException

from app.core.auth import get_current_user
from app.core.exceptions import ValidationException, create_http_exception
from app.core.logging import get_api_logger
from app.services.cache_service import cache_service, get_cache_health
from app.services.monitoring_service import get_system_health, monitoring_service
from app.services.social_media_publisher import (
    ContentType,
    PlatformType,
    PublishRequest,
    create_multi_platform_publisher,
)

router = APIRouter(prefix="/system", tags=["系统管理"])
logger = get_api_logger()

# 全局多平台发布器实例
multi_publisher = create_multi_platform_publisher()


@router.get("/health")
async def system_health_check():
    """系统健康检查"""
    try:
        logger.info("系统健康检查请求")

        # 获取系统状态
        system_status = await get_system_health()

        # 获取缓存状态
        cache_status = await get_cache_health()

        # 整体健康状态
        overall_status = "healthy"

        system_health = system_status.get("system_health", {})
        system_health_status = system_health.get("status")
        cache_health_status = cache_status.get("status")

        if system_health_status == "critical" or cache_health_status == "disconnected":
            overall_status = "critical"
        elif system_health_status == "warning" or cache_health_status == "degraded":
            overall_status = "warning"

        result = {
            "overall_status": overall_status,
            "system": system_status,
            "cache": cache_status,
            "timestamp": datetime.now().isoformat(),
        }

        logger.info("系统健康检查完成", extra_data={"overall_status": overall_status})

        return result

    except Exception as e:
        logger.error("系统健康检查失败", extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.get("/metrics")
async def get_system_metrics(current_user: Dict[str, Any] = Depends(get_current_user)):
    """获取系统指标"""
    try:
        logger.info("系统指标查询", extra_data={"user_id": current_user["user_id"]})

        # 获取性能摘要
        monitoring_svc = monitoring_service
        performance_summary = await monitoring_svc.get_performance_summary()

        # 获取系统状态
        system_status = await monitoring_service.get_system_status()

        result = {
            "performance_summary": performance_summary,
            "system_status": system_status,
            "timestamp": datetime.now().isoformat(),
        }

        return result

    except Exception as e:
        logger.error(
            "系统指标查询失败",
            extra_data={"error": str(e), "user_id": current_user["user_id"]},
        )
        raise HTTPException(status_code=500, detail=f"指标查询失败: {str(e)}")


@router.get("/cache/stats")
async def get_cache_stats(current_user: Dict[str, Any] = Depends(get_current_user)):
    """获取缓存统计"""
    try:
        logger.info("缓存统计查询", extra_data={"user_id": current_user["user_id"]})

        stats = await cache_service.get_stats()

        return {"cache_stats": stats, "timestamp": datetime.now().isoformat()}

    except Exception as e:
        logger.error(
            "缓存统计查询失败",
            extra_data={"error": str(e), "user_id": current_user["user_id"]},
        )
        raise HTTPException(status_code=500, detail=f"缓存统计查询失败: {str(e)}")


@router.delete("/cache/clear")
async def clear_cache(
    service: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """清除缓存"""
    try:
        logger.info(
            "缓存清除请求",
            extra_data={"user_id": current_user["user_id"], "service": service},
        )

        if service:
            # 清除特定服务的缓存
            deleted_count = await cache_service.clear_service_cache(service)
            message = f"已清除服务 {service} 的 {deleted_count} 个缓存项"
        else:
            # 清除所有缓存
            deleted_count = await cache_service.delete_pattern("cache:*")
            message = f"已清除所有 {deleted_count} 个缓存项"

        logger.info(
            "缓存清除完成",
            extra_data={
                "user_id": current_user["user_id"],
                "service": service,
                "deleted_count": deleted_count,
            },
        )

        return {
            "message": message,
            "deleted_count": deleted_count,
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(
            "缓存清除失败",
            extra_data={"error": str(e), "user_id": current_user["user_id"]},
        )
        raise HTTPException(status_code=500, detail=f"缓存清除失败: {str(e)}")


@router.get("/publish/platforms")
async def get_supported_platforms():
    """获取支持的发布平台"""
    try:
        platforms = await multi_publisher.get_supported_platforms()

        # 获取所有可用平台类型
        all_platforms = [platform.value for platform in PlatformType]

        return {
            "registered_platforms": platforms,
            "available_platforms": all_platforms,
            "platform_details": {
                "weibo": {
                    "name": "微博",
                    "content_types": ["text", "image"],
                    "max_text_length": 140,
                },
                "douyin": {
                    "name": "抖音",
                    "content_types": ["video"],
                    "max_video_size": "100MB",
                },
                "xiaohongshu": {
                    "name": "小红书",
                    "content_types": ["text", "image", "mixed"],
                    "max_text_length": 1000,
                },
            },
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error("获取支持平台失败", extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail=f"获取平台信息失败: {str(e)}")


@router.get("/publish/platforms/{platform}/status")
async def get_platform_status(
    platform: str, current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取平台状态"""
    try:
        # 验证平台名称
        try:
            platform_type = PlatformType(platform)
        except ValueError:
            raise ValidationException(f"不支持的平台: {platform}")

        status = await multi_publisher.get_platform_status(platform_type)

        return {"platform_status": status, "timestamp": datetime.now().isoformat()}

    except ValidationException as e:
        raise create_http_exception(e)
    except Exception as e:
        logger.error(
            "获取平台状态失败",
            extra_data={
                "platform": platform,
                "error": str(e),
                "user_id": current_user["user_id"],
            },
        )
        raise HTTPException(status_code=500, detail=f"获取平台状态失败: {str(e)}")


@router.post("/publish")
async def publish_content(
    request_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """发布内容到多平台"""
    try:
        logger.info(
            "多平台发布请求",
            extra_data={
                "user_id": current_user["user_id"],
                "platforms": request_data.get("platforms", []),
            },
        )

        # 验证请求数据
        if not request_data.get("platforms"):
            raise ValidationException("请指定发布平台")

        if not request_data.get("title"):
            raise ValidationException("请提供标题")

        if not request_data.get("content"):
            raise ValidationException("请提供内容")

        # 解析平台列表
        platforms = []
        for platform_name in request_data["platforms"]:
            try:
                platform = PlatformType(platform_name)
                platforms.append(platform)
            except ValueError:
                raise ValidationException(f"不支持的平台: {platform_name}")

        # 解析内容类型
        try:
            content_type = ContentType(request_data.get("content_type", "text"))
        except ValueError:
            raise ValidationException(
                f"不支持的内容类型: {request_data.get('content_type')}"
            )

        # 创建发布请求
        publish_request = PublishRequest(
            platform=platforms[0],  # 主平台
            content_type=content_type,
            title=request_data["title"],
            content=request_data["content"],
            media_files=request_data.get("media_files", []),
            tags=request_data.get("tags", []),
            metadata=request_data.get("metadata", {}),
        )

        # 执行多平台发布
        results = await multi_publisher.publish_to_multiple_platforms(
            platforms, publish_request
        )

        # 统计结果
        success_count = sum(
            1 for result in results.values() if result.get("status") == "success"
        )

        logger.info(
            "多平台发布完成",
            extra_data={
                "user_id": current_user["user_id"],
                "total_platforms": len(platforms),
                "success_count": success_count,
                "title": request_data["title"],
            },
        )

        return {
            "publish_results": results,
            "summary": {
                "total_platforms": len(platforms),
                "success_count": success_count,
                "failure_count": len(platforms) - success_count,
            },
            "timestamp": datetime.now().isoformat(),
        }

    except ValidationException as e:
        logger.warning(
            "发布请求验证失败",
            extra_data={"error": str(e), "user_id": current_user["user_id"]},
        )
        raise create_http_exception(e)
    except Exception as e:
        logger.error(
            "多平台发布失败",
            extra_data={"error": str(e), "user_id": current_user["user_id"]},
        )
        raise HTTPException(status_code=500, detail=f"发布失败: {str(e)}")


@router.post("/monitoring/register-service")
async def register_service_monitoring(
    service_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """注册服务监控"""
    try:
        service_name = service_data.get("service_name")
        if not service_name:
            raise ValidationException("请提供服务名称")

        # 这里应该注册实际的健康检查函数
        # 目前提供一个示例实现
        async def health_check():
            return {"healthy": True, "status": "running"}

        monitoring_service.register_health_check(service_name, health_check)

        logger.info(
            "服务监控注册成功",
            extra_data={
                "service_name": service_name,
                "user_id": current_user["user_id"],
            },
        )

        return {
            "message": f"服务 {service_name} 监控注册成功",
            "service_name": service_name,
            "timestamp": datetime.now().isoformat(),
        }

    except ValidationException as e:
        raise create_http_exception(e)
    except Exception as e:
        logger.error(
            "服务监控注册失败",
            extra_data={"error": str(e), "user_id": current_user["user_id"]},
        )
        raise HTTPException(status_code=500, detail=f"服务监控注册失败: {str(e)}")
