"""
开发监控API端点
提供实时监控数据的HTTP接口
"""

from datetime import datetime
from typing import Optional

from fastapi import APIRouter, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse

from app.utils.dev_monitor import (
    api_metrics,
    db_monitor,
    export_monitoring_data,
    print_monitoring_summary,
    system_monitor,
)

router = APIRouter(prefix="/dev", tags=["开发工具"])


@router.get("/", response_class=HTMLResponse)
async def dev_dashboard():
    """开发工具仪表板HTML页面"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AI视频系统 - 开发监控仪表板</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Monaco', 'Menlo', monospace;
                background: #1a1a1a;
                color: #fff;
                padding: 20px;
            }
            .container { max-width: 1200px; margin: 0 auto; }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #4CAF50;
                padding-bottom: 20px;
            }
            .header h1 { color: #4CAF50; margin-bottom: 10px; }
            .header p { color: #ccc; }

            .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .metric-card {
                background: #2a2a2a;
                border-radius: 8px;
                padding: 20px;
                border-left: 4px solid #4CAF50;
            }
            .metric-card h3 { color: #4CAF50; margin-bottom: 15px; }
            .metric-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
                padding: 5px 0;
            }
            .metric-label { color: #ccc; }
            .metric-value { color: #fff; font-weight: bold; }

            .control-panel {
                background: #2a2a2a;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
            }
            .btn {
                background: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                margin-right: 10px;
                margin-bottom: 10px;
            }
            .btn:hover { background: #45a049; }
            .btn-danger { background: #f44336; }
            .btn-danger:hover { background: #da190b; }
            .btn-info { background: #2196F3; }
            .btn-info:hover { background: #0b7dda; }

            .logs-container {
                background: #2a2a2a;
                border-radius: 8px;
                padding: 20px;
                max-height: 400px;
                overflow-y: auto;
            }
            .log-entry {
                padding: 8px;
                margin-bottom: 5px;
                background: #333;
                border-radius: 4px;
                font-size: 12px;
            }
            .log-success { border-left: 3px solid #4CAF50; }
            .log-error { border-left: 3px solid #f44336; }
            .log-warning { border-left: 3px solid #FF9800; }

            #status-indicator {
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: #4CAF50;
                margin-right: 8px;
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🛠️ AI视频内容创作系统 - 开发监控</h1>
                <p><span id="status-indicator"></span>实时监控运行中</p>
                <p id="last-update">最后更新: 等待数据...</p>
            </div>

            <div class="control-panel">
                <button class="btn" onclick="refreshData()">🔄 刷新数据</button>
                <button class="btn btn-info" onclick="exportData()">📊 导出数据</button>
                <button class="btn btn-danger" onclick="clearLogs()">🗑️ 清空日志</button>
                <button class="btn" onclick="toggleAutoRefresh()">⏱️ <span id="auto-refresh-text">启用</span>自动刷新</button>
            </div>

            <div class="metrics-grid" id="metrics-grid">
                <!-- 指标卡片将在这里动态生成 -->
            </div>

            <div class="logs-container">
                <h3>📝 系统日志</h3>
                <div id="logs-content">
                    <div class="log-entry log-success">系统启动完成，等待数据加载...</div>
                </div>
            </div>
        </div>

        <script>
            let autoRefresh = false;
            let refreshInterval;

            async function fetchData(endpoint) {
                try {
                    const response = await fetch(`/api/v1/dev/${endpoint}`);
                    return await response.json();
                } catch (error) {
                    console.error('Fetch error:', error);
                    return null;
                }
            }

            function createMetricCard(title, metrics) {
                const card = document.createElement('div');
                card.className = 'metric-card';

                let content = `<h3>${title}</h3>`;
                for (const [label, value] of Object.entries(metrics)) {
                    content += `
                        <div class="metric-item">
                            <span class="metric-label">${label}:</span>
                            <span class="metric-value">${value}</span>
                        </div>
                    `;
                }

                card.innerHTML = content;
                return card;
            }

            function addLogEntry(message, type = 'success') {
                const logsContent = document.getElementById('logs-content');
                const entry = document.createElement('div');
                entry.className = `log-entry log-${type}`;
                entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
                logsContent.insertBefore(entry, logsContent.firstChild);

                // 只保留最新50条日志
                while (logsContent.children.length > 50) {
                    logsContent.removeChild(logsContent.lastChild);
                }
            }

            async function refreshData() {
                try {
                    const [apiStats, systemInfo] = await Promise.all([
                        fetchData('api-stats'),
                        fetchData('system-info')
                    ]);

                    const metricsGrid = document.getElementById('metrics-grid');
                    metricsGrid.innerHTML = '';

                    if (apiStats) {
                        const apiMetrics = {
                            '总调用数': apiStats.total_calls,
                            '平均响应时间': `${apiStats.avg_response_time}ms`,
                            '错误率': `${apiStats.error_rate}%`,
                            '错误数量': apiStats.error_count
                        };
                        metricsGrid.appendChild(createMetricCard('📊 API统计', apiMetrics));
                    }

                    if (systemInfo) {
                        const sysMetrics = {
                            'CPU使用率': `${systemInfo.cpu_percent}%`,
                            '内存使用率': `${systemInfo.memory_percent}%`,
                            '可用内存': `${systemInfo.memory_available_gb}GB`,
                            '进程内存': `${systemInfo.process_memory_mb}MB`,
                            'CPU核心数': systemInfo.cpu_count
                        };
                        metricsGrid.appendChild(createMetricCard('💻 系统资源', sysMetrics));
                    }

                    // 显示活跃端点
                    if (apiStats && apiStats.endpoints) {
                        const topEndpoints = Object.entries(apiStats.endpoints)
                            .sort((a, b) => b[1].count - a[1].count)
                            .slice(0, 5);

                        if (topEndpoints.length > 0) {
                            const endpointMetrics = {};
                            topEndpoints.forEach(([endpoint, data]) => {
                                endpointMetrics[endpoint] = `${data.count}次 (${data.avg_duration}ms)`;
                            });
                            metricsGrid.appendChild(createMetricCard('🔥 热门端点', endpointMetrics));
                        }
                    }

                    document.getElementById('last-update').textContent =
                        `最后更新: ${new Date().toLocaleString()}`;

                    addLogEntry('数据刷新成功', 'success');

                } catch (error) {
                    addLogEntry(`数据刷新失败: ${error.message}`, 'error');
                }
            }

            async function exportData() {
                try {
                    const data = await fetchData('export');
                    const blob = new Blob([JSON.stringify(data, null, 2)],
                        { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `monitoring-data-${new Date().toISOString()}.json`;
                    a.click();
                    URL.revokeObjectURL(url);
                    addLogEntry('监控数据导出成功', 'success');
                } catch (error) {
                    addLogEntry(`导出失败: ${error.message}`, 'error');
                }
            }

            function clearLogs() {
                document.getElementById('logs-content').innerHTML =
                    '<div class="log-entry log-success">日志已清空</div>';
            }

            function toggleAutoRefresh() {
                autoRefresh = !autoRefresh;
                const button = document.getElementById('auto-refresh-text');

                if (autoRefresh) {
                    button.textContent = '停止';
                    refreshInterval = setInterval(refreshData, 10000); // 每10秒刷新
                    addLogEntry('自动刷新已启用 (10秒间隔)', 'success');
                } else {
                    button.textContent = '启用';
                    if (refreshInterval) {
                        clearInterval(refreshInterval);
                    }
                    addLogEntry('自动刷新已停用', 'warning');
                }
            }

            // 页面加载时获取初始数据
            document.addEventListener('DOMContentLoaded', refreshData);
        </script>
    </body>
    </html>
    """
    return html_content


@router.get("/api-stats")
async def get_api_stats():
    """获取API统计信息"""
    try:
        stats = api_metrics.get_stats()
        return JSONResponse(content=stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system-info")
async def get_system_info():
    """获取系统信息"""
    try:
        info = system_monitor.get_system_info()
        return JSONResponse(content=info)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/slow-queries")
async def get_slow_queries(threshold_ms: Optional[float] = 100):
    """获取慢查询列表"""
    try:
        slow_queries = db_monitor.get_slow_queries(threshold_ms)
        return JSONResponse(
            content={
                "slow_queries": slow_queries,
                "threshold_ms": threshold_ms,
                "count": len(slow_queries),
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/export")
async def export_all_data():
    """导出所有监控数据"""
    try:
        data = export_monitoring_data()
        return JSONResponse(content=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/clear-logs")
async def clear_monitoring_logs():
    """清空监控日志"""
    try:
        api_metrics.calls.clear()
        api_metrics.errors.clear()
        api_metrics.performance.clear()
        db_monitor.query_logs.clear()

        return JSONResponse(
            content={
                "message": "监控日志已清空",
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health-check")
async def dev_health_check():
    """开发工具健康检查"""
    try:
        system_info = system_monitor.get_system_info()
        api_stats = api_metrics.get_stats()

        # 判断系统状态
        status = "healthy"
        warnings = []

        # 检查CPU使用率
        if system_info.get("cpu_percent", 0) > 80:
            warnings.append("CPU使用率过高")
            status = "warning"

        # 检查内存使用率
        if system_info.get("memory_percent", 0) > 85:
            warnings.append("内存使用率过高")
            status = "warning"

        # 检查错误率
        if api_stats.get("error_rate", 0) > 5:
            warnings.append("API错误率过高")
            status = "warning"

        # 检查响应时间
        if api_stats.get("avg_response_time", 0) > 1000:
            warnings.append("平均响应时间过长")
            status = "warning"

        return JSONResponse(
            content={
                "status": status,
                "warnings": warnings,
                "system_info": system_info,
                "api_stats": api_stats,
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/console-summary")
async def print_console_summary():
    """在控制台打印监控摘要"""
    try:
        print_monitoring_summary()
        return JSONResponse(
            content={
                "message": "监控摘要已打印到控制台",
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/real-time-logs")
async def get_real_time_logs(limit: Optional[int] = 50):
    """获取实时日志"""
    try:
        recent_calls = api_metrics.calls[-limit:] if api_metrics.calls else []
        recent_errors = api_metrics.errors[-10:] if api_metrics.errors else []

        logs = []

        # 添加API调用日志
        for call in recent_calls:
            logs.append(
                {
                    "type": "api_call",
                    "timestamp": call["timestamp"],
                    "message": f"{call['method']} {call['path']} - {call['status_code']} ({call['duration_ms']}ms)",
                    "level": "info" if call["status_code"] < 400 else "error",
                }
            )

        # 添加错误日志
        for error in recent_errors:
            logs.append(
                {
                    "type": "error",
                    "timestamp": error["timestamp"],
                    "message": f"Error in {error['method']} {error['path']}: {error['error_message']}",
                    "level": "error",
                }
            )

        # 按时间排序
        logs.sort(key=lambda x: x["timestamp"], reverse=True)

        return JSONResponse(
            content={
                "logs": logs[:limit],
                "total_count": len(logs),
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
