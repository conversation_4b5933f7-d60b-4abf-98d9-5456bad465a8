<template>
  <aside class="bg-card flex flex-col h-full" :class="{ 'items-center': isCollapsed }">
    <!-- Logo区域 -->
    <div class="p-4 border-b border-border">
      <div class="flex items-center space-x-3" :class="{ 'justify-center': isCollapsed }">
        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
          <span class="text-primary-foreground font-bold">AI</span>
        </div>
        <h1 v-if="!isCollapsed" class="text-lg font-semibold text-foreground truncate">
          AI视频创作
        </h1>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="flex-1 p-4 space-y-2 overflow-y-auto" aria-label="主导航菜单">
      <!-- 主要导航 -->
      <div class="space-y-1">
        <router-link
          v-for="item in mainNavItems"
          :key="item.path"
          :to="item.path"
          class="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors"
          :class="[
            $route.path === item.path 
              ? 'bg-accent text-accent-foreground' 
              : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
          ]"
          :title="isCollapsed ? item.name : undefined"
        >
          <component :is="item.icon" class="w-5 h-5" />
          <span v-if="!isCollapsed" class="ml-3">{{ item.name }}</span>
        </router-link>
      </div>

      <!-- 分隔线 -->
      <div v-if="!isCollapsed" class="border-t border-border my-4"></div>
      
      <!-- AI工具 -->
      <div class="space-y-1">
        <div v-if="!isCollapsed" class="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
          AI工具
        </div>
        <router-link
          v-for="tool in aiTools"
          :key="tool.path"
          :to="tool.path"
          class="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors"
          :class="[
            $route.path === tool.path 
              ? 'bg-accent text-accent-foreground' 
              : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
          ]"
          :title="isCollapsed ? tool.name : undefined"
        >
          <component :is="tool.icon" class="w-5 h-5" />
          <span v-if="!isCollapsed" class="ml-3">{{ tool.name }}</span>
        </router-link>
      </div>
    </nav>

    <!-- 底部折叠按钮 -->
    <div class="p-4 border-t border-border">
      <button
        @click="$emit('toggle')"
        class="w-full flex items-center justify-center p-2 rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
        :title="isCollapsed ? '展开侧边栏' : '折叠侧边栏'"
        aria-label="切换侧边栏折叠"
      >
        <svg
          class="w-5 h-5 transition-transform duration-300"
          :class="isCollapsed ? 'rotate-180' : ''"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <path d="M15 19l-7-7 7-7" />
        </svg>
        <span v-if="!isCollapsed" class="ml-2 text-sm">折叠</span>
      </button>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Video,
  Home,
  PlayCircle,
  TestTube,
  Bot,
  FileText,
  Image,
  Settings,
  Upload,
  Edit,
  Share
} from 'lucide-vue-next'

// Props
interface Props {
  isCollapsed: boolean
}
defineProps<Props>()

// Emits
defineEmits<{
  toggle: []
}>()

// 主要导航项
const mainNavItems = computed(() => [
  {
    name: '首页',
    path: '/',
    icon: Home
  },
  {
    name: '视频创作',
    path: '/video-creation',
    icon: PlayCircle
  },
  {
    name: '计算引擎测试',
    path: '/compute-test',
    icon: TestTube
  },
  {
    name: '设置',
    path: '/settings',
    icon: Settings
  }
])

// AI工具
const aiTools = computed(() => [
  {
    name: 'AI对话',
    path: '/ai-chat',
    icon: Bot
  },
  {
    name: '脚本生成',
    path: '/script-generator',
    icon: FileText
  },
  {
    name: '图像生成',
    path: '/image-generator',
    icon: Image
  }
])


</script>

<style scoped>
.bg-card {
  background-color: hsl(var(--card));
}

.border-border {
  border-color: hsl(var(--border));
}

.text-foreground {
  color: hsl(var(--foreground));
}

.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.bg-muted {
  background-color: hsl(var(--muted));
}

.nav-item {
  @apply flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors;
  @apply text-muted-foreground hover:bg-accent hover:text-accent-foreground;
}

.nav-item-active {
  @apply bg-accent text-accent-foreground;
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}
</style>