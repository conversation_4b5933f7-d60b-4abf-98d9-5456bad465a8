import logging

from apscheduler.schedulers.asyncio import AsyncIOScheduler

from app.services.backup_service import create_backup

# 配置日志记录
logging.basicConfig()
logging.getLogger("apscheduler").setLevel(logging.INFO)

scheduler = AsyncIOScheduler()


def schedule_daily_backup():
    """
    安排每日备份任务
    """
    scheduler.add_job(
        create_backup,
        "interval",
        days=1,
        id="daily_db_backup",
        replace_existing=True,
        misfire_grace_time=3600,  # 1小时的宽限时间
    )
    logging.info("每日数据库备份任务已安排。")


def start_scheduler():
    """启动调度器"""
    if not scheduler.running:
        scheduler.start()
        logging.info("调度器已启动。")


def stop_scheduler():
    """停止调度器"""

    if scheduler.running:
        scheduler.shutdown()
        logging.info("调度器已关闭。")
