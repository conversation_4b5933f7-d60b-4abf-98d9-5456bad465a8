"""
Pydantic数据模式
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, EmailStr, Field


# 枚举类型
class UserRole(str, Enum):
    USER = "user"
    ADMIN = "admin"
    SUPERUSER = "superuser"


class ContentType(str, Enum):
    TEXT = "text"
    VIDEO = "video"
    IMAGE = "image"


class ContentStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ComplianceStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"


class Platform(str, Enum):
    DOUYIN = "douyin"
    KUAISHOU = "kuaishou"
    BILIBILI = "bilibili"
    XIAOHONGSHU = "xiaohongshu"


# 基础模式
class BaseSchema(BaseModel):
    class Config:
        from_attributes = True


# 用户相关模式
class UserBase(BaseSchema):
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    full_name: Optional[str] = Field(None, max_length=100)
    phone_number: Optional[str] = Field(None, max_length=20)  # 添加手机号字段
    bio: Optional[str] = None
    avatar_url: Optional[str] = None


class UserCreate(UserBase):
    password: str = Field(..., min_length=6, max_length=100)


class UserUpdate(BaseSchema):
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    password: Optional[str] = Field(None, min_length=6, max_length=100)


class UserResponse(UserBase):
    id: int
    is_active: bool
    is_superuser: bool
    created_at: datetime
    updated_at: Optional[datetime]
    last_login: Optional[datetime]


class UserLogin(BaseSchema):
    username: str
    password: str


# 认证相关模式
class Token(BaseSchema):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class TokenData(BaseSchema):
    username: Optional[str] = None
    user_id: Optional[int] = None


# 项目相关模式
class ProjectBase(BaseSchema):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None


class ProjectCreate(ProjectBase):
    pass


class ProjectUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    status: Optional[str] = None


class ProjectResponse(ProjectBase):
    id: int
    owner_id: int
    status: str
    created_at: datetime
    updated_at: Optional[datetime]


# 内容相关模式
class ContentItemBase(BaseSchema):
    title: str = Field(..., min_length=1, max_length=200)
    content_type: ContentType
    original_content: Optional[str] = None


class ContentItemCreate(ContentItemBase):
    project_id: int


class ContentItemUpdate(BaseSchema):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    original_content: Optional[str] = None
    processed_content: Optional[str] = None
    status: Optional[ContentStatus] = None
    compliance_status: Optional[ComplianceStatus] = None


class ContentItemResponse(ContentItemBase):
    id: int
    project_id: int
    status: ContentStatus
    compliance_status: ComplianceStatus
    compliance_result: Optional[Dict[str, Any]] = None
    file_path: Optional[str] = None
    thumbnail_path: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime]


# 内容相关模式
class ContentBase(BaseSchema):
    title: str = Field(..., min_length=1, max_length=200)
    content_type: str = Field(..., pattern="^(text|video|image|audio)$")
    original_text: Optional[str] = None
    project_id: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class ContentCreate(ContentBase):
    pass


class ContentUpdate(BaseSchema):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    content_type: Optional[str] = Field(None, pattern="^(text|video|image|audio)$")
    original_text: Optional[str] = None
    rewritten_text: Optional[str] = None
    status: Optional[str] = Field(None, pattern="^(draft|approved|rejected|published)$")
    metadata: Optional[Dict[str, Any]] = None


class ContentResponse(ContentBase):
    id: int
    rewritten_text: Optional[str]
    creator_id: int
    status: str
    compliance_score: Optional[float]
    compliance_issues: List[Dict[str, Any]] = []
    metadata: Dict[str, Any] = {}
    created_at: datetime
    updated_at: Optional[datetime]
    published_at: Optional[datetime] = None


# 分发相关模式
class DistributionTaskBase(BaseSchema):
    platform: Platform
    platform_config: Optional[Dict[str, Any]] = None


class DistributionTaskCreate(DistributionTaskBase):
    content_item_id: int


class DistributionTaskUpdate(BaseSchema):
    status: Optional[str] = None
    platform_config: Optional[Dict[str, Any]] = None
    platform_id: Optional[str] = None
    platform_url: Optional[str] = None
    error_message: Optional[str] = None


class DistributionTaskResponse(DistributionTaskBase):
    id: int
    content_item_id: int
    status: str
    platform_id: Optional[str] = None
    platform_url: Optional[str] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime]
    distributed_at: Optional[datetime]


# 分发相关模式
class DistributionBase(BaseSchema):
    content_id: int
    platform: str = Field(..., pattern="^(douyin|xiaohongshu|weibo|bilibili)$")
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    scheduled_time: Optional[datetime] = None
    platform_config: Optional[Dict[str, Any]] = None


class DistributionCreate(DistributionBase):
    pass


class DistributionUpdate(BaseSchema):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    scheduled_time: Optional[datetime] = None
    platform_config: Optional[Dict[str, Any]] = None
    status: Optional[str] = Field(
        None, pattern="^(pending|processing|completed|failed|cancelled)$"
    )


class DistributionResponse(DistributionBase):
    id: int
    creator_id: int
    status: str
    result_data: Dict[str, Any] = {}
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime]
    published_at: Optional[datetime] = None


# API响应模式
class APIResponse(BaseSchema):
    success: bool = True
    message: str = "操作成功"
    data: Optional[Any] = None
    code: int = 200


class ErrorResponse(BaseSchema):
    success: bool = False
    message: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class PaginatedResponse(BaseSchema):
    items: List[Any]
    total: int
    page: int = 1
    size: int = 10
    pages: int


# 内容合规相关模式
class ComplianceRequest(BaseSchema):
    content: str
    content_type: ContentType = ContentType.TEXT


class ComplianceResult(BaseSchema):
    is_compliant: bool
    confidence: float = Field(..., ge=0.0, le=1.0)
    risk_categories: List[str] = []
    suggestions: List[str] = []
    details: Optional[Dict[str, Any]] = None


# 文本转视频相关模式
class TextToVideoRequest(BaseSchema):
    text: str = Field(..., min_length=1, max_length=1000)
    style: Optional[str] = "default"
    duration: Optional[int] = Field(30, ge=5, le=300)
    resolution: Optional[str] = "1080p"
    parameters: Optional[Dict[str, Any]] = None


class TextToVideoResult(BaseSchema):
    task_id: str
    status: str
    video_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    progress: Optional[float] = None
    error_message: Optional[str] = None
