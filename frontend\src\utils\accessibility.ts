/**
 * 无障碍访问支持系统 - 2025年WCAG 2.2最佳实践
 * 提供全面的无障碍功能支持
 */

// 无障碍配置接口
interface AccessibilityConfig {
  enableHighContrast: boolean
  enableLargeText: boolean
  enableScreenReader: boolean
  enableKeyboardNavigation: boolean
  enableMotionReduction: boolean
  enableFocusIndicator: boolean
  fontSize: number
  contrastRatio: number
}

// 无障碍状态
interface AccessibilityState {
  isHighContrast: boolean
  isLargeText: boolean
  isMotionReduced: boolean
  currentFontSize: number
  keyboardNavigationEnabled: boolean
  screenReaderEnabled: boolean
}

class AccessibilityManager {
  private config: AccessibilityConfig
  private state: AccessibilityState
  private focusTracker: FocusTracker
  private keyboardHandler: KeyboardHandler
  private screenReaderAnnouncer: ScreenReaderAnnouncer

  constructor(config: Partial<AccessibilityConfig> = {}) {
    this.config = {
      enableHighContrast: true,
      enableLargeText: true,
      enableScreenReader: true,
      enableKeyboardNavigation: true,
      enableMotionReduction: true,
      enableFocusIndicator: true,
      fontSize: 16,
      contrastRatio: 4.5,
      ...config
    }

    this.state = {
      isHighContrast: false,
      isLargeText: false,
      isMotionReduced: false,
      currentFontSize: this.config.fontSize,
      keyboardNavigationEnabled: false,
      screenReaderEnabled: false
    }

    this.focusTracker = new FocusTracker()
    this.keyboardHandler = new KeyboardHandler()
    this.screenReaderAnnouncer = new ScreenReaderAnnouncer()

    this.init()
  }

  private init(): void {
    this.loadUserPreferences()
    this.setupMediaQueries()
    this.setupKeyboardNavigation()
    this.setupFocusManagement()
    this.setupScreenReaderSupport()
    this.setupARIALabels()
    this.injectAccessibilityStyles()

    console.log('♿ 无障碍访问系统已启动')
  }

  private loadUserPreferences(): void {
    const saved = localStorage.getItem('accessibility-preferences')
    if (saved) {
      try {
        const preferences = JSON.parse(saved)
        this.state = { ...this.state, ...preferences }
        this.applyPreferences()
      } catch (error) {
        console.warn('无法加载无障碍偏好设置:', error)
      }
    }
  }

  private saveUserPreferences(): void {
    localStorage.setItem('accessibility-preferences', JSON.stringify(this.state))
  }

  private setupMediaQueries(): void {
    // 检测用户系统偏好
    if (window.matchMedia) {
      // 高对比度偏好
      const highContrastQuery = window.matchMedia('(prefers-contrast: high)')
      if (highContrastQuery.matches) {
        this.toggleHighContrast(true)
      }
      highContrastQuery.addEventListener('change', (e) => {
        this.toggleHighContrast(e.matches)
      })

      // 减少动画偏好
      const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
      if (motionQuery.matches) {
        this.toggleMotionReduction(true)
      }
      motionQuery.addEventListener('change', (e) => {
        this.toggleMotionReduction(e.matches)
      })

      // 大字体偏好
      const fontSizeQuery = window.matchMedia('(prefers-reduced-data: reduce)')
      fontSizeQuery.addEventListener('change', (e) => {
        if (e.matches) {
          this.adjustFontSize(18)
        }
      })
    }
  }

  private setupKeyboardNavigation(): void {
    if (!this.config.enableKeyboardNavigation) return

    document.addEventListener('keydown', (event) => {
      this.keyboardHandler.handleKeydown(event)
      
      // Tab键导航
      if (event.key === 'Tab') {
        this.state.keyboardNavigationEnabled = true
        document.body.classList.add('keyboard-navigation')
      }
      
      // Escape键处理
      if (event.key === 'Escape') {
        this.handleEscapeKey()
      }
      
      // 快捷键处理
      this.handleAccessibilityShortcuts(event)
    })

    // 鼠标点击时禁用键盘导航样式
    document.addEventListener('mousedown', () => {
      this.state.keyboardNavigationEnabled = false
      document.body.classList.remove('keyboard-navigation')
    })
  }

  private setupFocusManagement(): void {
    this.focusTracker.init()
    
    // 焦点指示器
    if (this.config.enableFocusIndicator) {
      this.injectFocusStyles()
    }
  }

  private setupScreenReaderSupport(): void {
    if (!this.config.enableScreenReader) return

    // 检测屏幕阅读器
    this.detectScreenReader()
    
    // 设置ARIA live regions
    this.setupLiveRegions()
    
    // 路由变化通知
    this.setupRouteAnnouncements()
  }

  private setupARIALabels(): void {
    // 自动添加ARIA标签
    this.addMissingARIALabels()
    
    // 监听DOM变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.addARIALabelsToElement(node as Element)
            }
          })
        }
      })
    })
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }

  private injectAccessibilityStyles(): void {
    const style = document.createElement('style')
    style.id = 'accessibility-styles'
    style.textContent = `
      /* 高对比度模式 */
      .high-contrast {
        filter: contrast(150%) brightness(110%);
      }
      
      .high-contrast * {
        border-color: #000 !important;
        outline-color: #000 !important;
      }
      
      /* 大字体模式 */
      .large-text {
        font-size: 1.2em !important;
        line-height: 1.6 !important;
      }
      
      /* 减少动画 */
      .reduced-motion * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
      
      /* 键盘导航焦点 */
      .keyboard-navigation *:focus {
        outline: 3px solid #005fcc !important;
        outline-offset: 2px !important;
      }
      
      /* 跳转链接 */
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: #fff;
        padding: 8px;
        text-decoration: none;
        z-index: 10000;
      }
      
      .skip-link:focus {
        top: 6px;
      }
      
      /* 屏幕阅读器专用 */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      }
    `
    
    document.head.appendChild(style)
  }

  private injectFocusStyles(): void {
    const focusStyle = document.createElement('style')
    focusStyle.id = 'focus-styles'
    focusStyle.textContent = `
      /* 增强焦点指示器 */
      *:focus {
        outline: 2px solid #005fcc;
        outline-offset: 2px;
      }
      
      button:focus,
      input:focus,
      select:focus,
      textarea:focus,
      a:focus {
        outline: 3px solid #005fcc;
        outline-offset: 2px;
        box-shadow: 0 0 0 1px #fff, 0 0 0 4px #005fcc;
      }
    `
    
    document.head.appendChild(focusStyle)
  }

  private handleAccessibilityShortcuts(event: KeyboardEvent): void {
    // Alt + H: 切换高对比度
    if (event.altKey && event.key === 'h') {
      event.preventDefault()
      this.toggleHighContrast()
    }
    
    // Alt + L: 切换大字体
    if (event.altKey && event.key === 'l') {
      event.preventDefault()
      this.toggleLargeText()
    }
    
    // Alt + M: 切换动画减少
    if (event.altKey && event.key === 'm') {
      event.preventDefault()
      this.toggleMotionReduction()
    }
  }

  private handleEscapeKey(): void {
    // 关闭模态框、下拉菜单等
    const modals = document.querySelectorAll('[role="dialog"][aria-hidden="false"]')
    modals.forEach(modal => {
      (modal as HTMLElement).style.display = 'none'
      modal.setAttribute('aria-hidden', 'true')
    })
  }

  private detectScreenReader(): void {
    // 检测常见的屏幕阅读器
    const userAgent = navigator.userAgent.toLowerCase()
    const screenReaders = ['nvda', 'jaws', 'voiceover', 'narrator', 'orca']
    
    this.state.screenReaderEnabled = screenReaders.some(sr => 
      userAgent.includes(sr)
    )
    
    if (this.state.screenReaderEnabled) {
      document.body.classList.add('screen-reader-active')
    }
  }

  private setupLiveRegions(): void {
    // 创建ARIA live regions
    const announcer = document.createElement('div')
    announcer.id = 'aria-live-announcer'
    announcer.setAttribute('aria-live', 'polite')
    announcer.setAttribute('aria-atomic', 'true')
    announcer.className = 'sr-only'
    document.body.appendChild(announcer)
    
    const assertiveAnnouncer = document.createElement('div')
    assertiveAnnouncer.id = 'aria-live-assertive'
    assertiveAnnouncer.setAttribute('aria-live', 'assertive')
    assertiveAnnouncer.setAttribute('aria-atomic', 'true')
    assertiveAnnouncer.className = 'sr-only'
    document.body.appendChild(assertiveAnnouncer)
  }

  private setupRouteAnnouncements(): void {
    // 监听路由变化并通知屏幕阅读器
    window.addEventListener('popstate', () => {
      this.announcePageChange()
    })
  }

  private addMissingARIALabels(): void {
    // 为缺少标签的元素添加ARIA标签
    const buttons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])')
    buttons.forEach((button, index) => {
      if (!button.textContent?.trim()) {
        button.setAttribute('aria-label', `按钮 ${index + 1}`)
      }
    })
    
    const inputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])')
    inputs.forEach((input) => {
      const label = document.querySelector(`label[for="${input.id}"]`)
      if (!label && !input.getAttribute('placeholder')) {
        input.setAttribute('aria-label', '输入框')
      }
    })
  }

  private addARIALabelsToElement(element: Element): void {
    // 为新添加的元素添加ARIA标签
    if (element.tagName === 'BUTTON' && !element.getAttribute('aria-label')) {
      if (!element.textContent?.trim()) {
        element.setAttribute('aria-label', '按钮')
      }
    }
  }

  private applyPreferences(): void {
    if (this.state.isHighContrast) {
      document.body.classList.add('high-contrast')
    }
    
    if (this.state.isLargeText) {
      document.body.classList.add('large-text')
    }
    
    if (this.state.isMotionReduced) {
      document.body.classList.add('reduced-motion')
    }
    
    document.documentElement.style.fontSize = `${this.state.currentFontSize}px`
  }

  // 公共方法
  public toggleHighContrast(force?: boolean): void {
    this.state.isHighContrast = force ?? !this.state.isHighContrast
    document.body.classList.toggle('high-contrast', this.state.isHighContrast)
    this.saveUserPreferences()
    this.announce(this.state.isHighContrast ? '高对比度模式已开启' : '高对比度模式已关闭')
  }

  public toggleLargeText(force?: boolean): void {
    this.state.isLargeText = force ?? !this.state.isLargeText
    document.body.classList.toggle('large-text', this.state.isLargeText)
    this.saveUserPreferences()
    this.announce(this.state.isLargeText ? '大字体模式已开启' : '大字体模式已关闭')
  }

  public toggleMotionReduction(force?: boolean): void {
    this.state.isMotionReduced = force ?? !this.state.isMotionReduced
    document.body.classList.toggle('reduced-motion', this.state.isMotionReduced)
    this.saveUserPreferences()
    this.announce(this.state.isMotionReduced ? '动画减少模式已开启' : '动画减少模式已关闭')
  }

  public adjustFontSize(size: number): void {
    this.state.currentFontSize = Math.max(12, Math.min(24, size))
    document.documentElement.style.fontSize = `${this.state.currentFontSize}px`
    this.saveUserPreferences()
    this.announce(`字体大小已调整为 ${this.state.currentFontSize}px`)
  }

  public announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    this.screenReaderAnnouncer.announce(message, priority)
  }

  public announcePageChange(): void {
    const title = document.title
    this.announce(`页面已切换到 ${title}`, 'assertive')
  }

  public getState(): AccessibilityState {
    return { ...this.state }
  }
}

// 焦点跟踪器
class FocusTracker {
  private focusHistory: Element[] = []

  init(): void {
    document.addEventListener('focusin', (event) => {
      if (event.target instanceof Element) {
        this.focusHistory.push(event.target)
        if (this.focusHistory.length > 10) {
          this.focusHistory.shift()
        }
      }
    })
  }

  getPreviousFocus(): Element | null {
    return this.focusHistory[this.focusHistory.length - 2] || null
  }
}

// 键盘处理器
class KeyboardHandler {
  handleKeydown(event: KeyboardEvent): void {
    // 处理键盘导航
    if (event.key === 'Tab') {
      this.handleTabNavigation(event)
    }
  }

  private handleTabNavigation(event: KeyboardEvent): void {
    const focusableElements = this.getFocusableElements()
    const currentIndex = focusableElements.indexOf(document.activeElement as Element)
    
    if (event.shiftKey) {
      // Shift + Tab (向后)
      if (currentIndex <= 0) {
        event.preventDefault()
        focusableElements[focusableElements.length - 1]?.focus()
      }
    } else {
      // Tab (向前)
      if (currentIndex >= focusableElements.length - 1) {
        event.preventDefault()
        focusableElements[0]?.focus()
      }
    }
  }

  private getFocusableElements(): Element[] {
    const selector = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    return Array.from(document.querySelectorAll(selector))
      .filter(el => !el.hasAttribute('disabled') && this.isVisible(el))
  }

  private isVisible(element: Element): boolean {
    const style = window.getComputedStyle(element)
    return style.display !== 'none' && style.visibility !== 'hidden'
  }
}

// 屏幕阅读器通知器
class ScreenReaderAnnouncer {
  announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    const announcerId = priority === 'assertive' ? 'aria-live-assertive' : 'aria-live-announcer'
    const announcer = document.getElementById(announcerId)
    
    if (announcer) {
      announcer.textContent = message
      
      // 清空内容以便下次通知
      setTimeout(() => {
        announcer.textContent = ''
      }, 1000)
    }
  }
}

// 全局无障碍管理器实例
let accessibilityManager: AccessibilityManager | null = null

// 初始化无障碍系统
export function initAccessibility(config?: Partial<AccessibilityConfig>): AccessibilityManager {
  if (accessibilityManager) {
    return accessibilityManager
  }
  
  accessibilityManager = new AccessibilityManager(config)
  return accessibilityManager
}

// 获取无障碍管理器实例
export function getAccessibilityManager(): AccessibilityManager | null {
  return accessibilityManager
}

// 导出类型和类
export type { AccessibilityConfig, AccessibilityState }
export { AccessibilityManager }
