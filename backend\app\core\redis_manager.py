#!/usr/bin/env python3
"""
Redis配置和连接管理
"""

import logging
from typing import Any, Dict, Optional

import redis.asyncio as redis
from pydantic import Field
from pydantic_settings import BaseSettings
from redis.asyncio import Redis


class RedisSettings(BaseSettings):
    """Redis配置"""

    # Redis连接配置
    redis_host: str = Field(default="localhost", description="Redis主机地址")
    redis_port: int = Field(default=6379, description="Redis端口")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")
    redis_db: int = Field(default=0, description="Redis数据库编号")

    # 连接池配置
    redis_max_connections: int = Field(default=20, description="最大连接数")
    redis_retry_on_timeout: bool = Field(default=True, description="超时重试")
    redis_socket_timeout: float = Field(default=5.0, description="套接字超时")
    redis_socket_connect_timeout: float = Field(default=5.0, description="连接超时")

    # 缓存配置
    cache_default_ttl: int = Field(default=3600, description="默认缓存时间(秒)")
    cache_key_prefix: str = Field(default="ai_video:", description="缓存键前缀")

    class Config:
        env_prefix = "REDIS_"
        env_file = ".env"
        extra = "ignore"  # 忽略额外的字段


class RedisManager:
    """Redis连接管理器"""

    def __init__(self, settings: RedisSettings = None):
        self.settings = settings or RedisSettings()
        self.logger = logging.getLogger(__name__)
        self._redis: Optional[Redis] = None
        self._connection_pool = None

    async def connect(self) -> Redis:
        """建立Redis连接"""
        if self._redis is None:
            try:
                # 创建连接池
                self._connection_pool = redis.ConnectionPool(
                    host=self.settings.redis_host,
                    port=self.settings.redis_port,
                    password=self.settings.redis_password,
                    db=self.settings.redis_db,
                    max_connections=self.settings.redis_max_connections,
                    retry_on_timeout=self.settings.redis_retry_on_timeout,
                    socket_timeout=self.settings.redis_socket_timeout,
                    socket_connect_timeout=(self.settings.redis_socket_connect_timeout),
                    decode_responses=True,
                )

                # 创建Redis客户端
                self._redis = Redis(connection_pool=self._connection_pool)

                # 测试连接
                await self._redis.ping()
                self.logger.info(
                    f"Redis连接成功: "
                    f"{self.settings.redis_host}:{self.settings.redis_port}"
                )

            except Exception as e:
                self.logger.error(f"Redis连接失败: {e}")
                raise

        return self._redis

    async def disconnect(self):
        """断开Redis连接"""
        if self._redis:
            await self._redis.close()
            self._redis = None
            self.logger.info("Redis连接已断开")

    async def get_redis(self) -> Redis:
        """获取Redis客户端"""
        if self._redis is None:
            await self.connect()
        return self._redis

    async def health_check(self) -> Dict[str, Any]:
        """Redis健康检查"""
        try:
            redis_client = await self.get_redis()

            # 基本连接测试
            ping_result = await redis_client.ping()

            # 获取Redis信息
            info = await redis_client.info()

            return {
                "status": "healthy",
                "ping": ping_result,
                "redis_version": info.get("redis_version"),
                "used_memory": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "total_commands_processed": info.get("total_commands_processed"),
                "uptime_in_seconds": info.get("uptime_in_seconds"),
            }

        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    def get_cache_key(self, key: str) -> str:
        """生成缓存键"""
        return f"{self.settings.cache_key_prefix}{key}"


# 全局Redis管理器实例
redis_manager = RedisManager()


async def get_redis() -> Redis:
    """依赖注入：获取Redis客户端"""
    return await redis_manager.get_redis()
