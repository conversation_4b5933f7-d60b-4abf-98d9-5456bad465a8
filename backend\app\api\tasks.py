"""
任务管理API
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.core.auth import get_current_user
from app.core.celery_app import celery_app
from app.core.database import get_db
from app.models import Content, Distribution
from app.tasks.content_tasks import (
    generate_video_from_text,
    process_content_compliance,
    rewrite_content,
)
from app.tasks.distribution_tasks import (
    batch_distribute_content,
    distribute_content_to_platform,
)

router = APIRouter(prefix="/tasks", tags=["任务管理"])


class ContentRewriteRequest(BaseModel):
    """内容改写请求模型"""

    rewrite_prompt: Optional[str] = None


class DistributeContentRequest(BaseModel):
    """内容分发请求模型"""

    platform: str
    scheduled_time: Optional[str] = None
    distribution_config: Optional[Dict[str, Any]] = None


class BatchDistributeRequest(BaseModel):
    """批量分发请求模型"""

    content_ids: List[int]
    platforms: List[str]
    scheduled_time: Optional[str] = None


@router.post("/content/compliance/{content_id}", response_model=Dict[str, Any])
async def start_compliance_check(
    content_id: int,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """启动内容合规检测任务"""

    # 检查内容是否存在
    content = db.query(Content).filter(Content.id == content_id).first()
    if not content:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="内容不存在")

    # 检查权限
    if content.creator_id != current_user["user_id"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="无权限操作此内容"
        )

    # 启动异步任务
    task = process_content_compliance.delay(content_id, current_user["user_id"])

    return {
        "task_id": task.id,
        "content_id": content_id,
        "status": "started",
        "message": "合规检测任务已启动",
    }


@router.post("/content/rewrite/{content_id}", response_model=Dict[str, Any])
async def start_content_rewrite(
    content_id: int,
    request: ContentRewriteRequest,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """启动内容改写任务"""

    # 检查内容是否存在
    content = db.query(Content).filter(Content.id == content_id).first()
    if not content:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="内容不存在")

    # 检查权限
    if content.creator_id != current_user["user_id"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="无权限操作此内容"
        )

    # 启动异步任务
    task = rewrite_content.delay(content_id, request.rewrite_prompt)

    return {
        "task_id": task.id,
        "content_id": content_id,
        "rewrite_prompt": request.rewrite_prompt,
        "status": "started",
        "message": "内容改写任务已启动",
    }


@router.post("/content/generate-video/{content_id}", response_model=Dict[str, Any])
async def start_video_generation(
    content_id: int,
    video_config: Optional[Dict[str, Any]] = None,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """启动文本转视频任务"""

    # 检查内容是否存在
    content = db.query(Content).filter(Content.id == content_id).first()
    if not content:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="内容不存在")

    # 检查权限
    if content.creator_id != current_user["user_id"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="无权限操作此内容"
        )

    # 启动异步任务
    task = generate_video_from_text.delay(content_id, video_config or {})

    return {
        "task_id": task.id,
        "content_id": content_id,
        "video_config": video_config,
        "status": "started",
        "message": "视频生成任务已启动",
    }


@router.post("/distribution/single", response_model=Dict[str, Any])
async def start_single_distribution(
    request_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """启动单平台分发任务"""

    content_id = request_data.get("content_id")
    platform = request_data.get("platform")
    distribution_config = request_data.get("distribution_config")

    if not content_id or not platform:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少必需参数: content_id 和 platform",
        )

    # 检查内容是否存在
    content = db.query(Content).filter(Content.id == content_id).first()
    if not content:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="内容不存在")

    # 检查权限
    if content.creator_id != current_user["user_id"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="无权限操作此内容"
        )

    # 创建分发记录
    distribution = Distribution(
        content_id=content_id,
        platform=platform,
        creator_id=current_user["user_id"],
        title=content.title,
        description=f"分发到{platform}的内容",
        status="pending",
        platform_config=(str(distribution_config) if distribution_config else None),
    )

    db.add(distribution)
    db.commit()
    db.refresh(distribution)

    # 启动异步任务
    task = distribute_content_to_platform.delay(
        distribution.id,
        platform,
        {
            "title": content.title,
            "content": content.rewritten_text or content.original_text,
            "config": distribution_config or {},
        },
    )

    return {
        "task_id": task.id,
        "distribution_id": distribution.id,
        "content_id": content_id,
        "platform": platform,
        "status": "started",
        "message": f"分发到{platform}的任务已启动",
    }


@router.post("/distribution/batch", response_model=Dict[str, Any])
async def start_batch_distribution(
    request: BatchDistributeRequest,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """启动批量分发任务"""

    # 检查内容是否存在
    content = db.query(Content).filter(Content.id == request.content_ids[0]).first()
    if not content:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="内容不存在")

    # 检查权限
    if content.creator_id != current_user["user_id"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="无权限操作此内容"
        )

    # 启动批量分发任务
    task = batch_distribute_content.delay(
        request.content_ids[0],  # 暂时只处理第一个内容ID
        request.platforms,
        current_user["user_id"],
    )

    return {
        "task_id": task.id,
        "content_id": request.content_ids[0],
        "platforms": request.platforms,
        "status": "started",
        "message": f"批量分发到{len(request.platforms)}个平台的任务已启动",
    }


@router.get("/status/{task_id}", response_model=Dict[str, Any])
async def get_task_status(
    task_id: str, current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取任务状态"""

    try:
        # 获取任务结果
        result = celery_app.AsyncResult(task_id)

        response = {
            "task_id": task_id,
            "status": result.status,
            "result": None,
            "error": None,
            "progress": None,
        }

        if result.status == "PENDING":
            response["message"] = "任务等待中"
        elif result.status == "PROGRESS":
            response["progress"] = result.info
            response["message"] = result.info.get("message", "任务进行中")
        elif result.status == "SUCCESS":
            response["result"] = result.result
            response["message"] = "任务完成"
        elif result.status == "FAILURE":
            response["error"] = str(result.info)
            response["message"] = "任务失败"
        else:
            response["message"] = f"任务状态: {result.status}"

        return response

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务状态失败: {str(e)}",
        )


@router.post("/cancel/{task_id}", response_model=Dict[str, Any])
async def cancel_task(
    task_id: str, current_user: Dict[str, Any] = Depends(get_current_user)
):
    """取消任务"""

    try:
        # 撤销任务
        celery_app.control.revoke(task_id, terminate=True)

        return {
            "task_id": task_id,
            "status": "cancelled",
            "message": "任务已取消",
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消任务失败: {str(e)}",
        )


@router.get("/active", response_model=Dict[str, Any])
async def get_active_tasks(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取活跃任务列表"""

    try:
        # 获取活跃任务
        inspect = celery_app.control.inspect()
        active_tasks = inspect.active()

        return {
            "active_tasks": active_tasks,
            "message": "活跃任务列表获取成功",
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取活跃任务失败: {str(e)}",
        )


@router.get("/stats", response_model=Dict[str, Any])
async def get_task_stats(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """获取任务统计信息"""

    try:
        # 获取worker统计信息
        inspect = celery_app.control.inspect()
        stats = inspect.stats()

        return {"worker_stats": stats, "message": "任务统计信息获取成功"}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务统计失败: {str(e)}",
        )
