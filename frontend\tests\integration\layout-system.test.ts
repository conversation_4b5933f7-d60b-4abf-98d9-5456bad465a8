/**
 * 布局系统集成测试 - 2025年最佳实践
 * 测试UnifiedLayout与响应式布局的集成
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import UnifiedLayout from '@/components/layout/UnifiedLayout.vue'
import { useResponsiveLayout } from '@/composables/useResponsiveLayout'

// 模拟组件
const MockHome = { template: '<div>Home Content</div>' }
const MockLogin = { template: '<div>Login Content</div>' }
const MockVideoCreation = { template: '<div>Video Creation Content</div>' }

// 创建测试路由器
const createTestRouter = () => {
  return createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', name: 'Home', component: MockHome },
      { path: '/login', name: 'Login', component: MockLogin, meta: { layout: 'auth' } },
      { path: '/video-creation', name: 'VideoCreation', component: MockVideoCreation }
    ]
  })
}

// 模拟window尺寸
const mockWindowSize = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  })
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  })
}

describe('布局系统集成测试', () => {
  let router: any
  let pinia: any

  beforeEach(() => {
    router = createTestRouter()
    pinia = createPinia()
    
    // 重置window尺寸
    mockWindowSize(1920, 1080)
    
    // 清理document.body
    document.body.className = ''
    document.body.style.overflow = ''
  })

  describe('桌面端布局集成', () => {
    it('应该在桌面端正确渲染统一布局', async () => {
      mockWindowSize(1920, 1080)
      
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        },
        slots: {
          default: '<div class="test-content">Test Content</div>'
        }
      })

      await router.push('/')
      await wrapper.vm.$nextTick()

      // 应该使用桌面端布局
      expect(wrapper.find('.app-layout').exists()).toBe(true)
      expect(wrapper.find('.mobile-layout').exists()).toBe(false)
      expect(wrapper.find('.auth-layout').exists()).toBe(false)

      // 应该包含桌面端布局元素
      expect(wrapper.find('.app-header').exists()).toBe(true)
      expect(wrapper.find('.app-sidebar').exists()).toBe(true)
      expect(wrapper.find('.app-main').exists()).toBe(true)
    })

    it('应该在桌面端显示正确的导航元素', async () => {
      mockWindowSize(1920, 1080)
      
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        }
      })

      await router.push('/')
      await wrapper.vm.$nextTick()

      // 检查导航元素
      const headerNav = wrapper.find('.app-header__nav')
      const sidebarNav = wrapper.find('.app-sidebar__nav')
      
      expect(headerNav.exists()).toBe(true)
      expect(sidebarNav.exists()).toBe(true)

      // 检查导航链接数量
      const headerLinks = wrapper.findAll('.app-header__nav-item')
      const sidebarLinks = wrapper.findAll('.app-sidebar__nav-item')
      
      expect(headerLinks.length).toBe(4)
      expect(sidebarLinks.length).toBe(6)
    })
  })

  describe('移动端布局集成', () => {
    it('应该在移动端正确渲染移动布局', async () => {
      mockWindowSize(375, 667)
      
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        },
        slots: {
          default: '<div class="test-content">Test Content</div>'
        }
      })

      await router.push('/')
      await wrapper.vm.$nextTick()

      // 等待响应式布局更新
      const { updateWindowSize } = useResponsiveLayout()
      updateWindowSize()
      await wrapper.vm.$nextTick()

      // 应该使用移动端布局
      expect(wrapper.find('.mobile-layout').exists()).toBe(true)
      expect(wrapper.find('.app-layout').exists()).toBe(false)
      expect(wrapper.find('.auth-layout').exists()).toBe(false)
    })

    it('应该在移动端显示移动导航组件', async () => {
      mockWindowSize(375, 667)
      
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        }
      })

      await router.push('/')
      await wrapper.vm.$nextTick()

      // 更新响应式布局
      const { updateWindowSize } = useResponsiveLayout()
      updateWindowSize()
      await wrapper.vm.$nextTick()

      // 应该包含移动导航组件
      expect(wrapper.findComponent({ name: 'MobileNavigation' }).exists()).toBe(true)
    })
  })

  describe('认证布局集成', () => {
    it('应该为登录页面使用认证布局', async () => {
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        },
        slots: {
          default: '<div class="login-content">Login Content</div>'
        }
      })

      await router.push('/login')
      await wrapper.vm.$nextTick()

      // 应该使用认证布局
      expect(wrapper.find('.auth-layout').exists()).toBe(true)
      expect(wrapper.find('.app-layout').exists()).toBe(false)
      expect(wrapper.find('.mobile-layout').exists()).toBe(false)

      // 应该直接渲染插槽内容
      expect(wrapper.find('.login-content').exists()).toBe(true)
    })

    it('认证布局不应该包含统一布局元素', async () => {
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        }
      })

      await router.push('/login')
      await wrapper.vm.$nextTick()

      // 不应该包含统一布局元素
      expect(wrapper.find('.app-header').exists()).toBe(false)
      expect(wrapper.find('.app-sidebar').exists()).toBe(false)
      expect(wrapper.find('.app-main').exists()).toBe(false)
    })
  })

  describe('响应式切换集成', () => {
    it('应该在设备类型切换时正确更新布局', async () => {
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        }
      })

      await router.push('/')
      await wrapper.vm.$nextTick()

      // 初始为桌面端
      mockWindowSize(1920, 1080)
      const { updateWindowSize } = useResponsiveLayout()
      updateWindowSize()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.app-layout').exists()).toBe(true)
      expect(wrapper.find('.mobile-layout').exists()).toBe(false)

      // 切换到移动端
      mockWindowSize(375, 667)
      updateWindowSize()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.mobile-layout').exists()).toBe(true)
      expect(wrapper.find('.app-layout').exists()).toBe(false)
    })

    it('应该在平板端使用桌面布局', async () => {
      mockWindowSize(1024, 768)
      
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        }
      })

      await router.push('/')
      await wrapper.vm.$nextTick()

      const { updateWindowSize } = useResponsiveLayout()
      updateWindowSize()
      await wrapper.vm.$nextTick()

      // 平板端应该使用桌面布局（非移动布局）
      expect(wrapper.find('.app-layout').exists()).toBe(true)
      expect(wrapper.find('.mobile-layout').exists()).toBe(false)
    })
  })

  describe('页面标题集成', () => {
    it('应该为不同页面显示正确的标题', async () => {
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        }
      })

      // 测试首页标题
      await router.push('/')
      await wrapper.vm.$nextTick()

      let pageTitle = wrapper.find('.page-title')
      expect(pageTitle.text()).toBe('二创短视频分发系统')

      // 测试视频创作页面标题
      await router.push('/video-creation')
      await wrapper.vm.$nextTick()

      pageTitle = wrapper.find('.page-title')
      expect(pageTitle.text()).toBe('智能视频生成')
    })

    it('移动端应该显示移动页面标题', async () => {
      mockWindowSize(375, 667)
      
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        }
      })

      await router.push('/')
      await wrapper.vm.$nextTick()

      const { updateWindowSize } = useResponsiveLayout()
      updateWindowSize()
      await wrapper.vm.$nextTick()

      const mobileTitle = wrapper.find('.mobile-page-title')
      expect(mobileTitle.exists()).toBe(true)
      expect(mobileTitle.text()).toBe('二创短视频分发系统')
    })
  })

  describe('内容区域集成', () => {
    it('应该正确渲染插槽内容', async () => {
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        },
        slots: {
          default: '<div class="test-content">Test Content</div>'
        }
      })

      await router.push('/')
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.test-content').exists()).toBe(true)
      expect(wrapper.find('.test-content').text()).toBe('Test Content')
    })

    it('移动端应该在移动内容区域渲染插槽', async () => {
      mockWindowSize(375, 667)
      
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        },
        slots: {
          default: '<div class="mobile-test-content">Mobile Content</div>'
        }
      })

      await router.push('/')
      await wrapper.vm.$nextTick()

      const { updateWindowSize } = useResponsiveLayout()
      updateWindowSize()
      await wrapper.vm.$nextTick()

      const mobileContent = wrapper.find('.mobile-content')
      expect(mobileContent.exists()).toBe(true)
      expect(mobileContent.find('.mobile-test-content').exists()).toBe(true)
    })
  })

  describe('样式应用集成', () => {
    it('应该应用正确的内容边距', async () => {
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        }
      })

      await router.push('/')
      await wrapper.vm.$nextTick()

      const { contentMargin } = useResponsiveLayout()
      const mobileMain = wrapper.find('.mobile-main')
      
      if (mobileMain.exists()) {
        const style = mobileMain.attributes('style')
        expect(style).toContain('margin-top')
        expect(style).toContain('padding-left')
        expect(style).toContain('padding-right')
      }
    })

    it('认证布局应该占满全屏', async () => {
      const wrapper = mount(UnifiedLayout, {
        global: {
          plugins: [router, pinia]
        }
      })

      await router.push('/login')
      await wrapper.vm.$nextTick()

      const authLayout = wrapper.find('.auth-layout')
      expect(authLayout.exists()).toBe(true)
      
      // 检查样式类
      expect(authLayout.classes()).toContain('auth-layout')
    })
  })
})
