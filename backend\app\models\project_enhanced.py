"""
增强的项目模型定义
支持完整的项目管理功能
"""

from datetime import datetime
from enum import Enum

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Integer,
    String,
    Text,
    JSON,
    ForeignKey,
    Index,
    func,
)
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class ProjectStatus(str, Enum):
    """项目状态枚举"""

    DRAFT = "draft"  # 草稿
    PLANNING = "planning"  # 规划中
    IN_PROGRESS = "in_progress"  # 进行中
    REVIEW = "review"  # 审核中
    COMPLETED = "completed"  # 已完成
    ARCHIVED = "archived"  # 已归档
    CANCELLED = "cancelled"  # 已取消


class ProjectType(str, Enum):
    """项目类型枚举"""

    VIDEO_CREATION = "video_creation"  # 视频创作
    CONTENT_DISTRIBUTION = "content_distribution"  # 内容分发
    AI_ANALYSIS = "ai_analysis"  # AI分析
    AUTOMATION = "automation"  # 自动化
    INTEGRATION = "integration"  # 集成项目
    RESEARCH = "research"  # 研究项目


class ProjectPriority(str, Enum):
    """项目优先级枚举"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class Project(Base):
    """增强的项目模型"""

    __tablename__ = "projects"
    __table_args__ = (
        Index("idx_project_status", "status"),
        Index("idx_project_type", "project_type"),
        Index("idx_project_owner", "owner_id"),
        Index("idx_project_created", "created_at"),
        Index("idx_project_priority", "priority"),
        {"extend_existing": True},
    )

    # 主键
    id = Column(Integer, primary_key=True, index=True)

    # 基本信息
    name = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    project_type = Column(
        String(50), default=ProjectType.VIDEO_CREATION.value, nullable=False
    )

    # 关联信息
    owner_id = Column(Integer, nullable=False, index=True)  # 项目负责人
    created_by = Column(Integer, nullable=False)  # 创建者

    # 状态管理
    status = Column(String(20), default=ProjectStatus.DRAFT.value, nullable=False)
    priority = Column(String(10), default=ProjectPriority.MEDIUM.value, nullable=False)

    # 进度管理
    progress_percentage = Column(Integer, default=0)  # 进度百分比 0-100
    estimated_hours = Column(Integer, nullable=True)  # 预估工时
    actual_hours = Column(Integer, default=0)  # 实际工时

    # 时间管理
    start_date = Column(DateTime(timezone=True), nullable=True)
    due_date = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # 项目配置
    config = Column(JSON, nullable=True)  # 项目配置JSON
    tags = Column(JSON, nullable=True)  # 项目标签

    # 统计信息
    task_count = Column(Integer, default=0)
    completed_task_count = Column(Integer, default=0)
    member_count = Column(Integer, default=1)

    # 标记字段
    is_active = Column(Boolean, default=True)
    is_template = Column(Boolean, default=False)  # 是否为模板项目
    is_public = Column(Boolean, default=False)  # 是否公开

    # 时间戳
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 备注信息
    notes = Column(Text, nullable=True)
    last_activity = Column(Text, nullable=True)  # 最后活动记录

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', status='{self.status}')>"

    @property
    def is_overdue(self) -> bool:
        """检查项目是否逾期"""
        if not self.due_date or self.status in [
            ProjectStatus.COMPLETED,
            ProjectStatus.ARCHIVED,
        ]:
            return False
        return datetime.now() > self.due_date

    @property
    def completion_rate(self) -> float:
        """计算完成率"""
        if self.task_count == 0:
            return 0.0
        return (self.completed_task_count / self.task_count) * 100

    def update_progress(self):
        """更新项目进度"""
        if self.task_count > 0:
            self.progress_percentage = int(
                (self.completed_task_count / self.task_count) * 100
            )
        else:
            self.progress_percentage = 0


class ProjectMember(Base):
    """项目成员关联表"""

    __tablename__ = "project_members"
    __table_args__ = (
        Index("idx_project_member", "project_id", "user_id"),
        {"extend_existing": True},
    )

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    user_id = Column(Integer, nullable=False)  # 用户ID
    role = Column(String(50), default="member")  # 角色：owner, admin, member, viewer
    joined_at = Column(DateTime(timezone=True), server_default=func.now())
    is_active = Column(Boolean, default=True)


class ProjectHistory(Base):
    """项目历史记录表"""

    __tablename__ = "project_history"
    __table_args__ = (
        Index("idx_project_history", "project_id", "created_at"),
        {"extend_existing": True},
    )

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    user_id = Column(Integer, nullable=False)  # 操作用户
    action = Column(String(50), nullable=False)  # 操作类型
    description = Column(Text, nullable=True)  # 操作描述
    old_value = Column(JSON, nullable=True)  # 旧值
    new_value = Column(JSON, nullable=True)  # 新值
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class ProjectTemplate(Base):
    """项目模板表"""

    __tablename__ = "project_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    project_type = Column(String(50), nullable=False)
    template_config = Column(JSON, nullable=True)  # 模板配置
    is_active = Column(Boolean, default=True)
    created_by = Column(Integer, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
