<template>
  <div class="home-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">
          二创短视频分发系统
        </h1>
        <p class="welcome-subtitle">
          四步完成：视频转文案 → 文案优化 → 文案转视频 → 多平台分发
        </p>
      </div>
      <div class="workflow-stats">
        <div class="stat-item">
          <div class="stat-number">{{ isLoading ? '...' : processedCount }}</div>
          <div class="stat-label">已处理视频</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ isLoading ? '...' : successRate }}%</div>
          <div class="stat-label">成功率</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ isLoading ? '...' : queueCount }}</div>
          <div class="stat-label">队列任务</div>
        </div>
        <div class="stat-item">
          <div class="stat-indicator" :class="{ connected: apiConnected, disconnected: !apiConnected }">
            {{ apiConnected ? '🟢' : '🔴' }}
          </div>
          <div class="stat-label">API状态</div>
        </div>
      </div>
    </div>

    <!-- 四步工作流程 -->
    <div class="workflow-container">
      <h2 class="workflow-title">智能创作工作流程</h2>

      <!-- 步骤1：视频转文案 -->
      <div class="workflow-step" :class="{ active: currentStep === 1 }">
        <div class="step-header">
          <div class="step-number">1</div>
          <h3 class="step-title">视频转文案</h3>
          <div class="step-status">
            <span class="batch-info">批量处理：最多10个</span>
          </div>
        </div>

        <div class="step-content">
          <div class="input-methods">
            <div class="input-method">
              <h4>平台URL输入</h4>
              <div class="url-inputs">
                <textarea
                  v-model="urlInputs"
                  placeholder="请输入视频URL，每行一个，最多10个&#10;支持：抖音、快手、小红书、B站等"
                  rows="4"
                  class="url-textarea"
                ></textarea>
                <div class="url-count">{{ urlCount }}/10</div>
              </div>
            </div>

            <div class="input-divider">或</div>

            <div class="input-method">
              <h4>本地视频上传</h4>
              <div class="upload-area" @click="triggerFileUpload" @drop="handleFileDrop" @dragover.prevent>
                <input
                  ref="fileInput"
                  type="file"
                  multiple
                  accept="video/*"
                  @change="handleFileSelect"
                  style="display: none"
                >
                <div class="upload-icon">📁</div>
                <p>点击上传或拖拽视频文件</p>
                <p class="upload-limit">最多10个文件，每个不超过500MB</p>
              </div>
              <div class="uploaded-files" v-if="uploadedFiles.length">
                <div v-for="(file, index) in uploadedFiles" :key="index" class="file-item">
                  <span>{{ file.name }}</span>
                  <button @click="removeFile(index)" class="remove-btn">×</button>
                </div>
              </div>
            </div>
          </div>

          <div class="model-selection">
            <h4>AI模型选择</h4>
            <div class="model-options">
              <label class="model-option recommended">
                <input type="radio" v-model="step1Model" value="gpt4-vision" checked>
                <span class="model-name">GPT-4 Vision</span>
                <span class="model-tag">主推荐</span>
              </label>
              <label class="model-option">
                <input type="radio" v-model="step1Model" value="claude-vision">
                <span class="model-name">Claude Vision</span>
                <span class="model-tag">备选1</span>
              </label>
              <label class="model-option">
                <input type="radio" v-model="step1Model" value="gemini-vision">
                <span class="model-name">Gemini Vision</span>
                <span class="model-tag">备选2</span>
              </label>
            </div>
          </div>

          <div class="step-actions">
            <button
              class="btn btn-primary btn-large"
              @click="startStep1"
              :disabled="!canStartStep1"
            >
              开始转换文案
            </button>
          </div>
        </div>
      </div>

      <!-- 步骤2：文案优化 -->
      <div class="workflow-step" :class="{ active: currentStep === 2, disabled: !step1Completed }">
        <div class="step-header">
          <div class="step-number">2</div>
          <h3 class="step-title">文案二次创作与优化</h3>
          <div class="step-status">
            <span class="batch-info">批量处理：最多10个</span>
          </div>
        </div>

        <div class="step-content">
          <div class="content-preview" v-if="step1Results.length">
            <h4>待优化文案 ({{ step1Results.length }}个)</h4>
            <div class="content-list">
              <div v-for="(result, index) in step1Results" :key="index" class="content-item">
                <div class="content-preview-text">{{ result.content.substring(0, 100) }}...</div>
                <div class="content-meta">来源: {{ result.source }}</div>
              </div>
            </div>
          </div>

          <div class="optimization-options">
            <h4>优化选项</h4>
            <div class="option-grid">
              <label class="option-item">
                <input type="checkbox" v-model="optimizationOptions.tone">
                <span>语调优化</span>
              </label>
              <label class="option-item">
                <input type="checkbox" v-model="optimizationOptions.structure">
                <span>结构调整</span>
              </label>
              <label class="option-item">
                <input type="checkbox" v-model="optimizationOptions.keywords">
                <span>关键词优化</span>
              </label>
              <label class="option-item">
                <input type="checkbox" v-model="optimizationOptions.length">
                <span>长度调整</span>
              </label>
            </div>
          </div>

          <div class="model-selection">
            <h4>AI模型选择</h4>
            <div class="model-options">
              <label class="model-option recommended">
                <input type="radio" v-model="step2Model" value="gpt4-turbo" checked>
                <span class="model-name">GPT-4 Turbo</span>
                <span class="model-tag">主推荐</span>
              </label>
              <label class="model-option">
                <input type="radio" v-model="step2Model" value="claude-3">
                <span class="model-name">Claude 3</span>
                <span class="model-tag">备选1</span>
              </label>
              <label class="model-option">
                <input type="radio" v-model="step2Model" value="gemini-pro">
                <span class="model-name">Gemini Pro</span>
                <span class="model-tag">备选2</span>
              </label>
            </div>
          </div>

          <div class="step-actions">
            <button
              class="btn btn-primary btn-large"
              @click="startStep2"
              :disabled="!canStartStep2"
            >
              开始优化文案
            </button>
          </div>
        </div>
      </div>

      <!-- 步骤3：文案转视频 -->
      <div class="workflow-step" :class="{ active: currentStep === 3, disabled: !step2Completed }">
        <div class="step-header">
          <div class="step-number">3</div>
          <h3 class="step-title">文案转视频</h3>
          <div class="step-status">
            <span class="batch-info">批量处理：最多10个</span>
          </div>
        </div>

        <div class="step-content">
          <div class="content-preview" v-if="step2Results.length">
            <h4>待转换文案 ({{ step2Results.length }}个)</h4>
            <div class="content-list">
              <div v-for="(result, index) in step2Results" :key="index" class="content-item">
                <div class="content-preview-text">{{ result.optimizedContent.substring(0, 100) }}...</div>
                <div class="content-meta">优化完成</div>
              </div>
            </div>
          </div>

          <div class="video-settings">
            <h4>视频设置</h4>
            <div class="settings-grid">
              <div class="setting-item">
                <label>视频尺寸</label>
                <select v-model="videoSettings.size">
                  <option value="9:16">竖屏 (9:16)</option>
                  <option value="16:9">横屏 (16:9)</option>
                  <option value="1:1">方形 (1:1)</option>
                </select>
              </div>
              <div class="setting-item">
                <label>视频时长</label>
                <select v-model="videoSettings.duration">
                  <option value="15">15秒</option>
                  <option value="30">30秒</option>
                  <option value="60">60秒</option>
                </select>
              </div>
              <div class="setting-item">
                <label>背景音乐</label>
                <select v-model="videoSettings.bgMusic">
                  <option value="none">无音乐</option>
                  <option value="upbeat">轻快</option>
                  <option value="calm">舒缓</option>
                  <option value="dramatic">戏剧性</option>
                </select>
              </div>
            </div>
          </div>

          <div class="model-selection">
            <h4>AI模型选择</h4>
            <div class="model-options">
              <label class="model-option recommended">
                <input type="radio" v-model="step3Model" value="runway-gen2" checked>
                <span class="model-name">Runway Gen-2</span>
                <span class="model-tag">主推荐</span>
              </label>
              <label class="model-option">
                <input type="radio" v-model="step3Model" value="stable-video">
                <span class="model-name">Stable Video</span>
                <span class="model-tag">备选1</span>
              </label>
              <label class="model-option">
                <input type="radio" v-model="step3Model" value="pika-labs">
                <span class="model-name">Pika Labs</span>
                <span class="model-tag">备选2</span>
              </label>
            </div>
          </div>

          <div class="step-actions">
            <button
              class="btn btn-primary btn-large"
              @click="startStep3"
              :disabled="!canStartStep3"
            >
              开始生成视频
            </button>
          </div>
        </div>
      </div>

      <!-- 步骤4：多平台分发 -->
      <div class="workflow-step" :class="{ active: currentStep === 4, disabled: !step3Completed }">
        <div class="step-header">
          <div class="step-number">4</div>
          <h3 class="step-title">多平台分发</h3>
          <div class="step-status">
            <span class="batch-info">批量处理：最多10个</span>
          </div>
        </div>

        <div class="step-content">
          <div class="content-preview" v-if="step3Results.length">
            <h4>待分发视频 ({{ step3Results.length }}个)</h4>
            <div class="video-list">
              <div v-for="(result, index) in step3Results" :key="index" class="video-item">
                <div class="video-thumbnail">
                  <video :src="result.videoUrl" width="120" height="68" controls></video>
                </div>
                <div class="video-info">
                  <div class="video-title">{{ result.title }}</div>
                  <div class="video-meta">{{ result.duration }}s | {{ result.size }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="platform-selection">
            <h4>选择分发平台</h4>
            <div class="platform-grid">
              <label class="platform-item">
                <input type="checkbox" v-model="selectedPlatforms.douyin">
                <div class="platform-icon">🎵</div>
                <span>抖音</span>
              </label>
              <label class="platform-item">
                <input type="checkbox" v-model="selectedPlatforms.kuaishou">
                <div class="platform-icon">⚡</div>
                <span>快手</span>
              </label>
              <label class="platform-item">
                <input type="checkbox" v-model="selectedPlatforms.xiaohongshu">
                <div class="platform-icon">📕</div>
                <span>小红书</span>
              </label>
              <label class="platform-item">
                <input type="checkbox" v-model="selectedPlatforms.bilibili">
                <div class="platform-icon">📺</div>
                <span>B站</span>
              </label>
              <label class="platform-item">
                <input type="checkbox" v-model="selectedPlatforms.weibo">
                <div class="platform-icon">🐦</div>
                <span>微博</span>
              </label>
              <label class="platform-item">
                <input type="checkbox" v-model="selectedPlatforms.wechat">
                <div class="platform-icon">💬</div>
                <span>微信视频号</span>
              </label>
            </div>
          </div>

          <div class="publish-settings">
            <h4>发布设置</h4>
            <div class="settings-grid">
              <div class="setting-item">
                <label>发布时间</label>
                <select v-model="publishSettings.timing">
                  <option value="now">立即发布</option>
                  <option value="scheduled">定时发布</option>
                  <option value="optimal">最佳时间</option>
                </select>
              </div>
              <div class="setting-item" v-if="publishSettings.timing === 'scheduled'">
                <label>定时时间</label>
                <input type="datetime-local" v-model="publishSettings.scheduledTime">
              </div>
              <div class="setting-item">
                <label>标签策略</label>
                <select v-model="publishSettings.tagStrategy">
                  <option value="auto">自动生成</option>
                  <option value="custom">自定义</option>
                  <option value="trending">热门标签</option>
                </select>
              </div>
            </div>
          </div>

          <div class="model-selection">
            <h4>AI模型选择</h4>
            <div class="model-options">
              <label class="model-option recommended">
                <input type="radio" v-model="step4Model" value="platform-optimizer" checked>
                <span class="model-name">Platform Optimizer</span>
                <span class="model-tag">主推荐</span>
              </label>
              <label class="model-option">
                <input type="radio" v-model="step4Model" value="content-adapter">
                <span class="model-name">Content Adapter</span>
                <span class="model-tag">备选1</span>
              </label>
              <label class="model-option">
                <input type="radio" v-model="step4Model" value="social-ai">
                <span class="model-name">Social AI</span>
                <span class="model-tag">备选2</span>
              </label>
            </div>
          </div>

          <div class="step-actions">
            <button
              class="btn btn-primary btn-large"
              @click="startStep4"
              :disabled="!canStartStep4"
            >
              开始分发
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'

// 工作流程状态
const currentStep = ref(1)
const step1Completed = ref(false)
const step2Completed = ref(false)
const step3Completed = ref(false)

// 统计数据
const processedCount = ref(0)
const successRate = ref(0)
const queueCount = ref(0)
const isLoading = ref(true)
const apiConnected = ref(false)

// 步骤1：视频转文案
const urlInputs = ref('')
const uploadedFiles = ref<File[]>([])
const step1Model = ref('gpt4-vision')
const step1Results = ref<any[]>([])

// 步骤2：文案优化
const optimizationOptions = reactive({
  tone: true,
  structure: true,
  keywords: false,
  length: true
})
const step2Model = ref('gpt4-turbo')
const step2Results = ref<any[]>([])

// 步骤3：文案转视频
const videoSettings = reactive({
  size: '9:16',
  duration: '30',
  bgMusic: 'upbeat'
})
const step3Model = ref('runway-gen2')
const step3Results = ref<any[]>([])

// 步骤4：多平台分发
const selectedPlatforms = reactive({
  douyin: true,
  kuaishou: true,
  xiaohongshu: false,
  bilibili: false,
  weibo: false,
  wechat: false
})
const publishSettings = reactive({
  timing: 'optimal',
  scheduledTime: '',
  tagStrategy: 'auto'
})
const step4Model = ref('platform-optimizer')

// 计算属性
const urlCount = computed(() => {
  return urlInputs.value.split('\n').filter(url => url.trim()).length
})

const canStartStep1 = computed(() => {
  return urlInputs.value.trim() || uploadedFiles.value.length > 0
})

const canStartStep2 = computed(() => {
  return step1Completed.value && step1Results.value.length > 0
})

const canStartStep3 = computed(() => {
  return step2Completed.value && step2Results.value.length > 0
})

const canStartStep4 = computed(() => {
  return step3Completed.value && step3Results.value.length > 0
})

// 文件处理
const fileInput = ref<HTMLInputElement>()

const triggerFileUpload = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    const newFiles = Array.from(target.files).slice(0, 10 - uploadedFiles.value.length)
    uploadedFiles.value.push(...newFiles)
  }
}

const handleFileDrop = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer?.files) {
    const newFiles = Array.from(event.dataTransfer.files).slice(0, 10 - uploadedFiles.value.length)
    uploadedFiles.value.push(...newFiles)
  }
}

const removeFile = (index: number) => {
  uploadedFiles.value.splice(index, 1)
}

// 初始化数据
const initializeData = async () => {
  try {
    isLoading.value = true

    // 测试API连接
    try {
      const response = await fetch('/api/health')
      apiConnected.value = response.ok

      if (apiConnected.value) {
        // 获取系统统计
        const statsResponse = await fetch('/api/stats')
        const stats = await statsResponse.json()
        processedCount.value = stats.total_processing
        queueCount.value = stats.total_projects
        successRate.value = stats.total_processing > 0 ? 95.5 : 0
      }
    } catch (error) {
      apiConnected.value = false
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    apiConnected.value = false
  } finally {
    isLoading.value = false
  }
}

// 工作流程方法
const startStep1 = async () => {
  console.log('开始步骤1：视频转文案')
  currentStep.value = 1

  try {
    // 准备输入数据
    const inputData = urlInputs.value.trim() || uploadedFiles.value.map(f => f.name).join(', ')

    // 调用生产API
    const response = await fetch('/api/workflow/process', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        step: 1,
        input_type: urlInputs.value.trim() ? 'url' : 'file',
        input_data: inputData,
        ai_model: step1Model.value,
        user_id: 1
      })
    })
    const data = await response.json()

    if (response.ok && data.success) {
      step1Results.value = [
        {
          content: `处理结果: ${data.output.result}`,
          source: data.output.input_type === 'url' ? 'URL输入' : '本地上传',
          processing_id: data.processing_id
        }
      ]
      step1Completed.value = true
      currentStep.value = 2
    }
  } catch (error) {
    console.error('步骤1处理失败:', error)
    // 降级到模拟数据
    step1Results.value = [
      { content: '这是一个关于美食制作的视频文案...', source: 'URL输入' },
      { content: '旅行vlog的精彩内容描述...', source: '本地上传' }
    ]
    step1Completed.value = true
    currentStep.value = 2
  }
}

const startStep2 = async () => {
  console.log('开始步骤2：文案优化')
  currentStep.value = 2

  try {
    // 调用生产API
    const response = await fetch('/api/workflow/process', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        step: 2,
        input_type: 'text',
        input_data: step1Results.value.map(r => r.content).join('\n'),
        ai_model: step2Model.value,
        user_id: 1
      })
    })
    const data = await response.json()

    if (response.ok && data.success) {
      step2Results.value = step1Results.value.map(result => ({
        ...result,
        optimizedContent: result.content + ' [AI优化完成]'
      }))
      step2Completed.value = true
      currentStep.value = 3
    }
  } catch (error) {
    console.error('步骤2处理失败:', error)
    // 降级处理
    step2Results.value = step1Results.value.map(result => ({
      ...result,
      optimizedContent: result.content + ' [已优化]'
    }))
    step2Completed.value = true
    currentStep.value = 3
  }
}

const startStep3 = async () => {
  console.log('开始步骤3：文案转视频')
  currentStep.value = 3

  // 模拟处理
  setTimeout(() => {
    step3Results.value = step2Results.value.map((result, index) => ({
      ...result,
      videoUrl: `/mock-video-${index}.mp4`,
      title: `生成视频 ${index + 1}`,
      duration: videoSettings.duration,
      size: videoSettings.size
    }))
    step3Completed.value = true
    currentStep.value = 4
  }, 3000)
}

const startStep4 = async () => {
  console.log('开始步骤4：多平台分发')
  currentStep.value = 4

  try {
    // 调用生产API
    const response = await fetch('/api/workflow/process', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        step: 4,
        input_type: 'video',
        input_data: step3Results.value.map(r => r.title).join(', '),
        ai_model: step4Model.value,
        user_id: 1
      })
    })
    const data = await response.json()

    if (response.ok && data.success) {
      console.log('分发完成！')
      processedCount.value += step3Results.value.length
    }
  } catch (error) {
    console.error('步骤4处理失败:', error)
    // 降级处理
    console.log('分发完成！')
    processedCount.value += step3Results.value.length
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  initializeData()
})
</script>

<style scoped>
/* 引入统一设计系统 */
@import url('/shared-ui-design.css');

/* 首页特定样式 */
.home-dashboard {
  padding: 0;
}

.welcome-section {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  padding: var(--space-12) var(--space-6);
  margin: calc(-1 * var(--space-6));
  margin-bottom: var(--space-8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--radius-xl);
}

.welcome-content {
  flex: 1;
}

.welcome-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  line-height: 1.2;
}

.welcome-subtitle {
  font-size: var(--text-lg);
  opacity: 0.9;
  max-width: 600px;
}

.workflow-stats {
  display: flex;
  gap: var(--space-8);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-sm);
  opacity: 0.8;
}

.stat-indicator {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-1);
}

.stat-indicator.connected {
  color: var(--green-500);
}

.stat-indicator.disconnected {
  color: var(--red-500);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.feature-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-200);
}

.feature-card__header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.feature-card__icon {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-card__icon--primary {
  background: var(--primary-50);
  color: var(--primary-600);
}

.feature-card__title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.feature-card__description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.feature-card__actions {
  display: flex;
  gap: var(--space-3);
}

/* 工作流程样式 */
.workflow-container {
  max-width: 1200px;
  margin: 0 auto;
}

.workflow-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--space-8);
  color: var(--gray-900);
}

.workflow-step {
  background: white;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-6);
  transition: all 0.3s ease;
}

.workflow-step.active {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.workflow-step.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.step-header {
  display: flex;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.step-number {
  width: 40px;
  height: 40px;
  background: var(--primary-500);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  margin-right: var(--space-4);
}

.step-title {
  flex: 1;
  font-size: var(--text-xl);
  font-weight: 600;
  margin: 0;
}

.batch-info {
  background: var(--blue-100);
  color: var(--blue-800);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 500;
}

.step-content {
  padding: var(--space-6);
}

.input-methods {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.input-method h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.url-inputs {
  position: relative;
}

.url-textarea {
  width: 100%;
  min-height: 120px;
  padding: var(--space-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-family: inherit;
  resize: vertical;
}

.url-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.url-count {
  position: absolute;
  bottom: var(--space-2);
  right: var(--space-2);
  background: var(--gray-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  color: var(--gray-600);
}

.input-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--gray-500);
}

.upload-area {
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-md);
  padding: var(--space-8);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: var(--primary-500);
  background: var(--primary-50);
}

.upload-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-4);
}

.upload-limit {
  font-size: var(--text-sm);
  color: var(--gray-500);
  margin-top: var(--space-2);
}

.uploaded-files {
  margin-top: var(--space-4);
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-2) var(--space-3);
  background: var(--gray-100);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-2);
}

.remove-btn {
  background: var(--red-500);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: var(--space-6);
  }

  .workflow-stats {
    justify-content: center;
  }

  .input-methods {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .input-divider {
    display: none;
  }
}

/* 模型选择样式 */
.model-selection {
  margin-bottom: var(--space-6);
}

.model-selection h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.model-options {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.model-option {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.model-option:hover {
  border-color: var(--primary-400);
  background: var(--primary-50);
}

.model-option.recommended {
  border-color: var(--primary-500);
  background: var(--primary-50);
}

.model-option input[type="radio"] {
  margin-right: var(--space-2);
}

.model-name {
  font-weight: 600;
  margin-right: var(--space-2);
}

.model-tag {
  background: var(--primary-500);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}

.model-option.recommended .model-tag {
  background: var(--green-500);
}

/* 步骤操作按钮 */
.step-actions {
  display: flex;
  justify-content: center;
  margin-top: var(--space-6);
}

.btn-large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  font-weight: 600;
}

/* 内容预览样式 */
.content-preview {
  margin-bottom: var(--space-6);
}

.content-preview h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.content-list {
  display: grid;
  gap: var(--space-3);
}

.content-item {
  background: var(--gray-50);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-500);
}

.content-preview-text {
  font-size: var(--text-sm);
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.content-meta {
  font-size: var(--text-xs);
  color: var(--gray-500);
  font-weight: 500;
}

/* 优化选项样式 */
.optimization-options {
  margin-bottom: var(--space-6);
}

.optimization-options h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.option-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

.option-item {
  display: flex;
  align-items: center;
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-item:hover {
  background: var(--primary-50);
}

.option-item input[type="checkbox"] {
  margin-right: var(--space-2);
}

/* 视频设置样式 */
.video-settings {
  margin-bottom: var(--space-6);
}

.video-settings h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.setting-item {
  display: flex;
  flex-direction: column;
}

.setting-item label {
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--gray-700);
}

.setting-item select,
.setting-item input {
  padding: var(--space-3);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-family: inherit;
}

.setting-item select:focus,
.setting-item input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

/* 视频列表样式 */
.video-list {
  display: grid;
  gap: var(--space-4);
}

.video-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.video-thumbnail {
  flex-shrink: 0;
}

.video-thumbnail video {
  border-radius: var(--radius-md);
}

.video-info {
  flex: 1;
}

.video-title {
  font-weight: 600;
  margin-bottom: var(--space-1);
  color: var(--gray-900);
}

.video-meta {
  font-size: var(--text-sm);
  color: var(--gray-500);
}

/* 平台选择样式 */
.platform-selection {
  margin-bottom: var(--space-6);
}

.platform-selection h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.platform-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-3);
}

.platform-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.platform-item:hover {
  border-color: var(--primary-400);
  background: var(--primary-50);
}

.platform-item input[type="checkbox"]:checked + .platform-icon + span {
  color: var(--primary-600);
  font-weight: 600;
}

.platform-item input[type="checkbox"] {
  position: absolute;
  opacity: 0;
}

.platform-icon {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-2);
}

.platform-item span {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--gray-700);
}

/* 发布设置样式 */
.publish-settings {
  margin-bottom: var(--space-6);
}

.publish-settings h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}
</style>