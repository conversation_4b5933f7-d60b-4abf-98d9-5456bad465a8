<template>
  <div class="home-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">
          欢迎使用二创短视频分发系统
        </h1>
        <p class="welcome-subtitle">
          基于先进的AI技术，为您提供专业的视频内容创作解决方案
        </p>
      </div>
      <div class="welcome-stats">
        <div class="stat-item">
          <div class="stat-number">1,234</div>
          <div class="stat-label">视频已生成</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">98.5%</div>
          <div class="stat-label">成功率</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">24/7</div>
          <div class="stat-label">在线服务</div>
        </div>
      </div>
    </div>

    <!-- 功能卡片 -->
    <div class="features-grid">
      <div class="feature-card">
        <div class="feature-card__header">
          <div class="feature-card__icon feature-card__icon--primary">
            <VideoIcon class="w-6 h-6" />
          </div>
          <h3 class="feature-card__title">智能视频生成</h3>
        </div>
        <p class="feature-card__description">
          利用AI技术自动生成高质量视频内容，支持多种风格和主题
        </p>
        <div class="feature-card__actions">
          <router-link to="/video-creation" class="btn btn-primary">
            开始创作
          </router-link>
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-card__header">
          <div class="feature-card__icon feature-card__icon--success">
            <CpuIcon class="w-6 h-6" />
          </div>
          <h3 class="feature-card__title">计算引擎</h3>
        </div>
        <p class="feature-card__description">
          强大的本地计算能力，支持FFmpeg、TensorFlow.js等核心技术
        </p>
        <div class="feature-card__actions">
          <router-link to="/compute-test" class="btn btn-primary">
            测试引擎
          </router-link>
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-card__header">
          <div class="feature-card__icon feature-card__icon--warning">
            <SparklesIcon class="w-6 h-6" />
          </div>
          <h3 class="feature-card__title">AI增强</h3>
        </div>
        <p class="feature-card__description">
          集成多种AI模型，提供智能内容分析和优化建议
        </p>
        <div class="feature-card__actions">
          <button class="btn btn-secondary" disabled>
            即将推出
          </button>
        </div>
      </div>
    </div>

    <!-- 技术特性 -->
    <div class="bg-gray-50 rounded-xl p-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">
        核心技术特性
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CodeIcon class="w-8 h-8 text-blue-600" />
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">FFmpeg WASM</h3>
          <p class="text-sm text-gray-600">浏览器内视频处理</p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <BrainIcon class="w-8 h-8 text-green-600" />
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">TensorFlow.js</h3>
          <p class="text-sm text-gray-600">机器学习推理</p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <ZapIcon class="w-8 h-8 text-purple-600" />
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">WebGPU</h3>
          <p class="text-sm text-gray-600">GPU加速计算</p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <MicIcon class="w-8 h-8 text-orange-600" />
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Web Speech</h3>
          <p class="text-sm text-gray-600">语音识别合成</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Video as VideoIcon,
  Cpu as CpuIcon,
  Sparkles as SparklesIcon,
  Code as CodeIcon,
  Brain as BrainIcon,
  Zap as ZapIcon,
  Mic as MicIcon,
} from 'lucide-vue-next'
</script>

<style scoped>
/* 引入统一设计系统 */
@import url('/shared-ui-design.css');

/* 首页特定样式 */
.home-dashboard {
  padding: 0;
}

.welcome-section {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  padding: var(--space-12) var(--space-6);
  margin: calc(-1 * var(--space-6));
  margin-bottom: var(--space-8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--radius-xl);
}

.welcome-content {
  flex: 1;
}

.welcome-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  line-height: 1.2;
}

.welcome-subtitle {
  font-size: var(--text-lg);
  opacity: 0.9;
  max-width: 600px;
}

.welcome-stats {
  display: flex;
  gap: var(--space-8);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-sm);
  opacity: 0.8;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.feature-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-200);
}

.feature-card__header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.feature-card__icon {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-card__icon--primary {
  background: var(--primary-50);
  color: var(--primary-600);
}

.feature-card__title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.feature-card__description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.feature-card__actions {
  display: flex;
  gap: var(--space-3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: var(--space-6);
  }

  .welcome-stats {
    justify-content: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>