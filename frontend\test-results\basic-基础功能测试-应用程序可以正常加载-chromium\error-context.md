# Page snapshot

```yaml
- banner:
  - button "切换侧边栏":
    - img
  - heading "AI视频内容创作系统" [level=1]
  - button "切换主题":
    - img
  - button "切换状态栏":
    - img
  - button "用户菜单":
    - img
- complementary "主导航侧边栏":
  - text: AI
  - heading "AI视频创作" [level=1]
  - navigation "主导航菜单":
    - link "首页":
      - /url: /
      - img
      - text: 首页
    - link "视频创作":
      - /url: /video-creation
      - img
      - text: 视频创作
    - link "计算引擎测试":
      - /url: /compute-test
      - img
      - text: 计算引擎测试
    - link "设置":
      - /url: /settings
      - img
      - text: 设置
    - text: AI工具
    - link "AI对话":
      - /url: /ai-chat
      - img
      - text: AI对话
    - link "脚本生成":
      - /url: /script-generator
      - img
      - text: 脚本生成
    - link "图像生成":
      - /url: /image-generator
      - img
      - text: 图像生成
  - button "切换侧边栏折叠":
    - img
    - text: 折叠
- main:
  - heading "AI视频创作系统" [level=1]
  - paragraph: 基于先进的AI技术，为您提供专业的视频内容创作解决方案
  - img
  - heading "智能视频生成" [level=3]
  - paragraph: 利用AI技术自动生成高质量视频内容，支持多种风格和主题
  - link "开始创作":
    - /url: /video-creation
  - img
  - heading "计算引擎" [level=3]
  - paragraph: 强大的本地计算能力，支持FFmpeg、TensorFlow.js等核心技术
  - link "测试引擎":
    - /url: /compute-test
  - img
  - heading "AI增强" [level=3]
  - paragraph: 集成多种AI模型，提供智能内容分析和优化建议
  - button "即将推出" [disabled]
  - heading "核心技术特性" [level=2]
  - img
  - heading "FFmpeg WASM" [level=3]
  - paragraph: 浏览器内视频处理
  - img
  - heading "TensorFlow.js" [level=3]
  - paragraph: 机器学习推理
  - img
  - heading "WebGPU" [level=3]
  - paragraph: GPU加速计算
  - img
  - heading "Web Speech" [level=3]
  - paragraph: 语音识别合成
- complementary:
  - complementary:
    - heading "系统状态" [level=2]
    - heading "系统监控" [level=3]
    - text: CPU使用率 63% 内存使用率 49% 存储空间 52%
    - heading "AI模型状态" [level=3]
    - text: 文本模型 online 对话模型 online 图像模型 loading 语音模型 offline
    - heading "任务队列" [level=3]
    - text: 3 个任务 视频转文案处理 processing 83% 2分钟 内容生成任务 waiting 0% 等待中 文案优化 completed 100% 已完成
    - heading "最近通知" [level=3]
    - button "全部标记为已读"
    - paragraph: 任务完成
    - paragraph: 视频转文案任务已成功完成
    - paragraph: 2分钟前
    - paragraph: 系统更新
    - paragraph: AI模型已更新到最新版本
    - paragraph: 1小时前
    - paragraph: 存储警告
    - paragraph: 存储空间使用率已达到80%
    - paragraph: 3小时前
    - button "刷新状态"
    - button "系统设置"
```